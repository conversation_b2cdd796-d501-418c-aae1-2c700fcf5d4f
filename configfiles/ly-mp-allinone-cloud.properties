server.contextPath=/
# 分布式端口
server.port=8100
# tomcat最大线程数，默认为200
server.tomcat.max-threads=500
# tomcat的URI编码
server.tomcat.uri-encoding=UTF-8
# 存放Tomcat的日志、Dump等文件的临时文件夹，默认为系统的tmp文件夹（如：C:\Users\<USER>\AppData\Local\Temp）
server.tomcat.basedir=/springboot/allinone
# 打开Tomcat的Access日志，并可以设置日志格式的方法：
server.tomcat.access-log-enabled=true
# 日志文件目录
logging.path=/springboot/allinone
# 日志文件名称，默认为spring.log
logging.file=myapp.log

logging.config=classpath:logback-spring.xml
#logging.level.org.springframework.web=INFO  

#server.ssl.key-store: classpath:mp_keystore.p12
#server.ssl.key-store-password: szlanyou123
#server.ssl.keyStoreType: PKCS12
#server.ssl.keyAlias: szlanyou_mp

#ActiveProfile
spring.profiles.active=dev

#jackson
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.locale=zh_CN
spring.jackson.time-zone=GMT+8
spring.mvc.locale=zh_CN 

# Enable support of multi-part uploads.
multipart.enabled=true 
# Max file size. Values can use the suffixed "MB" or "KB" to indicate a Megabyte or Kilobyte size.
spring.servlet.multipart.max-file-size=50MB
# Max request size. Values can use the suffixed "MB" or "KB" to indicate a Megabyte or Kilobyte size.
spring.servlet.multipart.max-request-size=100MB

#--------------------短信验证码配置--------------------
#mp.auth.smsContentFormat=短信验证码:%s
mp.auth.smsContentFormat=登录验证码:%s
#--------------------短信验证码配置--------------------

#--------------------CC集成配置--------------------

mp.auth.cc.tompKey=ccmpjavaccmpjava

mp.auth.cc.toccKey=ccmpjavaccmpjava

#--------------------CC集成配置--------------------

#Job Service
#平台
#0  默认值（cp)
#1  eap
mp.job.platform=0
#邮件扫描间隔
mp.job.alertServiceMailInterval=0/12 * * * * ?
#业务邮件提醒发送频率
mp.job.businessRemindEmailInterval=0/12 * * * * ?
#个人提醒设置应用名：工作流
mp.job.remind.wfFunctionCode=CPP010404
#业务提醒应用功能编号：
mp.job.remind.businessFunctionCode=CPP010404
#引擎提醒发送错误，尝试次数
mp.job.remind.wfFailTime=3
#业务提醒发送错误，尝试次数
mp.job.remind.businessFaileTime=3
#邮件签名
#mp.job.mailSignature=邮件签名
mp.job.mailSignature=邮件签名
#value为true时,是邮件的测试模式,测试模式将不会向目标用户发送邮件
mp.job.mailTest=false
#邮件的测试模式时,所有邮件都将发送至该邮件
mp.job.testMailAccount=<EMAIL>
#邮件发送免打扰时间段
#格式说明，24小时制，前面时间必须小于后面时间，否则无效
#中间用英文逗号隔开
mp.job.emailNoDisturbTimes=00:00-8:30,11:30-13:30,18:00-24:00
#短信发送免打扰时间段
#格式同上
mp.job.sMSNoDisturbTimes=00:00-8:30,11:30-13:30,18:00-24:00
#邮件汇总时间，中间用逗号隔开
mp.job.emailGatherTimes=08:30,18:00
#汇总发送邮件标题
#mp.job.emailGatherTitle=新[PQC]系统邮件提醒
mp.job.emailGatherTitle=新[PQC]系统邮件提醒
#汇总邮件模板
#mp.job.emailGatherModel=@sendtouser 您好<br/>新PQC中，您总共待处理待办有@pengdingcount条<br/><br/>请尽快处理<a href='@url'>邮件汇总链接</a>,谢谢！@sendtouser 您好<br/>新PQC中，您总共待处理待办有@pengdingcount条<br/><br/>请尽快处理<a href='@url'>邮件汇总链接</a>,谢谢！
mp.job.emailGatherModel=@sendtouser 您好<br/>新PQC中，您总共待处理待办有@pengdingcount条<br/><br/>请尽快处理<a href='@url'>邮件汇总链接</a>,谢谢！
#必配项
##邮件汇总审批web服务接口，将localhost修改为入口地址，如：*************:8100
mp.job.emailGatherWebService=http://*************/
#短信服务器IP地址
mp.job.sms.serverID=************
#短信服务登录名
mp.job.sms.loginName=MP
#短信服务登录密码
mp.job.sms.loginPWD=XXX
#短信服务器端口号
mp.job.sms.serverPort=8003
#获取短信回执的频率
mp.job.sms.rptInterval=0 0/1 * * * ?
#value为true时,是短信的测试模式,测试模式将不会向短信服务器发送短信请求
mp.job.sms.test=false
#短信扫描间隔,单位秒
mp.job.alertServiceSMSInterval=0/12 * * * * ?
#业务短信提醒发送频率（单位：秒）
mp.job.businessRemindSmsInterval=0/12 * * * * ?
#自动审批作业时间间隔
mp.job.autoAuditInterval=0/12 * * * * ?
#必配项
#登录服务器地址，将*************修改为入口地址，如：*************:8100
mp.job.loginWebService=http://*************/mp/login/login.do
#数据归档条件：配置运行的时间点，如配置1，则运行时间点在凌晨1点运行
mp.job.archive.runTime=0 0/10 * * * ?
#数据归档条件：配置是否执行归档服务，true表示执行；false表示不执行
mp.job.archive.runStatus=true
#数据归档条件：配置需要归档流程跑完后多久的数据，单位：天
mp.job.archive.dataInterval=60
#转移待办数据时间
mp.job.archive.transPendingTime=0 0 * * * ?

#设置代理状态，1分钟执行一次
mp.job.archive.wfAgentStatusTime=0 0/1 * * * ?

#日志归档条件：配置运行的时间点，如配置1，则运行时间点在凌晨1点运行 
# 构建时间 Schedule 中填写 0 * * * * 多个构建时间之间，通过换行符隔开 第一个参数代表的是分钟 minute，取值
#0~59； 第二个参数代表的是小时 hour，取值 0~23； 第三个参数代表的是天 day，取值 1~31； 第四个参数代表的是月
# month，取值 1~12； 最后一个参数代表的是星期 week，取值 0~7，0 和 7 都是表示星期天。
mp.job.archive.logFilingTime=0 26 9 * * ?
#日志归档条件：配置是否执行归档服务，true表示执行；false表示不执行
mp.job.archive.logFilingStatus=true
#运行日志归档条件：配置需要归档流程跑完后多久的数据，单位：天
mp.job.archive.log_runInterval=11
#业务日志归档条件：配置需要归档流程跑完后多久的数据，单位：天
mp.job.archive.log_bssInterval=11
#菜单日志归档条件：配置需要归档流程跑完后多久的数据，单位：天
mp.job.archive.log_navigationInterval=11
#服务调用日志归档条件：配置需要归档流程跑完后多久的数据，单位：天
mp.job.archive.log_invokingInterval=11
#是否开启自动发起服务 0：不开启  1：开启
mp.job.autocreateOpen=1
#自动发起服务扫描间隔
mp.job.autocreateInterval=0/12 * * * * ?
#必配项
#配置格式说明：
#     每个应用的配置项采用 | 间隔
#     每个应用采用 # 间隔
#     平台说明：1 iPhone端，  2 iPad端，3 Android端（包含手机、平板）
#     样例：平台1|MasterSecretKey1|AppKey1#平台2|MasterSecretKey2|AppKey2
#     配置极光服务应用信息
mp.job.push.jPushKey=1|4ae744bd2ea227ce25f51149|6472d7dace866902f97221de#2|1fc3f64c1dfe8545dfd40ee2|b496edffe14a976e10a7907b#3|1fc3f64c1dfe8545dfd40ee2|b496edffe14a976e10a7907b
#消息推送提醒内容，格式如"[员工名]发起的关于[流程名]的决策事项，请您审批！",其中"员工名"使用 emp_name代替,"流程名"使用name代替
#mp.job.push.alertMsg=[emp_name]发起的关于[name]的决策事项，请您审批！
mp.job.push.alertMsg=【emp_name】发起的关于[name]的决策事项，请您审批！
#iOS推送APNS环境，true 生成环境，false 测试环境，默认"true"
mp.job.push.apnsProduction=true
#消息推送标签，用来区分不同系统推送，默认"DOA"
mp.job.push.pushTag=DOA
#Android提示标题，默认"MP平台"
mp.job.push.androidTitle=MP-JAVA-Mysql
#消息推送时间间隔
mp.job.push.pushInterval=0 0/2 * * * ?
#配置提示语的最大允许长度，单位字符长度
mp.job.push.msgLength=40
#任务加载的外部jar，虚拟机专用
#以每个jar以file协议(Windows下以file:/)开始，多个jar之间用,分隔
mp.job.external.jars=file:/home/<USER>/mpjob/ly.mp.project.job.sample.jar
#服务启动时启用服务的id，多个id之间用,分隔
mp.job.startJobs=082e2784cc4545c081b73e433efb03d1,102039409a7546f990ae25019f6d25ad,1e9380a928dc4bbd9e610c2e58a0fc6f,23afed65848b4c26bca1e5b583065679,33d4988629ce40e5acf9827119156d23,4917be7d0f1e418ead9ec02dac94ff01,4a32961509b645588cbf5b9a36c2cb4a,604e960fb41248979c2a6514a2385f1d,6e5d92e12e964c45ac1a1e484c8f83c8,763cec5e05164061a899c1f4b5bdaf65,78b6bbac36564d0eb50af83ef283d6c7,9943c67af06e470ead7de0ec4f0455a8,a54e39253b0640cfa509c15c23a903f5,b04318f3f3ff4f1aa34ff8a64c7de91c,b25b26a6064048d2ba1b9982e2419dfc

#==============================================================
#Configure Main Scheduler Properties  
#==============================================================
#配置集群时，quartz调度器的id，由于配置集群时，只有一个调度器，必须保证每个服务器该值都相同，可以不用修改，只要每个ams都一样就行
org.quartz.scheduler.instanceName=ly-mp-job
#集群中每台服务器自己的id，AUTO表示自动生成，无需修改
org.quartz.scheduler.instanceId=AUTO  
#==============================================================
#Configure ThreadPool  
#==============================================================
#quartz线程池的实现类，无需修改   
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool 
#quartz线程池中线程数，可根据任务数量和负责度来调整  
org.quartz.threadPool.threadCount=5   
#quartz线程优先级
org.quartz.threadPool.threadPriority=5  
#==============================================================
#Configure JobStore  
#==============================================================
#表示如果某个任务到达执行时间，而此时线程池中没有可用线程时，任务等待的最大时间，如果等待时间超过下面配置的值(毫秒)，本次就不在执行，而等待下一次执行时间的到来，可根据任务量和负责程度来调整
org.quartz.jobStore.misfireThreshold=60000   
#实现集群时，任务的存储实现方式，org.quartz.impl.jdbcjobstore.JobStoreTX表示数据库存储，无需修改，（org.quartz.simpl.RAMJobStore存放在内存，重启会丢失，此种方式需将后面的配置删除）
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX 
#quartz存储任务相关数据的表的前缀，无需修改 
org.quartz.jobStore.tablePrefix=qrtz_ 
#连接数据库数据源名称，与下面配置中org.quartz.dataSource.myDS的myDS一致即可，可以无需修改  
org.quartz.jobStore.dataSource=myDS
#是否启用集群，启用，改为true,注意：启用集群后，必须配置下面的数据源，否则quartz调度器会初始化失败   
org.quartz.jobStore.isClustered=true  
#集群中服务器相互检测间隔(毫秒)，每台服务器都会按照下面配置的时间间隔往服务器中更新自己的状态，如果某台服务器超过以下时间没有checkin，调度器就会认为该台服务器已经down掉，不会再分配任务给该台服务器
org.quartz.jobStore.clusterCheckinInterval=20000
#==============================================================
#Non-Managed Configure Datasource  
#==============================================================

org.quartz.dataSource.myDS.maxConnections=10

spring.application.name=ly-mp-allinone-cloud