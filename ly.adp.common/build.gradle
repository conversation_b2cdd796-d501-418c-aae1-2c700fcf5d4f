apply from: "$rootDir/gradle/docker.gradle"

dependencies {
    compile libs.swagger2Core
    implementation group: 'org.antlr', name: 'antlr4-runtime', version: '4.9.2'
	compile fileTree(dir: "$rootDir/extjar/bucn/desensitize/", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/xapi/dist/", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/valid/", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/config/", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/message/", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/bucnpack/", include: ['ly.bucn.component.ms*.jar'])
	compile(group: 'com.caucho', name: 'hessian', version: '4.0.65') { transitive(false) }
	
	implementation group: 'org.apache.pdfbox', name: 'pdfbox', version: '2.0.10'	
	implementation group: 'org.apache.pdfbox', name: 'fontbox', version: '2.0.10'
	
}

processResources {
	if (project.hasProperty('docker')){
		include '**/*'
	}else{
		include '**/*'
		exclude '**/*.xml'
		exclude '**/*.properties'
	}
}

