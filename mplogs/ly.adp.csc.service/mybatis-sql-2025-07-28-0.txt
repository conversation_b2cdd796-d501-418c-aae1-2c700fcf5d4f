2025-07-28 16:56:55,056 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectAccBuActivityCustomer
2025-07-28 16:56:55,061 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.insertAccBuActivityCustomer
2025-07-28 16:56:55,063 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.updateAccBuActivityCustomer
2025-07-28 16:56:55,131 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.checkSmallType
2025-07-28 16:56:55,132 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.updateSmallType
2025-07-28 16:56:55,133 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.insertSmallType
2025-07-28 16:56:55,133 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.activityApplyInfoSave
2025-07-28 16:56:55,134 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.checkRepeat
2025-07-28 16:56:55,134 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.updateAppId
2025-07-28 16:56:55,135 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.querySignNum
2025-07-28 16:56:55,135 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.queryActivityInfo
2025-07-28 16:56:55,135 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.queryActivityInfoSpecifyFields
2025-07-28 16:56:55,136 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.queryActivityApplyId
2025-07-28 16:56:55,136 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.queryUsername
2025-07-28 16:56:55,137 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.checkData
2025-07-28 16:56:55,137 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.getMdmDlrInfoQuery
2025-07-28 16:56:55,138 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.updateSignNum
2025-07-28 16:56:55,222 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.insert
2025-07-28 16:56:55,230 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.delete
2025-07-28 16:56:55,232 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.deleteByMap
2025-07-28 16:56:55,234 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.deleteById
2025-07-28 16:56:55,236 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.deleteBatchIds
2025-07-28 16:56:55,243 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.update
2025-07-28 16:56:55,246 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.updateById
2025-07-28 16:56:55,248 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectById
2025-07-28 16:56:55,250 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectBatchIds
2025-07-28 16:56:55,253 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectByMap
2025-07-28 16:56:55,256 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectOne
2025-07-28 16:56:55,264 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectCount
2025-07-28 16:56:55,268 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectMaps
2025-07-28 16:56:55,272 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectMapsPage
2025-07-28 16:56:55,276 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectObjs
2025-07-28 16:56:55,280 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectList
2025-07-28 16:56:55,285 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper.selectPage
2025-07-28 16:56:55,338 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.insertAccBuActivity
2025-07-28 16:56:55,339 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.updateAccBuActivity
2025-07-28 16:56:55,341 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.activityRepeat
2025-07-28 16:56:55,343 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectByPage
2025-07-28 16:56:55,346 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectActivityPage
2025-07-28 16:56:55,348 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.marketingActivitie
2025-07-28 16:56:55,350 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectActivityById
2025-07-28 16:56:55,352 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectDlrInfoByCode
2025-07-28 16:56:55,353 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectDlrInfoAll
2025-07-28 16:56:55,354 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.accBuActivityCalenderQuery
2025-07-28 16:56:55,355 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.accBuActivityCalenderQueryByWeek
2025-07-28 16:56:55,357 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectAttachmentByPage
2025-07-28 16:56:55,359 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.queryAcsSmallType
2025-07-28 16:56:55,360 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.queryActivitySubtypeCode
2025-07-28 16:56:55,362 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.queryNeedRealseActivity
2025-07-28 16:56:55,366 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectStatusList
2025-07-28 16:56:55,367 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectActivitSendMessage
2025-07-28 16:56:55,370 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.querySignNum
2025-07-28 16:56:55,371 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.insertAccBuActivityCustomer
2025-07-28 16:56:55,373 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.findHeadquartersActivity
2025-07-28 16:56:55,374 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.findDlr
2025-07-28 16:56:55,375 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.findDrlEmp
2025-07-28 16:56:55,377 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.insertHeadquartersActivity
2025-07-28 16:56:55,378 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.delActivity
2025-07-28 16:56:55,378 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.findRjectNode
2025-07-28 16:56:55,380 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectAgentStatusList
2025-07-28 16:56:55,380 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectActivityCounts
2025-07-28 16:56:55,386 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.accBuActivityNewCalenderQuery
2025-07-28 16:56:55,389 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectActivitySignStatus
2025-07-28 16:56:55,399 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.insert
2025-07-28 16:56:55,402 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.delete
2025-07-28 16:56:55,404 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.deleteByMap
2025-07-28 16:56:55,406 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.deleteById
2025-07-28 16:56:55,408 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.deleteBatchIds
2025-07-28 16:56:55,412 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.update
2025-07-28 16:56:55,415 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.updateById
2025-07-28 16:56:55,415 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectById
2025-07-28 16:56:55,417 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectBatchIds
2025-07-28 16:56:55,419 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectByMap
2025-07-28 16:56:55,422 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectOne
2025-07-28 16:56:55,424 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectCount
2025-07-28 16:56:55,427 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectMaps
2025-07-28 16:56:55,430 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectMapsPage
2025-07-28 16:56:55,433 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectObjs
2025-07-28 16:56:55,436 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectList
2025-07-28 16:56:55,439 [main] - addMappedStatement: com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper.selectPage
2025-07-28 16:56:55,445 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.queryDlrCodeByDlrId
2025-07-28 16:56:55,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.queryDlrIdByDlrCode
2025-07-28 16:56:55,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.queryEmployeeUserStatus
2025-07-28 16:56:55,447 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.getOverdueTime
2025-07-28 16:56:55,447 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.queryLookupValueName
2025-07-28 16:56:55,448 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BaseQueryMapper.newGetEmpByIdList
2025-07-28 16:56:55,462 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.insertArticle
2025-07-28 16:56:55,462 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.queryArticleBuild
2025-07-28 16:56:55,463 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.insertAudience
2025-07-28 16:56:55,463 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.delArticle
2025-07-28 16:56:55,463 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.delAudience
2025-07-28 16:56:55,464 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.updateArticle
2025-07-28 16:56:55,464 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.queryArticleCt
2025-07-28 16:56:55,464 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.saveArticleCt
2025-07-28 16:56:55,465 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.queryArticle
2025-07-28 16:56:55,470 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.insert
2025-07-28 16:56:55,472 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.delete
2025-07-28 16:56:55,474 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.deleteByMap
2025-07-28 16:56:55,476 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.deleteById
2025-07-28 16:56:55,477 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.deleteBatchIds
2025-07-28 16:56:55,480 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.update
2025-07-28 16:56:55,482 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.updateById
2025-07-28 16:56:55,483 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectById
2025-07-28 16:56:55,485 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectBatchIds
2025-07-28 16:56:55,487 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectByMap
2025-07-28 16:56:55,489 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectOne
2025-07-28 16:56:55,492 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectCount
2025-07-28 16:56:55,494 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectMaps
2025-07-28 16:56:55,496 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectMapsPage
2025-07-28 16:56:55,498 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectObjs
2025-07-28 16:56:55,500 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectList
2025-07-28 16:56:55,502 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcArticleMapper.selectPage
2025-07-28 16:56:55,516 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.saveBbcComment
2025-07-28 16:56:55,517 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.queryCommentStatu
2025-07-28 16:56:55,517 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.queryBbcComment
2025-07-28 16:56:55,518 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.queryBbcArticleCount
2025-07-28 16:56:55,518 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.queryBbcCommentCount
2025-07-28 16:56:55,521 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.insertBbcComment
2025-07-28 16:56:55,522 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.updateBbcComment
2025-07-28 16:56:55,522 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.queryBbcComment1
2025-07-28 16:56:55,523 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.findStation
2025-07-28 16:56:55,528 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.insert
2025-07-28 16:56:55,533 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.delete
2025-07-28 16:56:55,535 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.deleteByMap
2025-07-28 16:56:55,538 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.deleteById
2025-07-28 16:56:55,543 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.deleteBatchIds
2025-07-28 16:56:55,547 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.update
2025-07-28 16:56:55,552 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.updateById
2025-07-28 16:56:55,552 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectById
2025-07-28 16:56:55,556 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectBatchIds
2025-07-28 16:56:55,559 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectByMap
2025-07-28 16:56:55,563 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectOne
2025-07-28 16:56:55,567 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectCount
2025-07-28 16:56:55,571 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectMaps
2025-07-28 16:56:55,575 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectMapsPage
2025-07-28 16:56:55,579 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectObjs
2025-07-28 16:56:55,583 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectList
2025-07-28 16:56:55,587 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BbcCommentMapper.selectPage
2025-07-28 16:56:55,600 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOut
2025-07-28 16:56:55,601 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutQueryByDlr
2025-07-28 16:56:55,602 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.creatLogOut
2025-07-28 16:56:55,602 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.updateBuLogOutById
2025-07-28 16:56:55,603 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutQueryByCompanySponsor
2025-07-28 16:56:55,603 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutQueryByAgentSponsor
2025-07-28 16:56:55,603 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutQuery
2025-07-28 16:56:55,604 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.creatLogOutDetail
2025-07-28 16:56:55,604 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.updateBuLogOutDetail
2025-07-28 16:56:55,605 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutClearingQueryByDlr
2025-07-28 16:56:55,605 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectLogoutDetailByStatus
2025-07-28 16:56:55,605 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectLogoutByCompanyCode
2025-07-28 16:56:55,605 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectCompanyAndAgent
2025-07-28 16:56:55,606 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectLogoutCompanyAndAgent
2025-07-28 16:56:55,606 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectLogoutDetailByDlrCode
2025-07-28 16:56:55,607 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.creatLogoutRecord
2025-07-28 16:56:55,608 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutRecordQuery
2025-07-28 16:56:55,608 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.sacBuLogOutQueryByDlrLogout
2025-07-28 16:56:55,613 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.insert
2025-07-28 16:56:55,615 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.delete
2025-07-28 16:56:55,616 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.deleteByMap
2025-07-28 16:56:55,618 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.deleteById
2025-07-28 16:56:55,619 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.deleteBatchIds
2025-07-28 16:56:55,621 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.update
2025-07-28 16:56:55,623 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.updateById
2025-07-28 16:56:55,623 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectById
2025-07-28 16:56:55,625 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectBatchIds
2025-07-28 16:56:55,626 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectByMap
2025-07-28 16:56:55,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectOne
2025-07-28 16:56:55,629 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectCount
2025-07-28 16:56:55,631 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectMaps
2025-07-28 16:56:55,633 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectMapsPage
2025-07-28 16:56:55,635 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectObjs
2025-07-28 16:56:55,636 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectList
2025-07-28 16:56:55,639 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.BuLogoutMapper.selectPage
2025-07-28 16:56:55,645 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.batchSaveFirstOverDue
2025-07-28 16:56:55,659 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.insert
2025-07-28 16:56:55,662 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.delete
2025-07-28 16:56:55,664 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.deleteByMap
2025-07-28 16:56:55,666 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.deleteById
2025-07-28 16:56:55,668 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.deleteBatchIds
2025-07-28 16:56:55,670 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.update
2025-07-28 16:56:55,673 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.updateById
2025-07-28 16:56:55,673 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectById
2025-07-28 16:56:55,676 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectBatchIds
2025-07-28 16:56:55,678 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectByMap
2025-07-28 16:56:55,681 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectOne
2025-07-28 16:56:55,683 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectCount
2025-07-28 16:56:55,687 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectMaps
2025-07-28 16:56:55,690 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectMapsPage
2025-07-28 16:56:55,694 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectObjs
2025-07-28 16:56:55,697 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectList
2025-07-28 16:56:55,701 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper.selectPage
2025-07-28 16:56:55,722 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.insert
2025-07-28 16:56:55,726 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.delete
2025-07-28 16:56:55,729 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.deleteByMap
2025-07-28 16:56:55,731 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.deleteById
2025-07-28 16:56:55,734 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.deleteBatchIds
2025-07-28 16:56:55,737 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.update
2025-07-28 16:56:55,741 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.updateById
2025-07-28 16:56:55,741 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectById
2025-07-28 16:56:55,744 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectBatchIds
2025-07-28 16:56:55,748 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectByMap
2025-07-28 16:56:55,751 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectOne
2025-07-28 16:56:55,755 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectCount
2025-07-28 16:56:55,759 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectMaps
2025-07-28 16:56:55,762 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectMapsPage
2025-07-28 16:56:55,769 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectObjs
2025-07-28 16:56:55,772 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectList
2025-07-28 16:56:55,776 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.ClueResumeMapper.selectPage
2025-07-28 16:56:55,788 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.queryCommonAudit
2025-07-28 16:56:55,802 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.insert
2025-07-28 16:56:55,804 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.delete
2025-07-28 16:56:55,806 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.deleteByMap
2025-07-28 16:56:55,808 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.deleteById
2025-07-28 16:56:55,810 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.deleteBatchIds
2025-07-28 16:56:55,813 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.update
2025-07-28 16:56:55,815 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.updateById
2025-07-28 16:56:55,816 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectById
2025-07-28 16:56:55,817 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectBatchIds
2025-07-28 16:56:55,818 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectByMap
2025-07-28 16:56:55,821 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectOne
2025-07-28 16:56:55,823 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectCount
2025-07-28 16:56:55,826 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectMaps
2025-07-28 16:56:55,829 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectMapsPage
2025-07-28 16:56:55,833 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectObjs
2025-07-28 16:56:55,836 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectList
2025-07-28 16:56:55,838 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper.selectPage
2025-07-28 16:56:55,847 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.insertHandleMsgLog
2025-07-28 16:56:55,849 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.insert
2025-07-28 16:56:55,851 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.delete
2025-07-28 16:56:55,852 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.deleteByMap
2025-07-28 16:56:55,854 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.deleteById
2025-07-28 16:56:55,855 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.deleteBatchIds
2025-07-28 16:56:55,857 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.update
2025-07-28 16:56:55,859 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.updateById
2025-07-28 16:56:55,859 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectById
2025-07-28 16:56:55,860 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectBatchIds
2025-07-28 16:56:55,861 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectByMap
2025-07-28 16:56:55,863 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectOne
2025-07-28 16:56:55,865 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectCount
2025-07-28 16:56:55,866 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectMaps
2025-07-28 16:56:55,868 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectMapsPage
2025-07-28 16:56:55,870 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectObjs
2025-07-28 16:56:55,873 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectList
2025-07-28 16:56:55,875 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.HandleMsgLogMapper.selectPage
2025-07-28 16:56:55,889 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.insert
2025-07-28 16:56:55,892 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.delete
2025-07-28 16:56:55,894 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.deleteByMap
2025-07-28 16:56:55,897 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.deleteById
2025-07-28 16:56:55,900 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.deleteBatchIds
2025-07-28 16:56:55,903 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.update
2025-07-28 16:56:55,905 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.updateById
2025-07-28 16:56:55,906 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectById
2025-07-28 16:56:55,908 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectBatchIds
2025-07-28 16:56:55,910 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectByMap
2025-07-28 16:56:55,913 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectOne
2025-07-28 16:56:55,915 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectCount
2025-07-28 16:56:55,919 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectMaps
2025-07-28 16:56:55,922 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectMapsPage
2025-07-28 16:56:55,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectObjs
2025-07-28 16:56:55,928 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectList
2025-07-28 16:56:55,932 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.LookupValueMapper.selectPage
2025-07-28 16:56:55,943 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.queryCallPerson
2025-07-28 16:56:55,944 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.findDlrsWithCallPerson
2025-07-28 16:56:55,962 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.insert
2025-07-28 16:56:55,966 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.delete
2025-07-28 16:56:55,968 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.deleteByMap
2025-07-28 16:56:55,970 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.deleteById
2025-07-28 16:56:55,973 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.deleteBatchIds
2025-07-28 16:56:55,977 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.update
2025-07-28 16:56:55,980 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.updateById
2025-07-28 16:56:55,981 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectById
2025-07-28 16:56:55,983 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectBatchIds
2025-07-28 16:56:55,986 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectByMap
2025-07-28 16:56:55,989 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectOne
2025-07-28 16:56:55,992 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectCount
2025-07-28 16:56:55,996 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectMaps
2025-07-28 16:56:55,999 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectMapsPage
2025-07-28 16:56:56,002 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectObjs
2025-07-28 16:56:56,015 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectList
2025-07-28 16:56:56,019 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper.selectPage
2025-07-28 16:56:56,032 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectOutboundConfig
2025-07-28 16:56:56,033 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.insertOutboundConfig
2025-07-28 16:56:56,033 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.updateOutboundConfig
2025-07-28 16:56:56,034 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.checkRepeat
2025-07-28 16:56:56,034 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.checkOutBoundExists
2025-07-28 16:56:56,038 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.insert
2025-07-28 16:56:56,040 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.delete
2025-07-28 16:56:56,041 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.deleteByMap
2025-07-28 16:56:56,043 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.deleteById
2025-07-28 16:56:56,044 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.deleteBatchIds
2025-07-28 16:56:56,046 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.update
2025-07-28 16:56:56,047 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.updateById
2025-07-28 16:56:56,048 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectById
2025-07-28 16:56:56,049 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectBatchIds
2025-07-28 16:56:56,050 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectByMap
2025-07-28 16:56:56,052 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectOne
2025-07-28 16:56:56,054 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectCount
2025-07-28 16:56:56,055 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectMaps
2025-07-28 16:56:56,058 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectMapsPage
2025-07-28 16:56:56,059 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectObjs
2025-07-28 16:56:56,061 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectList
2025-07-28 16:56:56,062 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper.selectPage
2025-07-28 16:56:56,068 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.PurchaseIntentionMapper.findData
2025-07-28 16:56:56,076 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectRemoveRepeatConfig
2025-07-28 16:56:56,076 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.insertRemoveRepeatConfig
2025-07-28 16:56:56,077 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.updateRemoveRepeatConfig
2025-07-28 16:56:56,077 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.checkRepeat
2025-07-28 16:56:56,077 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.checkRemoveRepeatConfigExists
2025-07-28 16:56:56,080 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.insert
2025-07-28 16:56:56,082 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.delete
2025-07-28 16:56:56,084 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.deleteByMap
2025-07-28 16:56:56,085 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.deleteById
2025-07-28 16:56:56,088 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.deleteBatchIds
2025-07-28 16:56:56,091 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.update
2025-07-28 16:56:56,094 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.updateById
2025-07-28 16:56:56,095 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectById
2025-07-28 16:56:56,098 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectBatchIds
2025-07-28 16:56:56,100 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectByMap
2025-07-28 16:56:56,104 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectOne
2025-07-28 16:56:56,108 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectCount
2025-07-28 16:56:56,110 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectMaps
2025-07-28 16:56:56,112 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectMapsPage
2025-07-28 16:56:56,114 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectObjs
2025-07-28 16:56:56,116 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectList
2025-07-28 16:56:56,119 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper.selectPage
2025-07-28 16:56:56,131 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectSacAppointmentSheet
2025-07-28 16:56:56,131 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.insertSacAppointmentSheet
2025-07-28 16:56:56,132 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.updateSacAppointmentSheet
2025-07-28 16:56:56,133 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.checkRepeat
2025-07-28 16:56:56,133 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.checkLongRepeat
2025-07-28 16:56:56,133 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.checkPhoneRepeat
2025-07-28 16:56:56,134 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectCarCapacity
2025-07-28 16:56:56,134 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectCarCapacityTimeRange
2025-07-28 16:56:56,138 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.insert
2025-07-28 16:56:56,140 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.delete
2025-07-28 16:56:56,142 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.deleteByMap
2025-07-28 16:56:56,144 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.deleteById
2025-07-28 16:56:56,145 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.deleteBatchIds
2025-07-28 16:56:56,147 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.update
2025-07-28 16:56:56,149 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.updateById
2025-07-28 16:56:56,149 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectById
2025-07-28 16:56:56,151 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectBatchIds
2025-07-28 16:56:56,152 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectByMap
2025-07-28 16:56:56,154 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectOne
2025-07-28 16:56:56,156 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectCount
2025-07-28 16:56:56,159 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectMaps
2025-07-28 16:56:56,161 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectMapsPage
2025-07-28 16:56:56,163 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectObjs
2025-07-28 16:56:56,165 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectList
2025-07-28 16:56:56,167 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper.selectPage
2025-07-28 16:56:56,178 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.insertSacAttachment
2025-07-28 16:56:56,180 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.updateSacAttachment
2025-07-28 16:56:56,180 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectByPage
2025-07-28 16:56:56,183 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.insert
2025-07-28 16:56:56,185 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.delete
2025-07-28 16:56:56,187 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.deleteByMap
2025-07-28 16:56:56,189 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.deleteById
2025-07-28 16:56:56,191 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.deleteBatchIds
2025-07-28 16:56:56,193 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.update
2025-07-28 16:56:56,194 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.updateById
2025-07-28 16:56:56,195 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectById
2025-07-28 16:56:56,196 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectBatchIds
2025-07-28 16:56:56,197 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectByMap
2025-07-28 16:56:56,199 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectOne
2025-07-28 16:56:56,201 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectCount
2025-07-28 16:56:56,202 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectMaps
2025-07-28 16:56:56,203 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectMapsPage
2025-07-28 16:56:56,205 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectObjs
2025-07-28 16:56:56,207 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectList
2025-07-28 16:56:56,209 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper.selectPage
2025-07-28 16:56:56,216 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.sacBasisLogFindInfo
2025-07-28 16:56:56,217 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.createsacBasisLog
2025-07-28 16:56:56,217 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.sacSwitcchDlrLogInsertOne
2025-07-28 16:56:56,221 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.insert
2025-07-28 16:56:56,223 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.delete
2025-07-28 16:56:56,224 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.deleteByMap
2025-07-28 16:56:56,225 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.deleteById
2025-07-28 16:56:56,226 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.deleteBatchIds
2025-07-28 16:56:56,228 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.update
2025-07-28 16:56:56,229 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.updateById
2025-07-28 16:56:56,229 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectById
2025-07-28 16:56:56,230 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectBatchIds
2025-07-28 16:56:56,231 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectByMap
2025-07-28 16:56:56,233 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectOne
2025-07-28 16:56:56,234 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectCount
2025-07-28 16:56:56,236 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectMaps
2025-07-28 16:56:56,237 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectMapsPage
2025-07-28 16:56:56,238 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectObjs
2025-07-28 16:56:56,240 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectList
2025-07-28 16:56:56,242 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBasisLogMapper.selectPage
2025-07-28 16:56:56,250 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.querySacBuBoutiqueApply
2025-07-28 16:56:56,250 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.deleteSacBuBoutiqueApply
2025-07-28 16:56:56,251 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.createSacBuBoutiqueApply
2025-07-28 16:56:56,251 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.updateSacBuBoutiqueApply
2025-07-28 16:56:56,252 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectBoutiqueCount
2025-07-28 16:56:56,252 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.insertApplyDetail
2025-07-28 16:56:56,252 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.insertApply
2025-07-28 16:56:56,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.deleteDetailList
2025-07-28 16:56:56,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.updateApply
2025-07-28 16:56:56,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.sacBuBoutiqueApplyFindInfoQuery
2025-07-28 16:56:56,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.sacBuBoutiqueApplyByDetails
2025-07-28 16:56:56,254 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectApplyInfo
2025-07-28 16:56:56,254 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectDlrByDlrId
2025-07-28 16:56:56,254 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectSacTestCarSendMessage
2025-07-28 16:56:56,255 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.sacTestCarSheetSendMessage
2025-07-28 16:56:56,255 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.evaluateMsgData
2025-07-28 16:56:56,255 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.updateEvaluateFlag
2025-07-28 16:56:56,255 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.findUrl
2025-07-28 16:56:56,257 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.updateDriveEnd
2025-07-28 16:56:56,262 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.insert
2025-07-28 16:56:56,264 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.delete
2025-07-28 16:56:56,265 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.deleteByMap
2025-07-28 16:56:56,266 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.deleteById
2025-07-28 16:56:56,267 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.deleteBatchIds
2025-07-28 16:56:56,268 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.update
2025-07-28 16:56:56,269 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.updateById
2025-07-28 16:56:56,269 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectById
2025-07-28 16:56:56,270 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectBatchIds
2025-07-28 16:56:56,271 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectByMap
2025-07-28 16:56:56,273 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectOne
2025-07-28 16:56:56,274 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectCount
2025-07-28 16:56:56,275 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectMaps
2025-07-28 16:56:56,277 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectMapsPage
2025-07-28 16:56:56,278 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectObjs
2025-07-28 16:56:56,280 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectList
2025-07-28 16:56:56,281 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper.selectPage
2025-07-28 16:56:56,291 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.querySacBuBoutiqueDetail
2025-07-28 16:56:56,292 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.deleteSacBuBoutiqueDetail
2025-07-28 16:56:56,292 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.createSacBuBoutiqueDetail
2025-07-28 16:56:56,293 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.updateSacBuBoutiqueDetail
2025-07-28 16:56:56,293 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.sacBuBoutiqueDetailQuery
2025-07-28 16:56:56,293 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectByDetailId
2025-07-28 16:56:56,294 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.insertReceiving
2025-07-28 16:56:56,298 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.insert
2025-07-28 16:56:56,300 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.delete
2025-07-28 16:56:56,303 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.deleteByMap
2025-07-28 16:56:56,305 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.deleteById
2025-07-28 16:56:56,307 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.deleteBatchIds
2025-07-28 16:56:56,309 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.update
2025-07-28 16:56:56,311 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.updateById
2025-07-28 16:56:56,311 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectById
2025-07-28 16:56:56,313 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectBatchIds
2025-07-28 16:56:56,314 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectByMap
2025-07-28 16:56:56,317 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectOne
2025-07-28 16:56:56,319 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectCount
2025-07-28 16:56:56,324 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectMaps
2025-07-28 16:56:56,327 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectMapsPage
2025-07-28 16:56:56,330 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectObjs
2025-07-28 16:56:56,333 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectList
2025-07-28 16:56:56,336 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper.selectPage
2025-07-28 16:56:56,346 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.querySacBuBoutiqueDetailRecord
2025-07-28 16:56:56,347 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.deleteSacBuBoutiqueDetailRecord
2025-07-28 16:56:56,347 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.createSacBuBoutiqueDetailRecord
2025-07-28 16:56:56,348 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.updateSacBuBoutiqueDetailRecord
2025-07-28 16:56:56,348 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.querySacBuBoutiqueDetailRecordInfo
2025-07-28 16:56:56,353 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.insert
2025-07-28 16:56:56,355 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.delete
2025-07-28 16:56:56,357 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.deleteByMap
2025-07-28 16:56:56,359 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.deleteById
2025-07-28 16:56:56,361 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.deleteBatchIds
2025-07-28 16:56:56,363 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.update
2025-07-28 16:56:56,366 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.updateById
2025-07-28 16:56:56,366 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectById
2025-07-28 16:56:56,368 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectBatchIds
2025-07-28 16:56:56,370 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectByMap
2025-07-28 16:56:56,372 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectOne
2025-07-28 16:56:56,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectCount
2025-07-28 16:56:56,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectMaps
2025-07-28 16:56:56,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectMapsPage
2025-07-28 16:56:56,380 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectObjs
2025-07-28 16:56:56,382 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectList
2025-07-28 16:56:56,384 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper.selectPage
2025-07-28 16:56:56,394 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.querySacBuBoutiqueSet
2025-07-28 16:56:56,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.deleteSacBuBoutiqueSet
2025-07-28 16:56:56,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.createSacBuBoutiqueSet
2025-07-28 16:56:56,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.updateSacBuBoutiqueSet
2025-07-28 16:56:56,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insertSacBuBoutiqueDlrInfo
2025-07-28 16:56:56,396 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.querySacBuBoutiqueDlr
2025-07-28 16:56:56,396 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.deleteSacBuBoutiqueDlr
2025-07-28 16:56:56,396 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.updateSacBuBoutiqueDlr
2025-07-28 16:56:56,397 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.querySacBuBoutiqueDlrInfo
2025-07-28 16:56:56,397 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectByBoutiqueCode
2025-07-28 16:56:56,397 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectByBoutiqueName
2025-07-28 16:56:56,398 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insertBoutiqueSetImport
2025-07-28 16:56:56,398 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insertBoutiqueDetailImport
2025-07-28 16:56:56,398 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insertDoutiqueDetail
2025-07-28 16:56:56,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectByDlrCodeAndBoutiCode
2025-07-28 16:56:56,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.updateByDatailId
2025-07-28 16:56:56,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insertRecord
2025-07-28 16:56:56,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.querySacBuBoutiqueReceiving
2025-07-28 16:56:56,400 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.querySacBuBoutiqueStatement
2025-07-28 16:56:56,403 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.insert
2025-07-28 16:56:56,404 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.delete
2025-07-28 16:56:56,405 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.deleteByMap
2025-07-28 16:56:56,407 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.deleteById
2025-07-28 16:56:56,408 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.deleteBatchIds
2025-07-28 16:56:56,409 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.update
2025-07-28 16:56:56,410 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.updateById
2025-07-28 16:56:56,411 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectById
2025-07-28 16:56:56,412 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectBatchIds
2025-07-28 16:56:56,414 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectByMap
2025-07-28 16:56:56,415 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectOne
2025-07-28 16:56:56,416 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectCount
2025-07-28 16:56:56,418 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectMaps
2025-07-28 16:56:56,420 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectMapsPage
2025-07-28 16:56:56,422 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectObjs
2025-07-28 16:56:56,423 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectList
2025-07-28 16:56:56,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper.selectPage
2025-07-28 16:56:56,431 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.querySacBuMenusControl
2025-07-28 16:56:56,432 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.deleteSacBuMenusControl
2025-07-28 16:56:56,432 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.createSacBuMenusControl
2025-07-28 16:56:56,432 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.updateSacBuMenusControl
2025-07-28 16:56:56,433 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.updateSacBuMenusControlBymenusControlId
2025-07-28 16:56:56,435 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.insert
2025-07-28 16:56:56,437 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.delete
2025-07-28 16:56:56,438 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.deleteByMap
2025-07-28 16:56:56,439 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.deleteById
2025-07-28 16:56:56,440 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.deleteBatchIds
2025-07-28 16:56:56,441 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.update
2025-07-28 16:56:56,442 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.updateById
2025-07-28 16:56:56,443 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectById
2025-07-28 16:56:56,444 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectBatchIds
2025-07-28 16:56:56,445 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectByMap
2025-07-28 16:56:56,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectOne
2025-07-28 16:56:56,447 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectCount
2025-07-28 16:56:56,448 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectMaps
2025-07-28 16:56:56,449 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectMapsPage
2025-07-28 16:56:56,450 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectObjs
2025-07-28 16:56:56,451 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectList
2025-07-28 16:56:56,452 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper.selectPage
2025-07-28 16:56:56,461 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectByAll
2025-07-28 16:56:56,461 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectChannelByAll
2025-07-28 16:56:56,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.insertChannel
2025-07-28 16:56:56,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.updateById
2025-07-28 16:56:56,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.checkChannelCodeRepeat
2025-07-28 16:56:56,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.checkChannelInfoRepeat
2025-07-28 16:56:56,463 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.countById
2025-07-28 16:56:56,463 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.queryChannelLinkByName
2025-07-28 16:56:56,466 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.insert
2025-07-28 16:56:56,468 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.delete
2025-07-28 16:56:56,469 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.deleteByMap
2025-07-28 16:56:56,471 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.deleteById
2025-07-28 16:56:56,473 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.deleteBatchIds
2025-07-28 16:56:56,475 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.update
2025-07-28 16:56:56,477 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectById
2025-07-28 16:56:56,478 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectBatchIds
2025-07-28 16:56:56,480 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectByMap
2025-07-28 16:56:56,484 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectOne
2025-07-28 16:56:56,486 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectCount
2025-07-28 16:56:56,488 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectMaps
2025-07-28 16:56:56,490 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectMapsPage
2025-07-28 16:56:56,492 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectObjs
2025-07-28 16:56:56,496 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectList
2025-07-28 16:56:56,499 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper.selectPage
2025-07-28 16:56:56,513 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.querySacCityClueSwitch
2025-07-28 16:56:56,514 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.deleteSacCityClueSwitch
2025-07-28 16:56:56,514 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.createSacCityClueSwitch
2025-07-28 16:56:56,515 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.updateSacCityClueSwitch
2025-07-28 16:56:56,521 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.insert
2025-07-28 16:56:56,525 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.delete
2025-07-28 16:56:56,527 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.deleteByMap
2025-07-28 16:56:56,530 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.deleteById
2025-07-28 16:56:56,533 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.deleteBatchIds
2025-07-28 16:56:56,536 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.update
2025-07-28 16:56:56,540 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.updateById
2025-07-28 16:56:56,541 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectById
2025-07-28 16:56:56,543 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectBatchIds
2025-07-28 16:56:56,545 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectByMap
2025-07-28 16:56:56,549 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectOne
2025-07-28 16:56:56,552 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectCount
2025-07-28 16:56:56,556 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectMaps
2025-07-28 16:56:56,560 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectMapsPage
2025-07-28 16:56:56,563 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectObjs
2025-07-28 16:56:56,566 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectList
2025-07-28 16:56:56,569 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityClueSwitchMapper.selectPage
2025-07-28 16:56:56,581 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.querySacCityCounter
2025-07-28 16:56:56,582 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.deleteSacCityCounter
2025-07-28 16:56:56,582 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.createSacCityCounter
2025-07-28 16:56:56,583 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.updateSacCityCounterCityNum
2025-07-28 16:56:56,584 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.updateSacCityCounterDlrNum
2025-07-28 16:56:56,590 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.insert
2025-07-28 16:56:56,593 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.delete
2025-07-28 16:56:56,596 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.deleteByMap
2025-07-28 16:56:56,601 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.deleteById
2025-07-28 16:56:56,604 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.deleteBatchIds
2025-07-28 16:56:56,608 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.update
2025-07-28 16:56:56,611 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.updateById
2025-07-28 16:56:56,611 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectById
2025-07-28 16:56:56,613 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectBatchIds
2025-07-28 16:56:56,616 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectByMap
2025-07-28 16:56:56,617 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectOne
2025-07-28 16:56:56,619 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectCount
2025-07-28 16:56:56,621 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectMaps
2025-07-28 16:56:56,623 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectMapsPage
2025-07-28 16:56:56,625 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectObjs
2025-07-28 16:56:56,626 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectList
2025-07-28 16:56:56,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacCityCounterMapper.selectPage
2025-07-28 16:56:56,638 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.countSacClueAllocatingRecord
2025-07-28 16:56:56,638 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.querySacClueAllocatingRecord
2025-07-28 16:56:56,639 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.deleteSacClueAllocatingRecord
2025-07-28 16:56:56,639 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.createSacClueAllocatingRecord
2025-07-28 16:56:56,640 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.createSacClueAllocatingRecordBatch
2025-07-28 16:56:56,640 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.batchCreateSacClueAllocatingRecord
2025-07-28 16:56:56,641 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.updateSacClueAllocatingRecord
2025-07-28 16:56:56,641 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectReviewByReviewId
2025-07-28 16:56:56,645 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.insert
2025-07-28 16:56:56,646 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.delete
2025-07-28 16:56:56,647 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.deleteByMap
2025-07-28 16:56:56,649 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.deleteById
2025-07-28 16:56:56,650 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.deleteBatchIds
2025-07-28 16:56:56,651 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.update
2025-07-28 16:56:56,653 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.updateById
2025-07-28 16:56:56,653 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectById
2025-07-28 16:56:56,654 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectBatchIds
2025-07-28 16:56:56,655 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectByMap
2025-07-28 16:56:56,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectOne
2025-07-28 16:56:56,658 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectCount
2025-07-28 16:56:56,659 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectMaps
2025-07-28 16:56:56,662 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectMapsPage
2025-07-28 16:56:56,663 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectObjs
2025-07-28 16:56:56,665 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectList
2025-07-28 16:56:56,666 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper.selectPage
2025-07-28 16:56:56,673 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryReviewInfo
2025-07-28 16:56:56,674 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryReviewInfoNoPage
2025-07-28 16:56:56,674 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updatesacClue
2025-07-28 16:56:56,674 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updatesacClueBatch
2025-07-28 16:56:56,674 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updatesacReview
2025-07-28 16:56:56,675 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updatesacReviewBatch
2025-07-28 16:56:56,675 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyquery
2025-07-28 16:56:56,688 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyqueryServerOrder
2025-07-28 16:56:56,689 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyqueryPerformance
2025-07-28 16:56:56,690 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyqueryCount
2025-07-28 16:56:56,690 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyqueryNoPage
2025-07-28 16:56:56,691 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.sacUserCluedlrbyqueryNoPageSpecifyFields
2025-07-28 16:56:56,692 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryListByDlr
2025-07-28 16:56:56,693 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryListByDlrManual
2025-07-28 16:56:56,694 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryListByDlrCount
2025-07-28 16:56:56,696 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.deleteSacReview
2025-07-28 16:56:56,697 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.defeatCount
2025-07-28 16:56:56,699 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.defeatCountSpecifyFields
2025-07-28 16:56:56,700 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryUserClueInfo
2025-07-28 16:56:56,701 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryTestDrive
2025-07-28 16:56:56,701 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryTnviteUserNum
2025-07-28 16:56:56,702 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.queryOrderNum
2025-07-28 16:56:56,703 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.findData
2025-07-28 16:56:56,704 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updateCscClue
2025-07-28 16:56:56,704 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.deleteNewClue
2025-07-28 16:56:56,713 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.insert
2025-07-28 16:56:56,715 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.delete
2025-07-28 16:56:56,716 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.deleteByMap
2025-07-28 16:56:56,718 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.deleteById
2025-07-28 16:56:56,719 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.deleteBatchIds
2025-07-28 16:56:56,721 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.update
2025-07-28 16:56:56,724 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.updateById
2025-07-28 16:56:56,724 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectById
2025-07-28 16:56:56,726 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectBatchIds
2025-07-28 16:56:56,727 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectByMap
2025-07-28 16:56:56,729 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectOne
2025-07-28 16:56:56,731 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectCount
2025-07-28 16:56:56,733 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectMaps
2025-07-28 16:56:56,735 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectMapsPage
2025-07-28 16:56:56,737 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectObjs
2025-07-28 16:56:56,739 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectList
2025-07-28 16:56:56,742 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCenterMapper.selectPage
2025-07-28 16:56:56,751 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.sacClueCompeteRecordInsertOne
2025-07-28 16:56:56,752 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.sacClueCompeteRecordUpdate
2025-07-28 16:56:56,752 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.sacClueCompeteRecordQuery
2025-07-28 16:56:56,752 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.sacClueCompeteRecordDlrInfoQuery
2025-07-28 16:56:56,752 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.sacClueCompeteRecordManagedDlrQuery
2025-07-28 16:56:56,757 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.insert
2025-07-28 16:56:56,760 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.delete
2025-07-28 16:56:56,763 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.deleteByMap
2025-07-28 16:56:56,766 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.deleteById
2025-07-28 16:56:56,768 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.deleteBatchIds
2025-07-28 16:56:56,771 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.update
2025-07-28 16:56:56,774 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.updateById
2025-07-28 16:56:56,775 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectById
2025-07-28 16:56:56,776 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectBatchIds
2025-07-28 16:56:56,778 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectByMap
2025-07-28 16:56:56,780 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectOne
2025-07-28 16:56:56,784 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectCount
2025-07-28 16:56:56,787 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectMaps
2025-07-28 16:56:56,791 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectMapsPage
2025-07-28 16:56:56,793 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectObjs
2025-07-28 16:56:56,795 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectList
2025-07-28 16:56:56,799 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper.selectPage
2025-07-28 16:56:56,813 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.insertSacClueHatchPoolHis
2025-07-28 16:56:56,817 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.insert
2025-07-28 16:56:56,819 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.delete
2025-07-28 16:56:56,820 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.deleteByMap
2025-07-28 16:56:56,822 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.deleteById
2025-07-28 16:56:56,824 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.deleteBatchIds
2025-07-28 16:56:56,828 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.update
2025-07-28 16:56:56,832 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.updateById
2025-07-28 16:56:56,832 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectById
2025-07-28 16:56:56,834 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectBatchIds
2025-07-28 16:56:56,836 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectByMap
2025-07-28 16:56:56,839 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectOne
2025-07-28 16:56:56,842 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectCount
2025-07-28 16:56:56,844 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectMaps
2025-07-28 16:56:56,847 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectMapsPage
2025-07-28 16:56:56,849 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectObjs
2025-07-28 16:56:56,851 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectList
2025-07-28 16:56:56,853 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolHisMapper.selectPage
2025-07-28 16:56:56,865 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.insertSacClueHatchPool
2025-07-28 16:56:56,866 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectSacClueHatchPool
2025-07-28 16:56:56,866 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.updateSacClueHatchPool
2025-07-28 16:56:56,870 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.insert
2025-07-28 16:56:56,873 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.delete
2025-07-28 16:56:56,874 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.deleteByMap
2025-07-28 16:56:56,876 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.deleteById
2025-07-28 16:56:56,877 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.deleteBatchIds
2025-07-28 16:56:56,881 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.update
2025-07-28 16:56:56,883 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.updateById
2025-07-28 16:56:56,884 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectById
2025-07-28 16:56:56,885 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectBatchIds
2025-07-28 16:56:56,887 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectByMap
2025-07-28 16:56:56,889 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectOne
2025-07-28 16:56:56,892 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectCount
2025-07-28 16:56:56,895 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectMaps
2025-07-28 16:56:56,899 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectMapsPage
2025-07-28 16:56:56,902 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectObjs
2025-07-28 16:56:56,905 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectList
2025-07-28 16:56:56,908 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper.selectPage
2025-07-28 16:56:56,915 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.getClueInfoWithOrderStates
2025-07-28 16:56:56,934 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.insert
2025-07-28 16:56:56,937 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.delete
2025-07-28 16:56:56,940 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.deleteByMap
2025-07-28 16:56:56,942 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.deleteById
2025-07-28 16:56:56,944 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.deleteBatchIds
2025-07-28 16:56:56,948 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.update
2025-07-28 16:56:56,950 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.updateById
2025-07-28 16:56:56,951 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectById
2025-07-28 16:56:56,952 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectBatchIds
2025-07-28 16:56:56,955 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectByMap
2025-07-28 16:56:56,959 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectOne
2025-07-28 16:56:56,963 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectCount
2025-07-28 16:56:56,966 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectMaps
2025-07-28 16:56:56,970 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectMapsPage
2025-07-28 16:56:56,974 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectObjs
2025-07-28 16:56:56,978 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectList
2025-07-28 16:56:56,983 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper.selectPage
2025-07-28 16:56:56,998 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.querySacClueInfoDlrLog
2025-07-28 16:56:56,999 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.deleteSacClueInfoDlrLog
2025-07-28 16:56:57,000 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.createSacClueInfoDlrLog
2025-07-28 16:56:57,006 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.insert
2025-07-28 16:56:57,010 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.delete
2025-07-28 16:56:57,012 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.deleteByMap
2025-07-28 16:56:57,027 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.deleteById
2025-07-28 16:56:57,030 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.deleteBatchIds
2025-07-28 16:56:57,033 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.update
2025-07-28 16:56:57,035 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.updateById
2025-07-28 16:56:57,035 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectById
2025-07-28 16:56:57,037 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectBatchIds
2025-07-28 16:56:57,039 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectByMap
2025-07-28 16:56:57,041 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectOne
2025-07-28 16:56:57,043 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectCount
2025-07-28 16:56:57,045 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectMaps
2025-07-28 16:56:57,047 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectMapsPage
2025-07-28 16:56:57,049 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectObjs
2025-07-28 16:56:57,050 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectList
2025-07-28 16:56:57,052 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper.selectPage
2025-07-28 16:56:57,063 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.sacClueInfoDlrInsert
2025-07-28 16:56:57,064 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.sacClueInfoDlrUpdate
2025-07-28 16:56:57,064 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.sacClueInfoDlrUpdateBatch
2025-07-28 16:56:57,066 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectMapById
2025-07-28 16:56:57,066 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectHisById
2025-07-28 16:56:57,066 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectOneAndWrap
2025-07-28 16:56:57,066 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectOneAndWrapNew
2025-07-28 16:56:57,066 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.fetchCount
2025-07-28 16:56:57,067 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectByPage
2025-07-28 16:56:57,068 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectMap
2025-07-28 16:56:57,069 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectByPageSpecifyFields
2025-07-28 16:56:57,069 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectHisByPage
2025-07-28 16:56:57,070 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.checkRepeat
2025-07-28 16:56:57,071 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.checkRepeatNew
2025-07-28 16:56:57,071 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectComp
2025-07-28 16:56:57,073 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.getCustId
2025-07-28 16:56:57,074 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.getDlrShortName
2025-07-28 16:56:57,074 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.provinceByDlrCode
2025-07-28 16:56:57,075 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.createOnecustResumeInfo
2025-07-28 16:56:57,076 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.checkData
2025-07-28 16:56:57,076 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findDlrProvince
2025-07-28 16:56:57,078 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findDlrProvinceAndCity
2025-07-28 16:56:57,078 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findSecondaryChannels
2025-07-28 16:56:57,079 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertClueInfoDlr
2025-07-28 16:56:57,080 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findSecondaryChannelName
2025-07-28 16:56:57,081 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertClueInfoDlrLog
2025-07-28 16:56:57,081 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertinsertReview
2025-07-28 16:56:57,082 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertcustResumeInfo
2025-07-28 16:56:57,083 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.checkClueInfoDlr
2025-07-28 16:56:57,083 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertOnecustInfo
2025-07-28 16:56:57,084 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertcdpLeads
2025-07-28 16:56:57,085 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findReviewPerson
2025-07-28 16:56:57,086 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findCityClueSwitch
2025-07-28 16:56:57,086 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertcdpLeadsEvent
2025-07-28 16:56:57,087 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertEvent
2025-07-28 16:56:57,094 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findOverdueTime
2025-07-28 16:56:57,095 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findClueSwitch
2025-07-28 16:56:57,098 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.findObsData
2025-07-28 16:56:57,101 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.getSmByDlrCode
2025-07-28 16:56:57,103 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectNewClueDlrById
2025-07-28 16:56:57,104 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertNewClue
2025-07-28 16:56:57,105 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insertNewClueBatch
2025-07-28 16:56:57,106 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.deleteNewClue
2025-07-28 16:56:57,107 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.deleteNewClueBatch
2025-07-28 16:56:57,107 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectNewClue
2025-07-28 16:56:57,109 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.getReviewIdByNewTable
2025-07-28 16:56:57,109 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.dccBatchUpdateClue
2025-07-28 16:56:57,111 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.transferEmployeeDimissionClues
2025-07-28 16:56:57,114 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.getDlrAddrInfo
2025-07-28 16:56:57,122 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.insert
2025-07-28 16:56:57,125 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.delete
2025-07-28 16:56:57,126 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.deleteByMap
2025-07-28 16:56:57,128 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.deleteById
2025-07-28 16:56:57,129 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.deleteBatchIds
2025-07-28 16:56:57,132 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.update
2025-07-28 16:56:57,135 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.updateById
2025-07-28 16:56:57,135 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectById
2025-07-28 16:56:57,136 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectBatchIds
2025-07-28 16:56:57,138 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectByMap
2025-07-28 16:56:57,142 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectOne
2025-07-28 16:56:57,146 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectCount
2025-07-28 16:56:57,150 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectMaps
2025-07-28 16:56:57,153 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectMapsPage
2025-07-28 16:56:57,155 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectObjs
2025-07-28 16:56:57,159 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectList
2025-07-28 16:56:57,162 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper.selectPage
2025-07-28 16:56:57,178 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.sacClueInfoDlrTemporaryInsert
2025-07-28 16:56:57,179 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.sacClueInfoDlrTemporaryUpdate
2025-07-28 16:56:57,179 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectByPage
2025-07-28 16:56:57,186 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.insert
2025-07-28 16:56:57,190 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.delete
2025-07-28 16:56:57,193 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.deleteByMap
2025-07-28 16:56:57,195 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.deleteById
2025-07-28 16:56:57,197 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.deleteBatchIds
2025-07-28 16:56:57,202 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.update
2025-07-28 16:56:57,205 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.updateById
2025-07-28 16:56:57,205 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectById
2025-07-28 16:56:57,207 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectBatchIds
2025-07-28 16:56:57,209 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectByMap
2025-07-28 16:56:57,211 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectOne
2025-07-28 16:56:57,214 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectCount
2025-07-28 16:56:57,216 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectMaps
2025-07-28 16:56:57,218 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectMapsPage
2025-07-28 16:56:57,221 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectObjs
2025-07-28 16:56:57,223 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectList
2025-07-28 16:56:57,225 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper.selectPage
2025-07-28 16:56:57,243 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.insertSacClueInfo
2025-07-28 16:56:57,244 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.updateSacClueInfo
2025-07-28 16:56:57,244 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.updateSacClueInfoBatch
2025-07-28 16:56:57,245 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectMapById
2025-07-28 16:56:57,245 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectHisById
2025-07-28 16:56:57,246 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectDistinctDataByPage
2025-07-28 16:56:57,247 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectByPage
2025-07-28 16:56:57,247 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectMap
2025-07-28 16:56:57,248 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectHisByPage
2025-07-28 16:56:57,248 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.clueServerQueryDetailFromHeadquarters
2025-07-28 16:56:57,249 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.checkRepeat
2025-07-28 16:56:57,253 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.insert
2025-07-28 16:56:57,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.delete
2025-07-28 16:56:57,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.deleteByMap
2025-07-28 16:56:57,258 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.deleteById
2025-07-28 16:56:57,258 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.deleteBatchIds
2025-07-28 16:56:57,261 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.update
2025-07-28 16:56:57,262 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.updateById
2025-07-28 16:56:57,262 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectById
2025-07-28 16:56:57,263 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectBatchIds
2025-07-28 16:56:57,264 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectByMap
2025-07-28 16:56:57,268 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectOne
2025-07-28 16:56:57,270 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectCount
2025-07-28 16:56:57,272 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectMaps
2025-07-28 16:56:57,274 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectMapsPage
2025-07-28 16:56:57,276 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectObjs
2025-07-28 16:56:57,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectList
2025-07-28 16:56:57,281 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper.selectPage
2025-07-28 16:56:57,295 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.insertSacClueInfoTemporary
2025-07-28 16:56:57,295 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.updateSacClueInfoTemporary
2025-07-28 16:56:57,295 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectByPage
2025-07-28 16:56:57,300 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.insert
2025-07-28 16:56:57,302 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.delete
2025-07-28 16:56:57,303 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.deleteByMap
2025-07-28 16:56:57,304 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.deleteById
2025-07-28 16:56:57,305 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.deleteBatchIds
2025-07-28 16:56:57,308 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.update
2025-07-28 16:56:57,310 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.updateById
2025-07-28 16:56:57,310 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectById
2025-07-28 16:56:57,312 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectBatchIds
2025-07-28 16:56:57,313 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectByMap
2025-07-28 16:56:57,315 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectOne
2025-07-28 16:56:57,317 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectCount
2025-07-28 16:56:57,319 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectMaps
2025-07-28 16:56:57,322 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectMapsPage
2025-07-28 16:56:57,325 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectObjs
2025-07-28 16:56:57,328 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectList
2025-07-28 16:56:57,330 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacClueInfoTemporaryMapper.selectPage
2025-07-28 16:56:57,335 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.sacMsgRecordFindInfo
2025-07-28 16:56:57,336 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.updateMsgRecord
2025-07-28 16:56:57,336 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.updateMsgRead
2025-07-28 16:56:57,336 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.createMsgRecord
2025-07-28 16:56:57,336 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.sacMsgRecordReport
2025-07-28 16:56:57,339 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.insert
2025-07-28 16:56:57,342 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.delete
2025-07-28 16:56:57,344 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.deleteByMap
2025-07-28 16:56:57,346 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.deleteById
2025-07-28 16:56:57,348 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.deleteBatchIds
2025-07-28 16:56:57,349 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.update
2025-07-28 16:56:57,351 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.updateById
2025-07-28 16:56:57,352 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectById
2025-07-28 16:56:57,353 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectBatchIds
2025-07-28 16:56:57,355 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectByMap
2025-07-28 16:56:57,357 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectOne
2025-07-28 16:56:57,359 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectCount
2025-07-28 16:56:57,361 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectMaps
2025-07-28 16:56:57,363 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectMapsPage
2025-07-28 16:56:57,364 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectObjs
2025-07-28 16:56:57,366 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectList
2025-07-28 16:56:57,368 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper.selectPage
2025-07-28 16:56:57,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.sacComplaintsDealRecordFindByPage
2025-07-28 16:56:57,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.insertSacComplaintsDealRecord
2025-07-28 16:56:57,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectByComplaintsNo
2025-07-28 16:56:57,382 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.insert
2025-07-28 16:56:57,384 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.delete
2025-07-28 16:56:57,385 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.deleteByMap
2025-07-28 16:56:57,387 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.deleteById
2025-07-28 16:56:57,388 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.deleteBatchIds
2025-07-28 16:56:57,391 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.update
2025-07-28 16:56:57,393 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.updateById
2025-07-28 16:56:57,393 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectById
2025-07-28 16:56:57,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectBatchIds
2025-07-28 16:56:57,396 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectByMap
2025-07-28 16:56:57,398 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectOne
2025-07-28 16:56:57,400 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectCount
2025-07-28 16:56:57,402 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectMaps
2025-07-28 16:56:57,404 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectMapsPage
2025-07-28 16:56:57,407 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectObjs
2025-07-28 16:56:57,409 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectList
2025-07-28 16:56:57,411 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper.selectPage
2025-07-28 16:56:57,424 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.querySacComplaintsInfo
2025-07-28 16:56:57,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.deleteSacComplaintsInfo
2025-07-28 16:56:57,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.createSacComplaintsInfo
2025-07-28 16:56:57,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.updateSacComplaintsInfo
2025-07-28 16:56:57,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.sacComplaintsInfoFind
2025-07-28 16:56:57,426 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.sacServerClassReport
2025-07-28 16:56:57,426 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.updateStatusByIssueCaseNo
2025-07-28 16:56:57,426 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectByComplaintsNo
2025-07-28 16:56:57,427 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.findAgentDlrCodeList
2025-07-28 16:56:57,427 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.findAgentCompany
2025-07-28 16:56:57,427 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.findAuditId
2025-07-28 16:56:57,431 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.insert
2025-07-28 16:56:57,434 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.delete
2025-07-28 16:56:57,436 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.deleteByMap
2025-07-28 16:56:57,438 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.deleteById
2025-07-28 16:56:57,440 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.deleteBatchIds
2025-07-28 16:56:57,444 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.update
2025-07-28 16:56:57,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.updateById
2025-07-28 16:56:57,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectById
2025-07-28 16:56:57,449 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectBatchIds
2025-07-28 16:56:57,450 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectByMap
2025-07-28 16:56:57,457 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectOne
2025-07-28 16:56:57,469 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectCount
2025-07-28 16:56:57,479 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectMaps
2025-07-28 16:56:57,487 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectMapsPage
2025-07-28 16:56:57,543 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectObjs
2025-07-28 16:56:57,557 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectList
2025-07-28 16:56:57,561 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper.selectPage
2025-07-28 16:56:57,571 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectDbCarBrand
2025-07-28 16:56:57,574 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.insert
2025-07-28 16:56:57,576 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.delete
2025-07-28 16:56:57,577 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.deleteByMap
2025-07-28 16:56:57,579 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.deleteById
2025-07-28 16:56:57,580 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.deleteBatchIds
2025-07-28 16:56:57,582 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.update
2025-07-28 16:56:57,584 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.updateById
2025-07-28 16:56:57,584 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectById
2025-07-28 16:56:57,585 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectBatchIds
2025-07-28 16:56:57,587 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectByMap
2025-07-28 16:56:57,590 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectOne
2025-07-28 16:56:57,592 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectCount
2025-07-28 16:56:57,593 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectMaps
2025-07-28 16:56:57,595 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectMapsPage
2025-07-28 16:56:57,597 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectObjs
2025-07-28 16:56:57,598 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectList
2025-07-28 16:56:57,601 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper.selectPage
2025-07-28 16:56:57,611 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectDbCarColor
2025-07-28 16:56:57,613 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.insert
2025-07-28 16:56:57,614 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.delete
2025-07-28 16:56:57,616 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.deleteByMap
2025-07-28 16:56:57,618 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.deleteById
2025-07-28 16:56:57,620 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.deleteBatchIds
2025-07-28 16:56:57,622 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.update
2025-07-28 16:56:57,624 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.updateById
2025-07-28 16:56:57,624 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectById
2025-07-28 16:56:57,625 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectBatchIds
2025-07-28 16:56:57,627 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectByMap
2025-07-28 16:56:57,628 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectOne
2025-07-28 16:56:57,630 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectCount
2025-07-28 16:56:57,631 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectMaps
2025-07-28 16:56:57,634 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectMapsPage
2025-07-28 16:56:57,635 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectObjs
2025-07-28 16:56:57,637 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectList
2025-07-28 16:56:57,638 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper.selectPage
2025-07-28 16:56:57,646 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectDbCarIncolor
2025-07-28 16:56:57,648 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.insert
2025-07-28 16:56:57,651 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.delete
2025-07-28 16:56:57,652 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.deleteByMap
2025-07-28 16:56:57,653 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.deleteById
2025-07-28 16:56:57,654 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.deleteBatchIds
2025-07-28 16:56:57,655 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.update
2025-07-28 16:56:57,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.updateById
2025-07-28 16:56:57,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectById
2025-07-28 16:56:57,658 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectBatchIds
2025-07-28 16:56:57,659 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectByMap
2025-07-28 16:56:57,660 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectOne
2025-07-28 16:56:57,661 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectCount
2025-07-28 16:56:57,663 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectMaps
2025-07-28 16:56:57,664 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectMapsPage
2025-07-28 16:56:57,665 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectObjs
2025-07-28 16:56:57,666 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectList
2025-07-28 16:56:57,667 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarIncolorMapper.selectPage
2025-07-28 16:56:57,676 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectDbCarSeries
2025-07-28 16:56:57,679 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.insert
2025-07-28 16:56:57,682 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.delete
2025-07-28 16:56:57,683 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.deleteByMap
2025-07-28 16:56:57,684 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.deleteById
2025-07-28 16:56:57,685 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.deleteBatchIds
2025-07-28 16:56:57,686 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.update
2025-07-28 16:56:57,688 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.updateById
2025-07-28 16:56:57,688 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectById
2025-07-28 16:56:57,689 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectBatchIds
2025-07-28 16:56:57,690 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectByMap
2025-07-28 16:56:57,691 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectOne
2025-07-28 16:56:57,692 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectCount
2025-07-28 16:56:57,693 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectMaps
2025-07-28 16:56:57,695 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectMapsPage
2025-07-28 16:56:57,696 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectObjs
2025-07-28 16:56:57,697 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectList
2025-07-28 16:56:57,699 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbCarSeriesMapper.selectPage
2025-07-28 16:56:57,706 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.queryConfigList
2025-07-28 16:56:57,707 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.insertSacDbInnerConfig
2025-07-28 16:56:57,707 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.updateSacDbInnerConfig
2025-07-28 16:56:57,710 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.insert
2025-07-28 16:56:57,712 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.delete
2025-07-28 16:56:57,713 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.deleteByMap
2025-07-28 16:56:57,714 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.deleteById
2025-07-28 16:56:57,716 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.deleteBatchIds
2025-07-28 16:56:57,718 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.update
2025-07-28 16:56:57,719 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.updateById
2025-07-28 16:56:57,719 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectById
2025-07-28 16:56:57,721 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectBatchIds
2025-07-28 16:56:57,722 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectByMap
2025-07-28 16:56:57,725 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectOne
2025-07-28 16:56:57,727 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectCount
2025-07-28 16:56:57,730 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectMaps
2025-07-28 16:56:57,732 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectMapsPage
2025-07-28 16:56:57,734 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectObjs
2025-07-28 16:56:57,736 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectList
2025-07-28 16:56:57,738 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper.selectPage
2025-07-28 16:56:57,746 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.insertSacDbReviewNode
2025-07-28 16:56:57,746 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.updateSacDbReviewNode
2025-07-28 16:56:57,749 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.insert
2025-07-28 16:56:57,750 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.delete
2025-07-28 16:56:57,752 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.deleteByMap
2025-07-28 16:56:57,754 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.deleteById
2025-07-28 16:56:57,756 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.deleteBatchIds
2025-07-28 16:56:57,760 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.update
2025-07-28 16:56:57,763 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.updateById
2025-07-28 16:56:57,763 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectById
2025-07-28 16:56:57,765 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectBatchIds
2025-07-28 16:56:57,767 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectByMap
2025-07-28 16:56:57,769 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectOne
2025-07-28 16:56:57,771 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectCount
2025-07-28 16:56:57,777 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectMaps
2025-07-28 16:56:57,779 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectMapsPage
2025-07-28 16:56:57,781 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectObjs
2025-07-28 16:56:57,783 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectList
2025-07-28 16:56:57,784 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper.selectPage
2025-07-28 16:56:57,795 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.queryReviewNodeRfList
2025-07-28 16:56:57,796 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.queryReviewNodeRfListLimitCount
2025-07-28 16:56:57,797 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.insertSacDbReviewNodeRf
2025-07-28 16:56:57,797 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.updateSacDbReviewNodeRf
2025-07-28 16:56:57,801 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.insert
2025-07-28 16:56:57,805 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.delete
2025-07-28 16:56:57,809 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.deleteByMap
2025-07-28 16:56:57,811 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.deleteById
2025-07-28 16:56:57,814 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.deleteBatchIds
2025-07-28 16:56:57,817 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.update
2025-07-28 16:56:57,820 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.updateById
2025-07-28 16:56:57,821 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectById
2025-07-28 16:56:57,824 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectBatchIds
2025-07-28 16:56:57,827 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectByMap
2025-07-28 16:56:57,829 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectOne
2025-07-28 16:56:57,832 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectCount
2025-07-28 16:56:57,834 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectMaps
2025-07-28 16:56:57,836 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectMapsPage
2025-07-28 16:56:57,839 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectObjs
2025-07-28 16:56:57,841 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectList
2025-07-28 16:56:57,843 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper.selectPage
2025-07-28 16:56:57,854 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectDbSmallCarType
2025-07-28 16:56:57,858 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.insert
2025-07-28 16:56:57,860 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.delete
2025-07-28 16:56:57,861 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.deleteByMap
2025-07-28 16:56:57,863 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.deleteById
2025-07-28 16:56:57,864 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.deleteBatchIds
2025-07-28 16:56:57,867 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.update
2025-07-28 16:56:57,870 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.updateById
2025-07-28 16:56:57,871 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectById
2025-07-28 16:56:57,872 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectBatchIds
2025-07-28 16:56:57,874 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectByMap
2025-07-28 16:56:57,876 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectOne
2025-07-28 16:56:57,878 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectCount
2025-07-28 16:56:57,880 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectMaps
2025-07-28 16:56:57,882 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectMapsPage
2025-07-28 16:56:57,883 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectObjs
2025-07-28 16:56:57,885 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectList
2025-07-28 16:56:57,887 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper.selectPage
2025-07-28 16:56:57,894 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.queryDccInfoWithPage
2025-07-28 16:56:57,900 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.insert
2025-07-28 16:56:57,901 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.delete
2025-07-28 16:56:57,902 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.deleteByMap
2025-07-28 16:56:57,903 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.deleteById
2025-07-28 16:56:57,904 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.deleteBatchIds
2025-07-28 16:56:57,905 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.update
2025-07-28 16:56:57,906 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.updateById
2025-07-28 16:56:57,906 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectById
2025-07-28 16:56:57,907 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectBatchIds
2025-07-28 16:56:57,908 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectByMap
2025-07-28 16:56:57,909 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectOne
2025-07-28 16:56:57,910 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectCount
2025-07-28 16:56:57,912 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectMaps
2025-07-28 16:56:57,913 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectMapsPage
2025-07-28 16:56:57,915 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectObjs
2025-07-28 16:56:57,916 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectList
2025-07-28 16:56:57,917 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper.selectPage
2025-07-28 16:56:57,925 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectDemoCar
2025-07-28 16:56:57,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.insertDemoCar
2025-07-28 16:56:57,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.updateDemoCar
2025-07-28 16:56:57,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.checkPlateNumberRepeat
2025-07-28 16:56:57,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.checkCarVinRepeat
2025-07-28 16:56:57,926 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.checkRepeat
2025-07-28 16:56:57,927 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.updateRoadHaul
2025-07-28 16:56:57,929 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.insert
2025-07-28 16:56:57,931 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.delete
2025-07-28 16:56:57,932 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.deleteByMap
2025-07-28 16:56:57,933 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.deleteById
2025-07-28 16:56:57,934 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.deleteBatchIds
2025-07-28 16:56:57,936 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.update
2025-07-28 16:56:57,937 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.updateById
2025-07-28 16:56:57,937 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectById
2025-07-28 16:56:57,938 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectBatchIds
2025-07-28 16:56:57,939 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectByMap
2025-07-28 16:56:57,940 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectOne
2025-07-28 16:56:57,942 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectCount
2025-07-28 16:56:57,943 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectMaps
2025-07-28 16:56:57,945 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectMapsPage
2025-07-28 16:56:57,946 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectObjs
2025-07-28 16:56:57,948 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectList
2025-07-28 16:56:57,949 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper.selectPage
2025-07-28 16:56:57,959 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectByAll
2025-07-28 16:56:57,959 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.checkDlrRelationExists
2025-07-28 16:56:57,959 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.insertSacDlrRelation
2025-07-28 16:56:57,960 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.checkSacDlrRelation
2025-07-28 16:56:57,960 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.updateByRelationId
2025-07-28 16:56:57,960 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.machDlrRelation
2025-07-28 16:56:57,963 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.insert
2025-07-28 16:56:57,965 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.delete
2025-07-28 16:56:57,967 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.deleteByMap
2025-07-28 16:56:57,968 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.deleteById
2025-07-28 16:56:57,969 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.deleteBatchIds
2025-07-28 16:56:57,972 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.update
2025-07-28 16:56:57,975 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.updateById
2025-07-28 16:56:57,975 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectById
2025-07-28 16:56:57,978 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectBatchIds
2025-07-28 16:56:57,979 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectByMap
2025-07-28 16:56:57,982 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectOne
2025-07-28 16:56:57,984 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectCount
2025-07-28 16:56:57,986 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectMaps
2025-07-28 16:56:57,990 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectMapsPage
2025-07-28 16:56:57,994 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectObjs
2025-07-28 16:56:57,998 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectList
2025-07-28 16:56:58,002 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper.selectPage
2025-07-28 16:56:58,045 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectSacEvaluationInfo
2025-07-28 16:56:58,045 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.insertSacEvaluationInfo
2025-07-28 16:56:58,046 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.updateSacEvaluationInfo
2025-07-28 16:56:58,046 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectEvaluationType
2025-07-28 16:56:58,052 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.insert
2025-07-28 16:56:58,057 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.delete
2025-07-28 16:56:58,059 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.deleteByMap
2025-07-28 16:56:58,062 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.deleteById
2025-07-28 16:56:58,064 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.deleteBatchIds
2025-07-28 16:56:58,068 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.update
2025-07-28 16:56:58,071 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.updateById
2025-07-28 16:56:58,072 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectById
2025-07-28 16:56:58,076 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectBatchIds
2025-07-28 16:56:58,079 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectByMap
2025-07-28 16:56:58,082 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectOne
2025-07-28 16:56:58,084 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectCount
2025-07-28 16:56:58,086 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectMaps
2025-07-28 16:56:58,088 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectMapsPage
2025-07-28 16:56:58,091 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectObjs
2025-07-28 16:56:58,094 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectList
2025-07-28 16:56:58,096 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper.selectPage
2025-07-28 16:56:58,107 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectSacEvaluationItem
2025-07-28 16:56:58,107 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.insertSacEvaluationItem
2025-07-28 16:56:58,108 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.updateSacEvaluationItem
2025-07-28 16:56:58,108 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.checkRepeat
2025-07-28 16:56:58,112 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.insert
2025-07-28 16:56:58,114 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.delete
2025-07-28 16:56:58,115 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.deleteByMap
2025-07-28 16:56:58,118 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.deleteById
2025-07-28 16:56:58,119 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.deleteBatchIds
2025-07-28 16:56:58,122 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.update
2025-07-28 16:56:58,124 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.updateById
2025-07-28 16:56:58,125 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectById
2025-07-28 16:56:58,126 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectBatchIds
2025-07-28 16:56:58,128 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectByMap
2025-07-28 16:56:58,130 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectOne
2025-07-28 16:56:58,131 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectCount
2025-07-28 16:56:58,134 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectMaps
2025-07-28 16:56:58,135 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectMapsPage
2025-07-28 16:56:58,137 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectObjs
2025-07-28 16:56:58,139 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectList
2025-07-28 16:56:58,142 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper.selectPage
2025-07-28 16:56:58,155 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.insertSacFieldMappingConfig
2025-07-28 16:56:58,156 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.updateSacFieldMappingConfig
2025-07-28 16:56:58,156 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.fileMappingList
2025-07-28 16:56:58,161 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.insert
2025-07-28 16:56:58,165 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.delete
2025-07-28 16:56:58,167 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.deleteByMap
2025-07-28 16:56:58,169 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.deleteById
2025-07-28 16:56:58,170 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.deleteBatchIds
2025-07-28 16:56:58,173 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.update
2025-07-28 16:56:58,175 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.updateById
2025-07-28 16:56:58,176 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectById
2025-07-28 16:56:58,177 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectBatchIds
2025-07-28 16:56:58,179 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectByMap
2025-07-28 16:56:58,181 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectOne
2025-07-28 16:56:58,183 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectCount
2025-07-28 16:56:58,185 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectMaps
2025-07-28 16:56:58,187 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectMapsPage
2025-07-28 16:56:58,190 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectObjs
2025-07-28 16:56:58,192 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectList
2025-07-28 16:56:58,195 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper.selectPage
2025-07-28 16:56:58,204 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.sacIndexPageInfoQueryByDlr
2025-07-28 16:56:58,205 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryinfoMsgbydlr
2025-07-28 16:56:58,205 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.sacIndexPageUnAuditNumQueryByDlr
2025-07-28 16:56:58,205 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.sacIndexPageAuditNumQueryByDlr
2025-07-28 16:56:58,206 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryHeatRatio
2025-07-28 16:56:58,206 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryAdpReportForm
2025-07-28 16:56:58,206 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryUserGroupClueReport
2025-07-28 16:56:58,207 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryTestDriveReport
2025-07-28 16:56:58,207 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryActivityReport
2025-07-28 16:56:58,207 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryReportBoardClueAndOrder
2025-07-28 16:56:58,207 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryReportBoardDlr
2025-07-28 16:56:58,208 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryReportBoardAct
2025-07-28 16:56:58,208 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryReportBoardCar
2025-07-28 16:56:58,208 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.querytestCarReport
2025-07-28 16:56:58,209 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryUser
2025-07-28 16:56:58,209 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.querySaleConstant
2025-07-28 16:56:58,209 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.querySaleConstantOld
2025-07-28 16:56:58,210 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.querySaleConstantReport
2025-07-28 16:56:58,210 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryClueStatisticalReport
2025-07-28 16:56:58,211 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.querySaleConstantReportOld
2025-07-28 16:56:58,211 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryClueStatisticalReportOld
2025-07-28 16:56:58,212 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryMarketActivityReport
2025-07-28 16:56:58,212 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryAgentOfflineActivityReport
2025-07-28 16:56:58,213 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryAgentOnlineActivityReport
2025-07-28 16:56:58,213 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryClueDormancyReport
2025-07-28 16:56:58,214 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryAllActivityReport
2025-07-28 16:56:58,214 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.fetchCount
2025-07-28 16:56:58,215 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.findUrl
2025-07-28 16:56:58,215 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.findSum
2025-07-28 16:56:58,216 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.findStation
2025-07-28 16:56:58,217 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.findAgentCompany
2025-07-28 16:56:58,221 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryCustomerTestDriveInfo
2025-07-28 16:56:58,222 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.queryDccDefeatCount
2025-07-28 16:56:58,226 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacIndexPageMapper.getReactivationStat
2025-07-28 16:56:58,244 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.queryPhoneFast
2025-07-28 16:56:58,245 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.sacQueryResumeFindAllbyphone
2025-07-28 16:56:58,245 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.sacQueryResumeFindAll
2025-07-28 16:56:58,245 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.sacQueryResumeFindOne
2025-07-28 16:56:58,246 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.createOnecustResume
2025-07-28 16:56:58,246 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.findtestDriveSheet
2025-07-28 16:56:58,246 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.queryVirtualRecord
2025-07-28 16:56:58,247 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectProductNameDlr
2025-07-28 16:56:58,250 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectEventNameAndEventTimeBySmartIdAndMobile
2025-07-28 16:56:58,251 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.queryLeadsEvent
2025-07-28 16:56:58,251 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.findInfoDlr
2025-07-28 16:56:58,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.updateInfoDlr
2025-07-28 16:56:58,253 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.updateArrivalTime
2025-07-28 16:56:58,254 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectManagerUseDlr
2025-07-28 16:56:58,256 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.queryArrivalStoreReport
2025-07-28 16:56:58,257 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.findUrl
2025-07-28 16:56:58,258 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.findCustInfo
2025-07-28 16:56:58,266 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.insert
2025-07-28 16:56:58,269 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.delete
2025-07-28 16:56:58,274 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.deleteByMap
2025-07-28 16:56:58,276 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.deleteById
2025-07-28 16:56:58,277 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.deleteBatchIds
2025-07-28 16:56:58,279 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.update
2025-07-28 16:56:58,280 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.updateById
2025-07-28 16:56:58,281 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectById
2025-07-28 16:56:58,282 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectBatchIds
2025-07-28 16:56:58,283 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectByMap
2025-07-28 16:56:58,285 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectOne
2025-07-28 16:56:58,286 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectCount
2025-07-28 16:56:58,289 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectMaps
2025-07-28 16:56:58,291 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectMapsPage
2025-07-28 16:56:58,294 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectObjs
2025-07-28 16:56:58,296 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectList
2025-07-28 16:56:58,298 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper.selectPage
2025-07-28 16:56:58,308 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.sacOnecustChangeLogFindInfo
2025-07-28 16:56:58,308 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.queryChangLogExist
2025-07-28 16:56:58,308 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.updateBanchNo
2025-07-28 16:56:58,308 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.queryChangLogData
2025-07-28 16:56:58,309 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.updateStatus
2025-07-28 16:56:58,309 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.deleteTem
2025-07-28 16:56:58,309 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.createsacOnecustChangeLogTemp
2025-07-28 16:56:58,309 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.createsacOnecustChangeLog
2025-07-28 16:56:58,312 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.insert
2025-07-28 16:56:58,313 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.delete
2025-07-28 16:56:58,314 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.deleteByMap
2025-07-28 16:56:58,315 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.deleteById
2025-07-28 16:56:58,316 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.deleteBatchIds
2025-07-28 16:56:58,318 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.update
2025-07-28 16:56:58,319 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.updateById
2025-07-28 16:56:58,319 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectById
2025-07-28 16:56:58,320 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectBatchIds
2025-07-28 16:56:58,321 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectByMap
2025-07-28 16:56:58,322 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectOne
2025-07-28 16:56:58,324 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectCount
2025-07-28 16:56:58,325 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectMaps
2025-07-28 16:56:58,326 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectMapsPage
2025-07-28 16:56:58,327 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectObjs
2025-07-28 16:56:58,329 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectList
2025-07-28 16:56:58,330 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper.selectPage
2025-07-28 16:56:58,338 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.sacOnecustConfigFindInfo
2025-07-28 16:56:58,340 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.insert
2025-07-28 16:56:58,342 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.delete
2025-07-28 16:56:58,343 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.deleteByMap
2025-07-28 16:56:58,344 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.deleteById
2025-07-28 16:56:58,345 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.deleteBatchIds
2025-07-28 16:56:58,347 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.update
2025-07-28 16:56:58,348 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.updateById
2025-07-28 16:56:58,348 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectById
2025-07-28 16:56:58,349 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectBatchIds
2025-07-28 16:56:58,351 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectByMap
2025-07-28 16:56:58,353 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectOne
2025-07-28 16:56:58,355 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectCount
2025-07-28 16:56:58,356 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectMaps
2025-07-28 16:56:58,357 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectMapsPage
2025-07-28 16:56:58,359 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectObjs
2025-07-28 16:56:58,360 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectList
2025-07-28 16:56:58,361 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper.selectPage
2025-07-28 16:56:58,373 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.sacOnecustInfoFindInfo
2025-07-28 16:56:58,373 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.sacOnecustInfoFindInfoNoPage
2025-07-28 16:56:58,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.sacOnecustInfoFindInfoSpecifyFields
2025-07-28 16:56:58,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.sacOnecustInfoFindCount
2025-07-28 16:56:58,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updatedlePhone
2025-07-28 16:56:58,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateReviewPhone
2025-07-28 16:56:58,375 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfoPhone
2025-07-28 16:56:58,375 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.custCount
2025-07-28 16:56:58,375 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfo
2025-07-28 16:56:58,375 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateCustNameForReview
2025-07-28 16:56:58,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateCustNameForDrive
2025-07-28 16:56:58,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.createOnecustInfo
2025-07-28 16:56:58,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfo1
2025-07-28 16:56:58,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfo2
2025-07-28 16:56:58,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfo3
2025-07-28 16:56:58,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateOnecustInfo4
2025-07-28 16:56:58,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectMapping
2025-07-28 16:56:58,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.queryOneCustSupplementInfo
2025-07-28 16:56:58,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.findinfoDlr
2025-07-28 16:56:58,379 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.customerTagWhitelis
2025-07-28 16:56:58,379 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.queryClueLevel
2025-07-28 16:56:58,379 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateClueLevel
2025-07-28 16:56:58,380 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.findClueLevel
2025-07-28 16:56:58,380 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.insertClueLevel
2025-07-28 16:56:58,380 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectDlrName
2025-07-28 16:56:58,381 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.deleteClue
2025-07-28 16:56:58,381 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.deleteReview
2025-07-28 16:56:58,388 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.insert
2025-07-28 16:56:58,391 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.delete
2025-07-28 16:56:58,391 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.deleteByMap
2025-07-28 16:56:58,393 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.deleteById
2025-07-28 16:56:58,395 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.deleteBatchIds
2025-07-28 16:56:58,397 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.update
2025-07-28 16:56:58,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.updateById
2025-07-28 16:56:58,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectById
2025-07-28 16:56:58,401 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectBatchIds
2025-07-28 16:56:58,403 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectByMap
2025-07-28 16:56:58,406 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectOne
2025-07-28 16:56:58,409 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectCount
2025-07-28 16:56:58,411 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectMaps
2025-07-28 16:56:58,413 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectMapsPage
2025-07-28 16:56:58,414 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectObjs
2025-07-28 16:56:58,416 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectList
2025-07-28 16:56:58,418 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper.selectPage
2025-07-28 16:56:58,429 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.querySacOnetaskAuditRecord
2025-07-28 16:56:58,430 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.deleteSacOnetaskAuditRecord
2025-07-28 16:56:58,431 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.createSacOnetaskAuditRecord
2025-07-28 16:56:58,431 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.updateSacOnetaskAuditRecord
2025-07-28 16:56:58,434 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.insert
2025-07-28 16:56:58,436 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.delete
2025-07-28 16:56:58,438 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.deleteByMap
2025-07-28 16:56:58,439 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.deleteById
2025-07-28 16:56:58,442 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.deleteBatchIds
2025-07-28 16:56:58,444 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.update
2025-07-28 16:56:58,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.updateById
2025-07-28 16:56:58,446 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectById
2025-07-28 16:56:58,447 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectBatchIds
2025-07-28 16:56:58,449 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectByMap
2025-07-28 16:56:58,450 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectOne
2025-07-28 16:56:58,452 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectCount
2025-07-28 16:56:58,454 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectMaps
2025-07-28 16:56:58,456 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectMapsPage
2025-07-28 16:56:58,459 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectObjs
2025-07-28 16:56:58,461 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectList
2025-07-28 16:56:58,464 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper.selectPage
2025-07-28 16:56:58,477 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.querySacOnetaskDetail
2025-07-28 16:56:58,478 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.deleteSacOnetaskDetail
2025-07-28 16:56:58,479 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.createSacOnetaskDetail
2025-07-28 16:56:58,479 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.updateSacOnetaskDetail
2025-07-28 16:56:58,479 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.updateSacOnetaskDetailByTaskId
2025-07-28 16:56:58,480 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectSacOnetaskInfoDResume
2025-07-28 16:56:58,480 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.updateDetailCancel
2025-07-28 16:56:58,482 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.queryAreaInfo
2025-07-28 16:56:58,483 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.batchInsert
2025-07-28 16:56:58,483 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.batchUpdate
2025-07-28 16:56:58,488 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.insert
2025-07-28 16:56:58,490 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.delete
2025-07-28 16:56:58,493 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.deleteByMap
2025-07-28 16:56:58,495 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.deleteById
2025-07-28 16:56:58,496 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.deleteBatchIds
2025-07-28 16:56:58,500 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.update
2025-07-28 16:56:58,501 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.updateById
2025-07-28 16:56:58,503 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectById
2025-07-28 16:56:58,504 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectBatchIds
2025-07-28 16:56:58,505 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectByMap
2025-07-28 16:56:58,507 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectOne
2025-07-28 16:56:58,509 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectCount
2025-07-28 16:56:58,512 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectMaps
2025-07-28 16:56:58,514 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectMapsPage
2025-07-28 16:56:58,516 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectObjs
2025-07-28 16:56:58,518 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectList
2025-07-28 16:56:58,521 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper.selectPage
2025-07-28 16:56:58,531 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.querySacOnetaskExecutePlan
2025-07-28 16:56:58,531 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.sacOnetaskExecutePlanQueryByTime
2025-07-28 16:56:58,531 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.deleteSacOnetaskExecutePlan
2025-07-28 16:56:58,532 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.createSacOnetaskExecutePlan
2025-07-28 16:56:58,532 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.updateSacOnetaskExecutePlan
2025-07-28 16:56:58,535 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.insert
2025-07-28 16:56:58,537 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.delete
2025-07-28 16:56:58,538 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.deleteByMap
2025-07-28 16:56:58,539 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.deleteById
2025-07-28 16:56:58,540 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.deleteBatchIds
2025-07-28 16:56:58,542 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.update
2025-07-28 16:56:58,543 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.updateById
2025-07-28 16:56:58,543 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectById
2025-07-28 16:56:58,544 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectBatchIds
2025-07-28 16:56:58,545 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectByMap
2025-07-28 16:56:58,546 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectOne
2025-07-28 16:56:58,547 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectCount
2025-07-28 16:56:58,548 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectMaps
2025-07-28 16:56:58,550 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectMapsPage
2025-07-28 16:56:58,552 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectObjs
2025-07-28 16:56:58,553 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectList
2025-07-28 16:56:58,555 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper.selectPage
2025-07-28 16:56:58,566 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.querySacOnetaskInfo
2025-07-28 16:56:58,567 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.querySacOnetaskInfoCount
2025-07-28 16:56:58,567 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.deleteSacOnetaskInfo
2025-07-28 16:56:58,568 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.createSacOnetaskInfo
2025-07-28 16:56:58,568 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.updateSacOnetaskInfo
2025-07-28 16:56:58,568 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.sacOnetaskInfoMsg
2025-07-28 16:56:58,569 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.sacOnetaskInfoMsgCount
2025-07-28 16:56:58,569 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.checkIsStoreManager
2025-07-28 16:56:58,572 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.queryTaskForMq
2025-07-28 16:56:58,575 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.queryTaskClueForMq
2025-07-28 16:56:58,580 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.insert
2025-07-28 16:56:58,582 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.delete
2025-07-28 16:56:58,585 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.deleteByMap
2025-07-28 16:56:58,587 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.deleteById
2025-07-28 16:56:58,590 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.deleteBatchIds
2025-07-28 16:56:58,592 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.update
2025-07-28 16:56:58,594 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.updateById
2025-07-28 16:56:58,594 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectById
2025-07-28 16:56:58,596 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectBatchIds
2025-07-28 16:56:58,599 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectByMap
2025-07-28 16:56:58,601 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectOne
2025-07-28 16:56:58,604 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectCount
2025-07-28 16:56:58,608 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectMaps
2025-07-28 16:56:58,610 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectMapsPage
2025-07-28 16:56:58,612 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectObjs
2025-07-28 16:56:58,614 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectList
2025-07-28 16:56:58,616 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper.selectPage
2025-07-28 16:56:58,626 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.querySacOnetaskLeadInTemp
2025-07-28 16:56:58,626 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.deleteSacOnetaskLeadInTemp
2025-07-28 16:56:58,627 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.createSacOnetaskLeadInTemp
2025-07-28 16:56:58,627 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.updateSacOnetaskLeadInTemp
2025-07-28 16:56:58,627 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.insertTaskImport
2025-07-28 16:56:58,627 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryTaskImportExist
2025-07-28 16:56:58,627 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.updateBanchNo
2025-07-28 16:56:58,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryTaskImportData
2025-07-28 16:56:58,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.saveSussess
2025-07-28 16:56:58,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.saveFaile
2025-07-28 16:56:58,628 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.deleteTem
2025-07-28 16:56:58,629 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.reviewList
2025-07-28 16:56:58,629 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.reviewListForTask
2025-07-28 16:56:58,629 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryPhoneBySmartId
2025-07-28 16:56:58,633 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryClueInfoForTask
2025-07-28 16:56:58,633 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryClueInfoForTasks
2025-07-28 16:56:58,634 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.checkTaskPhone
2025-07-28 16:56:58,636 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.queryUserStatus
2025-07-28 16:56:58,639 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.insert
2025-07-28 16:56:58,640 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.delete
2025-07-28 16:56:58,641 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.deleteByMap
2025-07-28 16:56:58,642 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.deleteById
2025-07-28 16:56:58,644 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.deleteBatchIds
2025-07-28 16:56:58,646 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.update
2025-07-28 16:56:58,647 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.updateById
2025-07-28 16:56:58,647 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectById
2025-07-28 16:56:58,648 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectBatchIds
2025-07-28 16:56:58,649 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectByMap
2025-07-28 16:56:58,651 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectOne
2025-07-28 16:56:58,652 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectCount
2025-07-28 16:56:58,653 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectMaps
2025-07-28 16:56:58,654 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectMapsPage
2025-07-28 16:56:58,656 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectObjs
2025-07-28 16:56:58,657 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectList
2025-07-28 16:56:58,659 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper.selectPage
2025-07-28 16:56:58,671 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireReviewInfo
2025-07-28 16:56:58,672 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoPhone
2025-07-28 16:56:58,673 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoCountSingle
2025-07-28 16:56:58,675 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoCount
2025-07-28 16:56:58,676 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfo
2025-07-28 16:56:58,682 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoEntity
2025-07-28 16:56:58,683 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoNoPage
2025-07-28 16:56:58,684 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoNoPageCount
2025-07-28 16:56:58,685 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoNew
2025-07-28 16:56:58,686 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoNewCount
2025-07-28 16:56:58,687 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoDefeat
2025-07-28 16:56:58,692 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfo3
2025-07-28 16:56:58,693 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfo1
2025-07-28 16:56:58,694 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.queryLookup
2025-07-28 16:56:58,695 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.queryLookupMap
2025-07-28 16:56:58,695 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.queryLookupMapSpecifyFields
2025-07-28 16:56:58,696 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.getMdmDlrInfoQuery
2025-07-28 16:56:58,697 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfo4
2025-07-28 16:56:58,698 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectUserIdAndEmpNameByParam
2025-07-28 16:56:58,699 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.findEmployee
2025-07-28 16:56:58,700 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.queryDlrCodeByDlrId
2025-07-28 16:56:58,704 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.getClueDetailAndCustomInfo
2025-07-28 16:56:58,706 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoActivity
2025-07-28 16:56:58,707 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoUserGroup
2025-07-28 16:56:58,708 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.entireDlrClueInfoAllFields
2025-07-28 16:56:58,709 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectCallPersonByParam
2025-07-28 16:56:58,714 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.insert
2025-07-28 16:56:58,716 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.delete
2025-07-28 16:56:58,718 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.deleteByMap
2025-07-28 16:56:58,719 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.deleteById
2025-07-28 16:56:58,721 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.deleteBatchIds
2025-07-28 16:56:58,724 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.update
2025-07-28 16:56:58,726 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.updateById
2025-07-28 16:56:58,726 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectById
2025-07-28 16:56:58,727 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectBatchIds
2025-07-28 16:56:58,728 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectByMap
2025-07-28 16:56:58,730 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectOne
2025-07-28 16:56:58,733 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectCount
2025-07-28 16:56:58,735 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectMaps
2025-07-28 16:56:58,737 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectMapsPage
2025-07-28 16:56:58,739 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectObjs
2025-07-28 16:56:58,741 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectList
2025-07-28 16:56:58,743 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper.selectPage
2025-07-28 16:56:58,752 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.insertSacReviewAuditHis
2025-07-28 16:56:58,752 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.updateSacReviewAuditHis
2025-07-28 16:56:58,755 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.insert
2025-07-28 16:56:58,756 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.delete
2025-07-28 16:56:58,757 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.deleteByMap
2025-07-28 16:56:58,758 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.deleteById
2025-07-28 16:56:58,759 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.deleteBatchIds
2025-07-28 16:56:58,760 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.update
2025-07-28 16:56:58,762 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.updateById
2025-07-28 16:56:58,762 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectById
2025-07-28 16:56:58,763 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectBatchIds
2025-07-28 16:56:58,764 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectByMap
2025-07-28 16:56:58,765 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectOne
2025-07-28 16:56:58,767 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectCount
2025-07-28 16:56:58,768 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectMaps
2025-07-28 16:56:58,771 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectMapsPage
2025-07-28 16:56:58,773 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectObjs
2025-07-28 16:56:58,775 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectList
2025-07-28 16:56:58,776 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper.selectPage
2025-07-28 16:56:58,787 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.queryListAuditReviewInfo
2025-07-28 16:56:58,787 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.checkAuditReviewInfo
2025-07-28 16:56:58,787 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.insertSacReviewAudit
2025-07-28 16:56:58,788 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.updateSacReviewAudit
2025-07-28 16:56:58,788 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.findActivationAfterDefeat
2025-07-28 16:56:58,788 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.findActivationAfterDefeatV2
2025-07-28 16:56:58,788 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectProductByDlrCode
2025-07-28 16:56:58,789 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.newFindActivationAfterDefeat
2025-07-28 16:56:58,789 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.queryListCount
2025-07-28 16:56:58,789 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.batchUpdateReviewAudit
2025-07-28 16:56:58,789 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.batchSaveResume
2025-07-28 16:56:58,790 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.batchSaveMsgRecord
2025-07-28 16:56:58,793 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.insert
2025-07-28 16:56:58,795 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.delete
2025-07-28 16:56:58,796 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.deleteByMap
2025-07-28 16:56:58,797 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.deleteById
2025-07-28 16:56:58,799 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.deleteBatchIds
2025-07-28 16:56:58,801 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.update
2025-07-28 16:56:59,035 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.updateById
2025-07-28 16:56:59,036 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectById
2025-07-28 16:56:59,039 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectBatchIds
2025-07-28 16:56:59,041 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectByMap
2025-07-28 16:56:59,045 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectOne
2025-07-28 16:56:59,048 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectCount
2025-07-28 16:56:59,065 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectMaps
2025-07-28 16:56:59,074 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectMapsPage
2025-07-28 16:56:59,079 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectObjs
2025-07-28 16:56:59,084 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectList
2025-07-28 16:56:59,087 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper.selectPage
2025-07-28 16:56:59,120 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.insertSacReviewBak
2025-07-28 16:56:59,121 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.updateSacReviewBak
2025-07-28 16:56:59,138 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.insert
2025-07-28 16:56:59,143 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.delete
2025-07-28 16:56:59,147 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.deleteByMap
2025-07-28 16:56:59,150 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.deleteById
2025-07-28 16:56:59,155 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.deleteBatchIds
2025-07-28 16:56:59,162 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.update
2025-07-28 16:56:59,167 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.updateById
2025-07-28 16:56:59,168 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectById
2025-07-28 16:56:59,170 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectBatchIds
2025-07-28 16:56:59,174 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectByMap
2025-07-28 16:56:59,179 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectOne
2025-07-28 16:56:59,184 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectCount
2025-07-28 16:56:59,188 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectMaps
2025-07-28 16:56:59,192 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectMapsPage
2025-07-28 16:56:59,196 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectObjs
2025-07-28 16:56:59,200 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectList
2025-07-28 16:56:59,203 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper.selectPage
2025-07-28 16:56:59,228 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListMeReviewInfo
2025-07-28 16:56:59,229 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListByDlr
2025-07-28 16:56:59,229 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryTOrcVeBuSaleOrderToC
2025-07-28 16:56:59,229 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryTOrcVeBuEcReturnOrder
2025-07-28 16:56:59,230 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListByDlrNew
2025-07-28 16:56:59,230 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListByDlrCount
2025-07-28 16:56:59,230 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListAuditReviewInfo
2025-07-28 16:56:59,231 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListAuditReviewRecordInfoPerformance
2025-07-28 16:56:59,232 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListAuditReviewRecordInfo
2025-07-28 16:56:59,232 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryListAuditReviewRecordInfoCount
2025-07-28 16:56:59,233 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryReviewRecord
2025-07-28 16:56:59,233 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryReviewId
2025-07-28 16:56:59,234 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.queryTwoReviewId
2025-07-28 16:56:59,235 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.updateSacReview
2025-07-28 16:56:59,236 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.updateClueInfoDlr
2025-07-28 16:56:59,237 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.updateReview
2025-07-28 16:56:59,238 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.departClueTransfer
2025-07-28 16:56:59,239 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.departClueTransferNoPage
2025-07-28 16:56:59,240 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.departClueTransferCount
2025-07-28 16:56:59,241 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.findClueReview
2025-07-28 16:56:59,241 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.findClueReviewHis
2025-07-28 16:56:59,242 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper.getDlrCodeByCompanyId
2025-07-28 16:56:59,254 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.deleteBySetId
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.insertSacReviewFpSetD
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.insertSacReviewFpSet
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.updateByPrimaryKeySetId
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.queryListReviewAssignInfo
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.checkReviewAssign
2025-07-28 16:56:59,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.queryReviewRuleInfo
2025-07-28 16:56:59,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.countBySetId
2025-07-28 16:56:59,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.deleteReviewAssignInfo
2025-07-28 16:56:59,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.machReviewFpRule
2025-07-28 16:56:59,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.queryReviewPersonBySetD
2025-07-28 16:56:59,257 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.queryReviewFpSetD
2025-07-28 16:56:59,261 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.insert
2025-07-28 16:56:59,263 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.delete
2025-07-28 16:56:59,263 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.deleteByMap
2025-07-28 16:56:59,264 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.deleteById
2025-07-28 16:56:59,266 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.deleteBatchIds
2025-07-28 16:56:59,268 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.update
2025-07-28 16:56:59,268 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.updateById
2025-07-28 16:56:59,269 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectById
2025-07-28 16:56:59,270 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectBatchIds
2025-07-28 16:56:59,271 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectByMap
2025-07-28 16:56:59,273 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectOne
2025-07-28 16:56:59,275 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectCount
2025-07-28 16:56:59,278 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectMaps
2025-07-28 16:56:59,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectMapsPage
2025-07-28 16:56:59,281 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectObjs
2025-07-28 16:56:59,283 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectList
2025-07-28 16:56:59,284 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper.selectPage
2025-07-28 16:56:59,306 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.insertSacReviewHis
2025-07-28 16:56:59,307 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.updateSacReviewHis
2025-07-28 16:56:59,308 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.updateSacReviewHisBatch
2025-07-28 16:56:59,308 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectPhonesByReviewIdAndOrgCode
2025-07-28 16:56:59,308 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectPhoneByReviewIdAndOrgCode
2025-07-28 16:56:59,316 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.insert
2025-07-28 16:56:59,320 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.delete
2025-07-28 16:56:59,323 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.deleteByMap
2025-07-28 16:56:59,326 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.deleteById
2025-07-28 16:56:59,327 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.deleteBatchIds
2025-07-28 16:56:59,332 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.update
2025-07-28 16:56:59,334 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.updateById
2025-07-28 16:56:59,335 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectById
2025-07-28 16:56:59,337 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectBatchIds
2025-07-28 16:56:59,341 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectByMap
2025-07-28 16:56:59,345 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectOne
2025-07-28 16:56:59,349 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectCount
2025-07-28 16:56:59,352 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectMaps
2025-07-28 16:56:59,356 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectMapsPage
2025-07-28 16:56:59,360 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectObjs
2025-07-28 16:56:59,363 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectList
2025-07-28 16:56:59,368 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewHisMapper.selectPage
2025-07-28 16:56:59,391 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.deleteReview
2025-07-28 16:56:59,392 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryListMeReviewInfo
2025-07-28 16:56:59,392 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryListGroupReviewInfo
2025-07-28 16:56:59,392 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryListAuditReviewInfo
2025-07-28 16:56:59,392 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryListAuditReviewRecordInfo
2025-07-28 16:56:59,393 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryReviewRecord
2025-07-28 16:56:59,393 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateInfoDlr
2025-07-28 16:56:59,394 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateSleepCluePool
2025-07-28 16:56:59,394 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateReviewAssign
2025-07-28 16:56:59,394 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateReviewAssignBatch
2025-07-28 16:56:59,395 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.insertSacReview
2025-07-28 16:56:59,395 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateSacReview
2025-07-28 16:56:59,397 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateClue
2025-07-28 16:56:59,397 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryTaskNumByPerson
2025-07-28 16:56:59,398 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryTaskNumByPersonNoPage
2025-07-28 16:56:59,399 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryReviewInfoById
2025-07-28 16:56:59,399 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.queryReviewInfoHisById
2025-07-28 16:56:59,399 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectReviewByID
2025-07-28 16:56:59,400 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectEmpByDlrCode
2025-07-28 16:56:59,400 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.createMsgRecord
2025-07-28 16:56:59,401 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectDlrByPhone
2025-07-28 16:56:59,401 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.findPhone
2025-07-28 16:56:59,402 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateClueInfoDlr
2025-07-28 16:56:59,402 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateClueInfoDlrBatch
2025-07-28 16:56:59,403 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.findClueInfoDlr
2025-07-28 16:56:59,403 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateClueDlr
2025-07-28 16:56:59,404 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateClueDlrBatch
2025-07-28 16:56:59,405 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectOneProductByRand
2025-07-28 16:56:59,405 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.deleteNewClue
2025-07-28 16:56:59,406 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.batchUpdateReview
2025-07-28 16:56:59,406 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.saveReviewCust
2025-07-28 16:56:59,407 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.transferEmployeeDimissionReview
2025-07-28 16:56:59,416 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.insert
2025-07-28 16:56:59,421 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.delete
2025-07-28 16:56:59,422 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.deleteByMap
2025-07-28 16:56:59,424 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.deleteById
2025-07-28 16:56:59,426 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.deleteBatchIds
2025-07-28 16:56:59,433 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.update
2025-07-28 16:56:59,436 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.updateById
2025-07-28 16:56:59,436 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectById
2025-07-28 16:56:59,438 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectBatchIds
2025-07-28 16:56:59,441 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectByMap
2025-07-28 16:56:59,445 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectOne
2025-07-28 16:56:59,450 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectCount
2025-07-28 16:56:59,456 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectMaps
2025-07-28 16:56:59,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectMapsPage
2025-07-28 16:56:59,467 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectObjs
2025-07-28 16:56:59,496 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectList
2025-07-28 16:56:59,504 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewMapper.selectPage
2025-07-28 16:56:59,529 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectByAll
2025-07-28 16:56:59,531 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.checkReviewOverTime
2025-07-28 16:56:59,532 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.queryListRuleD
2025-07-28 16:56:59,533 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.checkReviewOverTimed
2025-07-28 16:56:59,533 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.insertReviewOverTimed
2025-07-28 16:56:59,533 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.updateReviewOvertimeRuled
2025-07-28 16:56:59,533 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.deleteRuledInfo
2025-07-28 16:56:59,533 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.deleteByRuled
2025-07-28 16:56:59,534 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.checkReviewOvertimeRuleExists
2025-07-28 16:56:59,534 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.checkReviewOvertimeRuleDExits
2025-07-28 16:56:59,546 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.insert
2025-07-28 16:56:59,551 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.delete
2025-07-28 16:56:59,558 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.deleteByMap
2025-07-28 16:56:59,566 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.deleteById
2025-07-28 16:56:59,579 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.deleteBatchIds
2025-07-28 16:56:59,586 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.update
2025-07-28 16:56:59,592 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.updateById
2025-07-28 16:56:59,594 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectById
2025-07-28 16:56:59,598 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectBatchIds
2025-07-28 16:56:59,602 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectByMap
2025-07-28 16:56:59,606 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectOne
2025-07-28 16:56:59,611 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectCount
2025-07-28 16:56:59,616 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectMaps
2025-07-28 16:56:59,621 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectMapsPage
2025-07-28 16:56:59,626 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectObjs
2025-07-28 16:56:59,630 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectList
2025-07-28 16:56:59,636 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper.selectPage
2025-07-28 16:56:59,654 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.checkReviewOvertime
2025-07-28 16:56:59,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.insertReviewOvertimeSet
2025-07-28 16:56:59,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.updateBySetIdAndUpdateControlId
2025-07-28 16:56:59,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.countBySetId
2025-07-28 16:56:59,657 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.queryListReviewOvertimeInfo
2025-07-28 16:56:59,658 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.machOverTimeRule
2025-07-28 16:56:59,658 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.queryBillOverTime
2025-07-28 16:56:59,667 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.insert
2025-07-28 16:56:59,672 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.delete
2025-07-28 16:56:59,677 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.deleteByMap
2025-07-28 16:56:59,682 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.deleteById
2025-07-28 16:56:59,686 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.deleteBatchIds
2025-07-28 16:56:59,691 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.update
2025-07-28 16:56:59,695 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.updateById
2025-07-28 16:56:59,696 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectById
2025-07-28 16:56:59,700 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectBatchIds
2025-07-28 16:56:59,705 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectByMap
2025-07-28 16:56:59,709 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectOne
2025-07-28 16:56:59,715 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectCount
2025-07-28 16:56:59,721 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectMaps
2025-07-28 16:56:59,727 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectMapsPage
2025-07-28 16:56:59,734 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectObjs
2025-07-28 16:56:59,740 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectList
2025-07-28 16:56:59,745 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper.selectPage
2025-07-28 16:56:59,764 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectByAll
2025-07-28 16:56:59,765 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.checkReviewPlanInfo
2025-07-28 16:56:59,766 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.checkReviewPlanExists
2025-07-28 16:56:59,777 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.insert
2025-07-28 16:56:59,782 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.delete
2025-07-28 16:56:59,788 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.deleteByMap
2025-07-28 16:56:59,793 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.deleteById
2025-07-28 16:56:59,798 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.deleteBatchIds
2025-07-28 16:56:59,806 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.update
2025-07-28 16:56:59,814 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.updateById
2025-07-28 16:56:59,817 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectById
2025-07-28 16:56:59,822 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectBatchIds
2025-07-28 16:56:59,827 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectByMap
2025-07-28 16:56:59,832 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectOne
2025-07-28 16:56:59,836 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectCount
2025-07-28 16:56:59,841 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectMaps
2025-07-28 16:56:59,845 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectMapsPage
2025-07-28 16:56:59,849 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectObjs
2025-07-28 16:56:59,853 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectList
2025-07-28 16:56:59,857 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper.selectPage
2025-07-28 16:56:59,874 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.querySacServerClass
2025-07-28 16:56:59,875 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.deleteSacServerClass
2025-07-28 16:56:59,875 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.createSacServerClass
2025-07-28 16:56:59,876 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.updateSacServerClass
2025-07-28 16:56:59,884 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.insert
2025-07-28 16:56:59,888 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.delete
2025-07-28 16:56:59,892 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.deleteByMap
2025-07-28 16:56:59,896 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.deleteById
2025-07-28 16:56:59,900 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.deleteBatchIds
2025-07-28 16:56:59,904 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.update
2025-07-28 16:56:59,910 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.updateById
2025-07-28 16:56:59,912 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectById
2025-07-28 16:56:59,916 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectBatchIds
2025-07-28 16:56:59,922 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectByMap
2025-07-28 16:56:59,928 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectOne
2025-07-28 16:56:59,933 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectCount
2025-07-28 16:56:59,939 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectMaps
2025-07-28 16:56:59,945 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectMapsPage
2025-07-28 16:56:59,950 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectObjs
2025-07-28 16:56:59,959 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectList
2025-07-28 16:56:59,966 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacServerClassMapper.selectPage
2025-07-28 16:56:59,985 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.insertSystemConfigValue
2025-07-28 16:56:59,987 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.updateByConfigValueIdAndUpdateControlId
2025-07-28 16:56:59,988 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.countByConfigValueId
2025-07-28 16:56:59,988 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.countByConfigId
2025-07-28 16:56:59,989 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectByAll
2025-07-28 16:56:59,989 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectByConfigCode
2025-07-28 16:56:59,989 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectConfigInfo
2025-07-28 16:56:59,989 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.updateByConfigId
2025-07-28 16:57:00,036 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.insert
2025-07-28 16:57:00,038 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.delete
2025-07-28 16:57:00,039 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.deleteByMap
2025-07-28 16:57:00,041 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.deleteById
2025-07-28 16:57:00,042 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.deleteBatchIds
2025-07-28 16:57:00,043 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.update
2025-07-28 16:57:00,044 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.updateById
2025-07-28 16:57:00,045 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectById
2025-07-28 16:57:00,047 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectBatchIds
2025-07-28 16:57:00,048 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectByMap
2025-07-28 16:57:00,050 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectOne
2025-07-28 16:57:00,052 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectCount
2025-07-28 16:57:00,055 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectMaps
2025-07-28 16:57:00,057 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectMapsPage
2025-07-28 16:57:00,059 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectObjs
2025-07-28 16:57:00,061 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectList
2025-07-28 16:57:00,065 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper.selectPage
2025-07-28 16:57:00,080 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.updateSacTestDriveSheet
2025-07-28 16:57:00,081 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.sacTestDriveLongApplySaveOne
2025-07-28 16:57:00,081 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.sacTestDriveLongApplyFindAll
2025-07-28 16:57:00,082 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.sacTestDriveLongApplyUpdateById
2025-07-28 16:57:00,082 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.sacTestDriveLongApplyFindByApplyTime
2025-07-28 16:57:00,082 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.sacTestDriveAppointmentFindByTime
2025-07-28 16:57:00,082 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.queryMobileByActiviteId
2025-07-28 16:57:00,082 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectSmartIdByMobile
2025-07-28 16:57:00,084 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.queryLookUpValue
2025-07-28 16:57:00,084 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.findtestCarDriveSheet
2025-07-28 16:57:00,084 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.checkIsSendMessage
2025-07-28 16:57:00,089 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.insert
2025-07-28 16:57:00,091 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.delete
2025-07-28 16:57:00,092 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.deleteByMap
2025-07-28 16:57:00,093 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.deleteById
2025-07-28 16:57:00,095 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.deleteBatchIds
2025-07-28 16:57:00,100 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.update
2025-07-28 16:57:00,103 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.updateById
2025-07-28 16:57:00,104 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectById
2025-07-28 16:57:00,105 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectBatchIds
2025-07-28 16:57:00,108 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectByMap
2025-07-28 16:57:00,112 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectOne
2025-07-28 16:57:00,116 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectCount
2025-07-28 16:57:00,119 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectMaps
2025-07-28 16:57:00,123 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectMapsPage
2025-07-28 16:57:00,126 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectObjs
2025-07-28 16:57:00,129 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectList
2025-07-28 16:57:00,132 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper.selectPage
2025-07-28 16:57:00,144 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.sacTestDriveReviewRecordFindAll
2025-07-28 16:57:00,145 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.createTestDriveReviewRecordInfo
2025-07-28 16:57:00,150 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.insert
2025-07-28 16:57:00,153 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.delete
2025-07-28 16:57:00,156 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.deleteByMap
2025-07-28 16:57:00,159 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.deleteById
2025-07-28 16:57:00,160 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.deleteBatchIds
2025-07-28 16:57:00,163 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.update
2025-07-28 16:57:00,165 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.updateById
2025-07-28 16:57:00,165 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectById
2025-07-28 16:57:00,166 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectBatchIds
2025-07-28 16:57:00,168 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectByMap
2025-07-28 16:57:00,170 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectOne
2025-07-28 16:57:00,173 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectCount
2025-07-28 16:57:00,175 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectMaps
2025-07-28 16:57:00,177 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectMapsPage
2025-07-28 16:57:00,178 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectObjs
2025-07-28 16:57:00,180 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectList
2025-07-28 16:57:00,182 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper.selectPage
2025-07-28 16:57:00,193 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.insertSacTestDriveSheetHis
2025-07-28 16:57:00,197 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.insert
2025-07-28 16:57:00,199 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.delete
2025-07-28 16:57:00,201 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.deleteByMap
2025-07-28 16:57:00,202 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.deleteById
2025-07-28 16:57:00,203 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.deleteBatchIds
2025-07-28 16:57:00,206 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.update
2025-07-28 16:57:00,208 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.updateById
2025-07-28 16:57:00,208 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectById
2025-07-28 16:57:00,209 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectBatchIds
2025-07-28 16:57:00,211 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectByMap
2025-07-28 16:57:00,212 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectOne
2025-07-28 16:57:00,215 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectCount
2025-07-28 16:57:00,217 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectMaps
2025-07-28 16:57:00,219 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectMapsPage
2025-07-28 16:57:00,221 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectObjs
2025-07-28 16:57:00,223 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectList
2025-07-28 16:57:00,226 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetHisMapper.selectPage
2025-07-28 16:57:00,241 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectSacTestDriveSheetDetail
2025-07-28 16:57:00,241 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectSacTestDriveSheet
2025-07-28 16:57:00,241 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.insertSacTestDriveSheet
2025-07-28 16:57:00,242 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.updateSacTestDriveSheet
2025-07-28 16:57:00,245 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.insert
2025-07-28 16:57:00,247 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.delete
2025-07-28 16:57:00,249 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.deleteByMap
2025-07-28 16:57:00,250 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.deleteById
2025-07-28 16:57:00,251 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.deleteBatchIds
2025-07-28 16:57:00,254 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.update
2025-07-28 16:57:00,255 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.updateById
2025-07-28 16:57:00,256 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectById
2025-07-28 16:57:00,257 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectBatchIds
2025-07-28 16:57:00,258 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectByMap
2025-07-28 16:57:00,260 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectOne
2025-07-28 16:57:00,262 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectCount
2025-07-28 16:57:00,263 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectMaps
2025-07-28 16:57:00,264 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectMapsPage
2025-07-28 16:57:00,266 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectObjs
2025-07-28 16:57:00,268 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectList
2025-07-28 16:57:00,270 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTestDriveSheetMapper.selectPage
2025-07-28 16:57:00,278 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.insertSacTransferApply
2025-07-28 16:57:00,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.updateSacTransferApply
2025-07-28 16:57:00,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectByPage
2025-07-28 16:57:00,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectApplyByPage
2025-07-28 16:57:00,280 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectAuditByPage
2025-07-28 16:57:00,280 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.findAgentDlrCodeList
2025-07-28 16:57:00,280 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.findAgentCompany
2025-07-28 16:57:00,283 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.insert
2025-07-28 16:57:00,285 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.delete
2025-07-28 16:57:00,286 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.deleteByMap
2025-07-28 16:57:00,286 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.deleteById
2025-07-28 16:57:00,287 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.deleteBatchIds
2025-07-28 16:57:00,289 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.update
2025-07-28 16:57:00,290 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.updateById
2025-07-28 16:57:00,290 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectById
2025-07-28 16:57:00,291 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectBatchIds
2025-07-28 16:57:00,292 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectByMap
2025-07-28 16:57:00,293 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectOne
2025-07-28 16:57:00,294 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectCount
2025-07-28 16:57:00,296 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectMaps
2025-07-28 16:57:00,297 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectMapsPage
2025-07-28 16:57:00,298 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectObjs
2025-07-28 16:57:00,300 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectList
2025-07-28 16:57:00,301 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper.selectPage
2025-07-28 16:57:00,311 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.insertSacTransferAudit
2025-07-28 16:57:00,312 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.updateSacTransferAudit
2025-07-28 16:57:00,312 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.updateSacTransferAuditBatch
2025-07-28 16:57:00,312 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectByPage
2025-07-28 16:57:00,313 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectByPageNoPage
2025-07-28 16:57:00,313 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectTransferapplyByPhone
2025-07-28 16:57:00,314 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteAudit
2025-07-28 16:57:00,314 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteTransferapply
2025-07-28 16:57:00,314 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectReviewByPhone
2025-07-28 16:57:00,315 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteReviewAudit
2025-07-28 16:57:00,319 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.queryAgentInfoByDlrCode
2025-07-28 16:57:00,322 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.queryClueInfoByAuditId
2025-07-28 16:57:00,322 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.batchUpdateTransferCLue
2025-07-28 16:57:00,322 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.batchUpdateTransferReview
2025-07-28 16:57:00,325 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.queryPcTransferDlrInfo
2025-07-28 16:57:00,331 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.insert
2025-07-28 16:57:00,334 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.delete
2025-07-28 16:57:00,335 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteByMap
2025-07-28 16:57:00,337 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteById
2025-07-28 16:57:00,338 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.deleteBatchIds
2025-07-28 16:57:00,341 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.update
2025-07-28 16:57:00,343 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.updateById
2025-07-28 16:57:00,344 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectById
2025-07-28 16:57:00,345 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectBatchIds
2025-07-28 16:57:00,346 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectByMap
2025-07-28 16:57:00,348 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectOne
2025-07-28 16:57:00,350 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectCount
2025-07-28 16:57:00,352 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectMaps
2025-07-28 16:57:00,353 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectMapsPage
2025-07-28 16:57:00,355 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectObjs
2025-07-28 16:57:00,357 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectList
2025-07-28 16:57:00,359 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper.selectPage
2025-07-28 16:57:00,369 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.querySacUserGroupDetail
2025-07-28 16:57:00,369 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.deleteSacUserGroupDetail
2025-07-28 16:57:00,370 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.createSacUserGroupDetail
2025-07-28 16:57:00,370 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.updateSacUserGroupDetail
2025-07-28 16:57:00,374 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.insert
2025-07-28 16:57:00,376 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.delete
2025-07-28 16:57:00,378 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.deleteByMap
2025-07-28 16:57:00,379 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.deleteById
2025-07-28 16:57:00,380 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.deleteBatchIds
2025-07-28 16:57:00,382 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.update
2025-07-28 16:57:00,384 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.updateById
2025-07-28 16:57:00,384 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectById
2025-07-28 16:57:00,385 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectBatchIds
2025-07-28 16:57:00,387 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectByMap
2025-07-28 16:57:00,389 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectOne
2025-07-28 16:57:00,391 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectCount
2025-07-28 16:57:00,392 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectMaps
2025-07-28 16:57:00,394 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectMapsPage
2025-07-28 16:57:00,396 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectObjs
2025-07-28 16:57:00,398 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectList
2025-07-28 16:57:00,399 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupDetailMapper.selectPage
2025-07-28 16:57:00,410 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.querySacUserGroup
2025-07-28 16:57:00,410 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.deleteSacUserGroup
2025-07-28 16:57:00,410 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.createSacUserGroup
2025-07-28 16:57:00,411 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.updateSacUserGroup
2025-07-28 16:57:00,414 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.insert
2025-07-28 16:57:00,416 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.delete
2025-07-28 16:57:00,417 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.deleteByMap
2025-07-28 16:57:00,420 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.deleteById
2025-07-28 16:57:00,421 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.deleteBatchIds
2025-07-28 16:57:00,424 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.update
2025-07-28 16:57:00,425 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.updateById
2025-07-28 16:57:00,426 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectById
2025-07-28 16:57:00,427 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectBatchIds
2025-07-28 16:57:00,427 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectByMap
2025-07-28 16:57:00,430 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectOne
2025-07-28 16:57:00,431 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectCount
2025-07-28 16:57:00,432 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectMaps
2025-07-28 16:57:00,433 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectMapsPage
2025-07-28 16:57:00,434 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectObjs
2025-07-28 16:57:00,435 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectList
2025-07-28 16:57:00,437 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.SacUserGroupMapper.selectPage
2025-07-28 16:57:00,445 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.insertSacWorkGroup
2025-07-28 16:57:00,445 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.updateSacWorkGroup
2025-07-28 16:57:00,445 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.checkWorkGroupRepeat
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.checkWorkGroupExists
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.checkWorkGroupUserExists
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.insertListWorkGroupUser
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.queryListWorkGroupInfo
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.updateIsLeaderByWorkGroupIdAndWorkGroupUserId
2025-07-28 16:57:00,446 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.deleteWorkGroupUser
2025-07-28 16:57:00,447 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.queryListWorkGroupUserInfo
2025-07-28 16:57:00,447 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectGroupUserList
2025-07-28 16:57:00,447 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectGroupUserReviewNum
2025-07-28 16:57:00,450 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.insert
2025-07-28 16:57:00,451 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.delete
2025-07-28 16:57:00,452 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.deleteByMap
2025-07-28 16:57:00,453 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.deleteById
2025-07-28 16:57:00,454 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.deleteBatchIds
2025-07-28 16:57:00,455 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.update
2025-07-28 16:57:00,456 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.updateById
2025-07-28 16:57:00,456 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectById
2025-07-28 16:57:00,457 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectBatchIds
2025-07-28 16:57:00,458 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectByMap
2025-07-28 16:57:00,459 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectOne
2025-07-28 16:57:00,460 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectCount
2025-07-28 16:57:00,461 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectMaps
2025-07-28 16:57:00,462 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectMapsPage
2025-07-28 16:57:00,463 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectObjs
2025-07-28 16:57:00,464 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectList
2025-07-28 16:57:00,466 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper.selectPage
2025-07-28 16:57:00,473 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.insertSacWorkGroupUser
2025-07-28 16:57:00,473 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.updateSacWorkGroupUser
2025-07-28 16:57:00,476 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.insert
2025-07-28 16:57:00,477 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.delete
2025-07-28 16:57:00,478 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.deleteByMap
2025-07-28 16:57:00,479 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.deleteById
2025-07-28 16:57:00,479 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.deleteBatchIds
2025-07-28 16:57:00,480 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.update
2025-07-28 16:57:00,481 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.updateById
2025-07-28 16:57:00,482 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectById
2025-07-28 16:57:00,483 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectBatchIds
2025-07-28 16:57:00,483 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectByMap
2025-07-28 16:57:00,484 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectOne
2025-07-28 16:57:00,486 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectCount
2025-07-28 16:57:00,487 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectMaps
2025-07-28 16:57:00,488 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectMapsPage
2025-07-28 16:57:00,489 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectObjs
2025-07-28 16:57:00,490 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectList
2025-07-28 16:57:00,491 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper.selectPage
2025-07-28 16:57:00,498 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.queryScheme
2025-07-28 16:57:00,498 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.insertScheme
2025-07-28 16:57:00,498 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.updateScheme
2025-07-28 16:57:00,498 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.delScheme
2025-07-28 16:57:00,501 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.insert
2025-07-28 16:57:00,502 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.delete
2025-07-28 16:57:00,503 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.deleteByMap
2025-07-28 16:57:00,503 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.deleteById
2025-07-28 16:57:00,504 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.deleteBatchIds
2025-07-28 16:57:00,505 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.update
2025-07-28 16:57:00,506 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.updateById
2025-07-28 16:57:00,506 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectById
2025-07-28 16:57:00,507 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectBatchIds
2025-07-28 16:57:00,508 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectByMap
2025-07-28 16:57:00,509 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectOne
2025-07-28 16:57:00,510 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectCount
2025-07-28 16:57:00,512 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectMaps
2025-07-28 16:57:00,513 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectMapsPage
2025-07-28 16:57:00,514 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectObjs
2025-07-28 16:57:00,515 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectList
2025-07-28 16:57:00,516 [main] - addMappedStatement: com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper.selectPage
2025-07-28 16:57:00,520 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.XapiHandleServiceMapper.queryData
2025-07-28 16:57:00,521 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.XapiHandleServiceMapper.insertData
2025-07-28 16:57:00,521 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.XapiHandleServiceMapper.delData
2025-07-28 16:57:00,532 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.queryUnAuditList
2025-07-28 16:57:00,533 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.queryAuditRecordList
2025-07-28 16:57:00,533 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.queryAuditInfo
2025-07-28 16:57:00,533 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.insertCommonAudit
2025-07-28 16:57:00,534 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.updateCommonAudit
2025-07-28 16:57:00,538 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.insert
2025-07-28 16:57:00,540 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.delete
2025-07-28 16:57:00,542 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.deleteByMap
2025-07-28 16:57:00,544 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.deleteById
2025-07-28 16:57:00,546 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.deleteBatchIds
2025-07-28 16:57:00,548 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.update
2025-07-28 16:57:00,550 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.updateById
2025-07-28 16:57:00,551 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectById
2025-07-28 16:57:00,553 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectBatchIds
2025-07-28 16:57:00,554 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectByMap
2025-07-28 16:57:00,557 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectOne
2025-07-28 16:57:00,560 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectCount
2025-07-28 16:57:00,562 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectMaps
2025-07-28 16:57:00,564 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectMapsPage
2025-07-28 16:57:00,567 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectObjs
2025-07-28 16:57:00,569 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectList
2025-07-28 16:57:00,572 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditMapper.selectPage
2025-07-28 16:57:00,580 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.insertCommonAuditNodeR
2025-07-28 16:57:00,581 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.updateCommonAuditNodeR
2025-07-28 16:57:00,585 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.insert
2025-07-28 16:57:00,588 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.delete
2025-07-28 16:57:00,591 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.deleteByMap
2025-07-28 16:57:00,592 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.deleteById
2025-07-28 16:57:00,594 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.deleteBatchIds
2025-07-28 16:57:00,596 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.update
2025-07-28 16:57:00,598 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.updateById
2025-07-28 16:57:00,598 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectById
2025-07-28 16:57:00,599 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectBatchIds
2025-07-28 16:57:00,601 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectByMap
2025-07-28 16:57:00,603 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectOne
2025-07-28 16:57:00,605 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectCount
2025-07-28 16:57:00,607 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectMaps
2025-07-28 16:57:00,609 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectMapsPage
2025-07-28 16:57:00,611 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectObjs
2025-07-28 16:57:00,613 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectList
2025-07-28 16:57:00,614 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.CommonAuditNodeRMapper.selectPage
2025-07-28 16:57:00,621 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.insertSacFieldMappingConfig
2025-07-28 16:57:00,621 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.updateSacFieldMappingConfig
2025-07-28 16:57:00,621 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.fileMappingList
2025-07-28 16:57:00,624 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.insert
2025-07-28 16:57:00,626 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.delete
2025-07-28 16:57:00,627 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.deleteByMap
2025-07-28 16:57:00,628 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.deleteById
2025-07-28 16:57:00,630 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.deleteBatchIds
2025-07-28 16:57:00,631 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.update
2025-07-28 16:57:00,633 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.updateById
2025-07-28 16:57:00,633 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectById
2025-07-28 16:57:00,635 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectBatchIds
2025-07-28 16:57:00,636 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectByMap
2025-07-28 16:57:00,638 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectOne
2025-07-28 16:57:00,639 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectCount
2025-07-28 16:57:00,641 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectMaps
2025-07-28 16:57:00,642 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectMapsPage
2025-07-28 16:57:00,643 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectObjs
2025-07-28 16:57:00,644 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectList
2025-07-28 16:57:00,645 [main] - addMappedStatement: com.ly.mp.assembly.approve.idal.mapper.FieldMappingConfigMapper.selectPage
2025-07-28 16:57:04,172 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.insert
2025-07-28 16:57:04,174 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.delete
2025-07-28 16:57:04,175 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.deleteByMap
2025-07-28 16:57:04,176 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.deleteById
2025-07-28 16:57:04,176 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.deleteBatchIds
2025-07-28 16:57:04,177 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.update
2025-07-28 16:57:04,178 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.updateById
2025-07-28 16:57:04,178 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectById
2025-07-28 16:57:04,179 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectBatchIds
2025-07-28 16:57:04,180 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectByMap
2025-07-28 16:57:04,181 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectOne
2025-07-28 16:57:04,182 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectCount
2025-07-28 16:57:04,183 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectMaps
2025-07-28 16:57:04,184 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectMapsPage
2025-07-28 16:57:04,185 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectObjs
2025-07-28 16:57:04,186 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectList
2025-07-28 16:57:04,187 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOneCustRemarkMapper.selectPage
2025-07-28 16:57:04,288 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.insert
2025-07-28 16:57:04,290 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.delete
2025-07-28 16:57:04,291 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.deleteByMap
2025-07-28 16:57:04,292 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.deleteById
2025-07-28 16:57:04,293 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.deleteBatchIds
2025-07-28 16:57:04,295 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.update
2025-07-28 16:57:04,298 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.updateById
2025-07-28 16:57:04,298 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectById
2025-07-28 16:57:04,299 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectBatchIds
2025-07-28 16:57:04,300 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectByMap
2025-07-28 16:57:04,302 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectOne
2025-07-28 16:57:04,304 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectCount
2025-07-28 16:57:04,305 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectMaps
2025-07-28 16:57:04,307 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectMapsPage
2025-07-28 16:57:04,309 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectObjs
2025-07-28 16:57:04,310 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectList
2025-07-28 16:57:04,312 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacClueInfoDlrHisMapper.selectPage
2025-07-28 16:57:04,355 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.insert
2025-07-28 16:57:04,356 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.delete
2025-07-28 16:57:04,357 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.deleteByMap
2025-07-28 16:57:04,358 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.deleteById
2025-07-28 16:57:04,359 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.deleteBatchIds
2025-07-28 16:57:04,360 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.update
2025-07-28 16:57:04,362 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.updateById
2025-07-28 16:57:04,362 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectById
2025-07-28 16:57:04,363 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectBatchIds
2025-07-28 16:57:04,363 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectByMap
2025-07-28 16:57:04,365 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectOne
2025-07-28 16:57:04,366 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectCount
2025-07-28 16:57:04,367 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectMaps
2025-07-28 16:57:04,368 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectMapsPage
2025-07-28 16:57:04,370 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectObjs
2025-07-28 16:57:04,371 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectList
2025-07-28 16:57:04,372 [main] - addMappedStatement: com.ly.mp.csc.clue.mapper.SacOnecustInfoSelfMapper.selectPage
2025-07-28 16:57:05,278 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.insert
2025-07-28 16:57:05,279 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.delete
2025-07-28 16:57:05,280 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.deleteByMap
2025-07-28 16:57:05,281 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.deleteById
2025-07-28 16:57:05,282 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.deleteBatchIds
2025-07-28 16:57:05,284 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.update
2025-07-28 16:57:05,284 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.updateById
2025-07-28 16:57:05,285 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectById
2025-07-28 16:57:05,285 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectBatchIds
2025-07-28 16:57:05,286 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectByMap
2025-07-28 16:57:05,287 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectOne
2025-07-28 16:57:05,288 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectCount
2025-07-28 16:57:05,289 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectMaps
2025-07-28 16:57:05,290 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectMapsPage
2025-07-28 16:57:05,291 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectObjs
2025-07-28 16:57:05,292 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectList
2025-07-28 16:57:05,293 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SwitchMapper.selectPage
2025-07-28 16:57:08,967 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.insert
2025-07-28 16:57:08,968 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.delete
2025-07-28 16:57:08,969 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.deleteByMap
2025-07-28 16:57:08,970 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.deleteById
2025-07-28 16:57:08,971 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.deleteBatchIds
2025-07-28 16:57:08,972 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.update
2025-07-28 16:57:08,973 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.updateById
2025-07-28 16:57:08,973 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectById
2025-07-28 16:57:08,974 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectBatchIds
2025-07-28 16:57:08,975 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectByMap
2025-07-28 16:57:08,976 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectOne
2025-07-28 16:57:08,977 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectCount
2025-07-28 16:57:08,978 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectMaps
2025-07-28 16:57:08,979 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectMapsPage
2025-07-28 16:57:08,980 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectObjs
2025-07-28 16:57:08,981 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectList
2025-07-28 16:57:08,984 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.MdsLookupValueSelfMapper.selectPage
2025-07-28 16:57:13,754 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.insert
2025-07-28 16:57:13,756 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.delete
2025-07-28 16:57:13,757 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.deleteByMap
2025-07-28 16:57:13,758 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.deleteById
2025-07-28 16:57:13,759 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.deleteBatchIds
2025-07-28 16:57:13,762 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.update
2025-07-28 16:57:13,763 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.updateById
2025-07-28 16:57:13,763 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectById
2025-07-28 16:57:13,764 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectBatchIds
2025-07-28 16:57:13,765 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectByMap
2025-07-28 16:57:13,766 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectOne
2025-07-28 16:57:13,768 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectCount
2025-07-28 16:57:13,770 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectMaps
2025-07-28 16:57:13,773 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectMapsPage
2025-07-28 16:57:13,775 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectObjs
2025-07-28 16:57:13,777 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectList
2025-07-28 16:57:13,779 [main] - addMappedStatement: com.ly.mp.csc.clue.idal.mapper.SacPcClueInfoDlrMapper.selectPage
