#运行gradle时使用的内存
org.gradle.jvmargs=-Xms1024m 
#Nexus Repository地址
repositoryUrl=http://172.26.157.113:8081
repositoryUrlRelease=https://devrepo.devcloud.cn-east-3.huaweicloud.com/04/nexus/content/repositories/0aeb4a9baf00f3e70f6bc018ff034740_1_0/
repositoryUrlSnapshot=https://devrepo.devcloud.cn-east-3.huaweicloud.com/04/nexus/content/repositories/0aeb4a9baf00f3e70f6bc018ff034740_2_0/
#repositoryUrl=http://maven.gacnio-inc.com
#Nexus Repository用户,上传时需要
#repositoryUsername=mpdev
repositoryUsername=0aeb4a9baf00f3e70f6bc018ff034740_105b2f04ebdd40d19938bdc34d486cc8
#Nexus Repository密码,上传时需要
#repositoryPassword=mpdev
repositoryPassword=r2I^[Xp35e
#项目版本号:快照版本需要加snapshot,如'2.12-SNAPSHOT';RELEASE版本直接使用版本号,如'2.12'
#SNAPSHOT版本上传的SNAPSHOT仓库,RELEASE版本上传到RELEASE仓库
versionId=1.0-RELEASE
#项目组升级mp平台时，直接修改mp的版本号即可
mpversionId=2.42-008-20220413
#项目组名,为了避免重名,常用使用网址名倒序做为组名,如com.szlanyou.mp
groupId=com.szlanyou.adp
#项目组公共项目名称,一般项目的属性配置文件都在这项目内
commonProName=ly.adp.common

org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching = true