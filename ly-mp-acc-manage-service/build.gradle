apply from: "$rootDir/gradle/docker.gradle"
ext{
    bucnComVersion = "0.2.5-SNAPSHOT"
    bucnComVersion1 = "1.0-SNAPSHOT" 
}
dependencies {

	compile project(':ly.adp.common')
	compile project(':ly.bucn.xrule')
	compile project(':ly.adp.csc.protocol')

	compile project(':ly-mp-acc-manage-otherservice-interface')	
	compile fileTree(dir:"$rootDir/extjar/bucn/bucnpack", include: ['*.jar'])
	compile fileTree(dir: "$rootDir/extjar/bucn/rpc", include: ['*.jar'])
}

if (!project.hasProperty('upload')){
	apply from: "$rootDir/gradle/springboot.gradle"
}
