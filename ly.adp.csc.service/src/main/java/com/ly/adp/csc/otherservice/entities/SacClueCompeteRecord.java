package com.ly.adp.csc.otherservice.entities;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 线索竞争获取记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@TableName("t_sac_clue_compete_record")
@ApiModel(value = "SacClueCompeteRecord对象", description = "线索竞争获取记录表")
public class SacClueCompeteRecord {

	@ApiModelProperty("记录主键ID")
	private String recordId;

	@ApiModelProperty("经销商ID")
	private String dlrId;

	@ApiModelProperty("经销商编码")
	private String dlrCode;

	@ApiModelProperty("经销商名称")
	private String dlrShortName;

	@ApiModelProperty("获取线索次数，最大为10")
	private Integer getClueTimes;

	@ApiModelProperty("线索获取开关状态, 1-开启, 0-关闭")
	private Integer getSwitch;

	@ApiModelProperty("扩展字段1")
	private String column1;

	@ApiModelProperty("扩展字段2")
	private String column2;

	@ApiModelProperty("扩展字段3")
	private String column3;

	@ApiModelProperty("扩展字段4")
	private String column4;

	@ApiModelProperty("扩展字段5")
	private String column5;

	@ApiModelProperty("扩展字段6")
	private String column6;

	@ApiModelProperty("扩展字段7")
	private String column7;

	@ApiModelProperty("扩展字段8")
	private String column8;

	@ApiModelProperty("扩展字段9")
	private String column9;

	@ApiModelProperty("扩展字段10")
	private String column10;

	@ApiModelProperty("JSON扩展字段")
	private String extendsJson;

	@ApiModelProperty("厂商标识ID")
	private String oemId;

	@ApiModelProperty("集团标识ID")
	private String groupId;

	@ApiModelProperty("创建人ID")
	private String creator;

	@ApiModelProperty("创建人")
	private String createdName;

	@ApiModelProperty("创建日期")
	private LocalDateTime createdDate;

	@ApiModelProperty("修改人ID")
	private String modifier;

	@ApiModelProperty("修改人")
	private String modifyName;

	@ApiModelProperty("最后更新日期")
	private LocalDateTime lastUpdatedDate;

	@ApiModelProperty("是否可用")
	private String isEnable;

	@ApiModelProperty("并发控制ID")
	private String updateControlId;

	public String getRecordId() {
		return recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getDlrId() {
		return dlrId;
	}

	public void setDlrId(String dlrId) {
		this.dlrId = dlrId;
	}

	public String getDlrCode() {
		return dlrCode;
	}

	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}

	public String getDlrShortName() {
		return dlrShortName;
	}

	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}

	public Integer getGetClueTimes() {
		return getClueTimes;
	}

	public void setGetClueTimes(Integer getClueTimes) {
		this.getClueTimes = getClueTimes;
	}

	public Integer getSwitch() {
		return getSwitch;
	}

	public void setSwitch(Integer getSwitch) {
		this.getSwitch = getSwitch;
	}

	public String getColumn1() {
		return column1;
	}

	public void setColumn1(String column1) {
		this.column1 = column1;
	}

	public String getColumn2() {
		return column2;
	}

	public void setColumn2(String column2) {
		this.column2 = column2;
	}

	public String getColumn3() {
		return column3;
	}

	public void setColumn3(String column3) {
		this.column3 = column3;
	}

	public String getColumn4() {
		return column4;
	}

	public void setColumn4(String column4) {
		this.column4 = column4;
	}

	public String getColumn5() {
		return column5;
	}

	public void setColumn5(String column5) {
		this.column5 = column5;
	}

	public String getColumn6() {
		return column6;
	}

	public void setColumn6(String column6) {
		this.column6 = column6;
	}

	public String getColumn7() {
		return column7;
	}

	public void setColumn7(String column7) {
		this.column7 = column7;
	}

	public String getColumn8() {
		return column8;
	}

	public void setColumn8(String column8) {
		this.column8 = column8;
	}

	public String getColumn9() {
		return column9;
	}

	public void setColumn9(String column9) {
		this.column9 = column9;
	}

	public String getColumn10() {
		return column10;
	}

	public void setColumn10(String column10) {
		this.column10 = column10;
	}

	public String getExtendsJson() {
		return extendsJson;
	}

	public void setExtendsJson(String extendsJson) {
		this.extendsJson = extendsJson;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	@Override
	public String toString() {
		return "SacClueCompeteRecord{" + "recordId=" + recordId + ", dlrId=" + dlrId + ", dlrCode=" + dlrCode
				+ ", dlrShortName=" + dlrShortName + ", getClueTimes=" + getClueTimes + ", switch=" + getSwitch
				+ ", column1=" + column1 + ", column2=" + column2 + ", column3=" + column3 + ", column4=" + column4
				+ ", column5=" + column5 + ", column6=" + column6 + ", column7=" + column7 + ", column8=" + column8
				+ ", column9=" + column9 + ", column10=" + column10 + ", extendsJson=" + extendsJson + ", oemId="
				+ oemId + ", groupId=" + groupId + ", creator=" + creator + ", createdName=" + createdName
				+ ", createdDate=" + createdDate + ", modifier=" + modifier + ", modifyName=" + modifyName
				+ ", lastUpdatedDate=" + lastUpdatedDate + ", isEnable=" + isEnable + ", updateControlId="
				+ updateControlId + "}";
	}
}
