package com.ly.adp.csc.util;

import com.ly.adp.csc.entities.enums.CommonErrEnum;
import com.ly.mp.busicen.common.util.EntityResultBuilder;
import com.ly.mp.busicen.common.util.OptResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.OptResult;

import java.util.regex.Pattern;

/**
 * 通用结果返回工具类
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/
public class CommonResultUtil {
    /**
     * 返回一个自定义异常内容的Opt
     * @param errMsg
     * @return
     */

    public static OptResult bizErrOpt(String errMsg) {
        return OptResultBuilder
                .create()
                .result(CommonErrEnum.BIZ_ERR.getCode())
                .Msg(errMsg)
                .build();
    }
    public  static EntityResult bizErrEntity(String errMsg){
        return EntityResultBuilder
                .create()
                .result(CommonErrEnum.BIZ_ERR.getCode())
                .msg(errMsg).build();
    }


}
