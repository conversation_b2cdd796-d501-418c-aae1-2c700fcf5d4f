package com.ly.adp.csc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnetaskLeadInTemp;
import com.ly.adp.csc.entities.req.CommonSaveTaskReq;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <p>
 * 任务导入临时表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
public interface ISacOnetaskLeadInTempService extends IService<SacOnetaskLeadInTemp> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskLeadInTempFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskLeadInTempSaveInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 任务人员导入
	 * @param info
	 * @return
	 */
	Result taskInfoImport(String authentication,String taskTypeCode, String taskType, String taskId, MultipartFile uploadfile);
	Result taskInfoImport1(String authentication,String taskTypeCode, String taskType, String taskId, MultipartFile uploadfile);

	/**
	 * 任务人员导入定时调用（接口表到业务表）
	 * @param info
	 * @return
	 */
	void taskInfoDbjob();
	
	/**
	 * 任务发送消息定时调用
	 * @param info
	 * @return
	 */
	void taskMsgDbjob();

	EntityResult commonSaveTask(String authentication, CommonSaveTaskReq req);
}
