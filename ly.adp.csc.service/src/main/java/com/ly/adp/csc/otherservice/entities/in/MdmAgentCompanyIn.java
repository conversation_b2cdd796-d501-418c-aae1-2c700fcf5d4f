package com.ly.adp.csc.otherservice.entities.in;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.adp.csc.entities.in.PageInfo;
import com.ly.adp.csc.otherservice.entities.TUscMdmStockHolder;

import io.swagger.annotations.ApiModelProperty;

public class MdmAgentCompanyIn extends PageInfo implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public String getBusinessLicense() {
		return businessLicense;
	}

	public void setBusinessLicense(String businessLicense) {
		this.businessLicense = businessLicense;
	}

	@ApiModelProperty(value = "营业执照",required = false)
	private String businessLicense;

	public String getShareholderResolution() {
		return shareholderResolution;
	}

	public void setShareholderResolution(String shareholderResolution) {
		this.shareholderResolution = shareholderResolution;
	}

	public List<TUscMdmStockHolder> getStockHolderList() {
		return stockHolderList;
	}

	public void setStockHolderList(List<TUscMdmStockHolder> stockHolderList) {
		this.stockHolderList = stockHolderList;
	}

	@ApiModelProperty(value = "股东协议",required = false)
	private String shareholderResolution;

	@ApiModelProperty(value = "股东协议",required = false)
	private List<TUscMdmStockHolder> stockHolderList;



	@ApiModelProperty(value = "状态", required=false)
    private String isEnable;
	
	@ApiModelProperty(value = "开始时间", required=false)
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdStartDate;
	
	@ApiModelProperty(value = "结束时间", required=false)
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdEndtDate;
	
    @ApiModelProperty(value = "代理商城市公司ID", required=false)
    private String agentCompanyId;

    @ApiModelProperty(value = "代理商ID", required=false)
    private String agentId;
    
    @ApiModelProperty(value = "代理商名称", required=false)
    private String agentName;

    @ApiModelProperty(value = "城市公司编码", required=false)
    private String agentCompanyCode;
    
    @ApiModelProperty(value = "城市公司名称", required=false)
    private String agentCompanyName;

    @ApiModelProperty(value = "建店协议", required=false)
    private String dlrBuildAgreementUrl;
    
    @ApiModelProperty(value = "建店协议开始时间", required=false)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compBeginDate;
    
    @ApiModelProperty(value = "建店协议结束时间", required=false)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compEndDate;

    @ApiModelProperty(value = "代理公司区域", required=false)
    private String agentCompanyArea;
    
    @ApiModelProperty(value = "省份ID", required=false)
    private String provinceId;
    
    @ApiModelProperty(value = "城市ID", required=false)
    private String cityId;
    
    @ApiModelProperty(value = "区县ID", required=false)
    private String countyId;
    
    @ApiModelProperty(value = "附件", required=false)
    private String attachUrl;


    
	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public LocalDateTime getCreatedStartDate() {
		return createdStartDate;
	}

	public void setCreatedStartDate(LocalDateTime createdStartDate) {
		this.createdStartDate = createdStartDate;
	}

	public LocalDateTime getCreatedEndtDate() {
		return createdEndtDate;
	}

	public void setCreatedEndtDate(LocalDateTime createdEndtDate) {
		this.createdEndtDate = createdEndtDate;
	}

	public String getAgentCompanyId() {
		return agentCompanyId;
	}

	public void setAgentCompanyId(String agentCompanyId) {
		this.agentCompanyId = agentCompanyId;
	}

	public String getAgentId() {
		return agentId;
	}

	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentCompanyCode() {
		return agentCompanyCode;
	}

	public void setAgentCompanyCode(String agentCompanyCode) {
		this.agentCompanyCode = agentCompanyCode;
	}

	public String getAgentCompanyName() {
		return agentCompanyName;
	}

	public void setAgentCompanyName(String agentCompanyName) {
		this.agentCompanyName = agentCompanyName;
	}

	public String getDlrBuildAgreementUrl() {
		return dlrBuildAgreementUrl;
	}

	public void setDlrBuildAgreementUrl(String dlrBuildAgreementUrl) {
		this.dlrBuildAgreementUrl = dlrBuildAgreementUrl;
	}

	public LocalDateTime getCompBeginDate() {
		return compBeginDate;
	}

	public void setCompBeginDate(LocalDateTime compBeginDate) {
		this.compBeginDate = compBeginDate;
	}

	public LocalDateTime getCompEndDate() {
		return compEndDate;
	}

	public void setCompEndDate(LocalDateTime compEndDate) {
		this.compEndDate = compEndDate;
	}

	public String getAgentCompanyArea() {
		return agentCompanyArea;
	}

	public void setAgentCompanyArea(String agentCompanyArea) {
		this.agentCompanyArea = agentCompanyArea;
	}

	public String getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}

	public String getCountyId() {
		return countyId;
	}

	public void setCountyId(String countyId) {
		this.countyId = countyId;
	}

	public String getAttachUrl() {
		return attachUrl;
	}

	public void setAttachUrl(String attachUrl) {
		this.attachUrl = attachUrl;
	}
	
	
	
}
