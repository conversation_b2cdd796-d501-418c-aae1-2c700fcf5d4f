package com.ly.adp.csc.controller;


import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.impl.UserQuerySchemeBiz;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 用户查询 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@RestController
@RequestMapping("/ly/adp/csc/userQueryScheme")
@Api(value = "查询方案",tags = {"查询方案"})
public class UserQuerySchemeController {
    @Resource
    UserQuerySchemeBiz userQuerySchemeBiz;
    @ApiOperation(value = "查询方案", notes = "查询方案")
    @PostMapping(value = "/queryScheme.do")
    public ListResult<Map<String, Object>> queryScheme(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
    @RequestBody(required = false) Map map ){
        return BusicenInvoker.doList(() -> {return userQuerySchemeBiz.query(authentication,map);}).result();
    }

    @ApiOperation(value = "方案管理", notes = "方案管理")
    @PostMapping(value = "/managerScheme.do")
    public OptResult managerScheme(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                    @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return userQuerySchemeBiz.updateScheme(dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "方案删除", notes = "方案删除")
    @PostMapping(value = "/delScheme.do")
    public OptResult delScheme(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                   @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return userQuerySchemeBiz.delScheme(dateInfo, authentication);}).result();
    }

    @PostMapping(value = "/script.do")
    public OptResult script(@RequestBody(required = false) String sql) {
       return userQuerySchemeBiz.script(sql);
    }
}

