package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacCityCounter;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 城市计数 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface ISacCityCounterService extends IService<SacCityCounter> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacCityCounterFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacCityCounterSaveInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacCityCounterGeneral(Map<String, Object> dataInfo,String token);
}
