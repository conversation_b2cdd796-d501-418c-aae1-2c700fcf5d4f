package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.entities.qry.CommonAuditQry;
import com.ly.adp.csc.entities.vo.CommonAuditVO;
import com.ly.adp.csc.service.ICommonAuditExtendService;
import com.ly.mp.component.entities.ListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(value = "公共审批表-扩展控制器", tags = "公共审批表-扩展控制器")
@RestController
@RequestMapping(value = "/ly/adp/csc/commonAudit", produces = { MediaType.APPLICATION_JSON_VALUE })
public class CommonAuditExtendController {

	@Autowired
	private ICommonAuditExtendService commonAuditExtendService;

	@ApiOperation(value = "查询公共审批表记录-扩展使用", notes = "查询公共审批表记录-扩展使用")
	@PostMapping(value = "/queryCommonAudit.do")
	public ListResult<CommonAuditVO> queryCommonAudit(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
														 @RequestBody(required = false) ParamPage<CommonAuditQry> paramPage) {
		return commonAuditExtendService.queryCommonAudit(paramPage, authentication);
	}
}
