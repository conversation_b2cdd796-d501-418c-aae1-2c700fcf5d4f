package com.ly.adp.csc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnetaskInfo;
import com.ly.adp.csc.entities.req.SendFinishTaskMqReq;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.Map;

/**
 * <p>
 * 任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
public interface ISacOnetaskInfoService extends IService<SacOnetaskInfo> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskInfoFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskInfoSaveInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断更新任务详情状态
	 * @param info
	 * @return
	 */
	OptResult sacOnetaskFinishUpdate(Map<String, Object> dataInfo, String token);
	
	/**
	 * 任务重复执行定时任务 
	 * @param info
	 * @return
	 */
	void cycleExecuteTask();
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskDriveUse(Map<String, Object> dataInfo,String token);
	
	void taskPublishJob();
	
	/**
	 * 任务消息查询
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskInfoMsg(Map<String, Object> dataInfo,String token);
	/**
	 * 任务消息查询，优化接口性能
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskInfoMsg_performance(Map<String, Object> dataInfo,String token);
	/**
	 * 任务消息查询，优化接口性能
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskInfoMsgNew(Map<String, Object> dataInfo,String token);

    ListResult<Map<String, Object>> selectProductNameDlr(Map<String, Object> dateInfo, String authentication);

    void sendFinishTaskMq(SendFinishTaskMqReq sendFinishTaskMqReq);

	void sendFinishTaskMqJob();
}
