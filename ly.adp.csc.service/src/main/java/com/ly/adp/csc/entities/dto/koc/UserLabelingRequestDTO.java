package com.ly.adp.csc.entities.dto.koc;

import java.io.Serializable;
import java.util.List;

/**
 * 用户打标请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class UserLabelingRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String smartId;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 达人类型ID列表
     */
    private List<String> expertTypeIds;

    /**
     * 备注列表
     */
    private List<String> remarks;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public List<String> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<String> tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getExpertTypeIds() {
        return expertTypeIds;
    }

    public void setExpertTypeIds(List<String> expertTypeIds) {
        this.expertTypeIds = expertTypeIds;
    }

    public List<String> getRemarks() {
        return remarks;
    }

    public void setRemarks(List<String> remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "UserLabelingRequestDTO{" +
                "smartId='" + smartId + '\'' +
                ", tagIds=" + tagIds +
                ", expertTypeIds=" + expertTypeIds +
                ", remarks=" + remarks +
                '}';
    }
}
