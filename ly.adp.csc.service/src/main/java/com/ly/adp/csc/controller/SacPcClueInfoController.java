package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.qry.GetClueInfoQry;
import com.ly.adp.csc.entities.vo.ClueDetailCustomInfoVO;
import com.ly.adp.csc.entities.vo.ClueInfoVO;
import com.ly.adp.csc.service.ISacPcDlrClueInfoService;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busi.base.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <p>
 * CSC客制化PC线索服务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@RestController
@Api(value = "CSC客制化PC线索服务", tags = "CSC客制化PC线索服务")
@RequestMapping(value = "/ly/adp/csc/sacPcClueInfo", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacPcClueInfoController {
    @Autowired
    ISacPcDlrClueInfoService sacPcClueInfoService;

    @ApiOperation(value = "PC门店全部回访单查询", notes = "PC门店全部回访单查询")
    @RequestMapping(value = "/entireReviewInfo.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> entireReviewInfo(@RequestHeader(name = "authorization", required = false) String token, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.entireReviewInfo(dataInfo, token)).result();
    }

    @ApiOperation(value = "PC门店全部线索单查询", notes = "PC门店全部线索单查询")
    @RequestMapping(value = "/entireDlrClueInfo.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> entireDlrClueInfo(@RequestHeader(name = "authorization", required = false) String token, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.entireDlrClueInfo(dataInfo, token)).result();
    }
    @ApiOperation(value = "PC门店全部线索单查询", notes = "PC门店全部线索单查询")
    @RequestMapping(value = "/entireDlrClueInfoNew.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> entireDlrClueInfoNew(@RequestHeader(name = "authorization", required = false) String token, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.entireDlrClueInfoNew(dataInfo, token)).result();
    }
    @ApiOperation(value = "手机号导入", notes = "手机号导入")
    @RequestMapping(value = "importPhone.do", method = RequestMethod.POST)
    @ApiImplicitParam(name = "excel", value = "Excel文件", dataType = "__file", paramType = "body", required = true)
    public EntityResult<String> importPhone(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                            HttpServletRequest request)  {
        return BusicenInvoker.doEntity(() -> sacPcClueInfoService.importPhone(request, authentication)).result();
    }

    @ApiOperation(value = "PC门店全部线索查询导出", notes = "PC门店全部线索查询导出")
    @PostMapping(value = "/entireDlrClueInfoExport.do")
    public void entireDlrClueInfoExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        sacPcClueInfoService.entireDlrClueInfoExport(dataInfo, token, response);
    }

    @ApiOperation(value = "PC门店战败线索查询导出", notes = "PC门店战败线索查询导出")
    @PostMapping(value = "/defeatEntireDlrClueInfoExport.do")
    public void defeatEntireDlrClueInfoExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        sacPcClueInfoService.defeatEntireDlrClueInfoExport(dataInfo, token, response);
    }

    @ApiOperation(value = "评价报表门店回复率", notes = "此路径为了防止修改后第三方调用该接口用不了 2024-08-28")
    @RequestMapping(value = "/getEvaluationDetails.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> getEvaluationDetails(@RequestHeader(name = "authorization", required = false) String token, @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.getEvaluatedPercent(dataInfo)).result();
    }

    @ApiOperation(value = "评价报表门店回复率", notes = "评价报表门店回复率")
    @RequestMapping(value = "/getEvaluatedPercent.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> getEvaluatedPercent(@RequestHeader(name = "authorization", required = false) String token, @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.getEvaluatedPercent(dataInfo)).result();
    }

    @ApiOperation(value = "产品专家查询", notes = "产品专家查询")
    @RequestMapping(value = "/findEmployee.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> findEmployee(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacPcClueInfoService.findEmployee(token, dataInfo)).result();
    }

    @ApiOperation(value = "获取线索信息", notes = "获取线索信息")
    @RequestMapping(value = "/getClueInfo.do", method = RequestMethod.POST)
    public EntityResult<ClueInfoVO> getClueInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token
            , @RequestBody(required = false) GetClueInfoQry qry) {
        return sacPcClueInfoService.getClueInfo(token, qry);
    }

    @ApiOperation(value = "试驾单获取线索基本信息", notes = "试驾单获取线索基本信息")
    @RequestMapping(value = "/getClueDetailCustomInfo.do", method = RequestMethod.POST)
    public EntityResult<ClueDetailCustomInfoVO> getClueDetailCustomInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token
            , @RequestBody(required = false) GetClueInfoQry qry) {
        return sacPcClueInfoService.getClueDetailAndCustomInfo(qry);
    }
}

