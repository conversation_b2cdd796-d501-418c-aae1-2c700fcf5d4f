package com.ly.adp.csc.controller;

import com.ly.adp.csc.entities.MaturityVO;
import com.ly.adp.csc.otherservice.entities.PurchaseIntentionDto;
import com.ly.adp.csc.otherservice.entities.PurchaseIntentionVO;
import com.ly.adp.csc.service.IPurchaseIntentionService;
import com.ly.mp.component.entities.ListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @description: 购买意向
 * @date 2023/4/20
 */
@Api(value = "购买意向", tags = "购买意向")
@RestController
@RequestMapping(value = "/ly/adp/csc/purchaseIntention", produces = {MediaType.APPLICATION_JSON_VALUE})
public class PurchaseIntentionController {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseIntentionController.class);
    final IPurchaseIntentionService purchaseIntentionBiz;

    public PurchaseIntentionController(IPurchaseIntentionService purchaseIntentionBiz) {
        this.purchaseIntentionBiz = purchaseIntentionBiz;
    }


    @ApiOperation(value = "购买意向热度指数", notes = "购买意向热度指数")
    @PostMapping(value = "/purchaseIntentionQuery.do")
    public ListResult<PurchaseIntentionVO> purchaseIntentionQuery(@RequestBody PurchaseIntentionDto purchaseIntention) {
        return purchaseIntentionBiz.purchaseIntentionQuery(purchaseIntention);
    }

    @ApiOperation(value = "用户成熟度", notes = "用户成熟度")
    @PostMapping(value = "/queryMaturity.do")
    public ListResult<MaturityVO> queryMaturity(@RequestBody PurchaseIntentionDto purchaseIntention) {
        return purchaseIntentionBiz.queryMaturity(purchaseIntention);
    }
}
