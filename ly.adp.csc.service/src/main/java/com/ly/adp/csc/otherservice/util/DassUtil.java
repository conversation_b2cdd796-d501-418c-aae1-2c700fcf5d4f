package com.ly.adp.csc.otherservice.util;

import com.cloud.apigateway.sdk.utils.Client;
import com.cloud.apigateway.sdk.utils.Request;
import com.ly.adp.csc.otherservice.entities.PurchaseIntentionDto;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 大数据意向车型util
 * @date 2023/4/20
 */
public class DassUtil {

    public static HttpResponse postPurchaseIntention(PurchaseIntentionDto purchaseIntention, Map<String, String> collect) {
        //Create a new request.
        Request request = new Request();
        try {
            //Set the request parameters.
            //AppKey, AppSecrect, Method and Url are required parameters.
            request.setKey(collect.get("ak"));
            request.setSecret(collect.get("sk"));
            request.setMethod("POST");
            request.setUrl(collect.get("url"));
            request.addHeader("Content-Type", "text/plain");
            //if it was published in other envs(except for Release),you need to add the information x-stage and the value is env's name
            request.addQueryStringParam("dt", purchaseIntention.getDt());
            request.addQueryStringParam("mobile_no", purchaseIntention.getMobile_no());
            //   request.setBody("{\"dt\":\"2023-04-20 12:11:11\",\"mobile_no\":\"15871016912\"}");
        } catch (Exception e) {
            e.printStackTrace();

        }

        CloseableHttpClient client = null;
        HttpResponse response = null;
        try {
            //Sign the request.
            HttpRequestBase signedRequest = Client.sign(request);
            signedRequest.releaseConnection();
            Header[] authorization = signedRequest.getHeaders("Authorization");
            signedRequest.addHeader("x-Authorization", authorization[0].getValue());

            // client = HttpClients.custom().build();
            //Send the request.
            client = HttpClients.custom().build();
            response = client.execute(signedRequest);

         /*   //Print the status line of the response.
            System.out.println(response.getStatusLine().toString());

            //Print the header fields of the response.
            Header[] resHeaders = response.getAllHeaders();
            for (Header h : resHeaders) {
                System.out.println(h.getName() + ":" + h.getValue());
            }
*/
            //Print the body of the response.


                System.out.println(EntityUtils.toString(response.getEntity(), "UTF-8"));

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (client != null) {
                    client.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return response;
    }
}
