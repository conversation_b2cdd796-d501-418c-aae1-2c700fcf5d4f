package com.ly.adp.csc.gateway;

import com.ly.mp.csc.clue.entities.copyEntity.MdmOrgEmployee;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Version 1.0.0
 **/
public interface SacEmployeeDimissionClueGateway {

    /**
     * 查询前一天离职审核通过的门店员工
     *
     * @return 离职门店员工信息
     */
    List<MdmOrgEmployee> queryYesterdayDimissionEmployee();

    /**
     * 查询各门店的店长或代理店长
     * @param dealerCodes 门店编码集合
     * @return 门店编码 -> 店长信息的映射
     */
    Map<String, MdmOrgEmployee> querySMInfo(Set<String> dealerCodes);

    /**
     * 处理所有离职员工的线索，批量更新
     * @param allEmployeeIds 离职员工id
     * @param dlrManagerMap 门店编码 -> 店长信息的映射
     */
    void processEmployeeCluesByBatch(List<String> allEmployeeIds, Map<String, MdmOrgEmployee> dlrManagerMap);
}
