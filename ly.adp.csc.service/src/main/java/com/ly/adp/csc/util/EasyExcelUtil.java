package com.ly.adp.csc.util;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Locale;

/**
 * @Author: e-xiao.zhang1
 * @CreateTime: 2024/9/27 18:13
 * @Description: easyExcel工具类
 * @Version: 1.0
 */
public class EasyExcelUtil {

    public static final String CONTENT_TYPE = "application/vnd.ms-excel";

    public static final String ENCODING = "utf-8";

    public static final String HEADER_KEY = "Content-disposition";

    public static final String HEADER_VALUE = "attachment;filename=";

    public static final String SUFFIX = ".xlsx";

    /**
     * easyExcel设置文件编码名字
     * @param response HttpServletResponse
     * @param name 文件名
     * @throws UnsupportedEncodingException
     */
    public static void setFileEncodeAndName(HttpServletResponse response, String name) throws UnsupportedEncodingException {
        //设置编码
        response.setContentType(CONTENT_TYPE);
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(name, ENCODING.toUpperCase(Locale.ROOT));
        response.setHeader(HEADER_KEY, HEADER_VALUE + fileName + SUFFIX);
    }
}
