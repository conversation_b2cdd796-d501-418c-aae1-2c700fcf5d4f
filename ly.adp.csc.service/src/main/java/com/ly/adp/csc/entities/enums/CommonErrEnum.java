package com.ly.adp.csc.entities.enums;

/**
 * csc 异常枚举
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/
public enum CommonErrEnum {
    BIZ_ERR("1600010001", "业务异常，异常信息自定义");

    private final String code;
    private final String errMsg;

    CommonErrEnum(String code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public static CommonErrEnum fromCode(String code) {
        for (CommonErrEnum errEnum : CommonErrEnum.values()) {
            if (errEnum.getCode().equals(code)) {
                return errEnum;
            }
        }
        throw new IllegalArgumentException("Invalid CommonErrEnum code: " + code);
    }

    @Override
    public String toString() {
        return this.errMsg;
    }
}
