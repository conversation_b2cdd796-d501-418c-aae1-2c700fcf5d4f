package com.ly.adp.csc.entities.dto.koc.wechat;

import java.io.Serializable;
import java.util.List;

/**
 * 企微端编辑用户达人类型请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class WechatUpdateUserExpertTypesRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String smartId;

    /**
     * 达人类型ID列表
     */
    private List<String> expertTypeIds;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public List<String> getExpertTypeIds() {
        return expertTypeIds;
    }

    public void setExpertTypeIds(List<String> expertTypeIds) {
        this.expertTypeIds = expertTypeIds;
    }

    @Override
    public String toString() {
        return "WechatUpdateUserExpertTypesRequestDTO{" +
                "smartId='" + smartId + '\'' +
                ", expertTypeIds=" + expertTypeIds +
                '}';
    }
}
