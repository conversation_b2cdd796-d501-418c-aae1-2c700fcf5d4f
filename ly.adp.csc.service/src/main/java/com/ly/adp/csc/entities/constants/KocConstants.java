package com.ly.adp.csc.entities.constants;

/**
 * KOC系统常量类
 * <AUTHOR>
 * @since 2025-07-25
 */
public class KocConstants {

    /**
     * 标签相关常量
     */
    public static class Tag {
        /** 标签最大层级 */
        public static final int MAX_LEVEL = 3;
        
        /** 标签名称最大长度 */
        public static final int MAX_NAME_LENGTH = 30;
        
        /** 标签路径最大长度 */
        public static final int MAX_PATH_LENGTH = 500;
        
        /** 标签状态：启用 */
        public static final int STATUS_ENABLED = 1;
        
        /** 标签状态：禁用 */
        public static final int STATUS_DISABLED = 0;
    }

    /**
     * 达人类型相关常量
     */
    public static class ExpertType {
        /** 达人类型名称最大长度 */
        public static final int MAX_NAME_LENGTH = 30;
        
        /** 达人类型内容最大长度 */
        public static final int MAX_CONTENT_LENGTH = 100;
    }

    /**
     * 用户关系相关常量
     */
    public static class UserRelation {
        /** 关系类型：标签 */
        public static final int REL_TYPE_TAG = 1;
        
        /** 关系类型：达人类型 */
        public static final int REL_TYPE_EXPERT = 2;
        
        /** 关系类型：备注 */
        public static final int REL_TYPE_REMARK = 3;
        
        /** 备注内容最大长度 */
        public static final int MAX_REMARK_LENGTH = 200;
    }

    /**
     * 批量操作相关常量
     */
    public static class Batch {
        /** 批量操作最大用户数 */
        public static final int MAX_USERS = 2000;
        
        /** 文件上传最大大小（字节） */
        public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        
        /** 批量操作超时时间（秒） */
        public static final int TIMEOUT_SECONDS = 300; // 5分钟
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 标签列表缓存Key */
        public static final String TAG_LIST_KEY = "tag:list";
        
        /** 达人类型列表缓存Key */
        public static final String EXPERT_TYPES_KEY = "expert:types";
        
        /** 用户标签缓存Key前缀 */
        public static final String USER_TAGS_PREFIX = "user:tags:";
        
        /** 用户达人类型缓存Key前缀 */
        public static final String USER_EXPERTS_PREFIX = "user:experts:";
        
        /** 用户完整信息缓存Key前缀 */
        public static final String USER_FULL_PREFIX = "user:full:";
        
        /** 标签列表缓存TTL（秒） */
        public static final long TAG_LIST_TTL = 7200L; // 2小时
        
        /** 达人类型列表缓存TTL（秒） */
        public static final long EXPERT_TYPES_TTL = 7200L; // 2小时
        
        /** 用户标签缓存TTL（秒） */
        public static final long USER_TAGS_TTL = 3600L; // 1小时
        
        /** 用户达人类型缓存TTL（秒） */
        public static final long USER_EXPERTS_TTL = 3600L; // 1小时
        
        /** 用户完整信息缓存TTL（秒） */
        public static final long USER_FULL_TTL = 1800L; // 30分钟
    }

    /**
     * 操作类型相关常量
     */
    public static class Operation {
        /** 创建标签 */
        public static final int CREATE_TAG = 1;
        
        /** 编辑标签 */
        public static final int EDIT_TAG = 2;
        
        /** 删除标签 */
        public static final int DELETE_TAG = 3;
        
        /** 下架标签 */
        public static final int DISABLE_TAG = 4;
        
        /** 上架标签 */
        public static final int ENABLE_TAG = 5;
        
        /** 添加达人类型 */
        public static final int ADD_EXPERT_TYPE = 6;
        
        /** 编辑达人类型 */
        public static final int EDIT_EXPERT_TYPE = 7;
        
        /** 删除达人类型 */
        public static final int DELETE_EXPERT_TYPE = 8;
        
        /** 添加标签到用户 */
        public static final int ADD_TAG_TO_USER = 9;
        
        /** 从用户移除标签 */
        public static final int REMOVE_TAG_FROM_USER = 10;
        
        /** 添加达人类别到用户 */
        public static final int ADD_EXPERT_TYPE_TO_USER = 11;
        
        /** 从用户移除达人类别 */
        public static final int REMOVE_EXPERT_TYPE_FROM_USER = 12;
        
        /** 添加备注 */
        public static final int ADD_REMARK = 13;
        
        /** 移除备注 */
        public static final int REMOVE_REMARK = 14;
        
        /** 编辑备注 */
        public static final int EDIT_REMARK = 15;
    }

    /**
     * 错误消息相关常量
     */
    public static class ErrorMessage {
        /** 参数错误 */
        public static final String PARAM_ERROR = "参数错误";
        
        /** 标签名称重复 */
        public static final String TAG_NAME_DUPLICATE = "同级标签名称已存在";
        
        /** 标签已被使用 */
        public static final String TAG_IN_USE = "标签已被使用，无法删除";
        
        /** 达人类型内容重复 */
        public static final String EXPERT_TYPE_DUPLICATE = "达人类型内容已存在";
        
        /** 达人类型已被使用 */
        public static final String EXPERT_TYPE_IN_USE = "达人类型已被使用，无法删除";
        
        /** 用户不存在 */
        public static final String USER_NOT_FOUND = "用户不存在";
        
        /** 批量操作用户数超限 */
        public static final String BATCH_USERS_EXCEED = "批量操作最多支持2000个用户";
        
        /** 文件大小超限 */
        public static final String FILE_SIZE_EXCEED = "文件大小不能超过10MB";
        
        /** UC系统调用失败 */
        public static final String UC_CALL_FAILED = "用户中心系统调用失败";
        
        /** 缓存操作失败 */
        public static final String CACHE_OPERATION_FAILED = "缓存操作失败";
    }
}
