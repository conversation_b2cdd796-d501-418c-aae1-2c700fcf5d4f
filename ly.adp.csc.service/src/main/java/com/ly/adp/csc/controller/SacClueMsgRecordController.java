package com.ly.adp.csc.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.in.SacBasisLogIn;
import com.ly.adp.csc.entities.in.SacClueMsgRecordIn;
import com.ly.adp.csc.service.ISacBasisLogService;
import com.ly.adp.csc.service.ISacClueMsgRecordService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(value = "CSC日志消息服务", tags = "CSC日志消息服务")
@RestController
@RequestMapping(value = "/ly/adp/csc/msgrecord", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacClueMsgRecordController {
    @Autowired
    ISacBasisLogService sacBasisLogService;
    @Autowired
    ISacClueMsgRecordService sacClueMsgRecordService;

    @ApiOperation(value = "消息提醒查询", notes = "消息提醒查询")
    @PostMapping(value = "/msgrecordquery.do")
    public ListResult<Map<String, Object>> sacMsgRecordFindInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) SacClueMsgRecordIn dateInfo) {
        return BusicenInvoker.doList(() -> sacClueMsgRecordService.sacMsgRecordFindInfo(dateInfo)).result();
    }

    @ApiOperation(value = "消息提醒维护", notes = "消息提醒维护")
    @PostMapping(value = "/msgrecordsave.do")
    public OptResult sacMsgRecordSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                          @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> sacClueMsgRecordService.sacMsgRecordSaveInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "消息报表查询", notes = "消息报表查询")
    @PostMapping(value = "/sacMsgRecordReport.do")
    public ListResult<Map<String, Object>> sacMsgRecordReport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacClueMsgRecordService.sacMsgRecordReport(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "消息报表查询todo", notes = "消息报表查询todo")
    @PostMapping(value = "/sacMsgRecordReportTodo.todo")
    public ListResult<Map<String, Object>> sacMsgRecordReportToDo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                               @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacClueMsgRecordService.sacMsgRecordReport(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "店端线索基础日志查询", notes = "店端线索基础日志查询")
    @PostMapping(value = "/basislogquery.do")
    public ListResult<Map<String, Object>> sacBasisLogFindInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) SacBasisLogIn dateInfo) {
        return BusicenInvoker.doList(() -> sacBasisLogService.sacBasisLogFindInfo(dateInfo)).result();
    }

    @ApiOperation(value = "店端线索基础日志维护", notes = "店端线索基础日志维护")
    @PostMapping(value = "/basislogsave.do")
    public OptResult sacBasisLogSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                         @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> sacBasisLogService.sacBasisLogSaveInfo(dateInfo, authentication)).result();
    }
}
