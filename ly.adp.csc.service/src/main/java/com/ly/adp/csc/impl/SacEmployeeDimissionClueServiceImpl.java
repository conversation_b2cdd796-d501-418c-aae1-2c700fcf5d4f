package com.ly.adp.csc.impl;

import com.ly.adp.csc.gateway.SacEmployeeDimissionClueGateway;
import com.ly.adp.csc.service.ISacEmployeeDimissionClueService;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.helper.CustomStopWatch;
import com.ly.mp.csc.clue.entities.copyEntity.MdmOrgEmployee;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 员工离职线索分配服务实现类
 */
@Service
public class SacEmployeeDimissionClueServiceImpl implements ISacEmployeeDimissionClueService {

    private static final Logger logger = LoggerFactory.getLogger(SacEmployeeDimissionClueServiceImpl.class);

    @Autowired
    private SacEmployeeDimissionClueGateway clueGateway;

    @Override
    public void transferDimissionEmployeeClues() {
        CustomStopWatch customStopWatch = new CustomStopWatch("transferDimissionEmployeeClues");
        try {
            logger.info("开始执行员工离职线索自动分配给店长定时任务");

            // 1. 获取前一天离职审核通过的员工列表
            customStopWatch.start("queryYesterdayDimissionEmployee");
            List<MdmOrgEmployee> dimissionEmployees = clueGateway.queryYesterdayDimissionEmployee();
            customStopWatch.stop();
            logger.info("找到{}个前一天离职审核通过的门店员工", dimissionEmployees.size());

            if (CollectionUtils.isEmpty(dimissionEmployees)) {
                logger.info("没有有效门店的离职员工，任务结束");
                return;
            }

            // 2. 查询各门店下是否存在店长 or 代理店长
            customStopWatch.start("querySMInfo");
            Set<String> dlrCodeSet = dimissionEmployees.stream().map(d -> d.getDlrCode()).collect(Collectors.toSet());
            Map<String, MdmOrgEmployee> dlrManagerMap = clueGateway.querySMInfo(dlrCodeSet);
            customStopWatch.stop();
            if (MapUtils.isEmpty(dlrManagerMap)) {
                logger.info("没有找到任何门店的店长或代理店长，任务结束");
                return;
            }

            // 4. 获取所有需要处理的员工ID
            List<String> allEmployeeIds = dimissionEmployees.stream().map(d -> d.getUserId()).collect(Collectors.toList());

            // 5. 按员工依次处理线索，每批次处理BATCH_SIZE条
            customStopWatch.start("processEmployeeCluesByBatch");
            clueGateway.processEmployeeCluesByBatch(allEmployeeIds, dlrManagerMap);
            customStopWatch.stop();

            logger.info("员工离职线索自动分配给店长定时任务执行完成,耗时：{}", customStopWatch.prettyPrint());
        } catch (Exception e) {
            logger.error("员工离职线索自动分配给店长定时任务执行异常", e);
            throw new BusicenException("员工离职线索自动分配给店长定时任务执行异常: " + e.getMessage());
        }
    }
}