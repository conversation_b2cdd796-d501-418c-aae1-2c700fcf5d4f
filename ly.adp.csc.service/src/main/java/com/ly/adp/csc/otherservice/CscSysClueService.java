package com.ly.adp.csc.otherservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.csc.service.ISacOnecustInfoService;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.csc.clue.otherservice.ICscSysClueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class CscSysClueService implements ICscSysClueService {

	@Autowired
	ISacOnecustInfoService sacOnecustInfoService;

	@Override
	public ListResult<Map<String, Object>> carSeriesQuery(ParamPage<Map<String, Object>> paramParamPage) {
		IPage<Map<String, Object>> page = new Page<>(1, -1);
		ListResult<Map<String, Object>> carSeriesListResult=new ListResult<Map<String,Object>>();
		List<Map<String, Object>> carSeriesList=new ArrayList<Map<String,Object>>();
 		Map<String, Object> carSeriesMap=new HashMap<String, Object>();
		carSeriesMap.put("carSeriesId", UUID.randomUUID().toString());
		carSeriesMap.put("carSeriesCode", "1");
		carSeriesMap.put("carBrandCode", "1");
		carSeriesMap.put("carSeriesCn", "smart");
		carSeriesMap.put("carSeriesEn", "smart");
		carSeriesList.add(carSeriesMap);
		BusicenUtils.page2ListResult(page);
		page.setRecords(carSeriesList);
		return carSeriesListResult;
	}

	@Override
	public EntityResult<Map<String, Object>> sacOnecustInfoSaveInfo(Map<String, Object> dataInfo, String token) {
		return sacOnecustInfoService.sacOnecustInfoSaveInfo(dataInfo,token);
	}
}
