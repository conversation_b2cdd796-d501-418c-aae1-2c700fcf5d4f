package com.ly.adp.csc.util;

import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/23
 */
@Component
public class RabbitMQUtil {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    private static RabbitTemplate staticRabbitTemplate;

    @PostConstruct
    public void init() {
        staticRabbitTemplate = this.rabbitTemplate;
    }

    public static void sendCustEventMessage(Object msg) {
        // 生成唯一关联ID（用于追踪）
        CorrelationData correlation = new CorrelationData(UUID.randomUUID().toString());

        // 发送消息
        staticRabbitTemplate.convertAndSend(
                "smart.ex.adp.custEvent",
                "",
                msg,
                message -> {
                    message.getMessageProperties().setContentType(MessageProperties.CONTENT_TYPE_JSON);
                    return message;
                },
                correlation
        );
    }
}
