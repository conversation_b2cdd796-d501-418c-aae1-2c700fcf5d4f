package com.ly.adp.csc.entities.enums;

/**
 * 任务状态编码
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/
public enum TaskStateEnum {
    DRAFT("1", "草稿"),
    PUBLISHED("3", "已发布"),
    CANCELED("4", "已取消"),
    FINISHED("5", "已结束");

    private final String code;
    private final String description;

    TaskStateEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
