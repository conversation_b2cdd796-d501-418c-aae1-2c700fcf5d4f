package com.ly.adp.csc.controller;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.google.common.collect.Lists;
import com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper;
import com.ly.adp.csc.otherservice.IBasedataFeignClient;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.component.helper.StringHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacComplaintsInfoService;
import com.ly.adp.csc.service.ISacServerClassService;
import com.ly.mp.assembly.approve.service.ICommonAuditService;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 投诉工单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@RestController
@Api(value = "CSC投诉工单表服务", tags = "CSC投诉工单表服务")
@RequestMapping(value = "/ly/sac/complaints", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacComplaintsInfoController {
    @Autowired
    ISacComplaintsInfoService sacComplaintsInfoService;
    @Autowired
    ICommonAuditService commonAuditService;
    @Autowired
    ISacServerClassService sacServerClassService;
    @Autowired
    IBasedataFeignClient basedataFeignClient;
    @Autowired
    SacComplaintsInfoMapper sacComplaintsInfoMapper;

    @ApiOperation(value = "投诉通用查询", notes = "投诉通用查询")
    @PostMapping(value = "/sacComplaintsInfoFind.do")
    public ListResult<Map<String, Object>> sacComplaintsInfoFind(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> {
            return sacComplaintsInfoService.sacComplaintsInfoFind(dateInfo, authentication);
        }).result();
    }

    @ApiOperation(value = "投诉工单表查询", notes = "投诉工单表查询")
    @PostMapping(value = "/querycomplaintsList.do")
    public ListResult<Map<String, Object>> sacComplaintsInfoFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacComplaintsInfoService.sacComplaintsInfoFindInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "投诉工单表维护", notes = "投诉工单表维护")
    @PostMapping(value = "/complaintssave.do")
    public EntityResult<Map<String, Object>> sacComplaintsInfoSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> {
            return sacComplaintsInfoService.sacComplaintsInfoSaveInfo(dateInfo, authentication);
        }).result();
    }

    @ApiOperation(value = "投诉工单表作废", notes = "投诉工单表作废")
    @PostMapping(value = "/complaintsVoid.do")
    public OptResult complaintsVoid(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {
            return sacComplaintsInfoService.complaintsVoid(dateInfo, authentication);
        }).result();
    }

    @ApiOperation(value = "投诉工单异常关单定时", notes = "投诉工单异常关单定时")
    @PostMapping(value = "/complaintsDbjob.do")
    public void complaintsDbjob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
        sacComplaintsInfoService.complaintsDbjob();
    }

    @ApiOperation(value = "投诉审核节点查询", notes = "投诉审核节点查询")
    @PostMapping(value = "/sacComplaintsInfoAuditQuery.do")
    public ListResult<Map<String, Object>> sacComplaintsInfoAuditQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacComplaintsInfoService.sacComplaintsInfoAuditQuery(dataInfo, authentication)).result();
    }

    @ApiOperation(value = "查询审核节点id", notes = "查询审核节点id")
    @PostMapping(value = "/findAuditId.do")
    public EntityResult<Map<String, Object>> findAuditId( @RequestBody(required = false) Map<String, Object> dataInfo) {
        return  sacComplaintsInfoService.findAuditId(dataInfo);
    }
    @ApiOperation(value = "公共审核工单查询", notes = "公共审核工单查询")
    @PostMapping(value = "/queryAuditRecordList.do")
    public ListResult<Map<String, Object>> queryAuditRecordList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        Map<String, Object> param = dataInfo.getParam();
        if ("1".equals(param.get("codeStatus"))) {
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(authentication);
            String orgType = userBusiEntity.getOrgType();
            if ("1".equals(orgType)) {
                param.put("dlrCodeIn", userBusiEntity.getDlrCode());
            }
            if ("0".equals(orgType)) {
                param.put("dlrCodeIn", StringUtils.join(_getUserDlrCodeList(userBusiEntity.getUserID()), ","));
            }
            if ("2".equals(orgType)) {
                param.put("dlrCodeIn", StringUtils.join(_getAgentDlrCodeList(userBusiEntity.getOrgCode()), ","));
            }
        }
        // 调用t_sac_mybatis_sql_plus表的逻辑
        return BusicenInvoker.doList(() -> commonAuditService.queryAuditRecordList(dataInfo, authentication)).result();
    }

    private List<String> _getAgentDlrCodeList(String orgCode) {
        List<String> agentDlrCodeList = sacComplaintsInfoMapper.findAgentDlrCodeList(orgCode);
        if (agentDlrCodeList.isEmpty()) {
            List<String> arrayList = Lists.newArrayList();
            arrayList.add("-1");
            return arrayList;
        }
        return agentDlrCodeList;
    }

    private List<String> _getUserDlrCodeList(String userId) {
        HashMap<String, Object> querymanagedlrMap = new HashMap<>();
        querymanagedlrMap.put("userId", userId);
        querymanagedlrMap.put("pageIndex", -1);
        querymanagedlrMap.put("pageSize", -1);
        ListResult<Map<String, Object>> dlrListResult = basedataFeignClient.findManagedDlrList(querymanagedlrMap);
        List<String> dlrCodeList = new ArrayList<>();
        if ("1".equals(dlrListResult.getResult())) {
            List<Map<String, Object>> rows = dlrListResult.getRows();
            for (Map<String, Object> map : rows) {
                if (!StringHelper.IsEmptyOrNull(map.get("dlrCode"))) {
                    dlrCodeList.add(map.get("dlrCode").toString());
                }
            }
            if (dlrCodeList.isEmpty()) {
                dlrCodeList.add("-1");
            }
        }
        return dlrCodeList;
    }

    @ApiOperation(value = "服务类别查询", notes = "服务类别查询")
    @PostMapping(value = "/sacServerClassFindInfo.do")
    public ListResult<Map<String, Object>> sacServerClassFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacServerClassService.sacServerClassFindInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "投诉工单报表查询", notes = "投诉工单报表查询")
    @PostMapping(value = "/sacServerClassReport.do")
    public ListResult<Map<String, Object>> sacServerClassReport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacComplaintsInfoService.sacServerClassReport(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "投诉工单表查询导出", notes = "投诉工单表查询导出")
    @PostMapping(value = "/querycomplaintsListExport.do")
    public OptResult buCustomerComplaintsExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> {
            return sacComplaintsInfoService.querycomplaintsListExport(dataInfo, token, response);
        }).result();
    }

    @ApiOperation(value = "投诉审核节点查询导出", notes = "投诉审核节点查询导出")
    @PostMapping(value = "/sacComplaintsInfoAuditQueryExport.do")
    public OptResult sacComplaintsInfoAuditQueryExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> {
            return sacComplaintsInfoService.sacComplaintsInfoAuditQueryExport(dataInfo, token, response);
        }).result();
    }

    @ApiOperation(value = "公共审核工单查询导出", notes = "公共审核工单查询导出")
    @PostMapping(value = "/queryAuditRecordListExport.do")
    public OptResult queryAuditRecordListExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> {
            return sacComplaintsInfoService.queryAuditRecordListExport(dataInfo, token, response);
        }).result();
    }
}
