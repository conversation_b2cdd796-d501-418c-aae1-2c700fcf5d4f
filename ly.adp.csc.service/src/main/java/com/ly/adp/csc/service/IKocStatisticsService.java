package com.ly.adp.csc.service;

import java.util.List;
import java.util.Map;

import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * KOC统计服务接口
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
public interface IKocStatisticsService {

    /**
     * 查询标签统计信息
     * @param token 用户token
     * @return 标签统计
     */
    List<Map<String, Object>> getTagStatistics(String token);

    /**
     * 查询达人类型统计信息
     * @param token 用户token
     * @return 达人类型统计
     */
    List<Map<String, Object>> getExpertTypeStatistics(String token);

    /**
     * 查询操作日志统计
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 操作统计
     */
    ListResult<Map<String, Object>> getOperationStatistics(Map<String, Object> dataInfo, String token);

    /**
     * 查询用户打标统计
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 用户统计
     */
    ListResult<Map<String, Object>> getUserLabelingStatistics(Map<String, Object> dataInfo, String token);

    /**
     * 查询系统概览统计
     * @param token 用户token
     * @return 概览统计
     */
    Map<String, Object> getSystemOverview(String token);

    /**
     * 查询热门标签统计
     * @param limit 限制数量
     * @param token 用户token
     * @return 热门标签
     */
    List<Map<String, Object>> getPopularTags(Integer limit, String token);

    /**
     * 查询热门达人类型统计
     * @param limit 限制数量
     * @param token 用户token
     * @return 热门达人类型
     */
    List<Map<String, Object>> getPopularExpertTypes(Integer limit, String token);

    /**
     * 查询操作趋势统计
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 趋势统计
     */
    List<Map<String, Object>> getOperationTrend(Map<String, Object> dataInfo, String token);
}
