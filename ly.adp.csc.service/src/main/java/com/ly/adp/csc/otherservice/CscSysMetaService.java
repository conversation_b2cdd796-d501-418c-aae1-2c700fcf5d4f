package com.ly.adp.csc.otherservice;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.csc.clue.otherservice.ICscSysMetaService;

@Service
public class CscSysMetaService implements ICscSysMetaService {
	 
	@Override
	public ListResult<Map<String, Object>> proConfigColumnQueryByPage(String paramString,
			HashMap<String, Object> paramHashMap) {
		return null;
	}

	@Override
	public ListResult<Map<String, Object>> proGlobalLabelQueryByPage(String paramString,
			ParamPage<Map<String, Object>> paramParamPage) {
		return null;
	}

	@Override
	public ListResult<Map<String, Object>> proConfigMappingQueryByPage(String paramString1, String paramString2) {
		return null;
	}

	@Override
	public EntityResult<Map<String, Object>> proConfigMappingTranField(String paramString1, String paramString2,
			Map<String, Object> paramMap, String paramString3) {
		return null;
	}

	@Override
	public ListResult<Map<String, Object>> proConfigPageviewQueryByPage(String authentication, String pageCode) {
		return null;
	}

}
