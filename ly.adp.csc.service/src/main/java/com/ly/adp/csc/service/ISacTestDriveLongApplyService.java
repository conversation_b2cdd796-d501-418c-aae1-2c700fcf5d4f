package com.ly.adp.csc.service;

import java.util.List;
import java.util.Map;

import com.ly.mp.component.entities.OptResult;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacTestDriveLongApply;
import com.ly.adp.csc.entities.in.SacTestDriveLongApplyIn;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 超长试驾申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ISacTestDriveLongApplyService extends IService<SacTestDriveLongApply> {
	
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacTestDriveLongApplyQueryFindAll(String token, SacTestDriveLongApplyIn dataInfo);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacTestDriveLongApplySave(SacTestDriveLongApplyIn dataInfo, String token);
	
	ListResult<Map<String, Object>> uploadImage(MultipartFile uploadfiles, String token);

	/**
	 * 获取H5链接发送短信
	 * @param mapParam
	 * @return
	 */
	public OptResult sendTemplateLinkMessage(Map<String, Object> mapParam);

	OptResult sacTestDriveSendMessage(List<Map<String, Object>> param, String token);

	public void sendMessageJoB();

	EntityResult<Map<String, Object>> checkIsSendMessage(Map<String, Object> dataInfo, String token);
}
