package com.ly.adp.csc.gateway.impl;

import com.ly.adp.csc.entities.SacUserTagRel;
import com.ly.adp.csc.enums.UserRelationTypeEnum;
import com.ly.adp.csc.gateway.UserRelationGateway;
import com.ly.adp.csc.idal.mapper.SacUserTagRelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户关系数据网关实现类
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class UserRelationGatewayImpl implements UserRelationGateway {

    private static final Logger log = LoggerFactory.getLogger(UserRelationGatewayImpl.class);

    @Autowired
    private SacUserTagRelMapper userTagRelMapper;

    @Override
    public List<SacUserTagRel> queryBatchUserRelations(List<String> smartIds, UserRelationTypeEnum relationType) {
        // 参数校验
        if (smartIds == null || smartIds.isEmpty()) {
            return new ArrayList<>();
        }

        if (relationType == null) {
            log.warn("关系类型为空，smartIds: {}", smartIds);
            return new ArrayList<>();
        }

        log.info("开始批量查询用户关系数据，smartIds: {}, 关系类型: {}, 数量: {}", 
                smartIds, relationType.getDesc(), smartIds.size());

        try {
            // 调用Mapper查询数据
            List<SacUserTagRel> relations = userTagRelMapper.selectList(SacUserTagRel.buildBatchQueryWrapper(smartIds, relationType));
            
            int resultCount = relations != null ? relations.size() : 0;
            log.info("批量查询用户关系数据完成，smartIds: {}, 关系类型: {}, 返回数据量: {}", 
                    smartIds, relationType.getDesc(), resultCount);
            
            return relations != null ? relations : new ArrayList<>();

        } catch (Exception e) {
            log.error("批量查询用户关系数据失败，smartIds: {}, 关系类型: {}, 异常信息: {}", 
                    smartIds, relationType.getDesc(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
