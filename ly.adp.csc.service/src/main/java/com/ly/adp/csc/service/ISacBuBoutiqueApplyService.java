package com.ly.adp.csc.service;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.entities.SacBuBoutiqueApply;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 精品申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
public interface ISacBuBoutiqueApplyService extends IService<SacBuBoutiqueApply> {
	/**
	 * 分页查询
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacBuBoutiqueApplyFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param dataInfo
	 * @return
	 */
	EntityResult<Map<String, Object>> sacBuBoutiqueApplySaveInfo(Map<String, Object> dataInfo,String token);

    EntityResult<Map<String, Object>> sacBuBoutiqueApplysaveByDlr(List<Map<String, Object>> dateInfo, String authentication);

	OptResult sacBuBoutiqueApplysaveByDlrUpdate(Map<String, Object> dateInfo, String authentication);

	OptResult sacBuBoutiqueApplyLock(ParamBase<List<Map<String, Object>>> dateInfo, String authentication);

	ListResult<Map<String, Object>> sacBuBoutiqueApplyFindInfoQuery(ParamPage<Map<String, Object>> dateInfo, String authentication);

    ListResult<Map<String, Object>> sacBuBoutiqueApplyByDetails(ParamPage<Map<String, Object>> dateInfo, String authentication);

	OptResult exportsacBuBoutiqueApplyByDetails(Map<String, Object> info, HttpServletResponse response, String authentication);

	OptResult exportsacBuBoutiqueApplyByDetailsByElchee(ParamPage<Map<String, Object>> info, HttpServletResponse response, String authentication);
}
