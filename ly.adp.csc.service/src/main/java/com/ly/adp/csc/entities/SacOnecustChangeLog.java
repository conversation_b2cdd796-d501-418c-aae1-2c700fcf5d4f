package com.ly.adp.csc.entities;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 客户信息变更日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@TableName("t_sac_onecust_change_log")
public class SacOnecustChangeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变更日志ID
     */
    @TableId("CHANGE_LOG_ID")
    private String changeLogId;

    /**
     * 客户ID
     */
    @TableField("CUST_ID")
    private String custId;

    /**
     * 变更字段编码
     */
    @TableField("CHANGE_FILED")
    private String changeFiled;

    /**
     * 变更内容
     */
    @TableField("CHANGE_CONTENT")
    private String changeContent;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人姓名
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 修改人姓名
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    public String getChangeLogId() {
        return changeLogId;
    }

    public void setChangeLogId(String changeLogId) {
        this.changeLogId = changeLogId;
    }
    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }
    public String getChangeFiled() {
        return changeFiled;
    }

    public void setChangeFiled(String changeFiled) {
        this.changeFiled = changeFiled;
    }
    public String getChangeContent() {
        return changeContent;
    }

    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }
    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }
    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }
    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }
    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    @Override
    public String toString() {
        return "SacOnecustChangeLog{" +
        "changeLogId=" + changeLogId +
        ", custId=" + custId +
        ", changeFiled=" + changeFiled +
        ", changeContent=" + changeContent +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", column6=" + column6 +
        ", column7=" + column7 +
        ", column8=" + column8 +
        ", column9=" + column9 +
        ", column10=" + column10 +
        ", oemId=" + oemId +
        ", groupId=" + groupId +
        ", oemCode=" + oemCode +
        ", groupCode=" + groupCode +
        ", createdName=" + createdName +
        ", modifyName=" + modifyName +
        "}";
    }
}
