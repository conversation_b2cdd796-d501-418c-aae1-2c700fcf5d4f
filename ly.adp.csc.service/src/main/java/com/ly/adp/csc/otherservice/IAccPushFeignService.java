package com.ly.adp.csc.otherservice;

import java.util.List;
import java.util.Map;

import com.ly.adp.csc.entities.MessageResponseEntity;
import com.ly.mp.component.entities.EntityResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.ly.mp.component.entities.OptResult;

@FeignClient(name = "${refer.url.adp.api:ly.adp.api}", url = "${refer.url.adp.api}")
public interface IAccPushFeignService {
	@PostMapping("/rest/ADP_INSERT/{ms}")
	Map sendCancleData(@PathVariable("ms") String ms, @RequestBody Map<String, Object> param);

	/*
	* 发送短信
	* */
	@PostMapping("/noauth/rest/SMS_ADP/SMS_ADP_001")
	MessageResponseEntity smsSendMessage(@RequestBody Map<String, Object> param);

	@PostMapping("/rest/ADP_MS/{ms}")
	EntityResult<List<String>> ms(@PathVariable("ms") String ms, @RequestBody Object param);


	@PostMapping("/rest/ADP_INSERT/{ms}")
	OptResult sendApiData(@PathVariable("ms") String ms, @RequestBody Map<String, Object> param);
}
