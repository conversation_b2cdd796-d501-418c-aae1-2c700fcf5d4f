package com.ly.adp.csc.otherservice;

import java.util.HashMap;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.ly.adp.csc.entities.MessageResponseEntity;

/**
 * 调用SMS短信发送接口
 */
@FeignClient(name = "SmsMessageService",
	url = "https://f71b14c86b7f480e89450a41c452c701.apic.cn-east-3.huaweicloudapis.com/sms", 
	fallback = ISmsMessageFeignClient.ISmsSendMessageImpl.class)
public interface ISmsMessageFeignClient {
	@PostMapping(value = "/smsSend/smsSendOne/v1", produces = MediaType.APPLICATION_JSON_VALUE)
	MessageResponseEntity rest(@RequestBody Map<String, Object> param);
	
	@Component
	public class ISmsSendMessageImpl implements ISmsMessageFeignClient {
		@Override
		public MessageResponseEntity rest(Map<String, Object> param) {
			MessageResponseEntity responseEntity = new MessageResponseEntity();
			responseEntity.setCode("500");
			responseEntity.setMessage("Feign调用错误");
			responseEntity.setResult(new HashMap<String, Object>());
			responseEntity.setSuccess("0");
			return responseEntity;
		}
	}
}
