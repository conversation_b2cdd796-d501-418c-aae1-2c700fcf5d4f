package com.ly.adp.csc.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class SacClueMsgRecordIn extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "消息ID", required = false, example = "")
	private String messageId;
	
	@ApiModelProperty(value = "0未读，1已读", required = false, example = "")
	private String isRead;

	@ApiModelProperty(value = "经销商编码", required = false, example = "")
	private String dlrCode;

	@ApiModelProperty(value = "手机号码", required = false, example = "")
	private String phone;

	@ApiModelProperty(value = "场景编码 0试乘试驾", required = false, example = "")
	private String messageType;

	@ApiModelProperty(value = "关键字ID", required = false, example = "")
	private String busiKeyvalue;

	@ApiModelProperty(value = "消息接收人", required = false, example = "")
	private String receiveEmpId;

	@ApiModelProperty(value = "消息内容", required = false, example = "")
	private String messageContent;

	@ApiModelProperty(value = "关联单据ID", required = false, example = "")
	private String relationBillId;

	@ApiModelProperty(value = "是否可用", required = false, example = "")
	private String isEnable;

	@ApiModelProperty(value = "并发控制ID", required = false, example = "")
	private String updateControlId;

	@ApiModelProperty(value = "复合场景编码", required = false, example = "")
	private String scenario;
	
	@ApiModelProperty(value = "消息类型分割", required = false, example = "")
	private String messageTypeIn;
	
	@ApiModelProperty(value = "是否已读分割", required = false, example = "")
	private String isReadIn;
	
	@ApiModelProperty(value = "是否任务", required = false, example = "")
	private String oneTask;
	
	@ApiModelProperty(value = "创建开始时间", required = false, example = "")
	private String createdDateStart;
	
	@ApiModelProperty(value = "创建结束时间", required = false, example = "")
	private String createdDateEnd;
	
	public String getCreatedDateStart() {
		return createdDateStart;
	}

	public void setCreatedDateStart(String createdDateStart) {
		this.createdDateStart = createdDateStart;
	}

	public String getCreatedDateEnd() {
		return createdDateEnd;
	}

	public void setCreatedDateEnd(String createdDateEnd) {
		this.createdDateEnd = createdDateEnd;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getOneTask() {
		return oneTask;
	}

	public void setOneTask(String oneTask) {
		this.oneTask = oneTask;
	}

	public String getMessageTypeIn() {
		return messageTypeIn;
	}

	public void setMessageTypeIn(String messageTypeIn) {
		this.messageTypeIn = messageTypeIn;
	}

	public String getIsReadIn() {
		return isReadIn;
	}

	public void setIsReadIn(String isReadIn) {
		this.isReadIn = isReadIn;
	}

	public String getScenario() {
		return scenario;
	}

	public void setScenario(String scenario) {
		this.scenario = scenario;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getIsRead() {
		return isRead;
	}

	public void setIsRead(String isRead) {
		this.isRead = isRead;
	}

	public String getDlrCode() {
		return dlrCode;
	}

	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getBusiKeyvalue() {
		return busiKeyvalue;
	}

	public void setBusiKeyvalue(String busiKeyvalue) {
		this.busiKeyvalue = busiKeyvalue;
	}

	public String getReceiveEmpId() {
		return receiveEmpId;
	}

	public void setReceiveEmpId(String receiveEmpId) {
		this.receiveEmpId = receiveEmpId;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public String getRelationBillId() {
		return relationBillId;
	}

	public void setRelationBillId(String relationBillId) {
		this.relationBillId = relationBillId;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}
}
