package com.ly.adp.csc.service;

import com.ly.adp.common.entity.ParamPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.BbcArticle;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 论坛文章 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
public interface IBbcArticleBiz extends IService<BbcArticle> {
    ListResult<Map<String, Object>> queryAruticle(String token, ParamPage<Map<String, Object>> paramPage);
     ListResult<Map<String, Object>> queryAruticleList(String token, ParamPage<Map<String, Object>> paramPage);
    EntityResult<List> queryArticleBuild( Map<String, Object> paramPage,String token);
    OptResult updateAruticle(Map<String, Object> dateInfoon,String token);
     OptResult insertAruticle(Map<String, Object> dateInfoon,String token);
     OptResult saveAruticleCount(Map<String, Object> dateInfoon, String token);
    OptResult delAruticle(Map<String, Object> dateInfoon,String token);
}
