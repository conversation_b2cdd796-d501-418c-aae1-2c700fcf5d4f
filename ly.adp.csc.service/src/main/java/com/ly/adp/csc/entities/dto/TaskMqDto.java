package com.ly.adp.csc.entities.dto;

/**
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/

import java.util.List;

/**
 * TaskMqDto
 */
public class TaskMqDto {
    /**
     * 执行结果列表
     */
    private List<TaskClueMqDto> executionResults;
    /**
     * 状态变更时间
     */
    private String statusChangeTime;
    /**
     * 任务创建人
     */
    private String taskCreator;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务状态，1 已结束 2 已取消
     */
    private String taskStatus;

    public List<TaskClueMqDto> getExecutionResults() { return executionResults; }
    public void setExecutionResults(List<TaskClueMqDto> executionResults) { this.executionResults = executionResults; }

    public String getStatusChangeTime() { return statusChangeTime; }
    public void setStatusChangeTime(String statusChangeTime) { this.statusChangeTime = statusChangeTime; }

    public String getTaskCreator() { return taskCreator; }
    public void setTaskCreator(String taskCreator) { this.taskCreator = taskCreator; }

    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }

    public String getTaskStatus() { return taskStatus; }
    public void setTaskStatus(String taskStatus) { this.taskStatus = taskStatus; }
}

