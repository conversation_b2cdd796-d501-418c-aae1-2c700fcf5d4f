package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacClueCenterService;
import com.ly.adp.csc.service.ISacClueInfoDlrLogService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 用户分组明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@RestController
@Api(value = "CSC客制化线索服务", tags = "CSC客制化线索服务")
@RequestMapping(value = "/ly/adp/csc/sacClueCenter", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacClueCenterController {
	@Autowired
	ISacClueCenterService sacClueCenterService;
	@Autowired
	ISacClueInfoDlrLogService sacClueInfoDlrLogService;

	@ApiOperation(value = "客制化线索信息查询", notes = "客制化线索信息查询")
	@PostMapping(value = "/sacUserCluedlrbyquery.do")
	public ListResult<Map<String, Object>> sacUserCluedlrbyquery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacClueCenterService.sacUserCluedlrbyquery(dateInfo,authentication)).result();
	}

	@ApiOperation(value = "客制化线索信息查询", notes = "客制化线索信息查询")
	@PostMapping(value = "/sacUserCluedlrbyqueryTodo.todo")
	public ListResult<Map<String, Object>> sacUserCluedlrbyqueryToDo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacClueCenterService.sacUserCluedlrbyquery(dateInfo,authentication)).result();
	}

	@ApiOperation(value = "客制化线索信息查询todo", notes = "客制化线索信息查询todo")
	@PostMapping(value = "/sacUserCluedlrbyquery-performance.do")
	public ListResult<Map<String, Object>> sacUserCluedlrbyquery_performance(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacClueCenterService.sacUserCluedlrbyquery_performance(dateInfo,authentication)).result();
	}

	@ApiOperation(value = "用户画像雷达图数据查询", notes = "用户画像雷达图数据查询")
	@PostMapping(value = "/queryUserRaderMapInfo.do")
	public EntityResult<Map<String, Object>> queryUserRaderMapInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doEntity(() -> sacClueCenterService.queryUserRaderMapInfo(dateInfo,authentication)).result();
	}

	@ApiOperation(value = "用户画像雷达图数据查询todo", notes = "用户画像雷达图数据查询")
	@PostMapping(value = "/queryUserRaderMapInfoTodo.todo")
	public EntityResult<Map<String, Object>> queryUserRaderMapInfoToDo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doEntity(() -> sacClueCenterService.queryUserRaderMapInfo(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "超24小时线索自动分配定时", notes = "超24小时线索自动分配定时")
	@PostMapping(value = "/clueAutoAsgnDbjob")
	public void taskMsgDbjob(@RequestBody(required = false) Map<String, Object> dateInfo) {
		sacClueCenterService.clueAutoAsgnDbjob();
	}
	
	@ApiOperation(value = "订单跟进更新线索信息", notes = "订单跟进更新线索信息")
	@PostMapping(value = "/updateSacClue.do")
	public OptResult updateSacClue(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacClueCenterService.updateSacClue(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "特别关注更新线索信息", notes = "特别关注更新线索信息")
	@PostMapping(value = "/specialFocusUpdateClue.do")
	public OptResult specialFocusUpdateClue(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacClueCenterService.specialFocusUpdateClue(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "本店回访任务查询", notes = "本店回访任务查询")
	@RequestMapping(value = "/querylistbydlr.do", method = RequestMethod.POST)
	public ListResult<Map<String, Object>> queryListByDlr(@RequestHeader(name = "authorization", required = false) String token,@RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
		return BusicenInvoker.doList(() -> sacClueCenterService.queryListByDlr(dataInfo, token)).result();
	}
	
	@ApiOperation(value = "客制化线索分配", notes = "客制化线索分配")
	@RequestMapping(value = "/sacClueCenterAllot.do", method = RequestMethod.POST)
	public OptResult sacClueCenterAllot(@RequestHeader(name = "authorization", required = false) String token,@RequestBody(required = true) ParamBase<Map<String, Object>> dataInfo) {
		return BusicenInvoker.doOpt(() ->  sacClueCenterService.sacClueCenterAllot(dataInfo.getParam(), token)).result();
	}
	
	@ApiOperation(value = "城市线索更新战败", notes = "城市线索更新战败")
	@PostMapping(value = "/updateCityClue.do")
	public OptResult updateCityClue(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacClueCenterService.updateCityClue(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "线索移交保存", notes = "线索移交保存")
	@PostMapping(value = "/sacClueDeliver.do")
	public EntityResult<Map<String, Object>> sacClueDeliver(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacClueCenterService.sacClueDeliver(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "休眠线索保存", notes = "休眠线索保存")
	@PostMapping(value = "/sleepCluePool.do")
	public EntityResult<Map<String, Object>> sleepCluePool(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacClueCenterService.sleepCluePool(dateInfo,authentication);}).result();
	}
	@ApiOperation(value = "全部线索-手动休眠线索", notes = "全部线索-手动休眠线索")
	@PostMapping(value = "/manualSleepClue.do")
	public OptResult manualSleepClue(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> { return sacClueCenterService.manualSleepClue(dateInfo,authentication);}).result();
	}
	@ApiOperation(value = "客制化线索日志查询", notes = "客制化线索日志查询")
	@PostMapping(value = "/sacClueInfoDlrLogquery.do")
	public ListResult<Map<String, Object>> sacClueInfoDlrLogFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacClueInfoDlrLogService.sacClueInfoDlrLogFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "获取线索记录查询", notes = "获取线索记录查询")
	@PostMapping(value = "/sacClueCompeteRecordQuery.do")
	public ListResult<Map<String, Object>> sacClueCompeteRecordQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacClueCenterService.sacClueCompeteRecordQuery(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "所有门店获取线索开关查询", notes = "所有门店获取线索开关查询")
	@PostMapping(value = "/sacClueGetAllDlrQuery.do")
	public ListResult<Map<String, Object>> sacClueCompeteRecordDlrInfoQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacClueCenterService.sacClueCompeteRecordDlrInfoQuery(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "管辖门店获取线索开关查询", notes = "管辖门店获取线索开关查询")
	@PostMapping(value = "/sacClueGetManagedDlrQuery.do")
	public ListResult<Map<String, Object>> sacClueCompeteRecordManagedDlrQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacClueCenterService.sacClueCompeteRecordManagedDlrQuery(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "门店获取线索开关状态更新", notes = "门店获取线索开关状态更新")
	@PostMapping(value = "/sacClueUpdateSwitchStatus.do")
	public EntityResult<Map<String, Object>> sacClueCompeteRecordUpdateSwitchStatus(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doEntity(() -> {return sacClueCenterService.sacClueCompeteRecordUpdateSwitchStatus(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "门店获取线索-抢单", notes = "门店获取线索-抢单")
	@PostMapping(value = "/sacClueDlrGet.do")
	public EntityResult<Map<String, Object>> sacClueDlrGet(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doEntity(() -> {return sacClueCenterService.sacClueCompeteRecordGetClue(dateInfo,authentication);}).result();
	}

	@ApiOperation(value = "门店线索战败激活", notes = "门店线索战败激活")
	@PostMapping(value = "/sacClueDlrActivate.do")
	public EntityResult<Map<String, Object>> sacClueDlrActivate(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doEntity(() -> {return sacClueCenterService.sacClueDlrActivate(dateInfo,authentication);}).result();
	}
}

