package com.ly.adp.csc.entities.vo;

import java.io.Serializable;

/**
 * @Author: e-xiao.zhang1
 * @CreateTime: 2024/8/26 19:55
 * @Description: 线索基本信息
 * @Version: 1.0
 */
public class ClueDetailCustomInfoVO implements Serializable {
    private String lastUpdatedDate;
    private String planReviewTime;
    private String overReviewTime;
    private String lastReviewTime;
    private String businessHeatName;
    private String infoChanMName;
    private String channelName;
    private String innerColorName;
    private String outColorName;
    private String phone;
    private String phoneTM;
    private String genderName;
    private String statusName;
    private String statusCode;
    private String reviewPersonName;
    private String reviewPersonId;
    private String intenCarTypeName;
    private String intenCarTypeCode;
    private String custName;
    private String custId;

    public ClueDetailCustomInfoVO() {
    }

    public String getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(String lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getPlanReviewTime() {
        return planReviewTime;
    }

    public void setPlanReviewTime(String planReviewTime) {
        this.planReviewTime = planReviewTime;
    }

    public String getOverReviewTime() {
        return overReviewTime;
    }

    public void setOverReviewTime(String overReviewTime) {
        this.overReviewTime = overReviewTime;
    }

    public String getLastReviewTime() {
        return lastReviewTime;
    }

    public void setLastReviewTime(String lastReviewTime) {
        this.lastReviewTime = lastReviewTime;
    }

    public String getBusinessHeatName() {
        return businessHeatName;
    }

    public void setBusinessHeatName(String businessHeatName) {
        this.businessHeatName = businessHeatName;
    }

    public String getInfoChanMName() {
        return infoChanMName;
    }

    public void setInfoChanMName(String infoChanMName) {
        this.infoChanMName = infoChanMName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getInnerColorName() {
        return innerColorName;
    }

    public void setInnerColorName(String innerColorName) {
        this.innerColorName = innerColorName;
    }

    public String getOutColorName() {
        return outColorName;
    }

    public void setOutColorName(String outColorName) {
        this.outColorName = outColorName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneTM() {
        return phoneTM;
    }

    public void setPhoneTM(String phoneTM) {
        this.phoneTM = phoneTM;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getReviewPersonName() {
        return reviewPersonName;
    }

    public void setReviewPersonName(String reviewPersonName) {
        this.reviewPersonName = reviewPersonName;
    }

    public String getReviewPersonId() {
        return reviewPersonId;
    }

    public void setReviewPersonId(String reviewPersonId) {
        this.reviewPersonId = reviewPersonId;
    }

    public String getIntenCarTypeName() {
        return intenCarTypeName;
    }

    public void setIntenCarTypeName(String intenCarTypeName) {
        this.intenCarTypeName = intenCarTypeName;
    }

    public String getIntenCarTypeCode() {
        return intenCarTypeCode;
    }

    public void setIntenCarTypeCode(String intenCarTypeCode) {
        this.intenCarTypeCode = intenCarTypeCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    @Override
    public String toString() {
        return "ClueDetailCustomInfoVO{" +
                "lastUpdatedDate='" + lastUpdatedDate + '\'' +
                ", planReviewTime='" + planReviewTime + '\'' +
                ", overReviewTime='" + overReviewTime + '\'' +
                ", lastReviewTime='" + lastReviewTime + '\'' +
                ", businessHeatName='" + businessHeatName + '\'' +
                ", infoChanMName='" + infoChanMName + '\'' +
                ", channelName='" + channelName + '\'' +
                ", innerColorName='" + innerColorName + '\'' +
                ", outColorName='" + outColorName + '\'' +
                ", phone='" + phone + '\'' +
                ", phoneTM='" + phoneTM + '\'' +
                ", genderName='" + genderName + '\'' +
                ", statusName='" + statusName + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", reviewPersonName='" + reviewPersonName + '\'' +
                ", reviewPersonId='" + reviewPersonId + '\'' +
                ", intenCarTypeName='" + intenCarTypeName + '\'' +
                ", intenCarTypeCode='" + intenCarTypeCode + '\'' +
                ", custName='" + custName + '\'' +
                ", custId='" + custId + '\'' +
                '}';
    }
}


