package com.ly.adp.csc.service;
import java.util.Map;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacBuMenusControl;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

/**
 * <p>
 * APP菜单权限控制 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
public interface ISacBuMenusControlService extends IService<SacBuMenusControl> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacBuMenusControlFindInfo(ParamPage<Map<String, Object>> dataInfo, String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	OptResult sacBuMenusControlSaveInfo(ParamBase<Map<String, Object>> dataInfo, String token);
}
