package com.ly.adp.csc.otherservice.in;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.ly.adp.csc.entities.in.PageInfo;

import io.swagger.annotations.ApiModelProperty;
 
public class MdsLookupValueIn extends PageInfo implements Serializable
{
    @ApiModelProperty(value = "值类型编码  T_MDS_LOOKUP_TYPE LOOKUP_TYPE_CODE", required = false, example = "")
    private String lookupTypeCode;

    @ApiModelProperty(value = "值编码", required = false, example = "")
    private String lookupValueCode;

    @ApiModelProperty(value = "值名称", required = false, example = "")
    private String lookupValueName;

    @ApiModelProperty(value = "序号", required = false, example = "")
    private BigDecimal orderNo;

    @ApiModelProperty(value = "属性1", required = false, example = "")
    private String attribute1;

    @ApiModelProperty(value = "属性2", required = false, example = "")
    private String attribute2;

    @ApiModelProperty(value = "属性3", required = false, example = "")
    private String attribute3;

    @ApiModelProperty(value = "属性4", required = false, example = "")
    private String attribute4;

    @ApiModelProperty(value = "属性5", required = false, example = "")
    private String attribute5;

    @ApiModelProperty(value = "备注", required = false, example = "")
    private String remark;

    @ApiModelProperty(value = "是否系统", required = false, example = "")
    private String isSystem;

    @ApiModelProperty(value = "值类型名称", required = false, example = "")
    private String lookupTypeName;

    @ApiModelProperty(value = "专营店ID", required = false, example = "")
    private String dlrId;

    @ApiModelProperty(value = "时间戳", required = false, example = "")
    private Long mycatOpTime;

    @ApiModelProperty(value = "厂商标识ID", required = false, example = "")
    private String oemId;

    @ApiModelProperty(value = "集团标识ID", required = false, example = "")
    private String groupId;

    @ApiModelProperty(value = "厂商标识", required = false, example = "")
    private String oemCode;

    @ApiModelProperty(value = "集团标识", required = false, example = "")
    private String groupCode;

    @ApiModelProperty(value = "创建人", required = false, example = "")
    private String creator;

    @ApiModelProperty(value = "创建时间", required = false, example = "")
    private LocalDateTime createdDate;

    @ApiModelProperty(value = "最后更新人员", required = false, example = "")
    private String modifier;

    @ApiModelProperty(value = "最后更新时间", required = false, example = "")
    private LocalDateTime lastUpdatedDate;

    @ApiModelProperty(value = "是否可用", required = false, example = "")
    private String isEnable;

    @ApiModelProperty(value = "SDP用户ID", required = false, example = "")
    private String sdpUserId;

    @ApiModelProperty(value = "SDP组织ID", required = false, example = "")
    private String sdpOrgId;

    @ApiModelProperty(value = "并发控制字段", required = false, example = "")
    private String updateControlId;

    @ApiModelProperty(value = "扩展字段1", required = false, example = "")
    private String column1;

    @ApiModelProperty(value = "扩展字段2", required = false, example = "")
    private String column2;

    @ApiModelProperty(value = "扩展字段3", required = false, example = "")
    private String column3;

    @ApiModelProperty(value = "扩展字段4", required = false, example = "")
    private String column4;

    @ApiModelProperty(value = "扩展字段5", required = false, example = "")
    private String column5;

    @ApiModelProperty(value = "扩展字段6", required = false, example = "")
    private String column6;

    @ApiModelProperty(value = "扩展字段7", required = false, example = "")
    private String column7;

    @ApiModelProperty(value = "扩展字段8", required = false, example = "")
    private String column8;

    @ApiModelProperty(value = "扩展字段9", required = false, example = "")
    private String column9;

    @ApiModelProperty(value = "扩展字段10", required = false, example = "")
    private String column10;

    @ApiModelProperty(value = "主键ID 用户ID", required = false, example = "")
    private String uid;

    public String getLookupTypeCode() {
        return lookupTypeCode;
    }

    public void setLookupTypeCode(String lookupTypeCode) {
        this.lookupTypeCode = lookupTypeCode;
    }

    public String getLookupValueCode() {
        return lookupValueCode;
    }

    public void setLookupValueCode(String lookupValueCode) {
        this.lookupValueCode = lookupValueCode;
    }

    public String getLookupValueName() {
        return lookupValueName;
    }

    public void setLookupValueName(String lookupValueName) {
        this.lookupValueName = lookupValueName;
    }

    public BigDecimal getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(BigDecimal orderNo) {
        this.orderNo = orderNo;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(String isSystem) {
        this.isSystem = isSystem;
    }

    public String getLookupTypeName() {
        return lookupTypeName;
    }

    public void setLookupTypeName(String lookupTypeName) {
        this.lookupTypeName = lookupTypeName;
    }

    public String getDlrId() {
        return dlrId;
    }

    public void setDlrId(String dlrId) {
        this.dlrId = dlrId;
    }

    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }

    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

}
