package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacUserGroup;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 用户分组主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface ISacUserGroupService extends IService<SacUserGroup> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacUserGroupFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacUserGroupSaveInfo(Map<String, Object> dataInfo,String token);
}
