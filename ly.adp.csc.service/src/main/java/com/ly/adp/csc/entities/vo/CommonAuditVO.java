package com.ly.adp.csc.entities.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 公共审批流视图对象，用于API响应
 */
@ApiModel("公共审批流VO")
public class CommonAuditVO implements Serializable {

    private static final long serialVersionUID = -2446847966541868664L;

    @ApiModelProperty(value = "审核ID")
    private String auditId;

    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "更新控制ID")
    private String updateControlId;

    @ApiModelProperty(value = "申请人员用户ID")
    private String applyPersonId;

    @ApiModelProperty(value = "申请人员名称")
    private String applyPersonName;

    @ApiModelProperty(value = "代理商编码")
    private String agentCode;

    @ApiModelProperty(value = "经销商区域名称")
    private String dlrRegionName;

    @ApiModelProperty(value = "驳回原因")
    private String bhReason;

    @ApiModelProperty(value = "代理公司编码")
    private String agentCompanyCode;

    @ApiModelProperty(value = "代理公司名称")
    private String agentCompanyName;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "经销商区域编码")
    private String dlrRegionCode;

    @ApiModelProperty(value = "经销商编码")
    private String dlrCode;

    @ApiModelProperty(value = "活动类型编码")
    private String activityTypeCode;

    @ApiModelProperty(value = "活动子类型编码")
    private String activitySubtypeCode;

    @ApiModelProperty(value = "活动子类型名称")
    private String activitySubtypeName;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "申请时间")
    private String applyTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "单据编码")
    private String billCode;

    @ApiModelProperty(value = "单据类型名称")
    private String billTypeName;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "活动目的")
    private String activityPurpose;

    @ApiModelProperty(value = "经销商地址详情")
    private String dlrAddressDetail;

    @ApiModelProperty(value = "经销商简称")
    private String dlrShortName;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "关联单号")
    private String relationBillCode;

    @ApiModelProperty(value = "申请描述")
    private String applyDesc;

    @ApiModelProperty(value = "审核人员用户ID")
    private String shPersonId;

    @ApiModelProperty(value = "审核人员名称")
    private String shPersonName;

    @ApiModelProperty(value = "审核描述")
    private String shDesc;

    @ApiModelProperty(value = "审核时间")
    private String shTime;

    @ApiModelProperty(value = "审核状态编码，0待审核,1审核通过,2驳回")
    private String shStatus;

    @ApiModelProperty(value = "审核状态名称")
    private String shStatusName;

    @ApiModelProperty(value = "活动类型名称")
    private String activityTypeName;

    @ApiModelProperty(value = "活动封面URL")
    private String activityCoverPageUrl;

    @ApiModelProperty(value = "申请结束时间")
    private String applyEndTime;

    @ApiModelProperty(value = "支持人数")
    private String numberOfPersonSupported;

    @ApiModelProperty(value = "申请身份限制")
    private String applyIdentityLimit;

    @ApiModelProperty(value = "应用程序申请次数")
    private String numOfApplicationOfApps;

    @ApiModelProperty(value = "线下申请次数")
    private String numOfApplicationOffline;

    @ApiModelProperty(value = "核销驳回原因")
    private String hxBhReason;

    @ApiModelProperty(value = "发布时间")
    private String publishTime;

    @ApiModelProperty(value = "活动id")
    private String activityId;

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getApplyPersonId() {
        return applyPersonId;
    }

    public void setApplyPersonId(String applyPersonId) {
        this.applyPersonId = applyPersonId;
    }

    public String getApplyPersonName() {
        return applyPersonName;
    }

    public void setApplyPersonName(String applyPersonName) {
        this.applyPersonName = applyPersonName;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getDlrRegionName() {
        return dlrRegionName;
    }

    public void setDlrRegionName(String dlrRegionName) {
        this.dlrRegionName = dlrRegionName;
    }

    public String getBhReason() {
        return bhReason;
    }

    public void setBhReason(String bhReason) {
        this.bhReason = bhReason;
    }

    public String getAgentCompanyCode() {
        return agentCompanyCode;
    }

    public void setAgentCompanyCode(String agentCompanyCode) {
        this.agentCompanyCode = agentCompanyCode;
    }

    public String getAgentCompanyName() {
        return agentCompanyName;
    }

    public void setAgentCompanyName(String agentCompanyName) {
        this.agentCompanyName = agentCompanyName;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getDlrRegionCode() {
        return dlrRegionCode;
    }

    public void setDlrRegionCode(String dlrRegionCode) {
        this.dlrRegionCode = dlrRegionCode;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getActivityTypeCode() {
        return activityTypeCode;
    }

    public void setActivityTypeCode(String activityTypeCode) {
        this.activityTypeCode = activityTypeCode;
    }

    public String getActivitySubtypeCode() {
        return activitySubtypeCode;
    }

    public void setActivitySubtypeCode(String activitySubtypeCode) {
        this.activitySubtypeCode = activitySubtypeCode;
    }

    public String getActivitySubtypeName() {
        return activitySubtypeName;
    }

    public void setActivitySubtypeName(String activitySubtypeName) {
        this.activitySubtypeName = activitySubtypeName;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getBillTypeName() {
        return billTypeName;
    }

    public void setBillTypeName(String billTypeName) {
        this.billTypeName = billTypeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getActivityPurpose() {
        return activityPurpose;
    }

    public void setActivityPurpose(String activityPurpose) {
        this.activityPurpose = activityPurpose;
    }

    public String getDlrAddressDetail() {
        return dlrAddressDetail;
    }

    public void setDlrAddressDetail(String dlrAddressDetail) {
        this.dlrAddressDetail = dlrAddressDetail;
    }

    public String getDlrShortName() {
        return dlrShortName;
    }

    public void setDlrShortName(String dlrShortName) {
        this.dlrShortName = dlrShortName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRelationBillCode() {
        return relationBillCode;
    }

    public void setRelationBillCode(String relationBillCode) {
        this.relationBillCode = relationBillCode;
    }

    public String getApplyDesc() {
        return applyDesc;
    }

    public void setApplyDesc(String applyDesc) {
        this.applyDesc = applyDesc;
    }

    public String getShPersonId() {
        return shPersonId;
    }

    public void setShPersonId(String shPersonId) {
        this.shPersonId = shPersonId;
    }

    public String getShPersonName() {
        return shPersonName;
    }

    public void setShPersonName(String shPersonName) {
        this.shPersonName = shPersonName;
    }

    public String getShDesc() {
        return shDesc;
    }

    public void setShDesc(String shDesc) {
        this.shDesc = shDesc;
    }

    public String getShTime() {
        return shTime;
    }

    public void setShTime(String shTime) {
        this.shTime = shTime;
    }

    public String getShStatus() {
        return shStatus;
    }

    public void setShStatus(String shStatus) {
        this.shStatus = shStatus;
    }

    public String getShStatusName() {
        return shStatusName;
    }

    public void setShStatusName(String shStatusName) {
        this.shStatusName = shStatusName;
    }

    public String getActivityTypeName() {
        return activityTypeName;
    }

    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }

    public String getActivityCoverPageUrl() {
        return activityCoverPageUrl;
    }

    public void setActivityCoverPageUrl(String activityCoverPageUrl) {
        this.activityCoverPageUrl = activityCoverPageUrl;
    }

    public String getApplyEndTime() {
        return applyEndTime;
    }

    public void setApplyEndTime(String applyEndTime) {
        this.applyEndTime = applyEndTime;
    }

    public String getNumberOfPersonSupported() {
        return numberOfPersonSupported;
    }

    public void setNumberOfPersonSupported(String numberOfPersonSupported) {
        this.numberOfPersonSupported = numberOfPersonSupported;
    }

    public String getApplyIdentityLimit() {
        return applyIdentityLimit;
    }

    public void setApplyIdentityLimit(String applyIdentityLimit) {
        this.applyIdentityLimit = applyIdentityLimit;
    }

    public String getNumOfApplicationOfApps() {
        return numOfApplicationOfApps;
    }

    public void setNumOfApplicationOfApps(String numOfApplicationOfApps) {
        this.numOfApplicationOfApps = numOfApplicationOfApps;
    }

    public String getNumOfApplicationOffline() {
        return numOfApplicationOffline;
    }

    public void setNumOfApplicationOffline(String numOfApplicationOffline) {
        this.numOfApplicationOffline = numOfApplicationOffline;
    }

    public String getHxBhReason() {
        return hxBhReason;
    }

    public void setHxBhReason(String hxBhReason) {
        this.hxBhReason = hxBhReason;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
}