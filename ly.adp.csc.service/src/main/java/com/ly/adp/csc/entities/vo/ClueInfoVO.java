package com.ly.adp.csc.entities.vo;

import io.swagger.annotations.ApiModel;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/8/14
 * @Version 1.0.0
 **/
@ApiModel("线索信息返回实体")
public class ClueInfoVO {
    private String id; // ID
    private String serverOrder; // 线索单号
    private String pvServerOrder; // 总部线索单号
    private String custId; // 客户ID
    private String custName; // 客户名称
    private String phone; // 联系号码
    private String phoneBackup; // 备用联系号码
    private String intenLevelCode; // 意向级别编码
    private String intenLevelName; // 意向级别名称
    private String intenBrandCode; // 意向品牌编码
    private String intenBrandName; // 意向品牌名称
    private String intenSeriesCode; // 意向车系编码
    private String intenSeriesName; // 意向车系名称
    private String intenCarTypeCode; // 意向车型编码
    private String intenCarTypeName; // 意向车型名称
    private String intenOptionPackageCode; // 选装包编码
    private String intenOptionPackageName; // 选装包名称
    private String innerColorCode; // 内饰色编码
    private String innerColorName; // 内饰色名称
    private String outColorCode; // 外观色编码
    private String outColorName; // 外观色名称
    private String dlrCode; // 经销商编码
    private String dlrShortName; // 经销商名称
    private String sourceSystemtCode; // 来源系统编码
    private String sourceSystemtName; // 来源系统名称
    private LocalDateTime receiveTime; // 下发时间
    private String cusSource; // 细分渠道
    private String sourceServerOrder; // 外部线索编码
    private String infoChanMCode; // 一级信息来源编码
    private String infoChanMName; // 一级信息来源名称
    private String infoChanDCode; // 二级信息来源编码
    private String infoChanDName; // 二级信息来源名称
    private String infoChanDdCode; // 三级信息来源编码
    private String infoChanDdName; // 三级信息来源名称
    private String channelCode; // 最低一级的信息来源编码
    private String channelName; // 最低一级的信息来源名称
    private String genderCode; // 性别编码
    private String genderName; // 性别名称
    private String statusCode; // 状态编码
    private String statusName; // 状态名称
    private String dealNodeCode; // 处理节点编码
    private String dealNodeName; // 处理节点名称
    private String reviewId; // 回访记录ID
    private LocalDateTime firstReviewTime; // 第一次回访时间
    private LocalDateTime lastReviewTime; // 最后一次回访时间
    private LocalDateTime assignTime; // 分配时间
    private String reviewPersonName; // 回访人员名称
    private String reviewPersonId; // 回访人员用户ID
    private String column1; // 扩展字段1
    private String column2; // 扩展字段2
    private String column3; // 扩展字段3
    private String column4; // 扩展字段4
    private String column5; // 扩展字段5
    private String column6; // 扩展字段6
    private String column7; // 扩展字段7
    private String column8; // 扩展字段8
    private String column9; // 扩展字段9
    private String column10; // smartId
    private String column11; // 扩展字段11
    private String column12; // 扩展字段12
    private String column13; // 扩展字段13
    private String column14; // 扩展字段14
    private String column15; // 扩展字段15
    private String column16; // 扩展字段16
    private String column17; // 扩展字段17
    private String column18; // 扩展字段18
    private String column19; // 扩展字段19
    private String column20; // 是否注销线索  1 注销
    private String bigColumn1; // 大字段1
    private String bigColumn2; // 大字段2
    private String bigColumn3; // 大字段3
    private String bigColumn4; // 大字段4
    private String bigColumn5; // 大字段5
    private String extendsJson; // JSON扩展字段
    private String oemId; // 厂商标识ID
    private String groupId; // 集团标识ID
    private String creator; // 创建人ID
    private String createdName; // 创建人
    private LocalDateTime createdDate; // 创建日期
    private String modifier; // 修改人ID
    private String modifyName; // 修改人
    private LocalDateTime lastUpdatedDate; // 最后更新日期
    private String isEnable; // 是否可用
    private String updateControlId; // 并发控制ID
    private String provinceCode; // 省份编码
    private String provinceName; // 省份名称
    private String cityCode; // 城市编码
    private String cityName; // 城市名称
    private String countyCode; // 区县编码
    private String countyName; // 区县名称
    private String cityFirmCode; // 城市公司编码
    private String cityFirmName; // 城市公司名称
    private String manageLabelCode; // 线索移交编码
    private String manageLabelName; // 线索移交名称
    private String openStatus; // 开放状态
    private LocalDateTime lastArrivalTime; // 最近一次到店时间
    private LocalDateTime lastTestdriverTime; // 最近一次试驾时间
    private LocalDateTime firstArrivalTime; // 第一次到店时间
    private LocalDateTime firstTestdriverTime; // 第一次试驾时间
    private LocalDateTime allocateTime; // 线索分配时间
    private LocalDateTime updateVersion; // 更新版本时间

    private String smartId; // 更新版本时间

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getServerOrder() {
        return serverOrder;
    }

    public void setServerOrder(String serverOrder) {
        this.serverOrder = serverOrder;
    }

    public String getPvServerOrder() {
        return pvServerOrder;
    }

    public void setPvServerOrder(String pvServerOrder) {
        this.pvServerOrder = pvServerOrder;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneBackup() {
        return phoneBackup;
    }

    public void setPhoneBackup(String phoneBackup) {
        this.phoneBackup = phoneBackup;
    }

    public String getIntenLevelCode() {
        return intenLevelCode;
    }

    public void setIntenLevelCode(String intenLevelCode) {
        this.intenLevelCode = intenLevelCode;
    }

    public String getIntenLevelName() {
        return intenLevelName;
    }

    public void setIntenLevelName(String intenLevelName) {
        this.intenLevelName = intenLevelName;
    }

    public String getIntenBrandCode() {
        return intenBrandCode;
    }

    public void setIntenBrandCode(String intenBrandCode) {
        this.intenBrandCode = intenBrandCode;
    }

    public String getIntenBrandName() {
        return intenBrandName;
    }

    public void setIntenBrandName(String intenBrandName) {
        this.intenBrandName = intenBrandName;
    }

    public String getIntenSeriesCode() {
        return intenSeriesCode;
    }

    public void setIntenSeriesCode(String intenSeriesCode) {
        this.intenSeriesCode = intenSeriesCode;
    }

    public String getIntenSeriesName() {
        return intenSeriesName;
    }

    public void setIntenSeriesName(String intenSeriesName) {
        this.intenSeriesName = intenSeriesName;
    }

    public String getIntenCarTypeCode() {
        return intenCarTypeCode;
    }

    public void setIntenCarTypeCode(String intenCarTypeCode) {
        this.intenCarTypeCode = intenCarTypeCode;
    }

    public String getIntenCarTypeName() {
        return intenCarTypeName;
    }

    public void setIntenCarTypeName(String intenCarTypeName) {
        this.intenCarTypeName = intenCarTypeName;
    }

    public String getIntenOptionPackageCode() {
        return intenOptionPackageCode;
    }

    public void setIntenOptionPackageCode(String intenOptionPackageCode) {
        this.intenOptionPackageCode = intenOptionPackageCode;
    }

    public String getIntenOptionPackageName() {
        return intenOptionPackageName;
    }

    public void setIntenOptionPackageName(String intenOptionPackageName) {
        this.intenOptionPackageName = intenOptionPackageName;
    }

    public String getInnerColorCode() {
        return innerColorCode;
    }

    public void setInnerColorCode(String innerColorCode) {
        this.innerColorCode = innerColorCode;
    }

    public String getInnerColorName() {
        return innerColorName;
    }

    public void setInnerColorName(String innerColorName) {
        this.innerColorName = innerColorName;
    }

    public String getOutColorCode() {
        return outColorCode;
    }

    public void setOutColorCode(String outColorCode) {
        this.outColorCode = outColorCode;
    }

    public String getOutColorName() {
        return outColorName;
    }

    public void setOutColorName(String outColorName) {
        this.outColorName = outColorName;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getDlrShortName() {
        return dlrShortName;
    }

    public void setDlrShortName(String dlrShortName) {
        this.dlrShortName = dlrShortName;
    }

    public String getSourceSystemtCode() {
        return sourceSystemtCode;
    }

    public void setSourceSystemtCode(String sourceSystemtCode) {
        this.sourceSystemtCode = sourceSystemtCode;
    }

    public String getSourceSystemtName() {
        return sourceSystemtName;
    }

    public void setSourceSystemtName(String sourceSystemtName) {
        this.sourceSystemtName = sourceSystemtName;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getCusSource() {
        return cusSource;
    }

    public void setCusSource(String cusSource) {
        this.cusSource = cusSource;
    }

    public String getSourceServerOrder() {
        return sourceServerOrder;
    }

    public void setSourceServerOrder(String sourceServerOrder) {
        this.sourceServerOrder = sourceServerOrder;
    }

    public String getInfoChanMCode() {
        return infoChanMCode;
    }

    public void setInfoChanMCode(String infoChanMCode) {
        this.infoChanMCode = infoChanMCode;
    }

    public String getInfoChanMName() {
        return infoChanMName;
    }

    public void setInfoChanMName(String infoChanMName) {
        this.infoChanMName = infoChanMName;
    }

    public String getInfoChanDCode() {
        return infoChanDCode;
    }

    public void setInfoChanDCode(String infoChanDCode) {
        this.infoChanDCode = infoChanDCode;
    }

    public String getInfoChanDName() {
        return infoChanDName;
    }

    public void setInfoChanDName(String infoChanDName) {
        this.infoChanDName = infoChanDName;
    }

    public String getInfoChanDdCode() {
        return infoChanDdCode;
    }

    public void setInfoChanDdCode(String infoChanDdCode) {
        this.infoChanDdCode = infoChanDdCode;
    }

    public String getInfoChanDdName() {
        return infoChanDdName;
    }

    public void setInfoChanDdName(String infoChanDdName) {
        this.infoChanDdName = infoChanDdName;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getDealNodeCode() {
        return dealNodeCode;
    }

    public void setDealNodeCode(String dealNodeCode) {
        this.dealNodeCode = dealNodeCode;
    }

    public String getDealNodeName() {
        return dealNodeName;
    }

    public void setDealNodeName(String dealNodeName) {
        this.dealNodeName = dealNodeName;
    }

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }

    public LocalDateTime getFirstReviewTime() {
        return firstReviewTime;
    }

    public void setFirstReviewTime(LocalDateTime firstReviewTime) {
        this.firstReviewTime = firstReviewTime;
    }

    public LocalDateTime getLastReviewTime() {
        return lastReviewTime;
    }

    public void setLastReviewTime(LocalDateTime lastReviewTime) {
        this.lastReviewTime = lastReviewTime;
    }

    public LocalDateTime getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(LocalDateTime assignTime) {
        this.assignTime = assignTime;
    }

    public String getReviewPersonName() {
        return reviewPersonName;
    }

    public void setReviewPersonName(String reviewPersonName) {
        this.reviewPersonName = reviewPersonName;
    }

    public String getReviewPersonId() {
        return reviewPersonId;
    }

    public void setReviewPersonId(String reviewPersonId) {
        this.reviewPersonId = reviewPersonId;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getColumn11() {
        return column11;
    }

    public void setColumn11(String column11) {
        this.column11 = column11;
    }

    public String getColumn12() {
        return column12;
    }

    public void setColumn12(String column12) {
        this.column12 = column12;
    }

    public String getColumn13() {
        return column13;
    }

    public void setColumn13(String column13) {
        this.column13 = column13;
    }

    public String getColumn14() {
        return column14;
    }

    public void setColumn14(String column14) {
        this.column14 = column14;
    }

    public String getColumn15() {
        return column15;
    }

    public void setColumn15(String column15) {
        this.column15 = column15;
    }

    public String getColumn16() {
        return column16;
    }

    public void setColumn16(String column16) {
        this.column16 = column16;
    }

    public String getColumn17() {
        return column17;
    }

    public void setColumn17(String column17) {
        this.column17 = column17;
    }

    public String getColumn18() {
        return column18;
    }

    public void setColumn18(String column18) {
        this.column18 = column18;
    }

    public String getColumn19() {
        return column19;
    }

    public void setColumn19(String column19) {
        this.column19 = column19;
    }

    public String getColumn20() {
        return column20;
    }

    public void setColumn20(String column20) {
        this.column20 = column20;
    }

    public String getBigColumn1() {
        return bigColumn1;
    }

    public void setBigColumn1(String bigColumn1) {
        this.bigColumn1 = bigColumn1;
    }

    public String getBigColumn2() {
        return bigColumn2;
    }

    public void setBigColumn2(String bigColumn2) {
        this.bigColumn2 = bigColumn2;
    }

    public String getBigColumn3() {
        return bigColumn3;
    }

    public void setBigColumn3(String bigColumn3) {
        this.bigColumn3 = bigColumn3;
    }

    public String getBigColumn4() {
        return bigColumn4;
    }

    public void setBigColumn4(String bigColumn4) {
        this.bigColumn4 = bigColumn4;
    }

    public String getBigColumn5() {
        return bigColumn5;
    }

    public void setBigColumn5(String bigColumn5) {
        this.bigColumn5 = bigColumn5;
    }

    public String getExtendsJson() {
        return extendsJson;
    }

    public void setExtendsJson(String extendsJson) {
        this.extendsJson = extendsJson;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getCityFirmCode() {
        return cityFirmCode;
    }

    public void setCityFirmCode(String cityFirmCode) {
        this.cityFirmCode = cityFirmCode;
    }

    public String getCityFirmName() {
        return cityFirmName;
    }

    public void setCityFirmName(String cityFirmName) {
        this.cityFirmName = cityFirmName;
    }

    public String getManageLabelCode() {
        return manageLabelCode;
    }

    public void setManageLabelCode(String manageLabelCode) {
        this.manageLabelCode = manageLabelCode;
    }

    public String getManageLabelName() {
        return manageLabelName;
    }

    public void setManageLabelName(String manageLabelName) {
        this.manageLabelName = manageLabelName;
    }

    public String getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(String openStatus) {
        this.openStatus = openStatus;
    }

    public LocalDateTime getLastArrivalTime() {
        return lastArrivalTime;
    }

    public void setLastArrivalTime(LocalDateTime lastArrivalTime) {
        this.lastArrivalTime = lastArrivalTime;
    }

    public LocalDateTime getLastTestdriverTime() {
        return lastTestdriverTime;
    }

    public void setLastTestdriverTime(LocalDateTime lastTestdriverTime) {
        this.lastTestdriverTime = lastTestdriverTime;
    }

    public LocalDateTime getFirstArrivalTime() {
        return firstArrivalTime;
    }

    public void setFirstArrivalTime(LocalDateTime firstArrivalTime) {
        this.firstArrivalTime = firstArrivalTime;
    }

    public LocalDateTime getFirstTestdriverTime() {
        return firstTestdriverTime;
    }

    public void setFirstTestdriverTime(LocalDateTime firstTestdriverTime) {
        this.firstTestdriverTime = firstTestdriverTime;
    }

    public LocalDateTime getAllocateTime() {
        return allocateTime;
    }

    public void setAllocateTime(LocalDateTime allocateTime) {
        this.allocateTime = allocateTime;
    }

    public LocalDateTime getUpdateVersion() {
        return updateVersion;
    }

    public void setUpdateVersion(LocalDateTime updateVersion) {
        this.updateVersion = updateVersion;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }
}
