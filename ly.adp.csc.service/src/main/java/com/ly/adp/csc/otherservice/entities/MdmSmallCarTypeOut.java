package com.ly.adp.csc.otherservice.entities;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class MdmSmallCarTypeOut implements Serializable{
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "车型小类ID", required = false, example = "")
	   private String smallCarTypeId;
	@ApiModelProperty(value = "车型小类编码", required = false, example = "")
	   private String smallCarTypeCode;
	@ApiModelProperty(value = "车型小类中文名称", required = false, example = "")
	   private String smallCarTypeCn;
	@ApiModelProperty(value = "车型小类英文名称", required = false, example = "")
	   private String smallCarTypeEn;
	@ApiModelProperty(value = "启用状态", required = false, example = "")
	   private String isEnable;
	@ApiModelProperty(value = "启用状态中文名称", required = false, example = "")
	   private String isEnableCn;
	@ApiModelProperty(value = "并发控制字段", required = false, example = "")
	   private String updateControlId;
	@ApiModelProperty(value = "车型中类ID", required = false, example = "")
	   private String stdCarId;
	@ApiModelProperty(value = "车型中类编码", required = false, example = "")
	   private String stdCarCode;
	@ApiModelProperty(value = "车型中类中文名称", required = false, example = "")
	   private String stdCarCn;
	@ApiModelProperty(value = "车型中类英文名称", required = false, example = "")
	   private String stdCarEn;
	@ApiModelProperty(value = "车型大类ID", required = false, example = "")
	   private String largeCarTypeId;
	@ApiModelProperty(value = "车型大类编码", required = false, example = "")
	   private String largeCarTypeCode;
	@ApiModelProperty(value = "车型大类中文名称", required = false, example = "")
	   private String largeCarTypeCn;
	@ApiModelProperty(value = "车型大类英文名称", required = false, example = "")
	   private String largeCarTypeEn;
	@ApiModelProperty(value = "基准车系ID", required = false, example = "")
	   private String baseSeriesId;
	@ApiModelProperty(value = "基准车系编码", required = false, example = "")
	   private String baseSeriesCode;
	@ApiModelProperty(value = "基准车系中文名称", required = false, example = "")
	   private String baseCarSeriesCn;
	@ApiModelProperty(value = "基准车系英文名称", required = false, example = "")
	   private String baseCarSeriesEn;
	@ApiModelProperty(value = "品牌编码", required = false, example = "")
	   private String carBrandCode;
	@ApiModelProperty(value = "车系ID", required = false, example = "")
	   private String carSeriesId;
	@ApiModelProperty(value = "品牌中文名称", required = false, example = "")
	   private String carBrandCn;
	@ApiModelProperty(value = "车系编码", required = false, example = "")
	   private String carSeriesCode;
	@ApiModelProperty(value = "车系中文名称", required = false, example = "")
	   private String carSeriesCn;
	@ApiModelProperty(value = "车系英文名称", required = false, example = "")
	   private String carSeriesEn;
	@ApiModelProperty(value = "服务车系编码", required = false, example = "")
	   private String sCarseriesCode;
	@ApiModelProperty(value = "服务车系名称", required = false, example = "")
	   private String sCarseriesDesc;
	public String getSmallCarTypeId() {
		return smallCarTypeId;
	}
	public void setSmallCarTypeId(String smallCarTypeId) {
		this.smallCarTypeId = smallCarTypeId;
	}
	public String getSmallCarTypeCode() {
		return smallCarTypeCode;
	}
	public void setSmallCarTypeCode(String smallCarTypeCode) {
		this.smallCarTypeCode = smallCarTypeCode;
	}
	public String getSmallCarTypeCn() {
		return smallCarTypeCn;
	}
	public void setSmallCarTypeCn(String smallCarTypeCn) {
		this.smallCarTypeCn = smallCarTypeCn;
	}
	public String getSmallCarTypeEn() {
		return smallCarTypeEn;
	}
	public void setSmallCarTypeEn(String smallCarTypeEn) {
		this.smallCarTypeEn = smallCarTypeEn;
	}
	public String getIsEnable() {
		return isEnable;
	}
	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}
	public String getIsEnableCn() {
		return isEnableCn;
	}
	public void setIsEnableCn(String isEnableCn) {
		this.isEnableCn = isEnableCn;
	}
	public String getUpdateControlId() {
		return updateControlId;
	}
	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}
	public String getStdCarId() {
		return stdCarId;
	}
	public void setStdCarId(String stdCarId) {
		this.stdCarId = stdCarId;
	}
	public String getStdCarCode() {
		return stdCarCode;
	}
	public void setStdCarCode(String stdCarCode) {
		this.stdCarCode = stdCarCode;
	}
	public String getStdCarCn() {
		return stdCarCn;
	}
	public void setStdCarCn(String stdCarCn) {
		this.stdCarCn = stdCarCn;
	}
	public String getStdCarEn() {
		return stdCarEn;
	}
	public void setStdCarEn(String stdCarEn) {
		this.stdCarEn = stdCarEn;
	}
	public String getLargeCarTypeId() {
		return largeCarTypeId;
	}
	public void setLargeCarTypeId(String largeCarTypeId) {
		this.largeCarTypeId = largeCarTypeId;
	}
	public String getLargeCarTypeCode() {
		return largeCarTypeCode;
	}
	public void setLargeCarTypeCode(String largeCarTypeCode) {
		this.largeCarTypeCode = largeCarTypeCode;
	}
	public String getLargeCarTypeCn() {
		return largeCarTypeCn;
	}
	public void setLargeCarTypeCn(String largeCarTypeCn) {
		this.largeCarTypeCn = largeCarTypeCn;
	}
	public String getLargeCarTypeEn() {
		return largeCarTypeEn;
	}
	public void setLargeCarTypeEn(String largeCarTypeEn) {
		this.largeCarTypeEn = largeCarTypeEn;
	}
	public String getBaseSeriesId() {
		return baseSeriesId;
	}
	public void setBaseSeriesId(String baseSeriesId) {
		this.baseSeriesId = baseSeriesId;
	}
	public String getBaseSeriesCode() {
		return baseSeriesCode;
	}
	public void setBaseSeriesCode(String baseSeriesCode) {
		this.baseSeriesCode = baseSeriesCode;
	}
	public String getBaseCarSeriesCn() {
		return baseCarSeriesCn;
	}
	public void setBaseCarSeriesCn(String baseCarSeriesCn) {
		this.baseCarSeriesCn = baseCarSeriesCn;
	}
	public String getBaseCarSeriesEn() {
		return baseCarSeriesEn;
	}
	public void setBaseCarSeriesEn(String baseCarSeriesEn) {
		this.baseCarSeriesEn = baseCarSeriesEn;
	}
	public String getCarBrandCode() {
		return carBrandCode;
	}
	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}
	public String getCarSeriesId() {
		return carSeriesId;
	}
	public void setCarSeriesId(String carSeriesId) {
		this.carSeriesId = carSeriesId;
	}
	public String getCarBrandCn() {
		return carBrandCn;
	}
	public void setCarBrandCn(String carBrandCn) {
		this.carBrandCn = carBrandCn;
	}
	public String getCarSeriesCode() {
		return carSeriesCode;
	}
	public void setCarSeriesCode(String carSeriesCode) {
		this.carSeriesCode = carSeriesCode;
	}
	public String getCarSeriesCn() {
		return carSeriesCn;
	}
	public void setCarSeriesCn(String carSeriesCn) {
		this.carSeriesCn = carSeriesCn;
	}
	public String getCarSeriesEn() {
		return carSeriesEn;
	}
	public void setCarSeriesEn(String carSeriesEn) {
		this.carSeriesEn = carSeriesEn;
	}
	public String getsCarseriesCode() {
		return sCarseriesCode;
	}
	public void setsCarseriesCode(String sCarseriesCode) {
		this.sCarseriesCode = sCarseriesCode;
	}
	public String getsCarseriesDesc() {
		return sCarseriesDesc;
	}
	public void setsCarseriesDesc(String sCarseriesDesc) {
		this.sCarseriesDesc = sCarseriesDesc;
	}


}
