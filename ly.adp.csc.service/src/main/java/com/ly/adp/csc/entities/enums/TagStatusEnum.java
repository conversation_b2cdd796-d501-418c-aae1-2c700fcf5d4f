package com.ly.adp.csc.entities.enums;

/**
 * 标签状态枚举
 * <AUTHOR>
 * @since 2025-07-25
 */
public enum TagStatusEnum {
    DISABLED(0, "下架"),
    ENABLED(1, "上架");

    private final Integer code;
    private final String description;

    TagStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static TagStatusEnum getByCode(Integer code) {
        for (TagStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
