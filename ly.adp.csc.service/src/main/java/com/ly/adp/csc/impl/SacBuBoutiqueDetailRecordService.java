package com.ly.adp.csc.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.adp.csc.entities.SacBuBoutiqueDetailRecord;
import com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper;
import com.ly.adp.csc.service.ISacBuBoutiqueDetailRecordService;
import com.ly.mp.busi.base.handler.EntityResultBuilder;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.busicen.common.util.BusicenUtils.SOU;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.helper.StringHelper;

/**
 * <p>
 * 精品明细记录日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Service
public class SacBuBoutiqueDetailRecordService
        extends ServiceImpl<SacBuBoutiqueDetailRecordMapper, SacBuBoutiqueDetailRecord>
        implements ISacBuBoutiqueDetailRecordService {

    private static final Logger log = LoggerFactory.getLogger(SacBuBoutiqueDetailRecordService.class);

    @Autowired
    SacBuBoutiqueDetailRecordMapper sacBuBoutiqueDetailRecordMapper;

    /**
     * 分页查询
     */
    @Override
    public ListResult<Map<String, Object>> sacBuBoutiqueDetailRecordFindInfo(Map<String, Object> dataInfo,
                                                                             String token) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        try {
            IPage<Map<String, Object>> page = new Page<>(Integer.parseInt(dataInfo.get("pageIndex").toString()),
                    Integer.parseInt(dataInfo.get("pageSize").toString()));
            List<Map<String, Object>> list = sacBuBoutiqueDetailRecordMapper.querySacBuBoutiqueDetailRecordInfo(page,
                    dataInfo);
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
            return result;
        } catch (Exception e) {
            log.error("sacBuBoutiqueDetailRecordFindInfo异常", e);
            // 抛出RuntimeException，事务才会回滚
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 根据主键判断插入或更新
     *
     * @param info
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    // @GlobalTransactional(name="sacBuBoutiqueDetailRecordSave")
    public EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordSaveInfo(Map<String, Object> dataInfo,
                                                                               String token) {
        try {
            int sacBuBoutiqueDetailRecordI = 0;
            if (StringHelper.IsEmptyOrNull(dataInfo.get("recordId"))) {
                BusicenUtils.invokeUserInfo(dataInfo, SOU.Save, token);
                sacBuBoutiqueDetailRecordI = sacBuBoutiqueDetailRecordMapper.createSacBuBoutiqueDetailRecord(dataInfo);
            } else {
                BusicenUtils.invokeUserInfo(dataInfo, SOU.Update, token);
                sacBuBoutiqueDetailRecordI = sacBuBoutiqueDetailRecordMapper.updateSacBuBoutiqueDetailRecord(dataInfo);
            }
            if (sacBuBoutiqueDetailRecordI < 1) {
                throw BusicenException.create("精品明细记录日志表维护失败！");
            }
            return EntityResultBuilder.<Map<String, Object>>creatOk().rows(dataInfo).build();
        } catch (Exception e) {
            log.error("sacBuBoutiqueDetailRecordSave", e);
            throw e;
        }
    }

    /**
     * @deprecated: 批量操作日志
     * @return: {@link null}
     * @author: Han
     * @date: 2022/5/5
     */
    @Override
    @Transactional
    public EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordsaveByList(List<Map<String, Object>> dateInfo, String authentication) {
        EntityResult<Map<String, Object>> result = new EntityResult<>();
        try {
            if (StringHelper.IsEmptyOrNull(dateInfo)) {
                throw BusicenException.create("操作数据不能为空");
            }
            for (Map<String, Object> map : dateInfo) {
                EntityResult<Map<String, Object>> entityResult = this.sacBuBoutiqueDetailRecordSaveInfo(map, authentication);
                if ("0".equals(entityResult.getResult())) {
                    throw BusicenException.create("操作日志记录添加失败,请重试");
                }
            }
            result.setResult("1");
            result.setMsg("添加成功");

        } catch (BusicenException e) {
          //  e.printStackTrace();
            log.error("批量操作日志:sacBuBoutiqueDetailRecordsaveByList", e);
        }
        return result;
    }


}
