package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;

/**
 * 企微端达人类型VO
 * <AUTHOR>
 * @since 2025-07-29
 */
public class ExpertTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 达人类型ID
     */
    private String typeId;

    /**
     * 达人类型名称
     */
    private String typeName;

    /**
     * 达人类型内容
     */
    private String typeContent;

    /**
     * 类型颜色（前端显示用）
     */
    private String color;

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeContent() {
        return typeContent;
    }

    public void setTypeContent(String typeContent) {
        this.typeContent = typeContent;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    @Override
    public String toString() {
        return "WechatExpertTypeVO{" +
                "typeId='" + typeId + '\'' +
                ", typeName='" + typeName + '\'' +
                ", typeContent='" + typeContent + '\'' +
                ", color='" + color + '\'' +
                '}';
    }
}
