package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 论坛文章
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@TableName("t_sac_bu_bbc_article")
public class BbcArticle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    @TableId("ARTICLE_ID")
    private String articleId;

    /**
     * 文章标题
     */
    @TableField("ARTICLE_TITELE")
    private String articleTitele;

    /**
     * 板块类型1 经验分享,2 行业资讯,3 活动分享
     */
    @TableField("ARTICLE_BUILD")
    private String articleBuild;

    /**
     * 板块名称
     */
    @TableField("ARTICLE_BUILD_NAME")
    private String articleBuildName;

    /**
     * 文章内容
     */
    @TableField("ARTICLE_CONTENT")
    private String articleContent;

    /**
     * 封面图片
     */
    @TableField("ARTICLE_IMAGE")
    private String articleImage;

    /**
     * 文章审核状态 21：发布审核中 22已发布 23 发布驳回
     */
    @TableField("ARTICLE_STATUS")
    private String articleStatus;

    /**
     * 文章审核状态名称
     */
    @TableField("ARTICLE_STATUS_NAME")
    private String articleStatusName;

    /**
     * 文章类型 1草稿 2发布
     */
    @TableField("ARTICLE_TYPE")
    private String articleType;

    /**
     * 文章类型名称
     */
    @TableField("ARTICLE_TYPE_NAME")
    private String articleTypeName;

    /**
     * 评论是否关闭
     */
    @TableField("IS_COMMENT")
    private String isComment;

    /**
     * 评论是否审核
     */
    @TableField("IS_PERSON_COMMENT")
    private String isPersonComment;

    /**
     * 是否匿名
     */
    @TableField("IS_ANONYMOUS")
    private String isAnonymous;

    /**
     * 审核人员用户ID
     */
    @TableField("SH_PERSON_ID")
    private String shPersonId;

    /**
     * 审核人员名称
     */
    @TableField("SH_PERSON_NAME")
    private String shPersonName;

    /**
     * 审核时间
     */
    @TableField("SH_TIME")
    private LocalDateTime shTime;

    /**
     * 驳回原因
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人名称
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新人员名称
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getArticleId() {
        return articleId;
    }

    public void setArticleId(String articleId) {
        this.articleId = articleId;
    }

    public String getArticleTitele() {
        return articleTitele;
    }

    public void setArticleTitele(String articleTitele) {
        this.articleTitele = articleTitele;
    }

    public String getArticleBuild() {
        return articleBuild;
    }

    public void setArticleBuild(String articleBuild) {
        this.articleBuild = articleBuild;
    }

    public String getArticleBuildName() {
        return articleBuildName;
    }

    public void setArticleBuildName(String articleBuildName) {
        this.articleBuildName = articleBuildName;
    }

    public String getArticleContent() {
        return articleContent;
    }

    public void setArticleContent(String articleContent) {
        this.articleContent = articleContent;
    }

    public String getArticleImage() {
        return articleImage;
    }

    public void setArticleImage(String articleImage) {
        this.articleImage = articleImage;
    }

    public String getArticleStatus() {
        return articleStatus;
    }

    public void setArticleStatus(String articleStatus) {
        this.articleStatus = articleStatus;
    }

    public String getArticleStatusName() {
        return articleStatusName;
    }

    public void setArticleStatusName(String articleStatusName) {
        this.articleStatusName = articleStatusName;
    }

    public String getArticleType() {
        return articleType;
    }

    public void setArticleType(String articleType) {
        this.articleType = articleType;
    }

    public String getArticleTypeName() {
        return articleTypeName;
    }

    public void setArticleTypeName(String articleTypeName) {
        this.articleTypeName = articleTypeName;
    }

    public String getIsComment() {
        return isComment;
    }

    public void setIsComment(String isComment) {
        this.isComment = isComment;
    }

    public String getIsPersonComment() {
        return isPersonComment;
    }

    public void setIsPersonComment(String isPersonComment) {
        this.isPersonComment = isPersonComment;
    }

    public String getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(String isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getShPersonId() {
        return shPersonId;
    }

    public void setShPersonId(String shPersonId) {
        this.shPersonId = shPersonId;
    }

    public String getShPersonName() {
        return shPersonName;
    }

    public void setShPersonName(String shPersonName) {
        this.shPersonName = shPersonName;
    }

    public LocalDateTime getShTime() {
        return shTime;
    }

    public void setShTime(LocalDateTime shTime) {
        this.shTime = shTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BbcArticle{" +
        "articleId=" + articleId +
        ", articleTitele=" + articleTitele +
        ", articleBuild=" + articleBuild +
        ", articleBuildName=" + articleBuildName +
        ", articleContent=" + articleContent +
        ", articleImage=" + articleImage +
        ", articleStatus=" + articleStatus +
        ", articleStatusName=" + articleStatusName +
        ", articleType=" + articleType +
        ", articleTypeName=" + articleTypeName +
        ", isComment=" + isComment +
        ", isPersonComment=" + isPersonComment +
        ", isAnonymous=" + isAnonymous +
        ", shPersonId=" + shPersonId +
        ", shPersonName=" + shPersonName +
        ", shTime=" + shTime +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
