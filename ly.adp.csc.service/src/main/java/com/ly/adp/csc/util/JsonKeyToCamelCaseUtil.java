package com.ly.adp.csc.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.*;

public class JsonKeyToCamelCaseUtil {

    // Jackson 的 ObjectMapper 实例
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将 JSON 字符串中的 key 转换为驼峰格式，并解析为 List<Map<String, Object>>
     *
     * @param jsonString 输入的 JSON 字符串
     * @return 转换后的 List<Map<String, Object>>
     */
    public static List<Map<String, Object>> convertToCamelCaseList(String jsonString) {
        try {
            // 将 JSON 字符串解析为 JsonNode
            JsonNode rootNode = objectMapper.readTree(jsonString);

            // 如果根节点是数组，直接处理
            if (rootNode.isArray()) {
                ArrayNode arrayNode = (ArrayNode) rootNode;
                List<Map<String, Object>> resultList = new ArrayList<>();
                for (JsonNode item : arrayNode) {
                    Map<String, Object> map = processNodeToMap(item);
                    resultList.add(map);
                }
                return resultList;
            } else {
                throw new IllegalArgumentException("Invalid JSON format: root is not an array");
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert JSON keys to camel case and parse to List<Map<String, Object>>", e);
        }
    }

    /**
     * 递归处理 JSON 节点，将 key 转换为驼峰格式，并转换为 Map<String, Object>
     *
     * @param node 当前处理的 JSON 节点
     * @return 转换后的 Map<String, Object>
     */
    private static Map<String, Object> processNodeToMap(JsonNode node) {
        Map<String, Object> resultMap = new HashMap<>();

        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();

            // 遍历所有字段
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String originalKey = field.getKey();
                JsonNode value = field.getValue();

                // 将 key 转换为驼峰格式
                String camelCaseKey = toCamelCase(originalKey);

                // 递归处理子节点
                if (value.isObject() || value.isArray()) {
                    resultMap.put(camelCaseKey, processNodeToMap(value));
                } else {
                    resultMap.put(camelCaseKey, getValue(value));
                }
            }
        } else if (node.isArray()) {
            // 如果是数组，递归处理每个元素
            ArrayNode arrayNode = (ArrayNode) node;
            List<Object> resultList = new ArrayList<>();
            for (JsonNode item : arrayNode) {
                resultList.add(processNodeToMap(item));
            }
            return (Map<String, Object>) resultList;
        }

        return resultMap;
    }

    /**
     * 获取 JSON 节点的值
     *
     * @param node JSON 节点
     * @return 节点的值
     */
    private static Object getValue(JsonNode node) {
        if (node.isTextual()) {
            return node.asText();
        } else if (node.isNumber()) {
            return node.numberValue();
        } else if (node.isBoolean()) {
            return node.asBoolean();
        } else if (node.isNull()) {
            return null;
        } else {
            throw new IllegalArgumentException("Unsupported JSON value type: " + node.getNodeType());
        }
    }

    /**
     * 将字符串转换为驼峰格式
     *
     * @param str 输入的字符串
     * @return 驼峰格式的字符串
     */
    private static String toCamelCase(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        // 将字符串按 "_" 分割
        String[] parts = str.split("_");
        StringBuilder camelCaseString = new StringBuilder(parts[0]);

        // 将每个部分的首字母大写（除了第一个部分）
        for (int i = 1; i < parts.length; i++) {
            camelCaseString.append(parts[i].substring(0, 1).toUpperCase())
                    .append(parts[i].substring(1));
        }

        return camelCaseString.toString();
    }
}