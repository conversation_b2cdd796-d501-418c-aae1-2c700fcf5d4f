package com.ly.adp.csc.entities.dto.koc.wechat;

import java.io.Serializable;
import java.util.List;

/**
 * 企微端移除用户标签/达人类型请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class WechatRemoveUserLabelsRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String smartId;

    /**
     * 要移除的标签ID列表
     */
    private List<String> tagIds;

    /**
     * 要移除的达人类型ID列表
     */
    private List<String> expertTypeIds;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public List<String> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<String> tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getExpertTypeIds() {
        return expertTypeIds;
    }

    public void setExpertTypeIds(List<String> expertTypeIds) {
        this.expertTypeIds = expertTypeIds;
    }

    @Override
    public String toString() {
        return "WechatRemoveUserLabelsRequestDTO{" +
                "smartId='" + smartId + '\'' +
                ", tagIds=" + tagIds +
                ", expertTypeIds=" + expertTypeIds +
                '}';
    }
}
