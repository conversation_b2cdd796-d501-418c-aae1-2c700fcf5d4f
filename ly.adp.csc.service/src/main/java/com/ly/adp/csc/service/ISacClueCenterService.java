package com.ly.adp.csc.service;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.Map;

public interface ISacClueCenterService {
	/**
	 * 超24小时线索自动分配
	 * 
	 * @param info
	 * @return
	 */
	void clueAutoAsgnDbjob();

	/**
	 * 客户生日和交车纪念日刷新跟进时间
	 *
	 * @param info
	 * @return
	 */
	void clueAutoFollowTimeDistJob();

	/**
	 * 回访信息查询
	 *
	 * @param token token
	 * @param map   输入参数
	 * @return ListResult
	 */
	ListResult<Map<String, Object>> queryReviewInfo(ParamPage<Map<String, Object>> map, String token);

	/**
	 * 客户实时刷新、客户定时拉取更新线索
	 * 
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> receiveCustUpdateClueInfo(Map<String, Object> dataInfo, String token);
	Map<String, Object> custUpdateClueInfo(Map<String, Object> dataInfo, String token);

	/**
	 * 分页查询
	 * 
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacUserCluedlrbyquery(Map<String, Object> dataInfo, String token);

	/**
	 * 客制化线索查询，优化后的
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacUserCluedlrbyquery_performance(Map<String, Object> dataInfo, String token);

	/**
	 * 更新线索信息
	 * 
	 * @param info
	 * @return
	 */
	OptResult updateSacClue(Map<String, Object> dataInfo, String token);

	/**
	 * 更新线索信息
	 * 
	 * @param info
	 * @return
	 */
	OptResult specialFocusUpdateClue(Map<String, Object> dataInfo, String token);

	/**
	 * 本店待回访任务查询
	 * 
	 * @param token token
	 * @param map   输入参数
	 * @return ListResult
	 */
	ListResult<Map<String, Object>> queryListByDlr(ParamPage<Map<String, Object>> map, String token);
	
    /**
	 * 自动分配
	 * @param map
	 * @param token
	 * @return
	 */
	OptResult sacClueCenterAllot(Map<String, Object> map, String token);
	
	/**
	 * 城市线索更新战败
	 * 
	 * @param info
	 * @return
	 */
	OptResult updateCityClue(Map<String, Object> dataInfo, String token);
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacClueDeliver(Map<String, Object> dataInfo,String token);
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sleepCluePool(Map<String, Object> dataInfo,String token);
	
	EntityResult<Map<String, Object>> sacClueCompeteRecordInsertOne(Map<String, Object> dataInfo, String token);

	/**
	 * 获取线索记录查询
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacClueCompeteRecordQuery(ParamPage<Map<String, Object>> map, String token);

	/**
	 * 管辖门店获取线索开关查询
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacClueCompeteRecordManagedDlrQuery(ParamPage<Map<String, Object>> dateInfo, String authentication);

	/**
	 * 门店获取线索开关状态更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacClueCompeteRecordUpdateSwitchStatus(Map<String, Object> dataInfo,String token);

	/**
	 * 门店获取线索-抢单
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacClueCompeteRecordGetClue(Map<String, Object> dataInfo, String token);

	/**
	 * 所有门店获取线索开关查询
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacClueCompeteRecordDlrInfoQuery(ParamPage<Map<String, Object>> map, String token);

	/**
	 * 门店线索战败激活
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacClueDlrActivate(Map<String, Object> dataInfo, String token);

	EntityResult<Map<String, Object>> queryUserRaderMapInfo(Map<String, Object> dateInfo, String token);

	OptResult manualSleepClue(Map<String, Object> dateInfo, String authentication);
}
