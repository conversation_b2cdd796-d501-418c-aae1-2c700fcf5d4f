package com.ly.adp.csc.entities.enums;

/**
 * 关系类型枚举
 * <AUTHOR>
 * @since 2025-07-25
 */
public enum RelTypeEnum {
    TAG(1, "标签"),
    EXPERT_TYPE(2, "达人类型"),
    REMARK(3, "备注");

    private final Integer code;
    private final String description;

    RelTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static RelTypeEnum getByCode(Integer code) {
        for (RelTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
