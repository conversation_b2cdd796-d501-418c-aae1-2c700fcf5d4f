package com.ly.adp.csc.otherservice.entities.in;

import java.io.Serializable;

import com.ly.adp.csc.entities.in.PageInfo;

import io.swagger.annotations.ApiModelProperty;

public class MdmSmallCarTypeIn extends PageInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "车辆品牌编码", required = false, example = "")
    private String carBrandCode;
	@ApiModelProperty(value = "车系id", required = false, example = "")
    private String carSeriesId;
	@ApiModelProperty(value = "车型小类编码", required = false, example = "")
    private String smallCarTypeCode;
	@ApiModelProperty(value = "车型小类中文名称", required = false, example = "")
    private String smallCarTypeCn;
	public String getCarBrandCode() {
		return carBrandCode;
	}
	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}
	public String getCarSeriesId() {
		return carSeriesId;
	}
	public void setCarSeriesId(String carSeriesId) {
		this.carSeriesId = carSeriesId;
	}
	public String getSmallCarTypeCode() {
		return smallCarTypeCode;
	}
	public void setSmallCarTypeCode(String smallCarTypeCode) {
		this.smallCarTypeCode = smallCarTypeCode;
	}
	public String getSmallCarTypeCn() {
		return smallCarTypeCn;
	}
	public void setSmallCarTypeCn(String smallCarTypeCn) {
		this.smallCarTypeCn = smallCarTypeCn;
	}
	
	
}
