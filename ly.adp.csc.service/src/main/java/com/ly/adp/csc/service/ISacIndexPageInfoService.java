package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.dto.SwitchStatus;
import com.ly.adp.csc.entities.qry.CustomerTestDriveQry;
import com.ly.adp.csc.entities.vo.ClueReactivationStatVO;
import com.ly.adp.csc.entities.vo.CustomerTestDriveVO;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ISacIndexPageInfoService {
	
	/**
	 * CSC-查询账户的首页显示信息
	 * @param token
	 * @param paramMap
	 * @return
	 */
	EntityResult<Map<String, Object>> sacIndexPageInfoQueryByDlr(String token, Map <String, Object> paramMap);

	//查询首页消息显示
	EntityResult<Map<String, Object>> queryinfoMsgbydlr(String token, Map<String, Object> paramMap);
	
	/**
	 * 根据岗位获取员工信息
	 * @param token
	 * @param dateInfo
	 * @return
	 */
	ListResult<Map<String, Object>> locationEmpInfo(String token, Map<String, Object> dateInfo);

	/**
	 * 根据SmartID查询客户试驾信息
	 * @param dateInfo
	 * @return
	 */
	ListResult<CustomerTestDriveVO> queryCustomerTestDriveInfo(CustomerTestDriveQry dateInfo);

	
	EntityResult<Map<String, Object>> queryHeatRatio(String token, Map<String, Object> dateInfo);

	/**
	 * 查询首页线索热度比例-接口优化
	 * @param token
	 * @param dateInfo
	 * @return
	 */
	EntityResult<Map<String, Object>> queryHeatRatio_performance(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryAdpReportForm(String token, Map<String, Object> dateInfo);

	/**
	 * 接口性能优化
	 * @param token
	 * @param dateInfo
	 * @return
	 */
	EntityResult<Map<String, Object>> queryAdpReportForm_performance(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryTestDriveReport(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryActivityReport(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryReportBoardClueAndOrder(String token, Map<String, Object> dateInfo);
	
	EntityResult<Map<String, Object>> queryReportBoardDlr(String token, Map<String, Object> dateInfo);
	
	EntityResult<Map<String, Object>> queryReportBoardAct(String token, Map<String, Object> dateInfo);

	/**
	 * 看板指标-报表展板-活动数量 性能优化
	 * @param token
	 * @param dateInfo
	 * @return
	 */
	EntityResult<Map<String, Object>> queryReportBoardAct_performance(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryReportBoardCar(String token, Map<String, Object> dateInfo);
	EntityResult<Map<String, Object>> queryUserBoardCar(String token, Map<String, Object> dateInfo);

	EntityResult<Map<String, Object>> queryUserGroupClueReport(String token, Map<String, Object> dateInfo);

	OptResult export(Map<String, Object> dataInfo, String token, HttpServletResponse response);
	OptResult exportSaleConstantManagerReport(Map<String, Object> dataInfo, String token, HttpServletResponse response);
	OptResult exportSaleConstantManagerReport_performance(Map<String, Object> dataInfo, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> querySaleConstantReport(String token, Map<String, Object> dateInfo);
	ListResult<Map<String, Object>> querySaleConstantManagerReport(String token, Map<String, Object> dateInfo);
	ListResult<Map<String, Object>> querytestCarReport(String token, Map<String, Object> dateInfo);

	EntityResult<Boolean> switchStatus(SwitchStatus dataInfo);

	OptResult exportQuerytestCarReport(Map<String, Object> dateInfo, HttpServletResponse response, String token);

	ListResult<Map<String, Object>> queryClueStatisticalReport(String token, Map<String, Object> dateInfo);

	ListResult<Map<String, Object>> queryClueStatisticalReport_performance(String token, Map<String, Object> dateInfo);

	OptResult queryClueStatisticalReportExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

	OptResult queryClueStatisticalReportExport_performance(Map<String, Object> dataInfo, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> queryMarketActivityReport(String token, ParamPage<Map<String, Object>> paramPage);

	OptResult queryMarketActivityReportExport(ParamPage<Map<String, Object>> paramPage, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> queryClueDormancyReport(String token, ParamPage<Map<String, Object>> dataInfo);

	OptResult queryClueDormancyReportExport(ParamPage<Map<String, Object>> dataInfo, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> queryAllActivityReport(String token, ParamPage<Map<String, Object>> dataInfo);

	OptResult queryActivityReportExport(ParamPage<Map<String, Object>> dataInfo, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> queryAgentOfflineActivityReport(String token, ParamPage<Map<String, Object>> paramPage);

	ListResult<Map<String, Object>> queryAgentOnlineActivityReport(String token, ParamPage<Map<String, Object>> paramPage);

    OptResult queryAgentOfflineActivityReportExport(ParamPage<Map<String, Object>> dateInfo, String token, HttpServletResponse response);

	OptResult queryAgentOnlineActivityReportExport(ParamPage<Map<String, Object>> dateInfo, String token, HttpServletResponse response);

    OptResult getEvaluationDetailsExport(Map<String, Object> dateInfo, String token, HttpServletResponse response);

	ListResult<Map<String, Object>> getEvaluationDetails(Map<String, Object> dateInfo, String token, HttpServletResponse response);

	EntityResult<ClueReactivationStatVO> getReactivationStat(String token);
}
