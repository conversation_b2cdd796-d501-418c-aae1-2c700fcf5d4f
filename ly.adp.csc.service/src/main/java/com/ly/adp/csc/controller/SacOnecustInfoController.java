package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.LeadsEventVO;
import com.ly.adp.csc.entities.in.LeadsEventDTO;
import com.ly.adp.csc.entities.in.SacDlrDTO;
import com.ly.adp.csc.entities.in.SacOneCustResumeIn;
import com.ly.adp.csc.entities.in.SacOnecustInfoIn;
import com.ly.adp.csc.service.ISacOneCustResumeService;
import com.ly.adp.csc.service.ISacOnecustInfoService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Api(value = "CSC客户信息服务", tags = "CSC客户信息服务")
@RestController
@RequestMapping(value = "/ly/adp/csc/onecustinfo", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacOnecustInfoController {

    @Autowired
    ISacOnecustInfoService sacOnecustInfoService;
    @Autowired
    ISacOneCustResumeService sacOneCustResumeService;

    @ApiOperation(value = "客户信息查询", notes = "客户查询")
    @PostMapping(value = "/queryonecustinfo.do")
    public ListResult<Map<String, Object>> sacOnecustInfoFindInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) SacOnecustInfoIn dateInfo) {
        return BusicenInvoker.doList(() -> sacOnecustInfoService.sacOnecustInfoFindInfo(dateInfo)).result();
    }

    @ApiOperation(value = "线索等级查询", notes = "线索等级查询")
    @PostMapping(value = "/queryClueLevel.do")
    public EntityResult<Map<String, Object>> queryClueLevel(
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.queryClueLevel(dateInfo)).result();
    }

    @ApiOperation(value = "线索等级修改", notes = "线索等级修改")
    @PostMapping(value = "/updateClueLevel.do")
    public OptResult updateClueLevel(
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> sacOnecustInfoService.updateClueLevel(dateInfo)).result();
    }

    @ApiOperation(value = "营销平台话术库白名单", notes = "营销平台话术库白名单")
    @PostMapping(value = "/customerTagWhitelis.do")
    public OptResult customerTagWhitelis(
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return sacOnecustInfoService.customerTagWhitelis(dateInfo);
    }

    @ApiOperation(value = "客户档案自动填充信息查询", notes = "客户查询")
    @PostMapping(value = "/queryOneCustSupplementInfo.do")
    public ListResult<Map<String, Object>> queryOneCustSupplementInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> param) {
        return BusicenInvoker.doList(() -> sacOnecustInfoService.queryOneCustSupplementInfo(param)).result();
    }

    @ApiOperation(value = "客户信息维护", notes = "客户信息维护")
    @PostMapping(value = "/save.do")
    public EntityResult<Map<String, Object>> sacOnecustInfoSaveInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.sacOnecustInfoSaveInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "客户信息单修改", notes = "客户信息单修改")
    @PostMapping(value = "/updateOneCustInfo.do")
    public EntityResult<Map<String, Object>> updateOneCustInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.updateOneCustInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "客户信息单修改1", notes = "客户信息单修改1")
    @PostMapping(value = "/updateOneCustInfoS.do")
    public EntityResult<Map<String, Object>> updateOneCustInfoS(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.updateOneCustInfoS(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "客户履历查询", notes = "客户履历查询")
    @PostMapping(value = "/queryresume.do")
    public ListResult<Map<String, Object>> sacOnecustResumeFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) SacOneCustResumeIn dataInfo) {
        return BusicenInvoker.doList(() -> sacOneCustResumeService.sacOnecustResumeFindAll(dataInfo)).result();
    }

    @ApiOperation(value = "到店明细报表", notes = "到店明细报表")
    @PostMapping(value = "/queryArrivalStoreReport.do")
    public ListResult<Map<String, Object>> queryArrivalStoreReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody ParamPage<Map<String, Object>> param) {
        return BusicenInvoker.doList(() -> sacOneCustResumeService.queryArrivalStoreReport(param, token)).result();
    }

    @ApiOperation(value = "到店明细报表导出", notes = "到店明细报表导出")
    @RequestMapping(value = "/exportArrivalStoreReport.do", method = RequestMethod.POST)
    public OptResult exportArrivalStoreReport(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                              @RequestBody ParamPage<Map<String, Object>> param,
                                              HttpServletResponse response) throws Exception {
        return sacOneCustResumeService.exportArrivalStoreReport(param, response, authentication);
    }

    @ApiOperation(value = "关键事件查询", notes = "关键事件查询")
    @PostMapping(value = "/queryLeadsEvent.do")
    public ListResult<LeadsEventVO> queryLeadsEvent(
            @RequestBody(required = false) LeadsEventDTO dataInfo) {
        return sacOneCustResumeService.queryLeadsEvent(dataInfo);
    }


    @ApiOperation(value = "客户虚拟通话记录查询", notes = "客户虚拟通话记录查询")
    @PostMapping(value = "/queryVirtualRecord.do")
    public ListResult<Map<String, Object>> queryVirtualRecord(
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacOneCustResumeService.queryVirtualRecord(dataInfo)).result();
    }

    @ApiOperation(value = "客户履历查询", notes = "客户履历查询")
    @PostMapping(value = "/queryresume.todo")
    public ListResult<Map<String, Object>> sacOnecustResumeFindAll(
            @RequestBody(required = false) SacOneCustResumeIn dataInfo) {
        return BusicenInvoker.doList(() -> sacOneCustResumeService.sacQueryResumeFindAllbyphone(dataInfo)).result();
    }
    @ApiOperation(value = "根据订单号查询线索锁单的一些信息", notes = "根据订单号查询线索锁单的一些信息")
    @PostMapping(value = "/queryDlrLockInfo.todo")
    public ListResult<Map<String, Object>> queryDlrLockInfo(
            @RequestBody SacDlrDTO dataInfo) {
        return BusicenInvoker.doList(() -> sacOneCustResumeService.queryDlrName(dataInfo)).result();
    }

    @ApiOperation(value = "去电", notes = "去电")
    @RequestMapping(value = "queryFastPhone.do", method = RequestMethod.POST)
    public EntityResult<Integer> queryPhone(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOneCustResumeService.queryFastPhoneo(dateInfo)).result();
    }

    @ApiOperation(value = "客户履历保存", notes = "客户履历保存")
    @PostMapping(value = "/resumesave.do")
    public EntityResult<Map<String, Object>> sacOnecustResumeSaveInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOneCustResumeService.sacOnecustResumeSaveInfo(dateInfo, authentication)).result();

    }

    @ApiOperation(value = "个人二维码从市场活动进入直接报名", notes = "个人二维码从市场活动进入直接报名")
    @RequestMapping(value = "/activityApply.do", method = RequestMethod.POST)
    public OptResult activityApply(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                   @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition) {
        return com.ly.mp.busi.base.context.BusicenInvoker.doOpt(() -> sacOneCustResumeService.activityApply(queryCondition.getParam(), authentication)).result();
    }

    @ApiOperation(value = "个人二维码活动报名数据保存", notes = "个人二维码活动报名数据保存")
    @PostMapping(value = "/activityApplyInfoSave.do")
    public EntityResult<Map<String, Object>> activityApplyInfoSave(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOneCustResumeService.activityApplyInfoSave(dateInfo, authentication)).result();

    }

    @ApiOperation(value = "客户履历批量保存", notes = "客户履历批量保存")
    @PostMapping(value = "/resumesaveBanch.do")
    public OptResult sacOnecustResumeSaveBanchInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) List<Map<String, Object>> dateInfo) {
        return BusicenInvoker.doOpt(() -> sacOneCustResumeService.sacOnecustResumeSaveBanchInfo(dateInfo, authentication)).result();


    }

    @ApiOperation(value = "客户信息变更记录查询", notes = "客户信息变更记录查询")
    @PostMapping(value = "/querycustlog.do")
    public ListResult<Map<String, Object>> sacOnecustChangeLogFindInfo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) SacOnecustInfoIn dateInfo) {
        return BusicenInvoker.doList(() -> sacOnecustInfoService.sacOnecustChangeLogFindInfo(dateInfo, token)).result();
    }

    @ApiOperation(value = "客户信息变更记录定时(临时)", notes = "客户信息变更记录定时(临时)")
    @PostMapping(value = "/logdbjob.do")
    public void SacOnecustChangeLogDbjob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
        sacOnecustInfoService.SacOnecustChangeLogDbjob();
    }

    @ApiOperation(value = "客户信息刷新保存", notes = "客户信息刷新保存")
    @PostMapping(value = "/SacOnecustRefresh.do")
    public EntityResult<Map<String, Object>> SacOnecustRefresh(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.SacOnecustRefresh(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "CDP参数加密解密", notes = "CDP参数加密解密")
    @PostMapping(value = "/crackCipher")
    public OptResult crackCipher(@RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> sacOnecustInfoService.crackCipher(dateInfo)).result();
    }

    @ApiOperation(value = "获取ec系统token", notes = "获取ec系统token")
    @PostMapping(value = "/getEcToken.do")
    public EntityResult<Map<String, Object>> getEcToken(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.getEcToken(authentication)).result();
    }

    @ApiOperation(value = "获取cdp系统token", notes = "获取cdp系统token")
    @PostMapping(value = "/getCdpToken.do")
    public EntityResult<Map<String, Object>> getCdpToken(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.getCdpToken(authentication)).result();
    }

    @ApiOperation(value = "ec系统获取车辆型号", notes = "ec系统获取车辆型号")
    @GetMapping(value = "/queryVehicleVersions.do")
    public EntityResult<Map<String, Object>> queryVehicleVersions(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.queryVehicleVersions(authentication)).result();
    }

    @ApiOperation(value = "ec系统获取计算结果", notes = "ec系统获取计算结果")
    @PostMapping(value = "/queryCalculate.do")
    public EntityResult<Map<String, Object>> queryCalculate(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.queryCalculate(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "ec系统获取金融方案", notes = "ec系统获取金融方案")
    @PostMapping(value = "/queryFinanceScheme.do")
    public EntityResult<Map<String, Object>> queryFinanceScheme(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacOnecustInfoService.queryFinanceScheme(dateInfo, authentication)).result();
    }
}
