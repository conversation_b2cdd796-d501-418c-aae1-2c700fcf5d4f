package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.vo.koc.ExpertTypeVO;
import com.ly.adp.csc.entities.vo.koc.RemarkVO;
import com.ly.adp.csc.entities.vo.koc.TagVO;

import java.util.List;
import java.util.Map;

/**
 * 用户标签缓存服务接口
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface IUserTagCacheService {

    /**
     * 批量获取用户标签信息
     * @param smartIds 用户ID列表
     * @return 用户ID -> 标签列表的映射
     */
    Map<String, List<TagVO>> getBatchUserTags(List<String> smartIds);

    /**
     * 批量获取用户达人类型信息
     * @param smartIds 用户ID列表
     * @return 用户ID -> 达人类型列表的映射
     */
    Map<String, List<ExpertTypeVO>> getBatchUserExpertTypes(List<String> smartIds);

    /**
     * 批量获取用户备注信息
     * @param smartIds 用户ID列表
     * @return 用户ID -> 备注列表的映射
     */
    Map<String, List<RemarkVO>> getBatchUserRemarks(List<String> smartIds);

    /**
     * 获取单个用户的标签信息
     * @param smartId 用户ID
     * @return 标签列表
     */
    List<TagVO> getUserTags(String smartId);

    /**
     * 获取单个用户的达人类型信息
     * @param smartId 用户ID
     * @return 达人类型列表
     */
    List<ExpertTypeVO> getUserExpertTypes(String smartId);

    /**
     * 获取单个用户的备注信息
     * @param smartId 用户ID
     * @return 备注列表
     */
    List<RemarkVO> getUserRemarks(String smartId);

    /**
     * 刷新用户标签缓存
     * @param smartId 用户ID
     */
    void refreshUserTagCache(String smartId);

    /**
     * 批量刷新用户标签缓存
     * @param smartIds 用户ID列表
     */
    void refreshBatchUserTagCache(List<String> smartIds);
}
