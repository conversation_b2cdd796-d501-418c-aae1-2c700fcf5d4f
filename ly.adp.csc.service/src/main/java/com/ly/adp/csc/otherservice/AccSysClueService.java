package com.ly.adp.csc.otherservice;

import com.ly.mp.acc.manage.otherservice.IAccSysClueService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@Service
public class AccSysClueService implements IAccSysClueService {
    @Autowired
    IAccClueFeignClient accClueFeignClient;
    @Autowired
    IBasedataFeignClient basedataFeignClient;

    @Override
    public EntityResult<Map<String, Object>> clueDlrCheckRepeat(String authentication,
                                                                ParamBase<Map<String, Object>> queryCondition) {
        return accClueFeignClient.clueDlrCheckRepeat(authentication, queryCondition);
    }

    @Override
    public EntityResult<Map<String, Object>> clueDlrCheckRepeat2(String authentication,
                                                                 ParamBase<Map<String, Object>> queryCondition) {
        return accClueFeignClient.clueDlrCheckRepeat2(authentication, queryCondition);
    }

    @Override
    public EntityResult<Map<String, Object>> clueDlrSave(String authentication, ParamBase<Map<String, Object>> queryCondition) {
        return accClueFeignClient.clueDlrSave(authentication, queryCondition);
    }

    @Override
    public EntityResult<Map<String, Object>> clueDlrSave2(String authentication, ParamBase<Map<String, Object>> queryCondition) {
        return accClueFeignClient.clueDlrSave2(authentication, queryCondition);
    }

    @Override
    public ListResult<Map<String, Object>> queryConfigList(String authentication, ParamPage<Map<String, Object>> dataInfo) {
        return accClueFeignClient.queryConfigList(authentication, dataInfo);
    }

    @Override
    public ListResult<Map<String, Object>> querycityinfobydlr(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.querycityinfobydlr(authentication, dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> querymanagedlr(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.querymanagedlr(authentication, dateInfo);
    }

    @Override
    public OptResult createMpTokenInfo(@RequestBody(required = false) Map<String, Object> dateInfo) {
        return basedataFeignClient.createMpTokenInfo(dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> querycitydlr(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.querycitydlr(authentication, dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> queryAreaCityRelation(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.queryAreaCityRelation(authentication, dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> queryUserAreaRelation(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.queryUserAreaRelation(authentication, dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> queryUserCityRelation(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.queryUserCityRelation(authentication, dateInfo);
    }

    @Override
    public ListResult<Map<String, Object>> bigAreaInfoQuery(String authentication, ParamPage<Map<String, Object>> mapParam) {
        return basedataFeignClient.bigAreaInfoQuery(authentication, mapParam);
    }

    @Override
    public EntityResult<Map<String, Object>> sacAttachmentAdd(String authentication,
                                                              ParamBase<Map<String, Object>> queryCondition) {
        return accClueFeignClient.sacAttachmentAdd(authentication, queryCondition);
    }

    @Override
    public String selectOBSFilePath(String authentication, String filePathm) {
        return basedataFeignClient.selectOBSFilePath(authentication, filePathm);
    }
}
