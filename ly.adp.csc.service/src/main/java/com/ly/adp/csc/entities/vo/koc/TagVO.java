package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;

/**
 * 标签VO
 * <AUTHOR>
 * @since 2025-07-28
 */
public class TagVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 完整路径（用于显示层级）
     */
    private String fullPath;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    @Override
    public String toString() {
        return "WechatTagVO{" +
                "tagId='" + tagId + '\'' +
                ", tagName='" + tagName + '\'' +
                ", fullPath='" + fullPath + '\'' +
                '}';
    }
}
