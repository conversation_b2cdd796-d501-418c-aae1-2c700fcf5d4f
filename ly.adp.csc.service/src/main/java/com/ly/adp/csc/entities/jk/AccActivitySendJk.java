package com.ly.adp.csc.entities.jk;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONField;

public class AccActivitySendJk implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 活动主键 */
	@JSONField(name="activityId")
	private String activityId;
	/** 活动名称 */
	@JSONField(name="activityName")
	private String activityName;
	/** 活动目的 */
	@JSONField(name="activityPurpose")
	private String activityPurpose;
	/** 活动创建类型编码 */
	@JSONField(name="createTypeCode")
	private String createTypeCode;
	/** 活动创建类型名称 */
	@JSONField(name="createTypeName")
	private String createTypeName;
	/** 活动状态编码 */
	@JSONField(name="statusCode")
	private String statusCode;
	/** 活动状态名称 */
	@JSONField(name="statusName")
	private String statusName;
	/** 活动发布状态编码 */
	@JSONField(name="releaseStatusCode")
	private String releaseStatusCode;
	/** 活动发布状态名称 */
	@JSONField(name="releaseStatusName")
	private String releaseStatusName;
	/** 经销商编码 */
	@JSONField(name="dlrCode")
	private String dlrCode;
	/** 经销商名称 */
	@JSONField(name="dlrShortName")
	private String dlrShortName;

	/** 支持门店名称 */
	@JSONField(name="dlrSupportedShortName")
	private String dlrSupportedShortName;
	/** 支持门店编码 */
	@JSONField(name="dlrSupportedCode")
	private String dlrSupportedCode;

	/** 门店城市编码 */
	@JSONField(name="dlrCityCode")
	private String dlrCityCode;
	/** 门店城市名称 */
	@JSONField(name="dlrCityName")
	private String dlrCityName;
	/** 门店区域编码 */
	@JSONField(name="dlrRegionCode")
	private String dlrRegionCode;
	/** 门店区域名称 */
	@JSONField(name="dlrRegionName")
	private String dlrRegionName;
	/** 门店空间编码 */
	@JSONField(name="dlrSpaceCode")
	private String dlrSpaceCode;
	/** 门店空间名称 */
	@JSONField(name="dlrSpaceName")
	private String dlrSpaceName;
	/** 场地使用开始时间 */
	@JSONField(name="dlrAreaUsedStartTime")
	private LocalDateTime dlrAreaUsedStartTime;
	/** 场地使用时长 */
	@JSONField(name="dlrAreaUsedLengthOfTime")
	private Integer dlrAreaUsedLengthOfTime;
	/** 具体地址 */
	@JSONField(name="dlrAddressDetail")
	private String dlrAddressDetail;
	/** 活动地点编码 */
	@JSONField(name="addressTypeCode")
	private String addressTypeCode;
	/** 活动地点名称 */
	@JSONField(name="addressTypeName")
	private String addressTypeName;
	/** 活动类型编码 */
	@JSONField(name="activityTypeCode")
	private String activityTypeCode;
	/** 活动类型名称 */
	@JSONField(name="activityTypeName")
	private String activityTypeName;
	/** 活动子类型编码 */
	@JSONField(name="typesCode.code")
	private String activitySubtypeCode;
	/** 活动子类型名称 */
	@JSONField(name="typesCode.name")
	private String activitySubtypeName;
	/** 活动资源编码 */
	@JSONField(name="activityResourceCode")
	private String activityResourceCode;
	/** 活动资源名称 */
	@JSONField(name="activityResourceName")
	private String activityResourceName;
	/** 活动提示时间编码 */
	@JSONField(name="tipsTimeCode")
	private String tipsTimeCode;
	/** 活动提示时间名称 */
	@JSONField(name="tipsTimeName")
	private String tipsTimeName;
	/** 取消报名时间编码 */
	@JSONField(name="applyCancelTimeCode")
	private String applyCancelTimeCode;
	/** 取消报名时间名称 */
	@JSONField(name="applyCancelTimeName")
	private String applyCancelTimeName;
	/** 支持人数 */
	@JSONField(name="numberOfPersonSupported")
	private Integer numberOfPersonSupported;
	/** 试驾车支持 */
	@JSONField(name="testDriveSupported")
	private String testDriveSupported;
	/** 活动预算 */
	@JSONField(name="budget")
	private BigDecimal budget;
	/** 活动预算明细 */
	@JSONField(name="budgetDetail")
	private String budgetDetail;
	/** 活动开始时间 */
	@JSONField(name="beginTime")
	private LocalDateTime beginTime;
	/** 活动结束时间 */
	@JSONField(name="endTime")
	private LocalDateTime endTime;
	/** 报名结束时间 */
	@JSONField(name="applyEndTime")
	private LocalDateTime applyEndTime;
	/** 活动发布时间 */
	@JSONField(name="publishTime")
	private LocalDateTime publishTime;
	/** 活动封面 */
	@JSONField(name="activityCoverPageUrl")
	private String activityCoverPageUrl;
	/** 活动介绍 */
	@JSONField(name="activityIntroduction")
	private String activityIntroduction;
	/** 温馨提示 */
	@JSONField(name="activityKindlyReminder")
	private String activityKindlyReminder;

	/** 资源分配顾问 */
	@JSONField(name="resourceAdviser")
	private String resourceAdviser;
	/** 资源分配车辆 */
	@JSONField(name="resourceCar")
	private String resourceCar;
	/** 资源分配支持时间开始 */
	@JSONField(name="resourceSupportedTimeStart")
	private LocalDateTime resourceSupportedTimeStart;
	/** 资源分配支持时间结束 */
	@JSONField(name="resourceSupportedTimeEnd")
	private LocalDateTime resourceSupportedTimeEnd;

	/** 核销状态名称 */
	@JSONField(name="hxStatusName")
	private String hxStatusName;
	/** 核销状态编码 */
	@JSONField(name="hxStatusCode")
	private String hxStatusCode;
	/** 上传发票 */
	@JSONField(name="fapiao")
	private String fapiao;
	/** 实际费用 */
	@JSONField(name="actualCost")
	private BigDecimal actualCost;
	/** 扩展字段1 */
	@JSONField(name="column1")
	private String column1;
	/** 扩展字段2 */
	@JSONField(name="column2")
	private String column2;
	/** 扩展字段3 */
	@JSONField(name="column3")
	private String column3;
	/** 扩展字段4 */
	@JSONField(name="column4")
	private String column4;
	/** 扩展字段5 */
	@JSONField(name="column5")
	private String column5;
	/** 扩展字段6 */
	@JSONField(name="column6")
	private String column6;
	/** 扩展字段7 */
	@JSONField(name="column7")
	private String column7;
	/** 扩展字段8 */
	@JSONField(name="column8")
	private String column8;
	/** 扩展字段9 */
	@JSONField(name="column9")
	private String column9;
	/** 扩展字段10 */
	@JSONField(name="column10")
	private String column10;

	/** 扩展字段11 */
	@JSONField(name="column11")
	private String column11;
	/** 扩展字段12 */
	@JSONField(name="column12")
	private String column12;
	/** 扩展字段13 */
	@JSONField(name="column13")
	private String column13;
	/** 扩展字段14 */
	@JSONField(name="column14")
	private String column14;
	/** 扩展字段15 */
	@JSONField(name="column15")
	private String column15;
	/** 扩展字段16 */
	@JSONField(name="column16")
	private String column16;
	/** 扩展字段17 */
	@JSONField(name="column17")
	private String column17;
	/** 扩展字段18 */
	@JSONField(name="column18")
	private String column18;
	/** 扩展字段19 */
	@JSONField(name="column19")
	private String column19;
	/** 扩展字段20 */
	@JSONField(name="column20")
	private String column20;

	/** JSON扩展字段 */
	@JSONField(name="extendsJson")
	private String extendsJson;
	/** 创建人ID */
	@JSONField(name="creator")
	private String creator;
	/** 创建人 */
	@JSONField(name="createdName")
	private String createdName;
	/** 创建日期 */
	@JSONField(name="createdDate")
	private LocalDateTime createdDate;
	/** 修改人ID */
	@JSONField(name="modifier")
	private String modifier;
	/** 修改人 */
	@JSONField(name="modifyName")
	private String modifyName;
	/** 最后更新日期 */
	@JSONField(name="lastUpdatedDate")
	private LocalDateTime lastUpdatedDate;
	/** 是否可用 */
	@JSONField(name="isEnable")
	private String isEnable;
	/** 并发控制ID */
	@JSONField(name="updateControlId")
	private String updateControlId;
	
	public String getActivityId() {
		return activityId;
	}
	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}
	public String getActivityName() {
		return activityName;
	}
	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}
	public String getCreateTypeCode() {
		return createTypeCode;
	}
	public void setCreateTypeCode(String createTypeCode) {
		this.createTypeCode = createTypeCode;
	}
	public String getCreateTypeName() {
		return createTypeName;
	}
	public void setCreateTypeName(String createTypeName) {
		this.createTypeName = createTypeName;
	}
	public String getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}
	public String getStatusName() {
		return statusName;
	}
	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	public String getReleaseStatusCode() {
		return releaseStatusCode;
	}
	public void setReleaseStatusCode(String releaseStatusCode) {
		this.releaseStatusCode = releaseStatusCode;
	}
	public String getReleaseStatusName() {
		return releaseStatusName;
	}
	public void setReleaseStatusName(String releaseStatusName) {
		this.releaseStatusName = releaseStatusName;
	}
	public String getDlrCode() {
		return dlrCode;
	}
	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}
	public String getDlrShortName() {
		return dlrShortName;
	}
	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}
	public String getDlrSupportedShortName() {
		return dlrSupportedShortName;
	}
	public void setDlrSupportedShortName(String dlrSupportedShortName) {
		this.dlrSupportedShortName = dlrSupportedShortName;
	}
	public String getDlrSupportedCode() {
		return dlrSupportedCode;
	}
	public void setDlrSupportedCode(String dlrSupportedCode) {
		this.dlrSupportedCode = dlrSupportedCode;
	}
	public String getDlrCityCode() {
		return dlrCityCode;
	}
	public void setDlrCityCode(String dlrCityCode) {
		this.dlrCityCode = dlrCityCode;
	}
	public String getDlrCityName() {
		return dlrCityName;
	}
	public void setDlrCityName(String dlrCityName) {
		this.dlrCityName = dlrCityName;
	}
	public String getDlrRegionCode() {
		return dlrRegionCode;
	}
	public void setDlrRegionCode(String dlrRegionCode) {
		this.dlrRegionCode = dlrRegionCode;
	}
	public String getDlrRegionName() {
		return dlrRegionName;
	}
	public void setDlrRegionName(String dlrRegionName) {
		this.dlrRegionName = dlrRegionName;
	}
	public String getDlrSpaceCode() {
		return dlrSpaceCode;
	}
	public void setDlrSpaceCode(String dlrSpaceCode) {
		this.dlrSpaceCode = dlrSpaceCode;
	}
	public String getDlrSpaceName() {
		return dlrSpaceName;
	}
	public void setDlrSpaceName(String dlrSpaceName) {
		this.dlrSpaceName = dlrSpaceName;
	}
	public LocalDateTime getDlrAreaUsedStartTime() {
		return dlrAreaUsedStartTime;
	}
	public void setDlrAreaUsedStartTime(LocalDateTime dlrAreaUsedStartTime) {
		this.dlrAreaUsedStartTime = dlrAreaUsedStartTime;
	}
	public Integer getDlrAreaUsedLengthOfTime() {
		return dlrAreaUsedLengthOfTime;
	}
	public void setDlrAreaUsedLengthOfTime(Integer dlrAreaUsedLengthOfTime) {
		this.dlrAreaUsedLengthOfTime = dlrAreaUsedLengthOfTime;
	}
	public String getDlrAddressDetail() {
		return dlrAddressDetail;
	}
	public void setDlrAddressDetail(String dlrAddressDetail) {
		this.dlrAddressDetail = dlrAddressDetail;
	}
	public String getAddressTypeCode() {
		return addressTypeCode;
	}
	public void setAddressTypeCode(String addressTypeCode) {
		this.addressTypeCode = addressTypeCode;
	}
	public String getAddressTypeName() {
		return addressTypeName;
	}
	public void setAddressTypeName(String addressTypeName) {
		this.addressTypeName = addressTypeName;
	}
	public String getActivityTypeCode() {
		return activityTypeCode;
	}
	public void setActivityTypeCode(String activityTypeCode) {
		this.activityTypeCode = activityTypeCode;
	}
	public String getActivityTypeName() {
		return activityTypeName;
	}
	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}
	public String getActivitySubtypeCode() {
		return activitySubtypeCode;
	}
	public void setActivitySubtypeCode(String activitySubtypeCode) {
		this.activitySubtypeCode = activitySubtypeCode;
	}
	public String getActivitySubtypeName() {
		return activitySubtypeName;
	}
	public void setActivitySubtypeName(String activitySubtypeName) {
		this.activitySubtypeName = activitySubtypeName;
	}
	public String getActivityResourceCode() {
		return activityResourceCode;
	}
	public void setActivityResourceCode(String activityResourceCode) {
		this.activityResourceCode = activityResourceCode;
	}
	public String getActivityResourceName() {
		return activityResourceName;
	}
	public void setActivityResourceName(String activityResourceName) {
		this.activityResourceName = activityResourceName;
	}
	public String getTipsTimeCode() {
		return tipsTimeCode;
	}
	public void setTipsTimeCode(String tipsTimeCode) {
		this.tipsTimeCode = tipsTimeCode;
	}
	public String getTipsTimeName() {
		return tipsTimeName;
	}
	public void setTipsTimeName(String tipsTimeName) {
		this.tipsTimeName = tipsTimeName;
	}
	public String getApplyCancelTimeCode() {
		return applyCancelTimeCode;
	}
	public void setApplyCancelTimeCode(String applyCancelTimeCode) {
		this.applyCancelTimeCode = applyCancelTimeCode;
	}
	public String getApplyCancelTimeName() {
		return applyCancelTimeName;
	}
	public void setApplyCancelTimeName(String applyCancelTimeName) {
		this.applyCancelTimeName = applyCancelTimeName;
	}
	public Integer getNumberOfPersonSupported() {
		return numberOfPersonSupported;
	}
	public void setNumberOfPersonSupported(Integer numberOfPersonSupported) {
		this.numberOfPersonSupported = numberOfPersonSupported;
	}
	public String getTestDriveSupported() {
		return testDriveSupported;
	}
	public void setTestDriveSupported(String testDriveSupported) {
		this.testDriveSupported = testDriveSupported;
	}
	public BigDecimal getBudget() {
		return budget;
	}
	public void setBudget(BigDecimal budget) {
		this.budget = budget;
	}
	public String getBudgetDetail() {
		return budgetDetail;
	}
	public void setBudgetDetail(String budgetDetail) {
		this.budgetDetail = budgetDetail;
	}
	public LocalDateTime getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(LocalDateTime beginTime) {
		this.beginTime = beginTime;
	}
	public LocalDateTime getEndTime() {
		return endTime;
	}
	public void setEndTime(LocalDateTime endTime) {
		this.endTime = endTime;
	}
	public LocalDateTime getApplyEndTime() {
		return applyEndTime;
	}
	public void setApplyEndTime(LocalDateTime applyEndTime) {
		this.applyEndTime = applyEndTime;
	}
	public LocalDateTime getPublishTime() {
		return publishTime;
	}
	public void setPublishTime(LocalDateTime publishTime) {
		this.publishTime = publishTime;
	}
	public String getActivityCoverPageUrl() {
		return activityCoverPageUrl;
	}
	public void setActivityCoverPageUrl(String activityCoverPageUrl) {
		this.activityCoverPageUrl = activityCoverPageUrl;
	}
	public String getActivityIntroduction() {
		return activityIntroduction;
	}
	public void setActivityIntroduction(String activityIntroduction) {
		this.activityIntroduction = activityIntroduction;
	}
	public String getActivityKindlyReminder() {
		return activityKindlyReminder;
	}
	public void setActivityKindlyReminder(String activityKindlyReminder) {
		this.activityKindlyReminder = activityKindlyReminder;
	}
	public String getResourceAdviser() {
		return resourceAdviser;
	}
	public void setResourceAdviser(String resourceAdviser) {
		this.resourceAdviser = resourceAdviser;
	}
	public String getResourceCar() {
		return resourceCar;
	}
	public void setResourceCar(String resourceCar) {
		this.resourceCar = resourceCar;
	}
	public LocalDateTime getResourceSupportedTimeStart() {
		return resourceSupportedTimeStart;
	}
	public void setResourceSupportedTimeStart(LocalDateTime resourceSupportedTimeStart) {
		this.resourceSupportedTimeStart = resourceSupportedTimeStart;
	}
	public LocalDateTime getResourceSupportedTimeEnd() {
		return resourceSupportedTimeEnd;
	}
	public void setResourceSupportedTimeEnd(LocalDateTime resourceSupportedTimeEnd) {
		this.resourceSupportedTimeEnd = resourceSupportedTimeEnd;
	}
	public String getHxStatusName() {
		return hxStatusName;
	}
	public void setHxStatusName(String hxStatusName) {
		this.hxStatusName = hxStatusName;
	}
	public String getHxStatusCode() {
		return hxStatusCode;
	}
	public void setHxStatusCode(String hxStatusCode) {
		this.hxStatusCode = hxStatusCode;
	}
	public String getFapiao() {
		return fapiao;
	}
	public void setFapiao(String fapiao) {
		this.fapiao = fapiao;
	}
	public BigDecimal getActualCost() {
		return actualCost;
	}
	public void setActualCost(BigDecimal actualCost) {
		this.actualCost = actualCost;
	}
	public String getColumn1() {
		return column1;
	}
	public void setColumn1(String column1) {
		this.column1 = column1;
	}
	public String getColumn2() {
		return column2;
	}
	public void setColumn2(String column2) {
		this.column2 = column2;
	}
	public String getColumn3() {
		return column3;
	}
	public void setColumn3(String column3) {
		this.column3 = column3;
	}
	public String getColumn4() {
		return column4;
	}
	public void setColumn4(String column4) {
		this.column4 = column4;
	}
	public String getColumn5() {
		return column5;
	}
	public void setColumn5(String column5) {
		this.column5 = column5;
	}
	public String getColumn6() {
		return column6;
	}
	public void setColumn6(String column6) {
		this.column6 = column6;
	}
	public String getColumn7() {
		return column7;
	}
	public void setColumn7(String column7) {
		this.column7 = column7;
	}
	public String getColumn8() {
		return column8;
	}
	public void setColumn8(String column8) {
		this.column8 = column8;
	}
	public String getColumn9() {
		return column9;
	}
	public void setColumn9(String column9) {
		this.column9 = column9;
	}
	public String getColumn10() {
		return column10;
	}
	public void setColumn10(String column10) {
		this.column10 = column10;
	}
	public String getColumn11() {
		return column11;
	}
	public void setColumn11(String column11) {
		this.column11 = column11;
	}
	public String getColumn12() {
		return column12;
	}
	public void setColumn12(String column12) {
		this.column12 = column12;
	}
	public String getColumn13() {
		return column13;
	}
	public void setColumn13(String column13) {
		this.column13 = column13;
	}
	public String getColumn14() {
		return column14;
	}
	public void setColumn14(String column14) {
		this.column14 = column14;
	}
	public String getColumn15() {
		return column15;
	}
	public void setColumn15(String column15) {
		this.column15 = column15;
	}
	public String getColumn16() {
		return column16;
	}
	public void setColumn16(String column16) {
		this.column16 = column16;
	}
	public String getColumn17() {
		return column17;
	}
	public void setColumn17(String column17) {
		this.column17 = column17;
	}
	public String getColumn18() {
		return column18;
	}
	public void setColumn18(String column18) {
		this.column18 = column18;
	}
	public String getColumn19() {
		return column19;
	}
	public void setColumn19(String column19) {
		this.column19 = column19;
	}
	public String getColumn20() {
		return column20;
	}
	public void setColumn20(String column20) {
		this.column20 = column20;
	}
	public String getExtendsJson() {
		return extendsJson;
	}
	public void setExtendsJson(String extendsJson) {
		this.extendsJson = extendsJson;
	}
	
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public String getCreatedName() {
		return createdName;
	}
	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}
	public LocalDateTime getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public String getModifyName() {
		return modifyName;
	}
	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}
	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}
	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}
	public String getIsEnable() {
		return isEnable;
	}
	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}
	public String getUpdateControlId() {
		return updateControlId;
	}
	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}
	public String getActivityPurpose() {
		return activityPurpose;
	}
	public void setActivityPurpose(String activityPurpose) {
		this.activityPurpose = activityPurpose;
	}
	@Override
	public String toString() {
		return "AccActivitySendJk [activityId=" + activityId + ", activityName=" + activityName + ", activityPurpose="
				+ activityPurpose + ", createTypeCode=" + createTypeCode + ", createTypeName=" + createTypeName
				+ ", statusCode=" + statusCode + ", statusName=" + statusName + ", releaseStatusCode="
				+ releaseStatusCode + ", releaseStatusName=" + releaseStatusName + ", dlrCode=" + dlrCode
				+ ", dlrShortName=" + dlrShortName + ", dlrSupportedShortName=" + dlrSupportedShortName
				+ ", dlrSupportedCode=" + dlrSupportedCode + ", dlrCityCode=" + dlrCityCode + ", dlrCityName="
				+ dlrCityName + ", dlrRegionCode=" + dlrRegionCode + ", dlrRegionName=" + dlrRegionName
				+ ", dlrSpaceCode=" + dlrSpaceCode + ", dlrSpaceName=" + dlrSpaceName + ", dlrAreaUsedStartTime="
				+ dlrAreaUsedStartTime + ", dlrAreaUsedLengthOfTime=" + dlrAreaUsedLengthOfTime + ", dlrAddressDetail="
				+ dlrAddressDetail + ", addressTypeCode=" + addressTypeCode + ", addressTypeName=" + addressTypeName
				+ ", activityTypeCode=" + activityTypeCode + ", activityTypeName=" + activityTypeName
				+ ", activitySubtypeCode=" + activitySubtypeCode + ", activitySubtypeName=" + activitySubtypeName
				+ ", activityResourceCode=" + activityResourceCode + ", activityResourceName=" + activityResourceName
				+ ", tipsTimeCode=" + tipsTimeCode + ", tipsTimeName=" + tipsTimeName + ", applyCancelTimeCode="
				+ applyCancelTimeCode + ", applyCancelTimeName=" + applyCancelTimeName + ", numberOfPersonSupported="
				+ numberOfPersonSupported + ", testDriveSupported=" + testDriveSupported + ", budget=" + budget
				+ ", budgetDetail=" + budgetDetail + ", beginTime=" + beginTime + ", endTime=" + endTime
				+ ", applyEndTime=" + applyEndTime + ", publishTime=" + publishTime + ", activityCoverPageUrl="
				+ activityCoverPageUrl + ", activityIntroduction=" + activityIntroduction + ", activityKindlyReminder="
				+ activityKindlyReminder + ", resourceAdviser=" + resourceAdviser + ", resourceCar=" + resourceCar
				+ ", resourceSupportedTimeStart=" + resourceSupportedTimeStart + ", resourceSupportedTimeEnd="
				+ resourceSupportedTimeEnd + ", hxStatusName=" + hxStatusName + ", hxStatusCode=" + hxStatusCode
				+ ", fapiao=" + fapiao + ", actualCost=" + actualCost + ", column1=" + column1 + ", column2=" + column2
				+ ", column3=" + column3 + ", column4=" + column4 + ", column5=" + column5 + ", column6=" + column6
				+ ", column7=" + column7 + ", column8=" + column8 + ", column9=" + column9 + ", column10=" + column10
				+ ", column11=" + column11 + ", column12=" + column12 + ", column13=" + column13 + ", column14="
				+ column14 + ", column15=" + column15 + ", column16=" + column16 + ", column17=" + column17
				+ ", column18=" + column18 + ", column19=" + column19 + ", column20=" + column20 + ", extendsJson="
				+ extendsJson + ", creator=" + creator + ", createdName=" + createdName + ", createdDate=" + createdDate
				+ ", modifier=" + modifier + ", modifyName=" + modifyName + ", lastUpdatedDate=" + lastUpdatedDate
				+ ", isEnable=" + isEnable + ", updateControlId=" + updateControlId + "]";
	}
	
	
}
