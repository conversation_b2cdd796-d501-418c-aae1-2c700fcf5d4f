package com.ly.adp.csc.service;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacUserGroupDetail;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

/**
 * <p>
 * 用户分组明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface ISacUserGroupDetailService extends IService<SacUserGroupDetail> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacUserGroupDetailFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacUserGroupDetailSaveInfo(Map<String, Object> dataInfo,String token);
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	OptResult sacUserGroupDelete(Map<String, Object> dataInfo,String token);

	OptResult sacUserGroupDetailDelete(List<Map<String, Object>> list, String authentication);
}
