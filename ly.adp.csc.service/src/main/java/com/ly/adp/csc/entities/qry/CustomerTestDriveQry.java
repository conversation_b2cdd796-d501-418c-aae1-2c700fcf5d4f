package com.ly.adp.csc.entities.qry;

/**
 * 客户试驾查询入参
 * <AUTHOR>
 */
public class CustomerTestDriveQry {
    /**
     * smartID
     */
    private String smartID;

    /**
     *
     * 分页
     */
    private Integer pageIndex;

    /**
     * 分页
     */
    private Integer pageSize;

    public String getSmartID() {
        return smartID;
    }

    public void setSmartID(String smartID) {
        this.smartID = smartID;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "CustomerTestDriveQry{" +
                "smartID='" + smartID + '\'' +
                ", pageIndex=" + pageIndex +
                ", pageSize=" + pageSize +
                '}';
    }
}
