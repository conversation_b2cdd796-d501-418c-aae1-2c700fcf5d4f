package com.ly.adp.csc.entities.qry;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.StringHelper;

import java.io.Serializable;

/**
 * 用户标签查询请求qry
 * <AUTHOR>
 * @since 2025-07-28
 */
public class UserTagQry implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * smartId
     */
    private String smartId;

    /**
     * 手机号
     */
    private String phone;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return "UcUserQry{" +
                "smartId='" + smartId + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }

    public void checkParams() {
        if (StringHelper.IsEmptyOrNull(this.smartId) && StringHelper.IsEmptyOrNull(this.phone)) {
            throw BusicenException.create("smartId和phone不能全部为空");
        }
    }
}
