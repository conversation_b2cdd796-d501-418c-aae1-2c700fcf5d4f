package com.ly.adp.csc.otherservice.entities.in;
import java.io.Serializable;

import com.ly.mp.component.entities.PageInfo;

import io.swagger.annotations.ApiModelProperty;
 
public class PrcProvinceLinkageIn extends PageInfo implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "省份编码", required = false, example = "")
    private String provinceCode;

    @ApiModelProperty(value = "省份名字", required = false, example = "")
    private String provinceName;

    @ApiModelProperty(value = "城市编码", required = false, example = "")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "")
    private String cityName;

    @ApiModelProperty(value = "区县编码", required = false, example = "")
    private String communityCode;

    @ApiModelProperty(value = "区县名称", required = false, example = "")
    private String communityName;

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCommunityCode() {
		return communityCode;
	}

	public void setCommunityCode(String communityCode) {
		this.communityCode = communityCode;
	}

	public String getCommunityName() {
		return communityName;
	}

	public void setCommunityName(String communityName) {
		this.communityName = communityName;
	}


   

    
}
