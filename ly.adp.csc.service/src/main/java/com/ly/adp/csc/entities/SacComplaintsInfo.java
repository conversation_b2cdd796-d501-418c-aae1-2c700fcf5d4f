package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 投诉工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@TableName("t_sac_complaints_info")
public class SacComplaintsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("COMPLAINTS_ID")
    private String complaintsId;

    /**
     * 工单编号
     */
    @TableField("COMPLAINTS_NO")
    private String complaintsNo;

    /**
     * smartID
     */
    @TableField("SMART_ID")
    private String smartId;

    /**
     * 客户姓名
     */
    @TableField("CUST_NAME")
    private String custName;

    /**
     * 联系电话
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 性别编码
     */
    @TableField("GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     */
    @TableField("GENDER_NAME")
    private String genderName;

    /**
     * 门店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 门店名称
     */
    @TableField("DLR_NAME")
    private String dlrName;

    /**
     * 投诉来源编码
     */
    @TableField("SOURCE_CODE")
    private String sourceCode;

    /**
     * 投诉来源名称
     */
    @TableField("SOURCE_NAME")
    private String sourceName;

    /**
     * 投诉主题
     */
    @TableField("COMPLAINT_THEME")
    private String complaintTheme;

    /**
     * 一级分类编码
     */
    @TableField("FIRST_TYPE_CODE")
    private String firstTypeCode;

    /**
     * 一级分类名称
     */
    @TableField("FIRST__TYPE_NAME")
    private String firstTypeName;

    /**
     * 二级分类编码
     */
    @TableField("SECOND_TYPE_CODE")
    private String secondTypeCode;

    /**
     * 二级分类名称
     */
    @TableField("SECOND_TYPE_NAME")
    private String secondTypeName;

    /**
     * 三级分类编码
     */
    @TableField("THIRD_TYPE_CODE")
    private String thirdTypeCode;

    /**
     * 三级分类名称
     */
    @TableField("THIRD_TYPE_NAME")
    private String thirdTypeName;

    /**
     * 四级分类编码
     */
    @TableField("FOUR_TYPE_CODE")
    private String fourTypeCode;

    /**
     * 四级分类名称
     */
    @TableField("FOUR_TYPE_NAME")
    private String fourTypeName;

    /**
     * 被投诉人ID
     */
    @TableField("RESPONDENT_ID")
    private String respondentId;

    /**
     * 被投诉人名称
     */
    @TableField("RESPONDENT_NAME")
    private String respondentName;

    /**
     * 客户类别编码
     */
    @TableField("CUST_TYPE_CODE")
    private String custTypeCode;

    /**
     * 客户类别名称
     */
    @TableField("CUST_TYPE_NAME")
    private String custTypeName;

    /**
     * 客诉等级编码
     */
    @TableField("PRIORITY_CODE")
    private String priorityCode;

    /**
     * 客诉等级名称
     */
    @TableField("PRIORITY_NAME")
    private String priorityName;

    /**
     * 投诉内容
     */
    @TableField("COMPLAINT_CONTENT")
    private String complaintContent;

    /**
     * 是否强制关单
     */
    @TableField("IS_FORCE_CLOSE")
    private String isForceClose;

    /**
     * 工单状态编码
     */
    @TableField("COMPLAINT_STATUS_CODE")
    private String complaintStatusCode;

    /**
     * 工单状态名称
     */
    @TableField("COMPLAINT_STATUS_NAME")
    private String complaintStatusName;

    /**
     * 是否需要客服回访
     */
    @TableField("IS_NEED_REVIEW")
    private String isNeedReview;

    /**
     * 是否异常关单
     */
    @TableField("IS_EXCEPTION_CLOSE")
    private String isExceptionClose;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getComplaintsId() {
        return complaintsId;
    }

    public void setComplaintsId(String complaintsId) {
        this.complaintsId = complaintsId;
    }
    public String getComplaintsNo() {
        return complaintsNo;
    }

    public void setComplaintsNo(String complaintsNo) {
        this.complaintsNo = complaintsNo;
    }
    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }
    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }
    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }
    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }
    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }
    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }
    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
    public String getComplaintTheme() {
        return complaintTheme;
    }

    public void setComplaintTheme(String complaintTheme) {
        this.complaintTheme = complaintTheme;
    }
    public String getFirstTypeCode() {
        return firstTypeCode;
    }

    public void setFirstTypeCode(String firstTypeCode) {
        this.firstTypeCode = firstTypeCode;
    }
    public String getFirstTypeName() {
        return firstTypeName;
    }

    public void setFirstTypeName(String firstTypeName) {
        this.firstTypeName = firstTypeName;
    }
    public String getSecondTypeCode() {
        return secondTypeCode;
    }

    public void setSecondTypeCode(String secondTypeCode) {
        this.secondTypeCode = secondTypeCode;
    }
    public String getSecondTypeName() {
        return secondTypeName;
    }

    public void setSecondTypeName(String secondTypeName) {
        this.secondTypeName = secondTypeName;
    }
    public String getThirdTypeCode() {
        return thirdTypeCode;
    }

    public void setThirdTypeCode(String thirdTypeCode) {
        this.thirdTypeCode = thirdTypeCode;
    }
    public String getThirdTypeName() {
        return thirdTypeName;
    }

    public void setThirdTypeName(String thirdTypeName) {
        this.thirdTypeName = thirdTypeName;
    }
    public String getFourTypeCode() {
        return fourTypeCode;
    }

    public void setFourTypeCode(String fourTypeCode) {
        this.fourTypeCode = fourTypeCode;
    }
    public String getFourTypeName() {
        return fourTypeName;
    }

    public void setFourTypeName(String fourTypeName) {
        this.fourTypeName = fourTypeName;
    }
    public String getRespondentId() {
        return respondentId;
    }

    public void setRespondentId(String respondentId) {
        this.respondentId = respondentId;
    }
    public String getRespondentName() {
        return respondentName;
    }

    public void setRespondentName(String respondentName) {
        this.respondentName = respondentName;
    }
    public String getCustTypeCode() {
        return custTypeCode;
    }

    public void setCustTypeCode(String custTypeCode) {
        this.custTypeCode = custTypeCode;
    }
    public String getCustTypeName() {
        return custTypeName;
    }

    public void setCustTypeName(String custTypeName) {
        this.custTypeName = custTypeName;
    }
    public String getPriorityCode() {
        return priorityCode;
    }

    public void setPriorityCode(String priorityCode) {
        this.priorityCode = priorityCode;
    }
    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }
    public String getComplaintContent() {
        return complaintContent;
    }

    public void setComplaintContent(String complaintContent) {
        this.complaintContent = complaintContent;
    }
    public String getIsForceClose() {
        return isForceClose;
    }

    public void setIsForceClose(String isForceClose) {
        this.isForceClose = isForceClose;
    }
    public String getComplaintStatusCode() {
        return complaintStatusCode;
    }

    public void setComplaintStatusCode(String complaintStatusCode) {
        this.complaintStatusCode = complaintStatusCode;
    }
    public String getComplaintStatusName() {
        return complaintStatusName;
    }

    public void setComplaintStatusName(String complaintStatusName) {
        this.complaintStatusName = complaintStatusName;
    }
    public String getIsNeedReview() {
        return isNeedReview;
    }

    public void setIsNeedReview(String isNeedReview) {
        this.isNeedReview = isNeedReview;
    }
    public String getIsExceptionClose() {
        return isExceptionClose;
    }

    public void setIsExceptionClose(String isExceptionClose) {
        this.isExceptionClose = isExceptionClose;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }
    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }
    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }
    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }
    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SacComplaintsInfo{" +
        "complaintsId=" + complaintsId +
        ", complaintsNo=" + complaintsNo +
        ", smartId=" + smartId +
        ", custName=" + custName +
        ", phone=" + phone +
        ", genderCode=" + genderCode +
        ", genderName=" + genderName +
        ", dlrCode=" + dlrCode +
        ", dlrName=" + dlrName +
        ", sourceCode=" + sourceCode +
        ", sourceName=" + sourceName +
        ", complaintTheme=" + complaintTheme +
        ", firstTypeCode=" + firstTypeCode +
        ", firstTypeName=" + firstTypeName +
        ", secondTypeCode=" + secondTypeCode +
        ", secondTypeName=" + secondTypeName +
        ", thirdTypeCode=" + thirdTypeCode +
        ", thirdTypeName=" + thirdTypeName +
        ", fourTypeCode=" + fourTypeCode +
        ", fourTypeName=" + fourTypeName +
        ", respondentId=" + respondentId +
        ", respondentName=" + respondentName +
        ", custTypeCode=" + custTypeCode +
        ", custTypeName=" + custTypeName +
        ", priorityCode=" + priorityCode +
        ", priorityName=" + priorityName +
        ", complaintContent=" + complaintContent +
        ", isForceClose=" + isForceClose +
        ", complaintStatusCode=" + complaintStatusCode +
        ", complaintStatusName=" + complaintStatusName +
        ", isNeedReview=" + isNeedReview +
        ", isExceptionClose=" + isExceptionClose +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", column6=" + column6 +
        ", column7=" + column7 +
        ", column8=" + column8 +
        ", column9=" + column9 +
        ", column10=" + column10 +
        ", mycatOpTime=" + mycatOpTime +
        ", oemId=" + oemId +
        ", groupId=" + groupId +
        ", oemCode=" + oemCode +
        ", groupCode=" + groupCode +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", sdpUserId=" + sdpUserId +
        ", sdpOrgId=" + sdpOrgId +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
