package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 精品明细记录日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@TableName("t_sac_bu_boutique_detail_record")
public class SacBuBoutiqueDetailRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("RECORD_ID")
    private String recordId;

    /**
     * 精品明细ID
     */
    @TableField("DETAIL_ID")
    private String detailId;

    /**
     * 记录日志类型(0:下架日志 1:使用日志)
     */
    @TableField("RECORD_TYPE")
    private String recordType;

    /**
     * 操作类型
     */
    @TableField("TYPE")
    private String type;

    /**
     * 操作人/使用人
     */
    @TableField("OPERATOR")
    private String operator;

    /**
     * 数量
     */
    @TableField("NUM")
    private String num;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人名称
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 最后更新人员名称
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }
    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }
    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SacBuBoutiqueDetailRecord{" +
        "recordId=" + recordId +
        ", detailId=" + detailId +
        ", recordType=" + recordType +
        ", type=" + type +
        ", operator=" + operator +
        ", num=" + num +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
