package com.ly.adp.csc.otherservice.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <p>
 * 专营店基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-07
 */
@TableName("t_usc_mdm_org_dlr")
public class MdmOrgDlr  implements Serializable{

    private static final long serialVersionUID = 1L;

	/**
	 * 专营店ID
	 */
	@TableId("DLR_ID")
	private String dlrId;

	/**
	 * 专营店编码
	 */
	@TableField("DLR_CODE")
	private String dlrCode;

	/**
	 * 专营店简称
	 */
	@TableField("DLR_SHORT_NAME")
	private String dlrShortName;

	/**
	 * 专营店全称
	 */
	@TableField("DLR_FULL_NAME")
	private String dlrFullName;

	/**
	 * 专营店英文名
	 */
	@TableField("DLR_EN_NAME")
	private String dlrEnName;

	/**
	 * 专营店曾用名
	 */
	@TableField("DLR_NAME_OLD")
	private String dlrNameOld;

	/**
	 * 公司曾用名
	 */
	@TableField("COMP_NAME_OLD")
	private String compNameOld;

	/**
	 * 公司拼音
	 */
	@TableField("COMP_SPELL")
	private String compSpell;

	/**
	 * 公司类型 P:PV;D:DLR;
	 */
	@TableField("COMP_TYPE")
	private String compType;

	/**
	 * 上级网点ID
	 */
	@TableField("PARENT_DLR_ID")
	private String parentDlrId;

	/**
	 * 专营店ANSWER编码
	 */
	@TableField("DLR_ANSWER_CODE")
	private String dlrAnswerCode;

	/**
	 * SAP专营店ID
	 */
	@TableField("SAP_DLR_ID")
	private String sapDlrId;

	/**
	 * 名称缩写代码
	 */
	@TableField("DLR_SYMBOL")
	private String dlrSymbol;

	/**
	 * 组织机构代号
	 */
	@TableField("GUNO")
	private String guno;

	/**
	 * 注册资金
	 */
	@TableField("REGISTER_MONEY")
	private Long registerMoney;

	/**
	 * 拟组建公司注册资金
	 */
	@TableField("S_REGISTER_MONEY")
	private Long sRegisterMoney;

	/**
	 * 专营店硬体级别
	 */
	@TableField("DLR_HARDWARE_CLASS")
	private String dlrHardwareClass;

	/**
	 * 展厅面积
	 */
	@TableField("SHOW_ACREAGE")
	private String showAcreage;

	/**
	 * 车间面积
	 */
	@TableField("FACTORY_ACREAGE")
	private String factoryAcreage;

	/**
	 * 占地面积
	 */
	@TableField("COVER_ACREAGE")
	private String coverAcreage;

	/**
	 * 总建筑面积
	 */
	@TableField("TATOL_ACREAGE")
	private String tatolAcreage;

	/**
	 * 经营范围
	 */
	@TableField("FARE_RANGE")
	private String fareRange;

	/**
	 * 专营店营业时间
	 */
	@TableField("DLR_BUSS_DATE")
	private LocalDateTime dlrBussDate;

	/**
	 * 专营店级别
	 */
	@TableField("DLR_LEVEL")
	private String dlrLevel;

	/**
	 * 开业验收时间
	 */
	@TableField("DLR_DEBUT_TIME")
	private LocalDateTime dlrDebutTime;

	/**
	 * 联络地址
	 */
	@TableField("LINK_ADDR")
	private String linkAddr;

	/**
	 * 小区ID
	 */
	@TableField("SMALL_AREA_ID")
	private String smallAreaId;

	/**
	 * 大区ID
	 */
	@TableField("BIG_AREA_ID")
	private String bigAreaId;

	/**
	 * 省份ID
	 */
	@TableField("PROVINCE_ID")
	private String provinceId;
	
	/**
	 * 省份名称
	 */
	@TableField(value = "PROVINCE_NAME", exist = false)
	private String provinceName;

	/**
	 * 城市ID
	 */
	@TableField("CITY_ID")
	private String cityId;

	/**
	 * 城市名称
	 */
	@TableField(value = "CITY_NAME", exist = false)
	private String cityName;
	
	/**
	 * 区县ID
	 */
	@TableField("COUNTY_ID")
	private String countyId;
	
	/**
	 * 区县名称
	 */
	@TableField(value = "COUNTY_NAME", exist = false)
	private String countyName;

	/**
	 * 传真
	 */
	@TableField("FAX")
	private String fax;

	/**
	 * 电话号码
	 */
	@TableField("PHONE")
	private String phone;

	/**
	 * 手机号码
	 */
	@TableField("MOBILE")
	private String mobile;

	/**
	 * 邮编
	 */
	@TableField("ZIP")
	private String zip;

	/**
	 * 邮箱
	 */
	@TableField("EMAIL")
	private String email;

	/**
	 * 紧急救援电话
	 */
	@TableField("URG_SOS_TEL")
	private String urgSosTel;

	/**
	 * 销售热线电话
	 */
	@TableField("SALE_TEL")
	private String saleTel;

	/**
	 * 售前传真
	 */
	@TableField("SALE_FAX")
	private String saleFax;

	/**
	 * 销售邮箱
	 */
	@TableField("SALE_EMAIL")
	private String saleEmail;

	/**
	 * 服务热线电话
	 */
	@TableField("SERVICE_TEL")
	private String serviceTel;

	/**
	 * 服务传真
	 */
	@TableField("SERVICE_FAX")
	private String serviceFax;

	/**
	 * 售后邮箱
	 */
	@TableField("SERVICE_EMAIL")
	private String serviceEmail;

	/**
	 * 法人
	 */
	@TableField("LEGAL_PERSON")
	private String legalPerson;

	/**
	 * 法人代表证件编码
	 */
	@TableField("LEGAL_PERSON_CARD")
	private String legalPersonCard;

	/**
	 * 法人代表证件类型
	 */
	@TableField("LEGAL_PERSON_CARD_TYPE")
	private BigDecimal legalPersonCardType;

	/**
	 * 签约公司法人代表
	 */
	@TableField("S_MASTER")
	private String sMaster;

	/**
	 * 法人代表联系方式
	 */
	@TableField("S_MASTER_CONN")
	private String sMasterConn;

	/**
	 * 通讯地址及邮编
	 */
	@TableField("S_ADDR")
	private String sAddr;

	/**
	 * 店长姓名
	 */
	@TableField("MANAGER_NAME")
	private String managerName;

	/**
	 * 店长电话
	 */
	@TableField("MANAGER_TEL")
	private String managerTel;

	/**
	 * 结算资格
	 */
	@TableField("BALANCE_CERTIFICATE")
	private String balanceCertificate;

	/**
	 * 结算日期
	 */
	@TableField("BALANCE_DATE")
	 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime balanceDate;

	/**
	 * 汽车维修资格
	 */
	@TableField("MAINTAIN_CERTIFICATE")
	private String maintainCertificate;

	/**
	 * 汽车维修资格获取日期
	 */
	@TableField("MAINTAIN_CERT_DATE")
	private LocalDateTime maintainCertDate;

	/**
	 * DMS店上线日期
	 */
	@TableField("INIT_DATE")
	 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime initDate;

	/**
	 * 接车人姓名
	 */
	@TableField("CEO")
	private String ceo;

	/**
	 * 接车人电话
	 */
	@TableField("CEO_CONN")
	private String ceoConn;

	/**
	 * 组织机构模型编号
	 */
	@TableField("DEPT_MODEL_ID")
	private String deptModelId;

	/**
	 * 上线初始化标志
	 */
	@TableField("INIT_FLAG")
	private BigDecimal initFlag;

	/**
	 * 认证标志
	 */
	@TableField("CERTIFICATE_FLAG")
	private String certificateFlag;

	/**
	 * 专营店状态：一网 0 停业，1 营业，2、在建 3、取消 4、整改；直营二网 1营业，2、在建 3、取消<br>
	 * 7、建店撤销，自动撤销，停业，升级，已批准一级店
	 */
	@TableField("DLR_STATUS")
	private String dlrStatus;

	/**
	 * 专营店关联关系 1-深圳风神专营店；2-关联专营店；3-非关联专营店
	 */
	@TableField("DLR_RELEATION")
	private String dlrReleation;

	/**
	 * 专营店类型 MDM.T_MDM_LOOKUP_VALUE(lookup_type_code =5005)
	 */
	@TableField("DLR_TYPE")
	private String dlrType;
	
	@TableField("DLR_TYPE_NAME")
	private String dlrTypeName;

	/**
	 * 关联关系维护状态 0-未维护；1-已维护
	 */
	@TableField("RELEATION_STATUS")
	private String releationStatus;

	/**
	 * 排列顺序
	 */
	@TableField("ORDER_NO")
	private BigDecimal orderNo;

	/**
	 * 备注
	 */
	@TableField("REMARK")
	private String remark;

	/**
	 * 品牌编码
	 */
	@TableField("CAR_BRAND_CODE")
	private String carBrandCode;
	
	/**
	 * 品牌名称
	 */
	@TableField("CAR_BRAND_CN")
	private String carBrandCn;
	
	/**
	 * DOQD标记
	 */
	@TableField("DOQD_FLAG")
	private String doqdFlag;

	/**
	 * 专营店分类 MDM.T_MDM_LOOKUP_VALUE(lookup_type_code =5003): 0:一级店 1:二级网点 2:二手车店
	 */
	@TableField("DLR_SORT")
	private String dlrSort;

	/**
	 * DP组织ID
	 */
	@TableField("DP_ORGID")
	private Long dpOrgid;

	/**
	 * 所属PV公司代码 例如：4000,4815，4911
	 */
	@TableField("PV_COMP_CODE")
	private String pvCompCode;

	/**
	 * 是否同步信息 1 是 2 否
	 */
	@TableField("IS_SYNCHRONOUS")
	private String isSynchronous;

	/**
	 * 区域
	 */
	@TableField("AREA_ID")
	private String areaId;

	/**
	 * 专营店类别(值列表: DB0062) 1. 4S店；2. 直营二网；3. 虚拟4S店；4. 备件采购类；5. 服务类；6. 钣喷中心；7.
	 * 二手车中心；8. 配送中心；9. 服务二网）
	 */
	@TableField("ORG_TYPE")
	private String orgType;

	/**
	 * 连体店
	 */
	@TableField("LINK_DLR_ID")
	private String linkDlrId;

	/**
	 * 新E3S上线时间
	 */
	@TableField("ONLINE_TIME")
	private LocalDateTime onlineTime;

	/**
	 * 微信号
	 */
	@TableField("WECHAT")
	private String wechat;

	/**
	 * 经度
	 */
	@TableField("LNG")
	private String lng;

	/**
	 * 纬度
	 */
	@TableField("LAT")
	private String lat;

	/**
	 * 是否保险认证店
	 */
	@TableField("IS_SECURITY")
	private String isSecurity;

	/**
	 * 专营店4S级别
	 */
	@TableField("DLR_4S_LEVEL")
	private String dlr4sLevel;

	/**
	 * 公司名称
	 */
	@TableField("COMP_NAME")
	private String compName;

	/**
	 * 旧E3SP一网编码
	 */
	@TableField("NET_ID")
	private Long netId;

	/**
	 * V模块网点编码
	 */
	@TableField("V_DLR_CODE")
	private String vDlrCode;

	/**
	 * P模块网点编码
	 */
	@TableField("P_DLR_CODE")
	private String pDlrCode;

	/**
	 * S模块网点编码
	 */
	@TableField("S_DLR_CODE")
	private String sDlrCode;

	/**
	 * UC模块网点编码
	 */
	@TableField("UC_DLR_CODE")
	private String ucDlrCode;

	/**
	 * AI模块网点编码
	 */
	@TableField("AI_DLR_CODE")
	private String aiDlrCode;

	/**
	 * CAP模块网点编码
	 */
	@TableField("CAP_DLR_CODE")
	private String capDlrCode;

	/**
	 * MDS大区ID MDS临时使用的大区ID
	 */
	@TableField("MDS_BIG_AREA_ID")
	private String mdsBigAreaId;

	/**
	 * V模块专用
	 */
	@TableField("OLD_DLR_ID")
	private Long oldDlrId;

	/**
	 * 排放标准
	 */
	@TableField("EMISSION_STANDARD")
	private String emissionStandard;

	/**
	 * 专属模块
	 */
	@TableField("BELONG_MODULE")
	private String belongModule;

	/**
	 * 气候状态
	 */
	@TableField("CLIMATE_STATUS")
	private String climateStatus;

	/**
	 * 新E3S上线标记(未上线0,上线1,旧店2)
	 */
	@TableField("ONLINE_FLAG")
	private String onlineFlag;

	/**
	 * 打印模板
	 */
	@TableField("PRINT_TEMPLET")
	private String printTemplet;

	/**
	 * 特需网点标记 0:一般网点，1:特需网点
	 */
	@TableField("SP_FLAG")
	private String spFlag;

	/**
	 * 是否OKCare店
	 */
	@TableField("IS_OK_CARE")
	private String isOkCare;

	/**
	 * 是否开通快速保养
	 */
	@TableField("IS_SSA_FAST")
	private String isSsaFast;

	/**
	 * 是否开通自助保养
	 */
	@TableField("IS_SSA_SELF")
	private String isSsaSelf;

	/**
	 * 是否开通极速钣喷
	 */
	@TableField("IS_SSA_SPRAY")
	private String isSsaSpray;

	/**
	 * 电子档案维修企业注册区域编码
	 */
	@TableField("COMPANY_AREA_CODE")
	private String companyAreaCode;

	/**
	 * 电子档案维修企业唯一标识
	 */
	@TableField("COMPANY_UNIQUE_CODE")
	private String companyUniqueCode;

	/**
	 * 电子档案企业许可名称
	 */
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	 * 是否需要发送维修记录到电子档案系统：0不需要，1需要
	 */
	@TableField("IS_SEND")
	private String isSend;

	/**
	 * 电子档案许可证号
	 */
	@TableField("TRANSPORT_LICENSE")
	private String transportLicense;

	/**
	 * 是否开通自助保养
	 */
	@TableField("IS_SSA_FALF")
	private String isSsaFalf;

	/**
	 * 厂商标识ID
	 */
	@TableField("OEM_ID")
	private String oemId;

	/**
	 * 集团标识ID
	 */
	@TableField("GROUP_ID")
	private String groupId;

	/**
	 * 厂商标识
	 */
	@TableField("OEM_CODE")
	private String oemCode;

	/**
	 * 集团标识
	 */
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	 * 创建人
	 */
	@TableField("CREATOR")
	private String creator;

	/**
	 * 创建人姓名
	 */
	@TableField("CREATED_NAME")
	private String createdName;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@TableField("CREATED_DATE")
	private LocalDateTime createdDate;

	/**
	 * 最后更新人员
	 */
	@TableField("MODIFIER")
	private String modifier;

	/**
	 * 修改人姓名
	 */
	@TableField("MODIFY_NAME")
	private String modifyName;

	/**
	 * 最后更新时间
	 */
	@TableField("LAST_UPDATED_DATE")
	private LocalDateTime lastUpdatedDate;

	/**
	 * 是否可用
	 */
	@TableField("IS_ENABLE")
	private String isEnable;

	/**
	 * SDP用户ID
	 */
	@TableField("SDP_USER_ID")
	private String sdpUserId;

	/**
	 * SDP组织ID
	 */
	@TableField("SDP_ORG_ID")
	private String sdpOrgId;

	/**
	 * 并发控制字段
	 */
	@TableField("UPDATE_CONTROL_ID")
	private String updateControlId;

	/**
	 * 二网类别
	 */
	@TableField("CHILD_DLR_TYPE")
	private String childDlrType;

	/**
	 * 是否NCH店
	 */
	@TableField("IS_NCH")
	private String isNch;
	/**
	 * 是否NCH授权销售店
	 */
	@TableField("IS_NCH_SALE_AUTH")
	private String isNchSaleAuth;
	/**
	 * 是否NCH授权售后店
	 */
	@TableField("IS_NCH_SERVICE_AUTH")
	private String isNchServiceAuth;

	/**
	 * 售后大区id
	 */
	@TableField("SERVICE_BIG_AREA_ID")
	private String serviceBigAreaId;
	/**
	 * 售后小区id
	 */
	@TableField("SERVICE_SMALL_AREA_ID")
	private String serviceSmallAreaId;
	
	
	/**
	 * 售后服务状态
	 */
	@TableField("SA_SERVICE_STATUS")
	private String saServiceStatus;
	/**
	 * 销售服务状态
	 */
	@TableField("CA_SERVICE_STATUS")
	private String caServiceStatus;
	
	/**
	 * 是否具备EV销售资质
	 */
	@TableField("HAS_EV_SALE")
	private String hasEvSale;
	/**
	 *是否具备EV售后资质
	 */
	@TableField("HAS_EV_SERVICE")
	private String hasEvService;

	/**
	 *代理区域
	 */
	@TableField("AGENT_AREA")
	private String agentArea;

	/**
	 *周末营业时间
	 */
	@TableField("WEEKEND_BUSINESS_HOUR")
	private String weekendBusinessHour;
	

	/**
	 *接车地址
	 */
	@TableField("PICK_UP_ADDRESS")
	private String pickUpAddress;
	
	/**
	 *门头照片
	 */
	@TableField("DOOR_PHOTO")
	private String doorPhoto;
	

	/**
	 *平时营业时间
	 */
	@TableField("WEEKDAYS_BUSINESS_HOUR")
	private String weekdaysBusinessHour;
	
	@TableField("IS_FIRST")
	private String isFirst;
	
	@TableField("STOCK_HOLDER")
	private String stockHolder;

	/**
	 *公司ID
	 */
	@TableField("COMPANY_ID")
	private String companyId;
	
	/**
	 *建店协议
	 */
	@TableField(exist = false)
	private String dlrBuildAgreementUrl;
	/**
	 * 代理商ID
	 */
	@TableField(exist = false)
	private String agentId;
	/**
	 * 代理商名称
	 */
	@TableField(exist = false)
	private String agentName;
	/**
	 * 城市公司ID
	 */
	@TableField(exist = false)
	private String agentCompanyId;
	/**
	 *城市公司名称
	 */
	@TableField(exist = false)
	private String agentCompanyName;
	
	/**
	 * 专营店状态
	 */
	@TableField(value = "DLR_STATUS_CN", exist = false)
	private String dlrStatusCn;
	
	/**
	 * 附件id
	 */
	@TableField(value = "AGREEMENT_ATTACHMENT_ID", exist = false)
	private String agreementAttachmentId;
	
	   /**
     * 附件
     */
    @TableField("ATTACH_URL")
    private String attachUrl;
	
	/**
	 * 用户名称
	 */
	@TableField(value = "USER_NAME", exist = false)
	private String userName;
	
	/**
	 * 轨道交通信息
	 */
	@TableField(value = "RAIL_TRANSIT_INFO")
	private String railTransitInfo;
	
	/**
	 * 附件id
	 */
	@TableField(value = "EMP_NAME", exist = false)
	private String empName;
	
	@TableField(value = "BIG_AREA_NAME", exist = false)
	private String bigAreaName;
	
	public String getBusinessLicense() {
		return businessLicense;
	}

	public void setBusinessLicense(String businessLicense) {
		this.businessLicense = businessLicense;
	}

	public String getShareholderResolution() {
		return shareholderResolution;
	}

	public void setShareholderResolution(String shareholderResolution) {
		this.shareholderResolution = shareholderResolution;
	}

	@TableId("营业执照")
	@TableField("BUSINESS_LICENSE")
	private String businessLicense;

	@TableId("股东协议")
	@TableField("SHAREHOLDER_RESOLUTION")
	private String shareholderResolution;

	public String getStockHoldreOne() {
		return stockHoldreOne;
	}

	public void setStockHoldreOne(String stockHoldreOne) {
		this.stockHoldreOne = stockHoldreOne;
	}

	public String getStockHoldreTwo() {
		return stockHoldreTwo;
	}

	public void setStockHoldreTwo(String stockHoldreTwo) {
		this.stockHoldreTwo = stockHoldreTwo;
	}

	public String getStockHoldreThree() {
		return stockHoldreThree;
	}

	public void setStockHoldreThree(String stockHoldreThree) {
		this.stockHoldreThree = stockHoldreThree;
	}

	public String getStockHoldreFour() {
		return stockHoldreFour;
	}

	public void setStockHoldreFour(String stockHoldreFour) {
		this.stockHoldreFour = stockHoldreFour;
	}

	public String getStockHoldreFive() {
		return stockHoldreFive;
	}

	public void setStockHoldreFive(String stockHoldreFive) {
		this.stockHoldreFive = stockHoldreFive;
	}

	public String getStockHoldreSix() {
		return stockHoldreSix;
	}

	public void setStockHoldreSix(String stockHoldreSix) {
		this.stockHoldreSix = stockHoldreSix;
	}

	public String getStockHoldreSeven() {
		return stockHoldreSeven;
	}

	public void setStockHoldreSeven(String stockHoldreSeven) {
		this.stockHoldreSeven = stockHoldreSeven;
	}

	public String getStockHoldreEight() {
		return stockHoldreEight;
	}

	public void setStockHoldreEight(String stockHoldreEight) {
		this.stockHoldreEight = stockHoldreEight;
	}

	public String getStockHoldreNine() {
		return stockHoldreNine;
	}

	public void setStockHoldreNine(String stockHoldreNine) {
		this.stockHoldreNine = stockHoldreNine;
	}

	public String getStockHoldreTen() {
		return stockHoldreTen;
	}

	public void setStockHoldreTen(String stockHoldreTen) {
		this.stockHoldreTen = stockHoldreTen;
	}

	public Double getProportionOne() {
		return proportionOne;
	}

	public void setProportionOne(Double proportionOne) {
		this.proportionOne = proportionOne;
	}

	public Double getProportionTwo() {
		return proportionTwo;
	}

	public void setProportionTwo(Double proportionTwo) {
		this.proportionTwo = proportionTwo;
	}

	public Double getProportionThree() {
		return proportionThree;
	}

	public void setProportionThree(Double proportionThree) {
		this.proportionThree = proportionThree;
	}

	public Double getProportionFour() {
		return proportionFour;
	}

	public void setProportionFour(Double proportionFour) {
		this.proportionFour = proportionFour;
	}

	public Double getProportionFive() {
		return proportionFive;
	}

	public void setProportionFive(Double proportionFive) {
		this.proportionFive = proportionFive;
	}

	public Double getProportionSix() {
		return proportionSix;
	}

	public void setProportionSix(Double proportionSix) {
		this.proportionSix = proportionSix;
	}

	public Double getProportionSeven() {
		return proportionSeven;
	}

	public void setProportionSeven(Double proportionSeven) {
		this.proportionSeven = proportionSeven;
	}

	public Double getProportionEight() {
		return proportionEight;
	}

	public void setProportionEight(Double proportionEight) {
		this.proportionEight = proportionEight;
	}

	public Double getProportionNine() {
		return proportionNine;
	}

	public void setProportionNine(Double proportionNine) {
		this.proportionNine = proportionNine;
	}

	public Double getProportionTen() {
		return proportionTen;
	}

	public void setProportionTen(Double proportionTen) {
		this.proportionTen = proportionTen;
	}

	@TableId("股东信息1")
	@TableField("STOCK_HOLDRE_ONE")
	private String stockHoldreOne;

	@TableId("股东信息2")
	@TableField("STOCK_HOLDRE_TWO")
	private String stockHoldreTwo;

	@TableId("股东信息3")
	@TableField("STOCK_HOLDRE_THREE")
	private String stockHoldreThree;

	@TableId("股东信息4")
	@TableField("STOCK_HOLDRE_FOUR")
	private String stockHoldreFour;

	@TableId("股东信息5")
	@TableField("STOCK_HOLDRE_FIVE")
	private String stockHoldreFive;

	@TableId("股东信息6")
	@TableField("STOCK_HOLDRE_SIX")
	private String stockHoldreSix;

	@TableId("股东信息7")
	@TableField("STOCK_HOLDRE_SEVEN")
	private String stockHoldreSeven;

	@TableId("股东信息8")
	@TableField("STOCK_HOLDRE_EIGHT")
	private String stockHoldreEight;

	@TableId("股东信息9")
	@TableField("STOCK_HOLDRE_NINE")
	private String stockHoldreNine;

	@TableId("股东信息10")
	@TableField("STOCK_HOLDRE_TEN")
	private String stockHoldreTen;

	@TableId("股比1")
	@TableField("PROPORTION_ONE")
	private Double proportionOne;

	@TableId("股比2")
	@TableField("PROPORTION_TWO")
	private Double proportionTwo;

	@TableId("股比3")
	@TableField("PROPORTION_THREE")
	private Double proportionThree;

	@TableId("股比4")
	@TableField("PROPORTION_FOUR")
	private Double proportionFour;

	@TableId("股比5")
	@TableField("PROPORTION_FIVE")
	private Double proportionFive;

	@TableId("股比6")
	@TableField("PROPORTION_SIX")
	private Double proportionSix;

	@TableId("股比7")
	@TableField("PROPORTION_SEVEN")
	private Double proportionSeven;

	@TableId("股比8")
	@TableField("PROPORTION_EIGHT")
	private Double proportionEight;

	@TableId("股比9")
	@TableField("PROPORTION_NINE")
	private Double proportionNine;

	@TableId("股比10")
	@TableField("PROPORTION_TEN")
	private Double proportionTen;

	/**
     * 合作框架协议
     */
    @TableField("COOPERATION_AGENT_URL")
    private String cooperationAgentUrl;
    
    /**
     * 合作协议开始时间
     */
    @TableField("COOPERATION_AGENT_BEGIN_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cooperationAgentBeginDate;
    
    
    /**
     * 合作协议结束时间
     */
    @TableField("COOPERATION_AGENT_END_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cooperationAgentEndDate;
	
	public String getIsFirst() {
		return isFirst;
	}

	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}

	public String getAgentId() {
		return agentId;
	}

	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentCompanyId() {
		return agentCompanyId;
	}

	public void setAgentCompanyId(String agentCompanyId) {
		this.agentCompanyId = agentCompanyId;
	}

	public String getAgentCompanyName() {
		return agentCompanyName;
	}

	public void setAgentCompanyName(String agentCompanyName) {
		this.agentCompanyName = agentCompanyName;
	}

	public String getStockHolder() {
		return stockHolder;
	}

	public void setStockHolder(String stockHolder) {
		this.stockHolder = stockHolder;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getWeekdaysBusinessHour() {
		return weekdaysBusinessHour;
	}

	public void setWeekdaysBusinessHour(String weekdaysBusinessHour) {
		this.weekdaysBusinessHour = weekdaysBusinessHour;
	}


	public String getSaServiceStatus() {
		return saServiceStatus;
	}


	public void setSaServiceStatus(String saServiceStatus) {
		this.saServiceStatus = saServiceStatus;
	}


	public String getCaServiceStatus() {
		return caServiceStatus;
	}


	public void setCaServiceStatus(String caServiceStatus) {
		this.caServiceStatus = caServiceStatus;
	}


	public String getHasEvSale() {
		return hasEvSale;
	}


	public void setHasEvSale(String hasEvSale) {
		this.hasEvSale = hasEvSale;
	}


	public String getHasEvService() {
		return hasEvService;
	}


	public void setHasEvService(String hasEvService) {
		this.hasEvService = hasEvService;
	}


	public String getDlrId() {
		return dlrId;
	}


	public void setDlrId(String dlrId) {
		this.dlrId = dlrId;
	}


	public String getDlrCode() {
		return dlrCode;
	}


	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}


	public String getDlrTypeName() {
		return dlrTypeName;
	}

	public void setDlrTypeName(String dlrTypeName) {
		this.dlrTypeName = dlrTypeName;
	}

	public String getDlrShortName() {
		return dlrShortName;
	}


	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}


	public String getDlrFullName() {
		return dlrFullName;
	}


	public void setDlrFullName(String dlrFullName) {
		this.dlrFullName = dlrFullName;
	}


	public String getDlrEnName() {
		return dlrEnName;
	}


	public void setDlrEnName(String dlrEnName) {
		this.dlrEnName = dlrEnName;
	}


	public String getDlrNameOld() {
		return dlrNameOld;
	}


	public void setDlrNameOld(String dlrNameOld) {
		this.dlrNameOld = dlrNameOld;
	}


	public String getCompNameOld() {
		return compNameOld;
	}


	public void setCompNameOld(String compNameOld) {
		this.compNameOld = compNameOld;
	}


	public String getCompSpell() {
		return compSpell;
	}


	public void setCompSpell(String compSpell) {
		this.compSpell = compSpell;
	}


	public String getCompType() {
		return compType;
	}


	public void setCompType(String compType) {
		this.compType = compType;
	}


	public String getParentDlrId() {
		return parentDlrId;
	}


	public void setParentDlrId(String parentDlrId) {
		this.parentDlrId = parentDlrId;
	}


	public String getDlrAnswerCode() {
		return dlrAnswerCode;
	}


	public void setDlrAnswerCode(String dlrAnswerCode) {
		this.dlrAnswerCode = dlrAnswerCode;
	}


	public String getSapDlrId() {
		return sapDlrId;
	}


	public void setSapDlrId(String sapDlrId) {
		this.sapDlrId = sapDlrId;
	}


	public String getDlrSymbol() {
		return dlrSymbol;
	}


	public void setDlrSymbol(String dlrSymbol) {
		this.dlrSymbol = dlrSymbol;
	}


	public String getGuno() {
		return guno;
	}


	public void setGuno(String guno) {
		this.guno = guno;
	}


	public Long getRegisterMoney() {
		return registerMoney;
	}


	public void setRegisterMoney(Long registerMoney) {
		this.registerMoney = registerMoney;
	}


	public Long getsRegisterMoney() {
		return sRegisterMoney;
	}


	public void setsRegisterMoney(Long sRegisterMoney) {
		this.sRegisterMoney = sRegisterMoney;
	}


	public String getDlrHardwareClass() {
		return dlrHardwareClass;
	}


	public void setDlrHardwareClass(String dlrHardwareClass) {
		this.dlrHardwareClass = dlrHardwareClass;
	}


	public String getShowAcreage() {
		return showAcreage;
	}


	public void setShowAcreage(String showAcreage) {
		this.showAcreage = showAcreage;
	}


	public String getFactoryAcreage() {
		return factoryAcreage;
	}


	public void setFactoryAcreage(String factoryAcreage) {
		this.factoryAcreage = factoryAcreage;
	}


	public String getCoverAcreage() {
		return coverAcreage;
	}


	public void setCoverAcreage(String coverAcreage) {
		this.coverAcreage = coverAcreage;
	}


	public String getTatolAcreage() {
		return tatolAcreage;
	}


	public void setTatolAcreage(String tatolAcreage) {
		this.tatolAcreage = tatolAcreage;
	}


	public String getFareRange() {
		return fareRange;
	}


	public void setFareRange(String fareRange) {
		this.fareRange = fareRange;
	}


	public String getDlrLevel() {
		return dlrLevel;
	}


	public void setDlrLevel(String dlrLevel) {
		this.dlrLevel = dlrLevel;
	}


	public LocalDateTime getDlrDebutTime() {
		return dlrDebutTime;
	}


	public void setDlrDebutTime(LocalDateTime dlrDebutTime) {
		this.dlrDebutTime = dlrDebutTime;
	}


	public String getLinkAddr() {
		return linkAddr;
	}


	public void setLinkAddr(String linkAddr) {
		this.linkAddr = linkAddr;
	}


	public String getSmallAreaId() {
		return smallAreaId;
	}


	public void setSmallAreaId(String smallAreaId) {
		this.smallAreaId = smallAreaId;
	}


	public String getBigAreaId() {
		return bigAreaId;
	}


	public void setBigAreaId(String bigAreaId) {
		this.bigAreaId = bigAreaId;
	}


	public String getProvinceId() {
		return provinceId;
	}


	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}


	public String getCityId() {
		return cityId;
	}


	public void setCityId(String cityId) {
		this.cityId = cityId;
	}


	public String getCountyId() {
		return countyId;
	}


	public void setCountyId(String countyId) {
		this.countyId = countyId;
	}


	public String getFax() {
		return fax;
	}


	public void setFax(String fax) {
		this.fax = fax;
	}


	public String getPhone() {
		return phone;
	}


	public void setPhone(String phone) {
		this.phone = phone;
	}


	public String getMobile() {
		return mobile;
	}


	public void setMobile(String mobile) {
		this.mobile = mobile;
	}


	public String getZip() {
		return zip;
	}


	public void setZip(String zip) {
		this.zip = zip;
	}


	public String getEmail() {
		return email;
	}


	public void setEmail(String email) {
		this.email = email;
	}


	public String getUrgSosTel() {
		return urgSosTel;
	}


	public void setUrgSosTel(String urgSosTel) {
		this.urgSosTel = urgSosTel;
	}


	public String getSaleTel() {
		return saleTel;
	}


	public void setSaleTel(String saleTel) {
		this.saleTel = saleTel;
	}


	public String getSaleFax() {
		return saleFax;
	}


	public void setSaleFax(String saleFax) {
		this.saleFax = saleFax;
	}


	public String getSaleEmail() {
		return saleEmail;
	}


	public void setSaleEmail(String saleEmail) {
		this.saleEmail = saleEmail;
	}


	public String getServiceTel() {
		return serviceTel;
	}


	public void setServiceTel(String serviceTel) {
		this.serviceTel = serviceTel;
	}


	public String getServiceFax() {
		return serviceFax;
	}


	public void setServiceFax(String serviceFax) {
		this.serviceFax = serviceFax;
	}


	public String getServiceEmail() {
		return serviceEmail;
	}


	public void setServiceEmail(String serviceEmail) {
		this.serviceEmail = serviceEmail;
	}


	public String getLegalPerson() {
		return legalPerson;
	}


	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}


	public String getLegalPersonCard() {
		return legalPersonCard;
	}


	public void setLegalPersonCard(String legalPersonCard) {
		this.legalPersonCard = legalPersonCard;
	}


	public BigDecimal getLegalPersonCardType() {
		return legalPersonCardType;
	}


	public void setLegalPersonCardType(BigDecimal legalPersonCardType) {
		this.legalPersonCardType = legalPersonCardType;
	}


	public String getsMaster() {
		return sMaster;
	}


	public void setsMaster(String sMaster) {
		this.sMaster = sMaster;
	}


	public String getsMasterConn() {
		return sMasterConn;
	}


	public void setsMasterConn(String sMasterConn) {
		this.sMasterConn = sMasterConn;
	}


	public String getsAddr() {
		return sAddr;
	}


	public void setsAddr(String sAddr) {
		this.sAddr = sAddr;
	}


	public String getManagerName() {
		return managerName;
	}


	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}


	public String getManagerTel() {
		return managerTel;
	}


	public void setManagerTel(String managerTel) {
		this.managerTel = managerTel;
	}


	public String getBalanceCertificate() {
		return balanceCertificate;
	}


	public void setBalanceCertificate(String balanceCertificate) {
		this.balanceCertificate = balanceCertificate;
	}


	public LocalDateTime getBalanceDate() {
		return balanceDate;
	}


	public void setBalanceDate(LocalDateTime balanceDate) {
		this.balanceDate = balanceDate;
	}


	public String getMaintainCertificate() {
		return maintainCertificate;
	}


	public void setMaintainCertificate(String maintainCertificate) {
		this.maintainCertificate = maintainCertificate;
	}


	public LocalDateTime getMaintainCertDate() {
		return maintainCertDate;
	}


	public void setMaintainCertDate(LocalDateTime maintainCertDate) {
		this.maintainCertDate = maintainCertDate;
	}


	public LocalDateTime getInitDate() {
		return initDate;
	}


	public void setInitDate(LocalDateTime initDate) {
		this.initDate = initDate;
	}


	public String getCeo() {
		return ceo;
	}


	public void setCeo(String ceo) {
		this.ceo = ceo;
	}


	public String getCeoConn() {
		return ceoConn;
	}


	public void setCeoConn(String ceoConn) {
		this.ceoConn = ceoConn;
	}


	public String getDeptModelId() {
		return deptModelId;
	}


	public void setDeptModelId(String deptModelId) {
		this.deptModelId = deptModelId;
	}


	public BigDecimal getInitFlag() {
		return initFlag;
	}


	public void setInitFlag(BigDecimal initFlag) {
		this.initFlag = initFlag;
	}


	public String getCertificateFlag() {
		return certificateFlag;
	}


	public void setCertificateFlag(String certificateFlag) {
		this.certificateFlag = certificateFlag;
	}


	public String getDlrStatus() {
		return dlrStatus;
	}


	public void setDlrStatus(String dlrStatus) {
		this.dlrStatus = dlrStatus;
	}


	public String getDlrReleation() {
		return dlrReleation;
	}


	public void setDlrReleation(String dlrReleation) {
		this.dlrReleation = dlrReleation;
	}


	public String getDlrType() {
		return dlrType;
	}


	public void setDlrType(String dlrType) {
		this.dlrType = dlrType;
	}


	public String getReleationStatus() {
		return releationStatus;
	}


	public void setReleationStatus(String releationStatus) {
		this.releationStatus = releationStatus;
	}


	public BigDecimal getOrderNo() {
		return orderNo;
	}


	public void setOrderNo(BigDecimal orderNo) {
		this.orderNo = orderNo;
	}


	public String getRemark() {
		return remark;
	}


	public void setRemark(String remark) {
		this.remark = remark;
	}


	public String getCarBrandCode() {
		return carBrandCode;
	}


	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}


	public String getDoqdFlag() {
		return doqdFlag;
	}


	public void setDoqdFlag(String doqdFlag) {
		this.doqdFlag = doqdFlag;
	}


	public String getDlrSort() {
		return dlrSort;
	}


	public void setDlrSort(String dlrSort) {
		this.dlrSort = dlrSort;
	}


	public Long getDpOrgid() {
		return dpOrgid;
	}


	public void setDpOrgid(Long dpOrgid) {
		this.dpOrgid = dpOrgid;
	}


	public String getPvCompCode() {
		return pvCompCode;
	}


	public void setPvCompCode(String pvCompCode) {
		this.pvCompCode = pvCompCode;
	}


	public String getIsSynchronous() {
		return isSynchronous;
	}


	public void setIsSynchronous(String isSynchronous) {
		this.isSynchronous = isSynchronous;
	}


	public String getAreaId() {
		return areaId;
	}


	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}


	public String getOrgType() {
		return orgType;
	}


	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}


	public String getLinkDlrId() {
		return linkDlrId;
	}


	public void setLinkDlrId(String linkDlrId) {
		this.linkDlrId = linkDlrId;
	}


	public LocalDateTime getOnlineTime() {
		return onlineTime;
	}


	public void setOnlineTime(LocalDateTime onlineTime) {
		this.onlineTime = onlineTime;
	}


	public String getWechat() {
		return wechat;
	}


	public void setWechat(String wechat) {
		this.wechat = wechat;
	}


	public String getLng() {
		return lng;
	}


	public void setLng(String lng) {
		this.lng = lng;
	}


	public String getLat() {
		return lat;
	}


	public void setLat(String lat) {
		this.lat = lat;
	}


	public String getIsSecurity() {
		return isSecurity;
	}


	public void setIsSecurity(String isSecurity) {
		this.isSecurity = isSecurity;
	}


	public String getDlr4sLevel() {
		return dlr4sLevel;
	}


	public void setDlr4sLevel(String dlr4sLevel) {
		this.dlr4sLevel = dlr4sLevel;
	}


	public String getCompName() {
		return compName;
	}


	public void setCompName(String compName) {
		this.compName = compName;
	}


	public Long getNetId() {
		return netId;
	}


	public void setNetId(Long netId) {
		this.netId = netId;
	}


	public String getvDlrCode() {
		return vDlrCode;
	}


	public void setvDlrCode(String vDlrCode) {
		this.vDlrCode = vDlrCode;
	}


	public String getpDlrCode() {
		return pDlrCode;
	}


	public void setpDlrCode(String pDlrCode) {
		this.pDlrCode = pDlrCode;
	}


	public String getsDlrCode() {
		return sDlrCode;
	}


	public void setsDlrCode(String sDlrCode) {
		this.sDlrCode = sDlrCode;
	}


	public String getUcDlrCode() {
		return ucDlrCode;
	}


	public void setUcDlrCode(String ucDlrCode) {
		this.ucDlrCode = ucDlrCode;
	}


	public String getAiDlrCode() {
		return aiDlrCode;
	}


	public void setAiDlrCode(String aiDlrCode) {
		this.aiDlrCode = aiDlrCode;
	}


	public String getCapDlrCode() {
		return capDlrCode;
	}


	public void setCapDlrCode(String capDlrCode) {
		this.capDlrCode = capDlrCode;
	}


	public String getMdsBigAreaId() {
		return mdsBigAreaId;
	}


	public void setMdsBigAreaId(String mdsBigAreaId) {
		this.mdsBigAreaId = mdsBigAreaId;
	}


	public Long getOldDlrId() {
		return oldDlrId;
	}


	public void setOldDlrId(Long oldDlrId) {
		this.oldDlrId = oldDlrId;
	}


	public String getEmissionStandard() {
		return emissionStandard;
	}


	public void setEmissionStandard(String emissionStandard) {
		this.emissionStandard = emissionStandard;
	}


	public String getBelongModule() {
		return belongModule;
	}


	public void setBelongModule(String belongModule) {
		this.belongModule = belongModule;
	}


	public String getClimateStatus() {
		return climateStatus;
	}


	public void setClimateStatus(String climateStatus) {
		this.climateStatus = climateStatus;
	}


	public String getOnlineFlag() {
		return onlineFlag;
	}


	public void setOnlineFlag(String onlineFlag) {
		this.onlineFlag = onlineFlag;
	}


	public String getPrintTemplet() {
		return printTemplet;
	}


	public void setPrintTemplet(String printTemplet) {
		this.printTemplet = printTemplet;
	}


	public String getSpFlag() {
		return spFlag;
	}


	public void setSpFlag(String spFlag) {
		this.spFlag = spFlag;
	}


	public String getIsOkCare() {
		return isOkCare;
	}


	public void setIsOkCare(String isOkCare) {
		this.isOkCare = isOkCare;
	}


	public String getIsSsaFast() {
		return isSsaFast;
	}


	public void setIsSsaFast(String isSsaFast) {
		this.isSsaFast = isSsaFast;
	}


	public String getIsSsaSelf() {
		return isSsaSelf;
	}


	public void setIsSsaSelf(String isSsaSelf) {
		this.isSsaSelf = isSsaSelf;
	}


	public String getIsSsaSpray() {
		return isSsaSpray;
	}


	public void setIsSsaSpray(String isSsaSpray) {
		this.isSsaSpray = isSsaSpray;
	}


	public String getCompanyAreaCode() {
		return companyAreaCode;
	}


	public void setCompanyAreaCode(String companyAreaCode) {
		this.companyAreaCode = companyAreaCode;
	}


	public String getCompanyUniqueCode() {
		return companyUniqueCode;
	}


	public void setCompanyUniqueCode(String companyUniqueCode) {
		this.companyUniqueCode = companyUniqueCode;
	}


	public String getCompanyName() {
		return companyName;
	}


	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}


	public String getIsSend() {
		return isSend;
	}


	public void setIsSend(String isSend) {
		this.isSend = isSend;
	}


	public String getTransportLicense() {
		return transportLicense;
	}


	public void setTransportLicense(String transportLicense) {
		this.transportLicense = transportLicense;
	}


	public String getIsSsaFalf() {
		return isSsaFalf;
	}


	public void setIsSsaFalf(String isSsaFalf) {
		this.isSsaFalf = isSsaFalf;
	}


	public String getOemId() {
		return oemId;
	}


	public void setOemId(String oemId) {
		this.oemId = oemId;
	}


	public String getGroupId() {
		return groupId;
	}


	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}


	public String getOemCode() {
		return oemCode;
	}


	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}


	public String getGroupCode() {
		return groupCode;
	}


	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}


	public String getCreator() {
		return creator;
	}


	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatedName() {
		return createdName;
	}


	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}


	public LocalDateTime getCreatedDate() {
		return createdDate;
	}


	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}


	public String getModifier() {
		return modifier;
	}


	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifyName() {
		return modifyName;
	}


	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}


	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}


	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}


	public String getIsEnable() {
		return isEnable;
	}


	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}


	public String getSdpUserId() {
		return sdpUserId;
	}


	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}


	public String getSdpOrgId() {
		return sdpOrgId;
	}


	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}


	public String getUpdateControlId() {
		return updateControlId;
	}


	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}


	public String getChildDlrType() {
		return childDlrType;
	}


	public void setChildDlrType(String childDlrType) {
		this.childDlrType = childDlrType;
	}


	public String getIsNch() {
		return isNch;
	}


	public void setIsNch(String isNch) {
		this.isNch = isNch;
	}


	public String getIsNchSaleAuth() {
		return isNchSaleAuth;
	}


	public void setIsNchSaleAuth(String isNchSaleAuth) {
		this.isNchSaleAuth = isNchSaleAuth;
	}


	public String getIsNchServiceAuth() {
		return isNchServiceAuth;
	}


	public void setIsNchServiceAuth(String isNchServiceAuth) {
		this.isNchServiceAuth = isNchServiceAuth;
	}


	public String getServiceBigAreaId() {
		return serviceBigAreaId;
	}


	public void setServiceBigAreaId(String serviceBigAreaId) {
		this.serviceBigAreaId = serviceBigAreaId;
	}


	public String getServiceSmallAreaId() {
		return serviceSmallAreaId;
	}


	public void setServiceSmallAreaId(String serviceSmallAreaId) {
		this.serviceSmallAreaId = serviceSmallAreaId;
	}


	public String getCarBrandCn() {
		return carBrandCn;
	}


	public void setCarBrandCn(String carBrandCn) {
		this.carBrandCn = carBrandCn;
	}

	public LocalDateTime getDlrBussDate() {
		return dlrBussDate;
	}


	public void setDlrBussDate(LocalDateTime localDateTime) {
		this.dlrBussDate = localDateTime;
	}


	public String getWeekendBusinessHour() {
		return weekendBusinessHour;
	}


	public void setWeekendBusinessHour(String weekendBusinessHour) {
		this.weekendBusinessHour = weekendBusinessHour;
	}


	public String getPickUpAddress() {
		return pickUpAddress;
	}


	public void setPickUpAddress(String pickUpAddress) {
		this.pickUpAddress = pickUpAddress;
	}


	public String getDoorPhoto() {
		return doorPhoto;
	}


	public void setDoorPhoto(String doorPhoto) {
		this.doorPhoto = doorPhoto;
	}

	public String getAgentArea() {
		return agentArea;
	}


	public void setAgentArea(String agentArea) {
		this.agentArea = agentArea;
	}

	public String getDlrBuildAgreementUrl() {
		return dlrBuildAgreementUrl;
	}

	public void setDlrBuildAgreementUrl(String dlrBuildAgreementUrl) {
		this.dlrBuildAgreementUrl = dlrBuildAgreementUrl;
	}

	
	public String getDlrStatusCn() {
		return dlrStatusCn;
	}

	public void setDlrStatusCn(String dlrStatusCn) {
		this.dlrStatusCn = dlrStatusCn;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public String getAgreementAttachmentId() {
		return agreementAttachmentId;
	}

	public void setAgreementAttachmentId(String agreementAttachmentId) {
		this.agreementAttachmentId = agreementAttachmentId;
	}

	public String getAttachUrl() {
		return attachUrl;
	}

	public void setAttachUrl(String attachUrl) {
		this.attachUrl = attachUrl;
	}

	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	
	
	public String getBigAreaName() {
		return bigAreaName;
	}

	public void setBigAreaName(String bigAreaName) {
		this.bigAreaName = bigAreaName;
	}

	
	
	public String getCooperationAgentUrl() {
		return cooperationAgentUrl;
	}

	public void setCooperationAgentUrl(String cooperationAgentUrl) {
		this.cooperationAgentUrl = cooperationAgentUrl;
	}

	public LocalDateTime getCooperationAgentBeginDate() {
		return cooperationAgentBeginDate;
	}

	public void setCooperationAgentBeginDate(LocalDateTime cooperationAgentBeginDate) {
		this.cooperationAgentBeginDate = cooperationAgentBeginDate;
	}

	public LocalDateTime getCooperationAgentEndDate() {
		return cooperationAgentEndDate;
	}

	public void setCooperationAgentEndDate(LocalDateTime cooperationAgentEndDate) {
		this.cooperationAgentEndDate = cooperationAgentEndDate;
	}

	public String getRailTransitInfo() {
		return railTransitInfo;
	}

	public void setRailTransitInfo(String railTransitInfo) {
		this.railTransitInfo = railTransitInfo;
	}

	@Override
	public String toString() {
		return "MdmOrgDlr [dlrId=" + dlrId + ", dlrCode=" + dlrCode + ", dlrShortName=" + dlrShortName
				+ ", dlrFullName=" + dlrFullName + ", dlrEnName=" + dlrEnName + ", dlrNameOld=" + dlrNameOld
				+ ", compNameOld=" + compNameOld + ", compSpell=" + compSpell + ", compType=" + compType
				+ ", parentDlrId=" + parentDlrId + ", dlrAnswerCode=" + dlrAnswerCode + ", sapDlrId=" + sapDlrId
				+ ", dlrSymbol=" + dlrSymbol + ", guno=" + guno + ", registerMoney=" + registerMoney
				+ ", sRegisterMoney=" + sRegisterMoney + ", dlrHardwareClass=" + dlrHardwareClass + ", showAcreage="
				+ showAcreage + ", factoryAcreage=" + factoryAcreage + ", coverAcreage=" + coverAcreage
				+ ", tatolAcreage=" + tatolAcreage + ", fareRange=" + fareRange + ", dlrBussDate=" + dlrBussDate
				+ ", dlrLevel=" + dlrLevel + ", dlrDebutTime=" + dlrDebutTime + ", linkAddr=" + linkAddr
				+ ", smallAreaId=" + smallAreaId + ", bigAreaId=" + bigAreaId + ", provinceId=" + provinceId
				+ ", provinceName=" + provinceName + ", cityId=" + cityId + ", cityName=" + cityName + ", countyId="
				+ countyId + ", countyName=" + countyName + ", fax=" + fax + ", phone=" + phone + ", mobile=" + mobile
				+ ", zip=" + zip + ", email=" + email + ", urgSosTel=" + urgSosTel + ", saleTel=" + saleTel
				+ ", saleFax=" + saleFax + ", saleEmail=" + saleEmail + ", serviceTel=" + serviceTel + ", serviceFax="
				+ serviceFax + ", serviceEmail=" + serviceEmail + ", legalPerson=" + legalPerson + ", legalPersonCard="
				+ legalPersonCard + ", legalPersonCardType=" + legalPersonCardType + ", sMaster=" + sMaster
				+ ", sMasterConn=" + sMasterConn + ", sAddr=" + sAddr + ", managerName=" + managerName + ", managerTel="
				+ managerTel + ", balanceCertificate=" + balanceCertificate + ", balanceDate=" + balanceDate
				+ ", maintainCertificate=" + maintainCertificate + ", maintainCertDate=" + maintainCertDate
				+ ", initDate=" + initDate + ", ceo=" + ceo + ", ceoConn=" + ceoConn + ", deptModelId=" + deptModelId
				+ ", initFlag=" + initFlag + ", certificateFlag=" + certificateFlag + ", dlrStatus=" + dlrStatus
				+ ", dlrReleation=" + dlrReleation + ", dlrType=" + dlrType + ", dlrTypeName=" + dlrTypeName
				+ ", releationStatus=" + releationStatus + ", orderNo=" + orderNo + ", remark=" + remark
				+ ", carBrandCode=" + carBrandCode + ", carBrandCn=" + carBrandCn + ", doqdFlag=" + doqdFlag
				+ ", dlrSort=" + dlrSort + ", dpOrgid=" + dpOrgid + ", pvCompCode=" + pvCompCode + ", isSynchronous="
				+ isSynchronous + ", areaId=" + areaId + ", orgType=" + orgType + ", linkDlrId=" + linkDlrId
				+ ", onlineTime=" + onlineTime + ", wechat=" + wechat + ", lng=" + lng + ", lat=" + lat
				+ ", isSecurity=" + isSecurity + ", dlr4sLevel=" + dlr4sLevel + ", compName=" + compName + ", netId="
				+ netId + ", vDlrCode=" + vDlrCode + ", pDlrCode=" + pDlrCode + ", sDlrCode=" + sDlrCode
				+ ", ucDlrCode=" + ucDlrCode + ", aiDlrCode=" + aiDlrCode + ", capDlrCode=" + capDlrCode
				+ ", mdsBigAreaId=" + mdsBigAreaId + ", oldDlrId=" + oldDlrId + ", emissionStandard=" + emissionStandard
				+ ", belongModule=" + belongModule + ", climateStatus=" + climateStatus + ", onlineFlag=" + onlineFlag
				+ ", printTemplet=" + printTemplet + ", spFlag=" + spFlag + ", isOkCare=" + isOkCare + ", isSsaFast="
				+ isSsaFast + ", isSsaSelf=" + isSsaSelf + ", isSsaSpray=" + isSsaSpray + ", companyAreaCode="
				+ companyAreaCode + ", companyUniqueCode=" + companyUniqueCode + ", companyName=" + companyName
				+ ", isSend=" + isSend + ", transportLicense=" + transportLicense + ", isSsaFalf=" + isSsaFalf
				+ ", oemId=" + oemId + ", groupId=" + groupId + ", oemCode=" + oemCode + ", groupCode=" + groupCode
				+ ", creator=" + creator + ", createdName=" + createdName + ", createdDate=" + createdDate
				+ ", modifier=" + modifier + ", modifyName=" + modifyName + ", lastUpdatedDate=" + lastUpdatedDate
				+ ", isEnable=" + isEnable + ", sdpUserId=" + sdpUserId + ", sdpOrgId=" + sdpOrgId
				+ ", updateControlId=" + updateControlId + ", childDlrType=" + childDlrType + ", isNch=" + isNch
				+ ", isNchSaleAuth=" + isNchSaleAuth + ", isNchServiceAuth=" + isNchServiceAuth + ", serviceBigAreaId="
				+ serviceBigAreaId + ", serviceSmallAreaId=" + serviceSmallAreaId + ", saServiceStatus="
				+ saServiceStatus + ", caServiceStatus=" + caServiceStatus + ", hasEvSale=" + hasEvSale
				+ ", hasEvService=" + hasEvService + ", agentArea=" + agentArea + ", weekendBusinessHour="
				+ weekendBusinessHour + ", pickUpAddress=" + pickUpAddress + ", doorPhoto=" + doorPhoto
				+ ", weekdaysBusinessHour=" + weekdaysBusinessHour + ", isFirst=" + isFirst + ", stockHolder="
				+ stockHolder + ", companyId=" + companyId + ", dlrBuildAgreementUrl=" + dlrBuildAgreementUrl
				+ ", agentId=" + agentId + ", agentName=" + agentName + ", agentCompanyId=" + agentCompanyId
				+ ", agentCompanyName=" + agentCompanyName + ", dlrStatusCn=" + dlrStatusCn + ", agreementAttachmentId="
				+ agreementAttachmentId + ", attachUrl=" + attachUrl + "]";
	}

	

}
