package com.ly.adp.csc.entities.in;

import com.ly.adp.csc.entities.constants.PageConstant;
import com.ly.mp.component.helper.StringHelper;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/9/15
 */
public class LeadsEventDTO implements Serializable {

    private static final long serialVersionUID = -5946992009694734284L;

    @ApiModelProperty("smartId")
    private String smartId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("行")
    private Integer pageIndex;

    @ApiModelProperty("页数")
    private Integer pageSize;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "LeadsEventDTO{" +
                "smartId='" + smartId + '\'' +
                ", pageIndex=" + pageIndex +
                ", pageSize=" + pageSize +
                '}';
    }

    /**
     * 获取合法页码
     *
     * @return Integer
     */
    public Integer validPageIndex() {
        return StringHelper.IsEmptyOrNull(pageIndex) || pageIndex < 1 ? PageConstant.DEFAULT_PAGE_INDEX : pageIndex;
    }

    /**
     * 获取合法页数
     *
     * @return Integer
     */
    public Integer validPageSize() {
//        return StringHelper.IsEmptyOrNull(pageSize) || pageSize < 1 ? PageConstant.DEFAULT_PAGE_SIZE : Math.min(pageSize, PageConstant.MAX_PAGE_SIZE);
        return StringHelper.IsEmptyOrNull(pageSize) || pageSize < 1 ? PageConstant.MAX_PAGE_SIZE : pageSize;
    }

}
