package com.ly.adp.csc.controller;


import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.impl.BuLogoutBiz;
import com.ly.adp.csc.otherservice.IBasedataFeignClient;
import com.ly.adp.csc.service.IBuLogoutBiz;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 退网信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@RestController
@Api(value = "退网管理服务", tags = "退网管理服务")
@RequestMapping("/ly/adp/csc/buLogout")
public class BuLogoutController {

    @Autowired
    IBuLogoutBiz iBuLogoutBiz;
    @Autowired
    IBasedataFeignClient baseClient;


    @ApiOperation(value = "门店退网发起查询", notes = "门店退网查询")
    @PostMapping(value = "/sacBuLogOutQueryByDlr.do")
    public ListResult<Map<String, Object>> sacBuLogOutQueryByDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                      @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutQueryByDlr(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网维护", notes = "退网维护")
    @PostMapping(value = "/sacBuLogOutSave.do")
    public OptResult sacBuLogOutSave(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                          @RequestBody(required = false) ParamBase<Map<String, Object>> paramBase){
        return BusicenInvoker.doOpt(() -> {return iBuLogoutBiz.sacBuLogOutSave(paramBase,authentication);}).result();
    }

    @ApiOperation(value = "城市公司退网发起查询", notes = "城市公司退网发起查询")
    @PostMapping(value = "/sacBuLogOutQueryByCompanySponsor.do")
    public ListResult<Map<String, Object>> sacBuLogOutQueryByCompanySponsor(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                      @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutQueryByCompanySponsor(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "代理商退网发起查询", notes = "代理商退网发起查询")
    @PostMapping(value = "/sacBuLogOutQueryByAgentSponsor.do")
    public ListResult<Map<String, Object>> sacBuLogOutQueryByAgentSponsor(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                            @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutQueryByAgentSponsor(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网查询", notes = "退网查询")
    @PostMapping(value = "/sacBuLogOutQuery.do")
    public ListResult<Map<String, Object>> sacBuLogOutQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                          @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutQuery(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网审批", notes = "退网审批")
    @PostMapping(value = "/sacBuLogOutAppPower.do")
    public EntityResult<Map<String, Object>> sacBuLogOutAppPower(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                 @RequestBody(required = false) ParamBase<Map<String, Object>> dateInfo){
        return BusicenInvoker.doEntity(() -> {return iBuLogoutBiz.sacBuLogOutAppPower(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网清算", notes = "退网清算")
    @PostMapping(value = "/sacBuLogOutClearing.do")
    public OptResult sacBuLogOutClearing(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                 @RequestBody(required = false) ParamBase<Map<String, Object>> dateInfo){
        return BusicenInvoker.doOpt(() -> {return iBuLogoutBiz.sacBuLogOutClearing(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网清算门店查询", notes = "退网清算门店查询")
    @PostMapping(value = "/sacBuLogOutClearingQueryByDlr.do")
    public ListResult<Map<String, Object>> sacBuLogOutClearingQueryByDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                         @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutClearingQueryByDlr(dateInfo,token);}).result();
    }

    @ApiOperation(value = "退网城市公司审核通过", notes = "退网城市公司审核通过")
    @PostMapping(value = "/sacBuLogOutAuditByCompany.do")
    public OptResult sacBuLogOutAuditByCompany(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                         @RequestBody(required = false) ParamBase<Map<String, Object>> dateInfo){
        return BusicenInvoker.doOpt(() -> {return iBuLogoutBiz.sacBuLogOutAuditByCompany(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "退网审核日志查询", notes = "退网审核日志查询")
    @PostMapping(value = "/sacBuLogOutRecordQuery.do")
    public ListResult<Map<String, Object>> sacBuLogOutRecordQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                               @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutRecordQuery(dateInfo,authentication);}).result();
    }

    @ApiOperation(value = "门店退网发起-总部导出", notes = "门店退网发起-总部导出")
    @RequestMapping(value = "/exportSacBuLogOutQueryByDlr.do", method = RequestMethod.POST)
    public OptResult exportSacBuLogOutQueryByDlr(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                                       @RequestBody(required = false) ParamPage<Map<String, Object>> info,
                                                       HttpServletResponse response) throws Exception {
        return iBuLogoutBiz.exportSacBuLogOutQueryByDlr(info, response, authentication);
    }

    @ApiOperation(value = "退网清算门店查询导出", notes = "退网清算门店查询导出")
    @RequestMapping(value = "/exportSacBuLogOutClearingQueryByDlr.do", method = RequestMethod.POST)
    public OptResult exportSacBuLogOutClearingQueryByDlr(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                                 @RequestBody(required = false) ParamPage<Map<String, Object>> info,
                                                 HttpServletResponse response) throws Exception {
        return iBuLogoutBiz.exportSacBuLogOutClearingQueryByDlr(info, response, authentication);
    }

    @ApiOperation(value = "门店确认退网导出", notes = "门店确认退网导出")
    @RequestMapping(value = "/exportsacBuLogOutQueryByDlrLogout.do", method = RequestMethod.POST)
    public OptResult exportsacBuLogOutQueryByDlrLogout(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                                         @RequestBody(required = false) ParamPage<Map<String, Object>> info,
                                                         HttpServletResponse response) throws Exception {
        return iBuLogoutBiz.exportsacBuLogOutQueryByDlrLogout(info, response, authentication);
    }

    @ApiOperation(value = "门店确认退网查询", notes = "门店确认退网查询")
    @PostMapping(value = "/sacBuLogOutQueryByDlrLogout.do")
    public ListResult<Map<String, Object>> sacBuLogOutQueryByDlrLogout(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                                  @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return iBuLogoutBiz.sacBuLogOutQueryByDlrLogout(dateInfo,authentication);}).result();
    }


}

