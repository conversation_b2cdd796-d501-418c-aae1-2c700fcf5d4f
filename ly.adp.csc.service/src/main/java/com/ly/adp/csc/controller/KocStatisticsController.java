//package com.ly.adp.csc.controller;
//
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.google.common.net.HttpHeaders;
//import com.ly.adp.csc.service.IKocStatisticsService;
//import com.ly.mp.busicen.common.context.BusicenInvoker;
//import com.ly.mp.component.entities.ListResult;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//
///**
// * <p>
// * KOC统计控制器
// * </p>
// *
// * <AUTHOR> System
// * @since 2025-01-25
// */
//@RestController
//@Api(value = "KOC统计服务", tags = "KOC统计服务")
//@RequestMapping(value = "/ly/adp/csc/koc/statistics", produces = { MediaType.APPLICATION_JSON_VALUE })
//public class KocStatisticsController {
//
//    @Autowired
//    private IKocStatisticsService kocStatisticsService;
//
//    @ApiOperation(value = "获取标签统计", notes = "获取标签统计信息")
//    @PostMapping(value = "/getTagStatistics.do")
//    public List<Map<String, Object>> getTagStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocStatisticsService.getTagStatistics(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取达人类型统计", notes = "获取达人类型统计信息")
//    @PostMapping(value = "/getExpertTypeStatistics.do")
//    public List<Map<String, Object>> getExpertTypeStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocStatisticsService.getExpertTypeStatistics(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取操作统计", notes = "获取操作日志统计")
//    @PostMapping(value = "/getOperationStatistics.do")
//    public ListResult<Map<String, Object>> getOperationStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doList(() -> {
//            return kocStatisticsService.getOperationStatistics(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取用户打标统计", notes = "获取用户打标统计")
//    @PostMapping(value = "/getUserLabelingStatistics.do")
//    public ListResult<Map<String, Object>> getUserLabelingStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doList(() -> {
//            return kocStatisticsService.getUserLabelingStatistics(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取系统概览", notes = "获取系统概览统计")
//    @PostMapping(value = "/getSystemOverview.do")
//    public Map<String, Object> getSystemOverview(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocStatisticsService.getSystemOverview(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取热门标签", notes = "获取热门标签统计")
//    @PostMapping(value = "/getPopularTags.do")
//    public List<Map<String, Object>> getPopularTags(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            Integer limit = dataInfo != null ? (Integer) dataInfo.get("limit") : null;
//            return kocStatisticsService.getPopularTags(limit, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取热门达人类型", notes = "获取热门达人类型统计")
//    @PostMapping(value = "/getPopularExpertTypes.do")
//    public List<Map<String, Object>> getPopularExpertTypes(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            Integer limit = dataInfo != null ? (Integer) dataInfo.get("limit") : null;
//            return kocStatisticsService.getPopularExpertTypes(limit, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取操作趋势", notes = "获取操作趋势统计")
//    @PostMapping(value = "/getOperationTrend.do")
//    public List<Map<String, Object>> getOperationTrend(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocStatisticsService.getOperationTrend(dataInfo, authentication);
//        }).result();
//    }
//}
