package com.ly.adp.csc.controller;

import java.util.List;
import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.in.SacTestDriveLongApplyIn;
import com.ly.adp.csc.entities.in.SacTestDriveReviewRecordIn;
import com.ly.adp.csc.service.ISacTestDriveLongApplyService;
import com.ly.adp.csc.service.ISacTestDriveReviewRecordService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(value = "CSC试乘试驾服务", tags = "CSC试乘试驾服务")
@RestController
@RequestMapping(value = "/ly/sac/testdrive/", produces = { MediaType.APPLICATION_JSON_VALUE })
public class TestDriveController {
	
	@Autowired
	ISacTestDriveReviewRecordService sacTestDriveReviewRecordService;
	
	@Autowired
	ISacTestDriveLongApplyService sacTestDriveLongApplyService;

	
//	@Autowired
//	SmsMessageService smsMessageService;
	
	@ApiOperation(value = "试乘试驾跟进记录查询", notes = "试乘试驾跟进记录查询")
	@PostMapping(value = "/queryrecord.do")
	public ListResult<Map<String, Object>> sacTestDriveReviewRecordFindAll(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
			@RequestBody(required = false) SacTestDriveReviewRecordIn dataInfo) {
		return BusicenInvoker.doList(() -> sacTestDriveReviewRecordService.sacTestDriveReviewRecordFindAll(dataInfo)).result();
	}
	
	@ApiOperation(value = "试乘试驾跟进记录新增", notes = "试乘试驾跟进记录新增")
	@PostMapping(value = "/reviewrecordsave.do")
	public OptResult sacTestDriveReviewRecordSave(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
			@RequestBody(required = false) SacTestDriveReviewRecordIn dataInfo) {
		return BusicenInvoker.doOpt(()->sacTestDriveReviewRecordService.sacTestDriveReviewRecordSave(dataInfo, token)).result();
	}
	
	/**
	 * 超长试驾申请维护
	 * @param token
	 * @param dataInfo
	 * @return
	 */
	@ApiOperation(value = "超长试驾申请维护", notes = "超长试驾申请维护")
	@PostMapping(value = "/longapplysave.do")
	public EntityResult<Map<String, Object>> longApplySave(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
			@RequestBody(required = false) SacTestDriveLongApplyIn dataInfo) {
		return BusicenInvoker.doEntity(() -> sacTestDriveLongApplyService.sacTestDriveLongApplySave(dataInfo, token)).result();
	}
	
	/**
	 * 超长试驾申请查询
	 * @param dataInfo
	 * @return
	 */
	@ApiOperation(value = "超长试驾申请查询", notes = "超长试驾申请查询")
	@PostMapping(value = "/querylongapply.do")
	public ListResult<Map<String, Object>> queryLongApply(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
			@RequestBody(required = false) SacTestDriveLongApplyIn dataInfo) {
		return BusicenInvoker.doList(() -> sacTestDriveLongApplyService.sacTestDriveLongApplyQueryFindAll(token, dataInfo)).result();
	}
	
    @ApiOperation(value = "上传图片", notes = "上传图片")
    @RequestMapping(value = "uploadimage.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> uploadImage(
    		@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
    		@RequestBody(required = false) MultipartFile uploadfile) {
    	return BusicenInvoker.doList(() -> sacTestDriveLongApplyService.uploadImage(uploadfile,authentication)).result();
	}

	@ApiOperation(value = "获取H5链接发送短信", notes = "获取H5链接发送短信")
	@RequestMapping(value = "/sendTemplateLinkMessage.do", method = RequestMethod.POST)
	public OptResult sendTemplateLinkMessage(
			@RequestHeader(name = "authorization", required = false) String authentication,
			@RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
		mapParam.getParam().put("token", authentication);
		return com.ly.mp.busi.base.context.BusicenInvoker.doOpt(() -> sacTestDriveLongApplyService.sendTemplateLinkMessage(mapParam.getParam())).result();
	}

	@ApiOperation(value = "手动推送五星随手评", notes = "手动推送五星随手评")
	@RequestMapping(value = "/sacTestDriveSendMessage.do", method = RequestMethod.POST)
	public OptResult sacTestDriveSendMessage(@RequestHeader(name = "authorization", required = false) String authentication,
											 @RequestBody(required = false) ParamBase<List<Map<String, Object>>> mapParam) {
		return com.ly.mp.busi.base.context.BusicenInvoker.doOpt(() -> sacTestDriveLongApplyService.sacTestDriveSendMessage(mapParam.getParam(),authentication)).result();
	}

	@ApiOperation(value = "校验是否发送消息", notes = "校验是否发送消息")
	@PostMapping(value = "/checkIsSendMessage.do")
	public EntityResult<Map<String, Object>> checkIsSendMessage(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
			@RequestBody(required = false) Map<String,Object> dataInfo) {
		return BusicenInvoker.doEntity(() -> sacTestDriveLongApplyService.checkIsSendMessage(dataInfo, token)).result();
	}

//    @ApiOperation(value = "测试发送短信", notes = "测试发送短信")
//    @RequestMapping(value = "sendmessage.do", method = RequestMethod.POST)
//    public OptResult sendMessage(
//    		@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//    		@RequestBody(required = false) Map<String, Object> param) {
//    	return BusicenInvoker.doOpt(() -> smsMessageService.sendAgentMessage(param, authentication)).result();
//	}
}