package com.ly.adp.csc.gateway;

import com.ly.adp.csc.entities.SacUserTagRel;
import com.ly.adp.csc.enums.UserRelationTypeEnum;

import java.util.List;

/**
 * 用户关系数据网关接口
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface UserRelationGateway {

    /**
     * 批量查询用户关系数据
     * @param smartIds 用户ID列表
     * @param relationType 关系类型
     * @return 关系数据列表
     */
    List<SacUserTagRel> queryBatchUserRelations(List<String> smartIds, UserRelationTypeEnum relationType);

    /**
     * 批量查询用户标签关系
     * @param smartIds 用户ID列表
     * @return 标签关系数据列表
     */
    default List<SacUserTagRel> queryBatchUserTags(List<String> smartIds) {
        return queryBatchUserRelations(smartIds, UserRelationTypeEnum.TAG);
    }

    /**
     * 批量查询用户达人类型关系
     * @param smartIds 用户ID列表
     * @return 达人类型关系数据列表
     */
    default List<SacUserTagRel> queryBatchUserExpertTypes(List<String> smartIds) {
        return queryBatchUserRelations(smartIds, UserRelationTypeEnum.EXPERT_TYPE);
    }

    /**
     * 批量查询用户备注关系
     * @param smartIds 用户ID列表
     * @return 备注关系数据列表
     */
    default List<SacUserTagRel> queryBatchUserRemarks(List<String> smartIds) {
        return queryBatchUserRelations(smartIds, UserRelationTypeEnum.REMARK);
    }
}
