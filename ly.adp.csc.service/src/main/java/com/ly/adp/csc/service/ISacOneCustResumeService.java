package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.LeadsEventVO;
import com.ly.adp.csc.entities.in.LeadsEventDTO;
import com.ly.adp.csc.entities.in.SacDlrDTO;
import com.ly.adp.csc.entities.in.SacOneCustResumeIn;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface ISacOneCustResumeService {

    ListResult<Map<String, Object>> sacQueryResumeFindAllbyphone(SacOneCustResumeIn dataInfo);

    /**
     * 客户履历查询
     *
     * @param pageInfo
     * @param info
     * @return
     */
    ListResult<Map<String, Object>> sacOnecustResumeFindAll(SacOneCustResumeIn dataInfo);

    EntityResult<Integer> queryFastPhoneo(Map<String, Object> dataInfo);

    /**
     * 客户履历新增
     *
     * @param info
     * @return
     */
    EntityResult<Map<String, Object>> sacOnecustResumeSaveInfo(Map<String, Object> dataInfo, String token);

    OptResult sacOnecustResumeSaveBanchInfo(List<Map<String, Object>> dateInfo, String authentication);

    OptResult activityApply(Map<String, Object> param, String token);

    EntityResult<Map<String, Object>> activityApplyInfoSave(Map<String, Object> dateInfo, String authentication);

    ListResult<Map<String, Object>> queryVirtualRecord(Map<String, Object> dataInfo);

    ListResult<LeadsEventVO> queryLeadsEvent(LeadsEventDTO dataInfo);

    ListResult<Map<String, Object>> queryArrivalStoreReport(ParamPage<Map<String, Object>> dataInfo, String token);

    OptResult exportArrivalStoreReport(ParamPage<Map<String, Object>> param, HttpServletResponse response, String authentication);

    ListResult<Map<String, Object>> queryDlrName(SacDlrDTO paramDto);
}
