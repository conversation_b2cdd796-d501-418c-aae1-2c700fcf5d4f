package com.ly.adp.csc.entities.qry;

import com.ly.adp.csc.entities.in.PageInfo;

import java.io.Serializable;

/**
 * PC用户标签查询请求qry
 * <AUTHOR>
 * @since 2025/7/28
 **/
public class PCUserTagQry extends PageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * smartId
     */
    private String smartId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 标签id
     */
    private String tagId;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    @Override
    public String toString() {
        return "PCUserTagQry{" +
                "smartId='" + smartId + '\'' +
                ", phone='" + phone + '\'' +
                ", nickName='" + nickName + '\'' +
                ", tagId='" + tagId + '\'' +
                '}';
    }
}