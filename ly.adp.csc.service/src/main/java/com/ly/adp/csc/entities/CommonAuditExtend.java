package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公共活动审批表-扩展使用
 * （为了方便自己对审批表做操作，不改原jar包的内容，只扩展，不修改）
 */
@TableName("t_sac_common_audit")
public class CommonAuditExtend implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId("AUDIT_ID")
    private String auditId;
    @TableField("BILL_TYPE")
    private String billType;
    @TableField("BILL_TYPE_NAME")
    private String billTypeName;
    @TableField("BUSINESS_TYPE")
    private String businessType;
    @TableField("BUSINESS_TYPE_NAME")
    private String businessTypeName;
    @TableField("BILL_CODE")
    private String billCode;
    @TableField("RELATION_BILL_CODE")
    private String relationBillCode;
    @TableField("ORG_CODE")
    private String orgCode;
    @TableField("ORG_NAME")
    private String orgName;
    @TableField("NODE_CODE")
    private String nodeCode;
    @TableField("NODE_NAME")
    private String nodeName;
    @TableField("APPLY_PERSON_ID")
    private String applyPersonId;
    @TableField("APPLY_PERSON_NAME")
    private String applyPersonName;
    @TableField("APPLY_TIME")
    private LocalDateTime applyTime;
    @TableField("APPLY_DESC")
    private String applyDesc;
    @TableField("SH_PERSON_ID")
    private String shPersonId;
    @TableField("SH_PERSON_NAME")
    private String shPersonName;
    @TableField("SH_DESC")
    private String shDesc;
    @TableField("SH_TIME")
    private LocalDateTime shTime;
    @TableField("SH_STATUS")
    private String shStatus;
    @TableField("SH_STATUS_NAME")
    private String shStatusName;
    @TableField("COLUMN1")
    private String column1;
    @TableField("COLUMN2")
    private String column2;
    @TableField("COLUMN3")
    private String column3;
    @TableField("COLUMN4")
    private String column4;
    @TableField("COLUMN5")
    private String column5;
    @TableField("COLUMN6")
    private String column6;
    @TableField("COLUMN7")
    private String column7;
    @TableField("COLUMN8")
    private String column8;
    @TableField("COLUMN9")
    private String column9;
    @TableField("COLUMN10")
    private String column10;
    @TableField("COLUMN11")
    private String column11;
    @TableField("COLUMN12")
    private String column12;
    @TableField("COLUMN13")
    private String column13;
    @TableField("COLUMN14")
    private String column14;
    @TableField("COLUMN15")
    private String column15;
    @TableField("COLUMN16")
    private String column16;
    @TableField("COLUMN17")
    private String column17;
    @TableField("COLUMN18")
    private String column18;
    @TableField("COLUMN19")
    private String column19;
    @TableField("COLUMN20")
    private String column20;
    @TableField("BIG_COLUMN1")
    private String bigColumn1;
    @TableField("BIG_COLUMN2")
    private String bigColumn2;
    @TableField("BIG_COLUMN3")
    private String bigColumn3;
    @TableField("BIG_COLUMN4")
    private String bigColumn4;
    @TableField("BIG_COLUMN5")
    private String bigColumn5;
    @TableField("EXTENDS_JSON")
    private String extendsJson;
    @TableField("OEM_ID")
    private String oemId;
    @TableField("GROUP_ID")
    private String groupId;
    @TableField(
            value = "CREATOR",
            fill = FieldFill.INSERT
    )
    private String creator;
    @TableField(
            value = "CREATED_NAME",
            fill = FieldFill.INSERT
    )
    private String createdName;
    @TableField(
            value = "CREATED_DATE",
            fill = FieldFill.INSERT
    )
    private LocalDateTime createdDate;
    @TableField(
            value = "MODIFIER",
            fill = FieldFill.INSERT_UPDATE
    )
    private String modifier;
    @TableField(
            value = "MODIFY_NAME",
            fill = FieldFill.INSERT_UPDATE
    )
    private String modifyName;
    @TableField(
            value = "LAST_UPDATED_DATE",
            fill = FieldFill.INSERT_UPDATE
    )
    private LocalDateTime lastUpdatedDate;
    @TableField("IS_ENABLE")
    private String isEnable;
    @TableField(
            value = "UPDATE_CONTROL_ID",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updateControlId;

    public CommonAuditExtend() {
    }

    public String getAuditId() {
        return this.auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getBillType() {
        return this.billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getBillTypeName() {
        return this.billTypeName;
    }

    public void setBillTypeName(String billTypeName) {
        this.billTypeName = billTypeName;
    }

    public String getBusinessType() {
        return this.businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessTypeName() {
        return this.businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getBillCode() {
        return this.billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getRelationBillCode() {
        return this.relationBillCode;
    }

    public void setRelationBillCode(String relationBillCode) {
        this.relationBillCode = relationBillCode;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getNodeCode() {
        return this.nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeName() {
        return this.nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getApplyPersonId() {
        return this.applyPersonId;
    }

    public void setApplyPersonId(String applyPersonId) {
        this.applyPersonId = applyPersonId;
    }

    public String getApplyPersonName() {
        return this.applyPersonName;
    }

    public void setApplyPersonName(String applyPersonName) {
        this.applyPersonName = applyPersonName;
    }

    public LocalDateTime getApplyTime() {
        return this.applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getApplyDesc() {
        return this.applyDesc;
    }

    public void setApplyDesc(String applyDesc) {
        this.applyDesc = applyDesc;
    }

    public String getShPersonId() {
        return this.shPersonId;
    }

    public void setShPersonId(String shPersonId) {
        this.shPersonId = shPersonId;
    }

    public String getShPersonName() {
        return this.shPersonName;
    }

    public void setShPersonName(String shPersonName) {
        this.shPersonName = shPersonName;
    }

    public String getShDesc() {
        return this.shDesc;
    }

    public void setShDesc(String shDesc) {
        this.shDesc = shDesc;
    }

    public LocalDateTime getShTime() {
        return this.shTime;
    }

    public void setShTime(LocalDateTime shTime) {
        this.shTime = shTime;
    }

    public String getShStatus() {
        return this.shStatus;
    }

    public void setShStatus(String shStatus) {
        this.shStatus = shStatus;
    }

    public String getShStatusName() {
        return this.shStatusName;
    }

    public void setShStatusName(String shStatusName) {
        this.shStatusName = shStatusName;
    }

    public String getColumn1() {
        return this.column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return this.column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return this.column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return this.column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return this.column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return this.column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return this.column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return this.column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return this.column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return this.column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getColumn11() {
        return this.column11;
    }

    public void setColumn11(String column11) {
        this.column11 = column11;
    }

    public String getColumn12() {
        return this.column12;
    }

    public void setColumn12(String column12) {
        this.column12 = column12;
    }

    public String getColumn13() {
        return this.column13;
    }

    public void setColumn13(String column13) {
        this.column13 = column13;
    }

    public String getColumn14() {
        return this.column14;
    }

    public void setColumn14(String column14) {
        this.column14 = column14;
    }

    public String getColumn15() {
        return this.column15;
    }

    public void setColumn15(String column15) {
        this.column15 = column15;
    }

    public String getColumn16() {
        return this.column16;
    }

    public void setColumn16(String column16) {
        this.column16 = column16;
    }

    public String getColumn17() {
        return this.column17;
    }

    public void setColumn17(String column17) {
        this.column17 = column17;
    }

    public String getColumn18() {
        return this.column18;
    }

    public void setColumn18(String column18) {
        this.column18 = column18;
    }

    public String getColumn19() {
        return this.column19;
    }

    public void setColumn19(String column19) {
        this.column19 = column19;
    }

    public String getColumn20() {
        return this.column20;
    }

    public void setColumn20(String column20) {
        this.column20 = column20;
    }

    public String getBigColumn1() {
        return this.bigColumn1;
    }

    public void setBigColumn1(String bigColumn1) {
        this.bigColumn1 = bigColumn1;
    }

    public String getBigColumn2() {
        return this.bigColumn2;
    }

    public void setBigColumn2(String bigColumn2) {
        this.bigColumn2 = bigColumn2;
    }

    public String getBigColumn3() {
        return this.bigColumn3;
    }

    public void setBigColumn3(String bigColumn3) {
        this.bigColumn3 = bigColumn3;
    }

    public String getBigColumn4() {
        return this.bigColumn4;
    }

    public void setBigColumn4(String bigColumn4) {
        this.bigColumn4 = bigColumn4;
    }

    public String getBigColumn5() {
        return this.bigColumn5;
    }

    public void setBigColumn5(String bigColumn5) {
        this.bigColumn5 = bigColumn5;
    }

    public String getExtendsJson() {
        return this.extendsJson;
    }

    public void setExtendsJson(String extendsJson) {
        this.extendsJson = extendsJson;
    }

    public String getOemId() {
        return this.oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return this.groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return this.createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return this.createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return this.modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return this.lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return this.isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return this.updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String toString() {
        return "CommonAudit{auditId=" + this.auditId + ", billType=" + this.billType + ", billTypeName=" + this.billTypeName + ", businessType=" + this.businessType + ", businessTypeName=" + this.businessTypeName + ", billCode=" + this.billCode + ", relationBillCode=" + this.relationBillCode + ", orgCode=" + this.orgCode + ", orgName=" + this.orgName + ", nodeCode=" + this.nodeCode + ", nodeName=" + this.nodeName + ", applyPersonId=" + this.applyPersonId + ", applyPersonName=" + this.applyPersonName + ", applyTime=" + this.applyTime + ", applyDesc=" + this.applyDesc + ", shPersonId=" + this.shPersonId + ", shPersonName=" + this.shPersonName + ", shDesc=" + this.shDesc + ", shTime=" + this.shTime + ", shStatus=" + this.shStatus + ", shStatusName=" + this.shStatusName + ", column1=" + this.column1 + ", column2=" + this.column2 + ", column3=" + this.column3 + ", column4=" + this.column4 + ", column5=" + this.column5 + ", column6=" + this.column6 + ", column7=" + this.column7 + ", column8=" + this.column8 + ", column9=" + this.column9 + ", column10=" + this.column10 + ", column11=" + this.column11 + ", column12=" + this.column12 + ", column13=" + this.column13 + ", column14=" + this.column14 + ", column15=" + this.column15 + ", column16=" + this.column16 + ", column17=" + this.column17 + ", column18=" + this.column18 + ", column19=" + this.column19 + ", column20=" + this.column20 + ", bigColumn1=" + this.bigColumn1 + ", bigColumn2=" + this.bigColumn2 + ", bigColumn3=" + this.bigColumn3 + ", bigColumn4=" + this.bigColumn4 + ", bigColumn5=" + this.bigColumn5 + ", extendsJson=" + this.extendsJson + ", oemId=" + this.oemId + ", groupId=" + this.groupId + ", creator=" + this.creator + ", createdName=" + this.createdName + ", createdDate=" + this.createdDate + ", modifier=" + this.modifier + ", modifyName=" + this.modifyName + ", lastUpdatedDate=" + this.lastUpdatedDate + ", isEnable=" + this.isEnable + ", updateControlId=" + this.updateControlId + "}";
    }
}
