package com.ly.adp.csc.otherservice.entities;

import io.swagger.annotations.ApiModelProperty;

public class MdmOrgEmployeeBrOut {


	@ApiModelProperty(value = "员工姓名", required = false, example = "")
	    private String empName;

	    @ApiModelProperty(value = "所属品牌", required = false, example = "")
	    private String carBrandCn;

	    @ApiModelProperty(value = "所属组织", required = false, example = "")
	    private String orgName;

	    @ApiModelProperty(value = "职员ID", required = false, example = "")
	    private String empId;

	    @ApiModelProperty(value = "品牌", required = false, example = "")
	    private String carBrandCode;

	    @ApiModelProperty(value = "备注", required = false, example = "")
	    private String remark;

	    @ApiModelProperty(value = "所属模块", required = false, example = "")
	    private String belongModule;

	    @ApiModelProperty(value = "主键ID", required = false, example = "")
	    private String brGuid;

	    @ApiModelProperty(value = "时间戳", required = false, example = "")
	    private Long mycatOpTime;

	    @ApiModelProperty(value = "厂商标识ID", required = false, example = "")
	    private String oemId;

	    @ApiModelProperty(value = "集团标识ID", required = false, example = "")
	    private String groupId;

	    @ApiModelProperty(value = "厂商标识", required = false, example = "")
	    private String oemCode;

	    @ApiModelProperty(value = "集团标识", required = false, example = "")
	    private String groupCode;

	    @ApiModelProperty(value = "创建人", required = false, example = "")
	    private String creator;

	    @ApiModelProperty(value = "创建人姓名", required = false, example = "")
	    private String createdName;

	    @ApiModelProperty(value = "创建时间", required = false, example = "")
	    private String createdDate;

	    @ApiModelProperty(value = "最后更新人员", required = false, example = "")
	    private String modifier;

	    @ApiModelProperty(value = "修改人姓名", required = false, example = "")
	    private String modifyName;

	    @ApiModelProperty(value = "最后更新时间", required = false, example = "")
	    private String lastUpdatedDate;

	    @ApiModelProperty(value = "是否可用", required = false, example = "")
	    private String isEnable;

	    @ApiModelProperty(value = "SDP用户ID", required = false, example = "")
	    private String sdpUserId;

	    @ApiModelProperty(value = "SDP组织ID", required = false, example = "")
	    private String sdpOrgId;

	    @ApiModelProperty(value = "并发控制字段", required = false, example = "")
	    private String updateControlId;

	    @ApiModelProperty(value = "扩展字段1", required = false, example = "")
	    private String column1;

	    @ApiModelProperty(value = "扩展字段2", required = false, example = "")
	    private String column2;

	    @ApiModelProperty(value = "扩展字段3", required = false, example = "")
	    private String column3;

	    @ApiModelProperty(value = "扩展字段4", required = false, example = "")
	    private String column4;

	    @ApiModelProperty(value = "扩展字段5", required = false, example = "")
	    private String column5;

	    @ApiModelProperty(value = "扩展字段6", required = false, example = "")
	    private String column6;

	    @ApiModelProperty(value = "扩展字段7", required = false, example = "")
	    private String column7;

	    @ApiModelProperty(value = "扩展字段8", required = false, example = "")
	    private String column8;

	    @ApiModelProperty(value = "扩展字段9", required = false, example = "")
	    private String column9;

	    @ApiModelProperty(value = "扩展字段10", required = false, example = "")
	    private String column10;
	    //生日
	    private String birthDate;
	    
		public String getBirthDate() {
			return birthDate;
		}

		public void setBirthDate(String birthDate) {
			this.birthDate = birthDate;
		}

		public String getEmpName() {
			return empName;
		}

		public void setEmpName(String empName) {
			this.empName = empName;
		}

		public String getCarBrandCn() {
			return carBrandCn;
		}

		public void setCarBrandCn(String carBrandCn) {
			this.carBrandCn = carBrandCn;
		}

		public String getOrgName() {
			return orgName;
		}

		public void setOrgName(String orgName) {
			this.orgName = orgName;
		}

		public String getEmpId() {
			return empId;
		}

		public void setEmpId(String empId) {
			this.empId = empId;
		}

		public String getCarBrandCode() {
			return carBrandCode;
		}

		public void setCarBrandCode(String carBrandCode) {
			this.carBrandCode = carBrandCode;
		}

		public String getRemark() {
			return remark;
		}

		public void setRemark(String remark) {
			this.remark = remark;
		}

		public String getBelongModule() {
			return belongModule;
		}

		public void setBelongModule(String belongModule) {
			this.belongModule = belongModule;
		}

		public String getBrGuid() {
			return brGuid;
		}

		public void setBrGuid(String brGuid) {
			this.brGuid = brGuid;
		}

		public Long getMycatOpTime() {
			return mycatOpTime;
		}

		public void setMycatOpTime(Long mycatOpTime) {
			this.mycatOpTime = mycatOpTime;
		}

		public String getOemId() {
			return oemId;
		}

		public void setOemId(String oemId) {
			this.oemId = oemId;
		}

		public String getGroupId() {
			return groupId;
		}

		public void setGroupId(String groupId) {
			this.groupId = groupId;
		}

		public String getOemCode() {
			return oemCode;
		}

		public void setOemCode(String oemCode) {
			this.oemCode = oemCode;
		}

		public String getGroupCode() {
			return groupCode;
		}

		public void setGroupCode(String groupCode) {
			this.groupCode = groupCode;
		}

		public String getCreator() {
			return creator;
		}

		public void setCreator(String creator) {
			this.creator = creator;
		}

		public String getCreatedName() {
			return createdName;
		}

		public void setCreatedName(String createdName) {
			this.createdName = createdName;
		}

		public String getCreatedDate() {
			return createdDate;
		}

		public void setCreatedDate(String createdDate) {
			this.createdDate = createdDate;
		}

		public String getModifier() {
			return modifier;
		}

		public void setModifier(String modifier) {
			this.modifier = modifier;
		}

		public String getModifyName() {
			return modifyName;
		}

		public void setModifyName(String modifyName) {
			this.modifyName = modifyName;
		}

		public String getLastUpdatedDate() {
			return lastUpdatedDate;
		}

		public void setLastUpdatedDate(String lastUpdatedDate) {
			this.lastUpdatedDate = lastUpdatedDate;
		}

		public String getIsEnable() {
			return isEnable;
		}

		public void setIsEnable(String isEnable) {
			this.isEnable = isEnable;
		}

		public String getSdpUserId() {
			return sdpUserId;
		}

		public void setSdpUserId(String sdpUserId) {
			this.sdpUserId = sdpUserId;
		}

		public String getSdpOrgId() {
			return sdpOrgId;
		}

		public void setSdpOrgId(String sdpOrgId) {
			this.sdpOrgId = sdpOrgId;
		}

		public String getUpdateControlId() {
			return updateControlId;
		}

		public void setUpdateControlId(String updateControlId) {
			this.updateControlId = updateControlId;
		}

		public String getColumn1() {
			return column1;
		}

		public void setColumn1(String column1) {
			this.column1 = column1;
		}

		public String getColumn2() {
			return column2;
		}

		public void setColumn2(String column2) {
			this.column2 = column2;
		}

		public String getColumn3() {
			return column3;
		}

		public void setColumn3(String column3) {
			this.column3 = column3;
		}

		public String getColumn4() {
			return column4;
		}

		public void setColumn4(String column4) {
			this.column4 = column4;
		}

		public String getColumn5() {
			return column5;
		}

		public void setColumn5(String column5) {
			this.column5 = column5;
		}

		public String getColumn6() {
			return column6;
		}

		public void setColumn6(String column6) {
			this.column6 = column6;
		}

		public String getColumn7() {
			return column7;
		}

		public void setColumn7(String column7) {
			this.column7 = column7;
		}

		public String getColumn8() {
			return column8;
		}

		public void setColumn8(String column8) {
			this.column8 = column8;
		}

		public String getColumn9() {
			return column9;
		}

		public void setColumn9(String column9) {
			this.column9 = column9;
		}

		public String getColumn10() {
			return column10;
		}

		public void setColumn10(String column10) {
			this.column10 = column10;
		}

	    
}
