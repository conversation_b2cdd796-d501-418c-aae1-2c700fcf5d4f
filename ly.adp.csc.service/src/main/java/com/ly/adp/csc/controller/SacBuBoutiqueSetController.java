package com.ly.adp.csc.controller;

import java.util.Map;

import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacBuBoutiqueSetService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;


/**
 * <p>
 * 精品基本数据维护表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@RestController
@Api(value = "精品基本数据维护表服务", tags = "精品基本数据维护表服务")
@RequestMapping(value = "/ly/adp/csc/sacBuBoutiqueSet", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacBuBoutiqueSetController {
	@Autowired
	ISacBuBoutiqueSetService sacBuBoutiqueSetService;
	
	@ApiOperation(value = "精品基本数据维护表查询", notes = "精品基本数据维护表查询")
	@PostMapping(value = "/sacBuBoutiqueSetquery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueSetFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacBuBoutiqueSetService.sacBuBoutiqueSetFindInfo(dateInfo,authentication);}).result();
	}

	@ApiOperation(value = "精品基本数据维护表维护", notes = "精品基本数据维护表维护")
	@PostMapping(value = "/sacBuBoutiqueSetsave.do")
	public OptResult sacBuBoutiqueSetSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																   @RequestBody(required = false) ParamBase<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacBuBoutiqueSetService.sacBuBoutiqueSetSaveInfo(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "精品门店信息维护", notes = "精品门店信息维护")
	@PostMapping(value = "/sacBuBoutiqueDlrSave.do")
	public EntityResult<Map<String, Object>> sacBuBoutiqueDlrSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacBuBoutiqueSetService.insertSacBuBoutiqueDlrInfo(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品门店信息查询-大使", notes = "精品门店信息查询-大使")
	@PostMapping(value = "/sacBuBoutiqueDlrQuery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueDlrInfoQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.querySacBuBoutiqueDlrInfo(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品门店信息查询-店长", notes = "精品门店信息查询-店长")
	@PostMapping(value = "/sacBuBoutiqueDlrQueryOne.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueDlrInfoQueryOne(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.querySacBuBoutiqueDlrInfoOne(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品明细查询", notes = "精品明细查询")
	@PostMapping(value = "/sacBuBoutiqueDetailQuery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueDetailQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.sacBuBoutiqueDetailQuery(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品明细单表查询", notes = "精品明细单表查询")
	@PostMapping(value = "/querySacBuBoutiqueDetail.do")
	public ListResult<Map<String, Object>> querySacBuBoutiqueDetail(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.querySacBuBoutiqueDetail(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品明细更新", notes = "精品明细更新")
	@PostMapping(value = "/sacBuBoutiqueDetailUpdate.do")
	public OptResult sacBuBoutiqueDetailUpdate(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacBuBoutiqueSetService.sacBuBoutiqueDetailUpdate(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品基础数据批量上传", notes = "精品基础数据批量上传")
	@PostMapping(value = "/sacBuBoutiqueSetImport.do")
	public Result sacBuBoutiqueSetImport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = true) MultipartFile uploadfile) {
		return sacBuBoutiqueSetService.sacBuBoutiqueSetImport(authentication, uploadfile);
	}
	
	@ApiOperation(value = "精品明细信息批量上传", notes = "精品明细信息批量上传")
	@PostMapping(value = "/sacBuBoutiqueDetailImport.do")
	public Result sacBuBoutiqueDetailImport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = true) MultipartFile uploadfile) {
		return sacBuBoutiqueSetService.sacBuBoutiqueDetailImport(authentication, uploadfile);
	}
	@ApiOperation(value = "精品明细数据上架", notes = "精品明细数据上架")
	@PostMapping(value = "/sacBuBoutiquePutOnSave.do")
	public OptResult sacBuBoutiquePutOnSave(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
													@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacBuBoutiqueSetService.sacBuBoutiquePutOnSave(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品明细数据上下架", notes = "精品明细数据上下架")
	@PostMapping(value = "/sacBuBoutiquePutOnOrDownUpdate.do")
	public OptResult sacBuBoutiquePutOnOrDownUpdate(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacBuBoutiqueSetService.sacBuBoutiquePutOnOrDownUpdate(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "精品明细数据全部下架", notes = "精品明细数据全部下架")
	@PostMapping(value = "/sacBuBoutiquePutDownAll.do")
	public OptResult sacBuBoutiquePutDownAll(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			  @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> sacBuBoutiqueSetService.sacBuBoutiquePutDownAll(dateInfo, authentication)).result();
	}

	@ApiOperation(value = "精品收货单表查询", notes = "精品收货单表查询")
	@PostMapping(value = "/querySacBuBoutiqueReceiving.do")
	public ListResult<Map<String, Object>> querySacBuBoutiqueReceiving(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	   @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.querySacBuBoutiqueReceiving(dateInfo, authentication)).result();
	}

	@ApiOperation(value = "精品明细导出", notes = "精品明细导出")
	@RequestMapping(value = "/exportSacBuBoutiqueDetailQuery.do", method = RequestMethod.POST)
	public OptResult exportSacBuBoutiqueDetailQuery(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
													   @RequestBody(required = false) Map<String, Object> info,
													   HttpServletResponse response) throws Exception {
		return sacBuBoutiqueSetService.exportSacBuBoutiqueDetailQuery(info, response, authentication);
	}

	@ApiOperation(value = "精品明细-大使导出", notes = "精品明细-大使导出")
	@RequestMapping(value = "/exportSacBuBoutiqueDetailQueryByElchee.do", method = RequestMethod.POST)
	public OptResult exportSacBuBoutiqueDetailQueryByElchee(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
													@RequestBody(required = false) Map<String, Object> info,
													HttpServletResponse response) throws Exception {
		return sacBuBoutiqueSetService.exportSacBuBoutiqueDetailQueryByElchee(info, response, authentication);
	}

	@ApiOperation(value = "精品明细报表查询", notes = "精品明细报表查询")
	@PostMapping(value = "/querySacBuBoutiqueStatement.do")
	public ListResult<Map<String, Object>> querySacBuBoutiqueStatement(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	   @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doList(() -> sacBuBoutiqueSetService.querySacBuBoutiqueStatement(dateInfo, authentication)).result();
	}

	@ApiOperation(value = "精品明细报表查询导出", notes = "精品明细报表查询导出")
	@RequestMapping(value = "/exportQuerySacBuBoutiqueStatement.do", method = RequestMethod.POST)
	public OptResult exportQuerySacBuBoutiqueStatement(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
															@RequestBody(required = false) ParamPage<Map<String, Object>> info,
															HttpServletResponse response) throws Exception {
		return sacBuBoutiqueSetService.exportquerySacBuBoutiqueStatement(info, response, authentication);
	}

}
