package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 店端线索基础日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@TableName("t_sac_basis_log")
public class SacBasisLog implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 日志ID
	 */
	@TableId("LOG_ID")
	private String logId;

	/**
	 * 日志类型 1：短信发送
	 */
	@TableField("LOG_type")
	private String logType;

	/**
	 * 日志标识
	 */
	@TableField("LOG_FLAG")
	private String logFlag;

	/**
	 * 日志内容
	 */
	@TableField("LOG_DESC")
	private String logDesc;

	/**
	 * 开始时间
	 */
	@TableField("BEG_TIME")
	private LocalDateTime begTime;

	/**
	 * 结束时间
	 */
	@TableField("END_TIME")
	private LocalDateTime endTime;

	/**
	 * 扩展信息
	 */
	@TableField("EXTEND_JSON")
	private String extendJson;

	/**
	 * 厂商标识ID
	 */
	@TableField("OEM_ID")
	private String oemId;

	/**
	 * 集团标识ID
	 */
	@TableField("GROUP_ID")
	private String groupId;

	/**
	 * 厂商标识
	 */
	@TableField("OEM_CODE")
	private String oemCode;

	/**
	 * 集团标识
	 */
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	 * 是否可用
	 */
	@TableField("IS_ENABLE")
	private String isEnable;

	/**
	 * 创建人
	 */
	@TableField(value = "CREATOR", fill = FieldFill.INSERT)
	private String creator;

	/**
	 * 创建人姓名
	 */
	@TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
	private String createdName;

	/**
	 * 创建时间
	 */
	@TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
	private LocalDateTime createdDate;

	/**
	 * 最后更新人员
	 */
	@TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
	private String modifier;

	/**
	 * 修改人姓名
	 */
	@TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
	private String modifyName;

	/**
	 * 最后更新时间
	 */
	@TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime lastUpdatedDate;

	/**
	 * SDP用户ID
	 */
	@TableField("SDP_USER_ID")
	private String sdpUserId;

	/**
	 * SDP组织ID
	 */
	@TableField("SDP_ORG_ID")
	private String sdpOrgId;

	/**
	 * 并发控制字段
	 */
	@TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
	private String updateControlId;

	public String getLogId() {
		return logId;
	}

	public void setLogId(String logId) {
		this.logId = logId;
	}

	public String getLogType() {
		return logType;
	}

	public void setLogType(String logType) {
		this.logType = logType;
	}

	public String getLogFlag() {
		return logFlag;
	}

	public void setLogFlag(String logFlag) {
		this.logFlag = logFlag;
	}

	public String getLogDesc() {
		return logDesc;
	}

	public void setLogDesc(String logDesc) {
		this.logDesc = logDesc;
	}

	public LocalDateTime getBegTime() {
		return begTime;
	}

	public void setBegTime(LocalDateTime begTime) {
		this.begTime = begTime;
	}

	public LocalDateTime getEndTime() {
		return endTime;
	}

	public void setEndTime(LocalDateTime endTime) {
		this.endTime = endTime;
	}

	public String getExtendJson() {
		return extendJson;
	}

	public void setExtendJson(String extendJson) {
		this.extendJson = extendJson;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getOemCode() {
		return oemCode;
	}

	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getSdpUserId() {
		return sdpUserId;
	}

	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}

	public String getSdpOrgId() {
		return sdpOrgId;
	}

	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	@Override
	public String toString() {
		return "SacBasisLog{" + "logId=" + logId + ", logType=" + logType + ", logFlag=" + logFlag + ", logDesc="
				+ logDesc + ", begTime=" + begTime + ", endTime=" + endTime + ", extendJson=" + extendJson + ", oemId="
				+ oemId + ", groupId=" + groupId + ", oemCode=" + oemCode + ", groupCode=" + groupCode + ", isEnable="
				+ isEnable + ", creator=" + creator + ", createdName=" + createdName + ", createdDate=" + createdDate
				+ ", modifier=" + modifier + ", modifyName=" + modifyName + ", lastUpdatedDate=" + lastUpdatedDate
				+ ", sdpUserId=" + sdpUserId + ", sdpOrgId=" + sdpOrgId + ", updateControlId=" + updateControlId + "}";
	}
}
