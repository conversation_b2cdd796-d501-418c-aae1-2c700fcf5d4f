package com.ly.adp.csc.controller;


import java.util.List;
import java.util.Map;

import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.service.ISacBuBoutiqueApplyService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.google.common.net.HttpHeaders;
import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.csc.service.ISacBuBoutiqueApplyService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 精品申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@RestController
@Api(value = "精品申请表服务", tags = "精品申请表服务")
@RequestMapping(value = "/ly/adp/csc/sacBuBoutiqueApply", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacBuBoutiqueApplyController {
	@Autowired
	ISacBuBoutiqueApplyService sacBuBoutiqueApplyService;
	
	@ApiOperation(value = "精品申请表查询", notes = "精品申请表查询")
	@PostMapping(value = "/sacBuBoutiqueApplyquery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueApplyFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplyFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "精品申请表维护", notes = "精品申请表维护")
	@PostMapping(value = "/sacBuBoutiqueApplysave.do")
	public EntityResult<Map<String, Object>> sacBuBoutiqueApplySaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplySaveInfo(dateInfo, authentication);}).result();
	}

	@ApiOperation(value = "精品申请", notes = "精品申请")
	@PostMapping(value = "/sacBuBoutiqueApplysaveByDlr.do")
	public EntityResult<Map<String, Object>> sacBuBoutiqueApplysaveByDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																		 @RequestBody(required = false) List<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplysaveByDlr(dateInfo, authentication);}).result();
	}

	@ApiOperation(value = "精品申请修改", notes = "精品申请修改")
	@PostMapping(value = "/sacBuBoutiqueApplysaveByDlrUpdate.do")
	public OptResult sacBuBoutiqueApplysaveByDlrUpdate(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
													   @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplysaveByDlrUpdate(dateInfo, authentication);}).result();
	}

	@ApiOperation(value = "精品申请锁定", notes = "精品申请锁定")
	@PostMapping(value = "/sacBuBoutiqueApplyLock.do")
	public OptResult sacBuBoutiqueApplyLock(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
											@RequestBody(required = false) ParamBase<List<Map<String, Object>>> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplyLock(dateInfo, authentication);}).result();
	}

	@ApiOperation(value = "精品申请查询", notes = "精品申请查询")
	@PostMapping(value = "/sacBuBoutiqueApplyFindInfoQuery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueApplyFindInfoQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																		   @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplyFindInfoQuery(dateInfo,authentication);}).result();
	}

	@ApiOperation(value = "精品申请明细查询", notes = "精品申请明细查询")
	@PostMapping(value = "/sacBuBoutiqueApplyByDetails.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueApplyByDetails (@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																		   @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacBuBoutiqueApplyService.sacBuBoutiqueApplyByDetails(dateInfo,authentication);}).result();
	}

	@ApiOperation(value = "精品申请明细导出", notes = "精品申请明细导出")
	@RequestMapping(value = "/exportsacBuBoutiqueApplyByDetails.do", method = RequestMethod.POST)
	public OptResult exportsacBuBoutiqueApplyByDetails(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
														 @RequestBody(required = false) Map<String, Object> info,
														 HttpServletResponse response) throws Exception {
		return sacBuBoutiqueApplyService.exportsacBuBoutiqueApplyByDetails(info, response, authentication);
	}

	@ApiOperation(value = "精品申请明细-大使导出", notes = "精品申请明细-大使导出")
	@RequestMapping(value = "/exportsacBuBoutiqueApplyByDetailsByElchee.do", method = RequestMethod.POST)
	public OptResult exportsacBuBoutiqueApplyByDetailsByElchee(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
													   @RequestBody(required = false) ParamPage<Map<String, Object>> info,
													   HttpServletResponse response) throws Exception {
		return sacBuBoutiqueApplyService.exportsacBuBoutiqueApplyByDetailsByElchee(info, response, authentication);
	}

}
