package com.ly.adp.csc.entities;

import java.util.List;

public class DccCheckResult {
    /**
     * 是否存在异常
     */
    private boolean hasError;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 是否都是dcc线索
     */
    private boolean isAllDcc;

    /**
     * 不符合要求的手机号列表
     */
    private List<String> unmatchedPhones;

    public DccCheckResult() {}
    
    public DccCheckResult(boolean hasError, String errorMessage) {
        this.hasError = hasError;
        this.errorMessage = errorMessage;
    }

    public DccCheckResult(boolean hasError, String errorMessage, boolean isAllDcc, List<String> unmatchedPhones) {
        this.hasError = hasError;
        this.errorMessage = errorMessage;
        this.isAllDcc = isAllDcc;
        this.unmatchedPhones = unmatchedPhones;
    }

    // Getters and Setters
    public boolean isHasError() { return hasError; }
    public void setHasError(boolean hasError) { this.hasError = hasError; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public boolean isAllDcc() { return isAllDcc; }
    public void setAllDcc(boolean allDcc) { isAllDcc = allDcc; }
    public List<String> getUnmatchedPhones() { return unmatchedPhones; }
    public void setUnmatchedPhones(List<String> unmatchedPhones) { this.unmatchedPhones = unmatchedPhones; }
}