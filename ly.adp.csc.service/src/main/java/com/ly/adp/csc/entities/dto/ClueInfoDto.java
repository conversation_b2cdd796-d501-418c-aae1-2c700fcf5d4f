package com.ly.adp.csc.entities.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/3
 * @Version 1.0.0
 **/
public class ClueInfoDto implements Serializable {

    private static final long serialVersionUID = -9005379994566682480L;

    /**
     * REVIEW_ID - 回访记录ID
     */
    private String reviewId;

    /**
     * REVIEW_PERSON_ID - 回访人员用户ID
     */
    private String reviewPersonId;

    /**
     * REVIEW_PERSON_NAME - 回访人员名称
     */
    private String reviewPersonName;

    /**
     * INTEN_LEVEL_CODE - 意向级别编码
     */
    private String intenLevelCode;

    /**
     * INTEN_LEVEL_NAME - 意向级别名称
     */
    private String intenLevelName;

    /**
     * INTEN_CAR_TYPE_NAME - 意向车型名称
     */
    private String intenCarTypeName;

    /**
     * planBuyDateName - 计划购买日期
     */
    private String planBuyDateName;

    /**
     * businessHeatCode - 业务热度代码
     */
    private String businessHeatCode;

    /**
     * businessHeatName - 业务热度名称
     */
    private String businessHeatName;

    /**
     * CHANNEL_NAME - 最低一级的信息来源名称
     */
    private String channelName;

    /**
     * GENDER_CODE - 性别编码
     */
    private String genderCode;

    /**
     * GENDER_NAME - 性别名称
     */
    private String genderName;

    /**
     * DLR_CODE - 经销商编码
     */
    private String dlrCode;

    /**
     * DLR_SHORT_NAME - 经销商名称
     */
    private String dlrShortName;

    /**
     * SERVER_ORDER - 线索单号
     */
    private String serverOrder;

    /**
     * CUST_ID - 客户ID
     */
    private String custId;

    /**
     * CUST_NAME - 客户姓名
     */
    private String custName;

    /**
     * PHONE - 联系号码
     */
    private String phone;

    /**
     * LAST_UPDATED_DATE - 最后更新时间
     */
    private String lastUpdatedDate;

    /**
     * USER_STATUS - 员工在职状态
     */
    private String userStatus;


    /**
     * dcc标识
     */
    private String dccFlag;

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }

    public String getReviewPersonId() {
        return reviewPersonId;
    }

    public void setReviewPersonId(String reviewPersonId) {
        this.reviewPersonId = reviewPersonId;
    }

    public String getReviewPersonName() {
        return reviewPersonName;
    }

    public void setReviewPersonName(String reviewPersonName) {
        this.reviewPersonName = reviewPersonName;
    }

    public String getIntenLevelCode() {
        return intenLevelCode;
    }

    public void setIntenLevelCode(String intenLevelCode) {
        this.intenLevelCode = intenLevelCode;
    }

    public String getIntenLevelName() {
        return intenLevelName;
    }

    public void setIntenLevelName(String intenLevelName) {
        this.intenLevelName = intenLevelName;
    }

    public String getIntenCarTypeName() {
        return intenCarTypeName;
    }

    public void setIntenCarTypeName(String intenCarTypeName) {
        this.intenCarTypeName = intenCarTypeName;
    }

    public String getPlanBuyDateName() {
        return planBuyDateName;
    }

    public void setPlanBuyDateName(String planBuyDateName) {
        this.planBuyDateName = planBuyDateName;
    }

    public String getBusinessHeatCode() {
        return businessHeatCode;
    }

    public void setBusinessHeatCode(String businessHeatCode) {
        this.businessHeatCode = businessHeatCode;
    }

    public String getBusinessHeatName() {
        return businessHeatName;
    }

    public void setBusinessHeatName(String businessHeatName) {
        this.businessHeatName = businessHeatName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getDlrShortName() {
        return dlrShortName;
    }

    public void setDlrShortName(String dlrShortName) {
        this.dlrShortName = dlrShortName;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getServerOrder() {
        return serverOrder;
    }

    public void setServerOrder(String serverOrder) {
        this.serverOrder = serverOrder;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(String lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getDccFlag() {
        return dccFlag;
    }

    public void setDccFlag(String dccFlag) {
        this.dccFlag = dccFlag;
    }
}
