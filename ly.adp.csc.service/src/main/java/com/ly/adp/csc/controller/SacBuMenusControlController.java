package com.ly.adp.csc.controller;


import java.util.Map;

import java.util.Map;

import com.ly.adp.csc.service.ISacBuMenusControlService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * APP菜单权限控制 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@RestController
@Api(value = "APP菜单权限控制服务", tags = "APP菜单权限控制服务")
@RequestMapping(value = "/adp/csc/sacBuMenusControl", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacBuMenusControlController {
	@Autowired
	ISacBuMenusControlService sacBuMenusControlService;
	
	@ApiOperation(value = "APP菜单权限控制查询", notes = "APP菜单权限控制查询")
	@PostMapping(value = "/sacBuMenusControlquery.do")
	public ListResult<Map<String, Object>> sacBuMenusControlFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																	 @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
		return BusicenInvoker.doList(() -> {return sacBuMenusControlService.sacBuMenusControlFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "APP菜单权限控制维护", notes = "APP菜单权限控制维护")
	@PostMapping(value = "/sacBuMenusControlsave.do")
	public OptResult sacBuMenusControlSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
																		   @RequestBody(required = false) ParamBase<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacBuMenusControlService.sacBuMenusControlSaveInfo(dateInfo, authentication);}).result();
	}
}
