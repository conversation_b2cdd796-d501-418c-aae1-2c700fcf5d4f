package com.ly.adp.csc.service;

import java.util.List;
import java.util.Map;

import com.ly.adp.csc.entities.dto.koc.TagCreateRequestDTO;
import com.ly.adp.csc.entities.vo.koc.TagInfoVO;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

/**
 * <p>
 * KOC标签管理服务接口
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
public interface IKocTagService {

    /**
     * 获取标签树形结构
     * @param token 用户token
     * @return 标签树
     */
    List<TagInfoVO> getTagTree(String token);

    /**
     * 获取标签扁平列表（缓存优先）
     * @param token 用户token
     * @return 标签扁平列表
     */
    List<TagInfoVO> getTagFlatList(String token);

    /**
     * 分页查询标签管理列表
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 分页结果
     */
    ListResult<Map<String, Object>> queryTagList(Map<String, Object> dataInfo, String token);

    /**
     * 创建标签
     * @param createRequest 创建请求
     * @param token 用户token
     * @return 创建结果
     */
    EntityResult<Map<String, Object>> createTag(TagCreateRequestDTO createRequest, String token);

    /**
     * 更新标签
     * @param dataInfo 更新数据
     * @param token 用户token
     * @return 更新结果
     */
    EntityResult<Map<String, Object>> updateTag(Map<String, Object> dataInfo, String token);

    /**
     * 删除标签
     * @param tagId 标签ID
     * @param token 用户token
     * @return 删除结果
     */
    OptResult deleteTag(String tagId, String token);

    /**
     * 批量删除标签
     * @param tagIds 标签ID列表
     * @param token 用户token
     * @return 删除结果
     */
    OptResult batchDeleteTags(List<String> tagIds, String token);

    /**
     * 上架/下架标签
     * @param tagId 标签ID
     * @param status 状态（1-上架，0-下架）
     * @param token 用户token
     * @return 操作结果
     */
    OptResult updateTagStatus(String tagId, Integer status, String token);

    /**
     * 批量导入标签
     * @param tagList 标签列表
     * @param token 用户token
     * @return 导入结果
     */
    OptResult batchImportTags(List<Map<String, Object>> tagList, String token);

    /**
     * 查询标签统计信息
     * @param token 用户token
     * @return 统计信息
     */
    List<Map<String, Object>> getTagStatistics(String token);

    /**
     * 刷新标签缓存
     * @param token 用户token
     * @return 操作结果
     */
    OptResult refreshTagCache(String token);
}
