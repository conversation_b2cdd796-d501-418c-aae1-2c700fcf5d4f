package com.ly.adp.csc.entities.enums;

/**
 * 系统岗位
 * <AUTHOR>
 * @Date 2024/5/29
 * @Version 1.0.0
 **/
public enum PositionEnum {
    SMART_SYS_0001("smart_sys_0001", "总监"),
    SMART_SYS_0002("smart_sys_0002", "高级区域经理"),
    SMART_SYS_0003("smart_sys_0003", "渠道建设"),
    SMART_SYS_0004("smart_sys_0004", "渠道开发"),
    SMART_SYS_0005("smart_sys_0005", "渠道管理"),
    SMART_SYS_0006("smart_sys_0006", "管理公司"),
    SMART_SYS_0007("smart_sys_0007", "代理商"),
    SMART_SYS_0008("smart_sys_0008", "城市公司"),
    SMART_SYS_0009("smart_sys_0009", "城市财务"),
    SMART_SYS_0010("smart_sys_0010", "门店店长"),
    SMART_SYS_0011("smart_sys_0011", "销售顾问"),
    SMART_SYS_0012("smart_sys_0012", "交付专员"),
    SMART_SYS_0013("smart_sys_0013", "区域经理"),
    SMART_SYS_0014("smart_sys_0014", "零售运营"),
    SMART_SYS_0015("smart_sys_0015", "OTD"),
    SMART_SYS_0016("smart_sys_0016", "售后技师"),
    SMART_SYS_0017("smart_sys_0017", "交付专家"),
    SMART_SYS_0018("smart_sys_0018", "客户"),
    SMART_SYS_0019("smart_sys_0019", "财务"),
    SMART_SYS_0020("smart_sys_0020", "会计"),
    SMART_SYS_0021("smart_sys_0021", "OTD"),
    SMART_SYS_0022("smart_sys_0022", "客户"),
    SMART_SYS_0023("smart_sys_0023", "渠道管理"),
    SMART_SYS_0024("smart_sys_0024", "品牌大使"),
    SMART_SYS_0026("smart_sys_0026", "论坛管理员"),
    SMART_SYS_0027("smart_sys_0027", "用车支持管理员"),
    SMART_SYS_0100("smart_sys_0100", "区域市场支持经理"),
    SMART_SYS_0101("smart_sys_0101", "总部市场(市场总监)"),
    SMART_SYS_0102("smart_sys_0102", "财务部门管理岗"),
    SMART_SYS_0103("smart_sys_0103", "门店市场经理"),
    SMART_SYS_0104("smart_sys_0104", "品牌总"),
    SMART_SYS_0105("smart_sys_0105", "外呼专员"),
    SMART_SYS_0106("smart_sys_0106", "总部管理员"),
    SMART_SYS_0200("smart_sys_0200", "总部RO(退网管理)"),
    SMART_SYS_0201("smart_sys_0201", "总部CS(退网管理)"),
    SMART_SYS_0202("smart_sys_0202", "总部CUD(退网管理)"),
    SMART_SYS_0203("smart_sys_0203", "总部SP(退网管理)"),
    SMART_SYS_0204("smart_sys_0204", "总部财务(退网管理)"),
    SMART_SYS_A999("smart_sys_a999", "代理商管理员");

    private final String code;
    private final String name;

    PositionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PositionEnum fromCode(String code) {
        for (PositionEnum position : PositionEnum.values()) {
            if (position.getCode().equals(code)) {
                return position;
            }
        }
        throw new IllegalArgumentException("Invalid PositionEnum code: " + code);
    }

    @Override
    public String toString() {
        return this.name;
    }
}