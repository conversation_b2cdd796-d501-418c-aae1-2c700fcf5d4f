package com.ly.adp.csc.entities;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/5/22
 */
public class DefeatEntireDlrClueInfoExport implements Serializable {

    private static final long serialVersionUID = 2400922926043961148L;

    @ExcelProperty("客户姓名")
    private String custName;

    @ExcelProperty("客户电话")
    private String tmdPhone;

    @ExcelProperty("性别")
    private String genderName;

    @ExcelProperty("门店名称")
    private String dlrShortName;

    @ExcelProperty("门店编码")
    private String dlrCode;

    @ExcelProperty("产品专家")
    private String reviewPersonName;

    @ExcelProperty("意向车型")
    private String intenCarTypeName;

    @ExcelProperty("颜色")
    private String outColorName;

    @ExcelProperty("内饰")
    private String innerColorName;

    @ExcelProperty("线索来源")
    private String channelName;

    @ExcelProperty("三级线索来源")
    private String infoChanDName;

    @ExcelProperty("cl_sp")
    private String source5;

    @ExcelProperty("cl_sr")
    private String source6;

    @ExcelProperty("首次跟进时间")
    private String firstReviewTime;

    @ExcelProperty("上次跟进时间")
    private String lastReviewTime;

    @ExcelProperty("下次跟进时间")
    private String planReviewTime;

    @ExcelProperty("是否逾期")
    private String isOverdue;

    @ExcelProperty("分配状态")
    private String assignStatusName;

    @ExcelProperty("战败时间")
    private String lastReviewTime1;

    @ExcelProperty("逾期时间")
    private String overReviewTime;

    @ExcelProperty("线索等级")
    private String intenLevelName;

    @ExcelProperty("热度")
    private String column6;

    @ExcelProperty("线索状态")
    private String statusName;

    @ExcelProperty("线索创建时间")
    private String createdDate;

    @ExcelProperty("smartId")
    private String column10;

    @ExcelProperty("是否完成试驾")
    private String isCompleteCarApply;

    @ExcelProperty("用户运营活动次数")
    private String yhSignNum;

    @ExcelProperty("代理商活动次数")
    private String dlSignNum;

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }


    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getDlrShortName() {
        return dlrShortName;
    }

    public void setDlrShortName(String dlrShortName) {
        this.dlrShortName = dlrShortName;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getReviewPersonName() {
        return reviewPersonName;
    }

    public void setReviewPersonName(String reviewPersonName) {
        this.reviewPersonName = reviewPersonName;
    }

    public String getIntenCarTypeName() {
        return intenCarTypeName;
    }

    public void setIntenCarTypeName(String intenCarTypeName) {
        this.intenCarTypeName = intenCarTypeName;
    }

    public String getOutColorName() {
        return outColorName;
    }

    public void setOutColorName(String outColorName) {
        this.outColorName = outColorName;
    }

    public String getInnerColorName() {
        return innerColorName;
    }

    public void setInnerColorName(String innerColorName) {
        this.innerColorName = innerColorName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getInfoChanDName() {
        return infoChanDName;
    }

    public void setInfoChanDName(String infoChanDName) {
        this.infoChanDName = infoChanDName;
    }

    public String getSource5() {
        return source5;
    }

    public void setSource5(String source5) {
        this.source5 = source5;
    }

    public String getSource6() {
        return source6;
    }

    public void setSource6(String source6) {
        this.source6 = source6;
    }

    public String getFirstReviewTime() {
        return firstReviewTime;
    }

    public void setFirstReviewTime(String firstReviewTime) {
        this.firstReviewTime = firstReviewTime;
    }

    public String getLastReviewTime() {
        return lastReviewTime;
    }

    public void setLastReviewTime(String lastReviewTime) {
        this.lastReviewTime = lastReviewTime;
    }

    public String getPlanReviewTime() {
        return planReviewTime;
    }

    public void setPlanReviewTime(String planReviewTime) {
        this.planReviewTime = planReviewTime;
    }

    public String getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(String isOverdue) {
        this.isOverdue = isOverdue;
    }

    public String getAssignStatusName() {
        return assignStatusName;
    }

    public void setAssignStatusName(String assignStatusName) {
        this.assignStatusName = assignStatusName;
    }

    public String getLastReviewTime1() {
        return lastReviewTime1;
    }

    public void setLastReviewTime1(String lastReviewTime1) {
        this.lastReviewTime1 = lastReviewTime1;
    }

    public String getOverReviewTime() {
        return overReviewTime;
    }

    public void setOverReviewTime(String overReviewTime) {
        this.overReviewTime = overReviewTime;
    }

    public String getIntenLevelName() {
        return intenLevelName;
    }

    public void setIntenLevelName(String intenLevelName) {
        this.intenLevelName = intenLevelName;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getIsCompleteCarApply() {
        return isCompleteCarApply;
    }

    public void setIsCompleteCarApply(String isCompleteCarApply) {
        this.isCompleteCarApply = isCompleteCarApply;
    }

    public String getYhSignNum() {
        return yhSignNum;
    }

    public void setYhSignNum(String yhSignNum) {
        this.yhSignNum = yhSignNum;
    }

    public String getDlSignNum() {
        return dlSignNum;
    }

    public void setDlSignNum(String dlSignNum) {
        this.dlSignNum = dlSignNum;
    }

    public String getTmdPhone() {
        return tmdPhone;
    }

    public void setTmdPhone(String tmdPhone) {
        this.tmdPhone = tmdPhone;
    }

    @Override
    public String toString() {
        return "DefeatEntireDlrClueInfoExport{" +
                "custName='" + custName + '\'' +
                ", tmdPhone='" + tmdPhone + '\'' +
                ", genderName='" + genderName + '\'' +
                ", dlrShortName='" + dlrShortName + '\'' +
                ", dlrCode='" + dlrCode + '\'' +
                ", reviewPersonName='" + reviewPersonName + '\'' +
                ", intenCarTypeName='" + intenCarTypeName + '\'' +
                ", outColorName='" + outColorName + '\'' +
                ", innerColorName='" + innerColorName + '\'' +
                ", channelName='" + channelName + '\'' +
                ", infoChanDName='" + infoChanDName + '\'' +
                ", source5='" + source5 + '\'' +
                ", source6='" + source6 + '\'' +
                ", firstReviewTime='" + firstReviewTime + '\'' +
                ", lastReviewTime='" + lastReviewTime + '\'' +
                ", planReviewTime='" + planReviewTime + '\'' +
                ", isOverdue='" + isOverdue + '\'' +
                ", assignStatusName='" + assignStatusName + '\'' +
                ", lastReviewTime1='" + lastReviewTime1 + '\'' +
                ", overReviewTime='" + overReviewTime + '\'' +
                ", intenLevelName='" + intenLevelName + '\'' +
                ", column6='" + column6 + '\'' +
                ", statusName='" + statusName + '\'' +
                ", createdDate='" + createdDate + '\'' +
                ", column10='" + column10 + '\'' +
                ", isCompleteCarApply='" + isCompleteCarApply + '\'' +
                ", yhSignNum='" + yhSignNum + '\'' +
                ", dlSignNum='" + dlSignNum + '\'' +
                '}';
    }
}
