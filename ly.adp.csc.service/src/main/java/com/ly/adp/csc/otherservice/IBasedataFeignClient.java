package com.ly.adp.csc.otherservice;

import com.ly.adp.csc.entities.MdmCarColor;
import com.ly.adp.csc.entities.MdmCarIncolor;
import com.ly.adp.csc.entities.qry.UserDlrRelationQry;
import com.ly.adp.csc.entities.vo.TUscMdmOrgEmployeeVO;
import com.ly.adp.csc.otherservice.entities.*;
import com.ly.adp.csc.otherservice.entities.in.*;
import com.ly.adp.csc.otherservice.in.MdsLookupValueIn;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "${refer.name.adp.csc:ly.adp.base}", url = "${refer.url.adp.base}")
public interface IBasedataFeignClient {
    /**
     * 根据区县ID查询门店
     *
     * @param authentication
     * @param datainfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/querydlrbycountyid.do")
    ListResult<Map<String, Object>> mdmOrgEmployeeQueryDlrByCountyId(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> datainfo);

    @PostMapping("/ly/adp/base/clue/querydlrbycountyDlr.do")
    ListResult<Map<String, Object>> mdmOrgEmployeeQueryDlrByCountyDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> datainfo);

    /**
     * 根据门店编码查询城市大区信息
     *
     * @param authentication
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/querypositionbydlr.do")
    ListResult<Map<String, Object>> queryPositionListByDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 根据门店编码查询城市大区信息
     *
     * @param authentication
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/querycityinfobydlr.do")
    ListResult<Map<String, Object>> querycityinfobydlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping("/ly/adp/base/clue/querycityinfobydlr.todo")
    ListResult<Map<String, Object>> querycityinfobydlr2(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping("/ly/adp/base/clue/queryuserdlrrelation.do")
    ListResult<Map<String, Object>> mdmOrgUserDlrRelationFindAll(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 当前登录人所管辖门店查询
     *
     * @param authentication
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/querymanagedlr.do")
    ListResult<Map<String, Object>> querymanagedlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 根据岗位获取员工信息
     *
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/locationempinfo.do")
    ListResult<Map<String, Object>> locationEmpInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping("/ly/adp/base/clue/locationempinfo.todo")
    ListResult<Map<String, Object>> locationEmpInfo2(@RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 获取用户门店关系信息
     *
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/userdlrrelation.do")
    ListResult<Map<String, Object>> userdlrrelation(@RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * createmptoken.do
     *
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/createmptoken.do")
    OptResult createMpToken(@RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping("/ly/adp/base/clue/createMpTokenInfo")
    OptResult createMpTokenInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * createmptoken.do
     *
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/createmptokenwithoutsuffix")
    OptResult createMpTokenWithoutSuffix(@RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 获取用户信息
     *
     * @param empId
     * @return
     * @throws Exception
     */
    @PostMapping("ly/busicen/base/eap/org/sysuser/getPartUserInfo.do")
    Map<String, Object> getPartUserInfo(@RequestBody(required = false) String empId) throws Exception;

    /**
     * 根据多个ID获取员工信息
     *
     * @param token
     * @param dateInfo
     * @return
     */
    @PostMapping("/ly/adp/base/clue/getempbyidlist.do")
    ListResult<Map<String, Object>> sacMsgRecordFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                         @RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 调用BASE-一期值列表查询
     *
     * @param authentication
     * @param lookupTypeCode
     * @param lookupValueCode
     * @return
     */
    @PostMapping("/ly/adp/base/lookuptype/getlookupvaluename.do")
    String getLookupValueName(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                              @RequestParam(value = "lookupTypeCode") String lookupTypeCode,
                              @RequestParam(value = "lookupValueCode") String lookupValueCode);

    /**
     * 调用BASE-一期值列表查询-不带.do
     *
     * @param lookupTypeCode
     * @param lookupValueCode
     * @return
     */
    @PostMapping("/ly/adp/base/clue/getlookupvaluename")
    String getLookupValueName1(@RequestParam(value = "lookupTypeCode") String lookupTypeCode, @RequestParam(value = "lookupValueCode") String lookupValueCode);

    /**
     * 调用BASE-一期值查询服务
     *
     * @param authentication
     * @param mdsLookupValue
     * @return
     */
    @PostMapping("/ly/adp/base/lookuptype/mdslookupvaluefindbypage.do")
    ListResult<MdsLookupValue> mdsLookupValueFindByPage(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                        @RequestBody(required = false) MdsLookupValueIn mdsLookupValue);

    /**
     * 调用BASE-一期值查询服务
     *
     * @param mdsLookupValue
     * @return
     */
    @PostMapping("/ly/adp/base/lookuptype/mdslookupvaluefindbypageNotDo")
    ListResult<MdsLookupValue> mdslookupvaluefindbypageNotDo(@RequestBody(required = false) MdsLookupValueIn mdsLookupValue);

    /**
     * 调用BASE-试驾车信息管理
     *
     * @param authentication
     * @param param
     * @return
     */
    @PostMapping("/ly/adp/base/buTestcarPrepare/buTestcarPrepareManage.do")
    EntityResult<BuTestcarPrepare> inset(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                         @RequestBody ParamPage<BuTestcarPrepare> param);

    /**
     * 调用BASE-门店员工信息管理
     *
     * @param authentication
     * @param info
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/busicen/base/usc/org/employee/mdmOrgEmployeeQueryFindDlr.do")
    ListResult<Map<String, Object>> mdmOrgEmployeeQueryFindDlr(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgEmployeeIn info) throws Exception;

    /**
     * 保证金管理服务-车型配置信息
     *
     * @param authentication
     * @param param
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/adp/base/tUscBuCashDeposit/mdmVeCarConfigQueryList.do")
    ListResult<Map<String, Object>> mdmVeCarConfigQueryList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> param) throws Exception;

    /**
     * 区县服务-区县查询服务
     *
     * @param authentication
     * @param info
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/busicen/base/usc/org/mdmorgcommunity/mdmorgcommunityqueryfindall.do")
    ListResult<MdmOrgCommunityOut> mdmOrgCommunityQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgCommunityExtIn info) throws Exception;

    /**
     * 车身颜色服务-车身颜色查询
     *
     * @param authentication
     * @param mdmCarColor
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/busicen/base/prc/basedata/MdmVeCarColorController/mdmCarColorQueryList.do")
    ListResult<MdmCarColor> mdmCarColorQueryList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                 @RequestBody(required = false) MdmCarColorIn mdmCarColor) throws Exception;

    /**
     * 内饰颜色服务-查询内饰色-订单
     *
     * @param authentication
     * @param dataInfo
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/busicen/base/prc/basedata/MdmVeCarIncolorController/mdmCarIncolorQueryListForPage.do")
    ListResult<MdmCarIncolor> mdmCarIncolorQueryListForPage(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmCarIncolorIn dataInfo) throws Exception;

    /**
     * 代理商服务-代理商基本查询服务
     *
     * @param authentication
     * @param mdmAgentInfo
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/adp/base/mdmAgentInfo/agentBasicInfoList.do")
    ListResult<Map<String, Object>> agentBasicInfoList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmAgentInfo mdmAgentInfo) throws Exception;

    /**
     * 城市公司信息服务-城市公司下门店查询
     *
     * @param authentication
     * @param mdmAgentCompanyIn
     * @return
     */
    @PostMapping("/ly/adp/base/mdmAgentCompany/getMdmCompanyDlrInfoList.do")
    ListResult<Map<String, Object>> getMdmCompanyDlrInfoList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmAgentCompanyIn mdmAgentCompanyIn);

    /**
     * 门店管理服务-经销商信息查询
     *
     * @param authentication
     * @param info
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/adp/base/mdmDlrInfo/mdmDlrInfoQuery.do")
    ListResult<Map<String, Object>> mdmDlrInfoQuery(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> info) throws Exception;

    /**
     * 门店信息查询
     *
     * @param token
     * @param mdmOrgDlrIn
     * @return
     */
    @PostMapping("/ly/adp/base/mdmDlrInfo/MdmDlrInfoFindAll.do")
    ListResult<MdmOrgDlr> mdmDlrInfoFindAll(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                            @RequestBody(required = false) MdmOrgDlrIn mdmOrgDlrIn);

    /**
     * 员工信息服务-xapi员工查询
     *
     * @param empId
     * @return
     */
    @PostMapping("/ly/busicen/base/usc/org/employee/selectById")
    MdmOrgEmployee selectById(@RequestBody(required = false) String empId);

    /**
     * 根据empId获取员工信息
     *
     * @param authentication
     * @param info
     * @return
     * @throws Exception
     */
    @PostMapping("/ly/busicen/base/usc/org/employee/mdmOrgEmployeeQueryFindById.do")
    List<Map<String, Object>> mdmOrgEmployeeQueryFindById(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> info) throws Exception;

    /**
     * 查询员工品牌信息
     *
     * @param authentication
     * @param info
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/employeebr/mdmOrgEmployeeBrQueryFindAll", method = RequestMethod.POST)
    ListResult<MdmOrgEmployeeBrOut> mdmOrgEmployeeBrQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgEmployeeBrIn info) throws Exception;

    // 获取员工岗位信息
    // 员工信息服务-员工岗位查询
    @RequestMapping(value = "/ly/busicen/base/usc/org/employee/selectMdmOrgEmployeeByStation.do", method = RequestMethod.POST)
    ListResult<Map<String, Object>> selectMdmOrgEmployeeByStation(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> info) throws Exception;

    // 调用BASE-查询品牌信息列表
    @PostMapping("/ly/busicen/base/prc/basedata/carbrand/qurymdmcarbrand.do")
    List<MdmCarBrand> quryMdmCarBrand(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) MdmCarBrand mdmCarBrand);

    // 调用BASE-APP版本信息查询
    @PostMapping("/appversion/queryAppVersionInfo")
    ListResult<Map<String, Object>> queryAppVersionInfo(@RequestBody(required = false) Map<String, Object> map,
                                                        @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication);

    // 调用BASE-单号生成服务
    @PostMapping("/ordercoderule/generateOrderCode.do")
    OptResult generateOrderCode(
            @RequestParam("dlrId") String dlrId, @RequestParam("billTypeId") String billTypeId) throws Exception;

    // 调用BASE-单号生成服务-feign(不用token)
    @PostMapping("/ordercoderule/generateOrderCodeForFeign.do")
    OptResult generateOrderCodeForFeign(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody Map<String, Object> map);

    // 调用BASE-省市区三级查询
    @PostMapping("/ly/busicen/base/queryPrcProvinceLinkage/queryPrcProvinceLinkages.do")
    ListResult<Map<String, Object>> queryPrcProvinceLinkage(
            @RequestBody(required = false) PrcProvinceLinkageIn dataInfo,
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication);

    // 调用BASE-省份信息查询服务
    @PostMapping("/ly/busicen/base/usc/org/mdmOrgProvince/mdmOrgProvinceQueryFindAll.do")
    ListResult<MdmOrgProvince> mdmOrgProvinceQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgProvinceIn info);

    // 调用BASE-城市信息查询服务
    @PostMapping("/ly/busicen/base/usc/org/mdmorgcity/mdmorgcityqueryfindall.do")
    ListResult<MdmOrgCityOut> mdmOrgCityQueryFindAll(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                     @RequestBody(required = false) MdmOrgCityIn info);

    // 根据多个ID获取员工信息
    @PostMapping(value = "/ly/adp/base/clue/getempbyidlist.do")
    public ListResult<Map<String, Object>> getEmpByIdList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo);

    // 根据userId获取员工信息
    @PostMapping(value = "/ly/adp/base/clue/getuserinfobyuserid.do")
    public ListResult<Map<String, Object>> getUserInfoByUserId(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询用户所属组织类型
    @PostMapping(value = "/ly/adp/base/clue/getbelongnodeinfo.do")
    public ListResult<Map<String, Object>> getBelongNodeInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询员工所属经销商信息
    @PostMapping(value = "/ly/adp/base/clue/getempdlrinfo.do")
    public ListResult<Map<String, Object>> getEmpDlrInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 获取经销商对应的主机厂信息
    @PostMapping(value = "/ly/adp/base/clue/getdlroeminfo.do")
    public ListResult<Map<String, Object>> getDlrOemInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询员工品牌信息
    @PostMapping(value = "/ly/adp/base/clue/getempbrandinfo.do")
    public ListResult<Map<String, Object>> getEmpBrandInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 获取员工岗位信息
    @PostMapping(value = "/ly/adp/base/clue/getempstationinfo.do")
     ListResult<Map<String, Object>> getEmpStationInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 初始化代理商
    @PostMapping(value = "/ly/adp/base/clue/applyinitfind.do")
     ListResult<Map<String, Object>> applyInitFind(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询区域经理管辖的门店
    @PostMapping(value = "/ly/adp/base/clue/querymanagedlr.do")
     ListResult<Map<String, Object>> findManagedDlrList(@RequestBody(required = false) Map<String, Object> dateInfo);

    //城市-门店关系查询
    @PostMapping(value = "/ly/adp/base/clue/querycitydlr.do")
    ListResult<Map<String, Object>> querycitydlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                 @RequestBody(required = false) Map<String, Object> dateInfo);

    //大区-城市关系查询
    @PostMapping(value = "/ly/adp/base/clue/queryareacity.do")
    ListResult<Map<String, Object>> queryAreaCityRelation(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                          @RequestBody(required = false) Map<String, Object> dateInfo);

    //大区用户关系查询
    @PostMapping(value = "/ly/adp/base/clue/queryuserarea.do")
    ListResult<Map<String, Object>> queryUserAreaRelation(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                          @RequestBody(required = false) Map<String, Object> dateInfo);

    //用户城市关系查询
    @PostMapping(value = "/ly/adp/base/clue/queryusercity.do")
    ListResult<Map<String, Object>> queryUserCityRelation(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                          @RequestBody(required = false) Map<String, Object> dateInfo);

    //大区信息查询
    @PostMapping(value = "/ly/adp/base/areainfo/bigAreaInfoQuery.do")
    ListResult<Map<String, Object>> bigAreaInfoQuery(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                     @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam);

    // 查询发布对象列表
    @PostMapping(value = "/ly/adp/base/clue/queryreceiveobject.do")
    ListResult<Map<String, Object>> taskReceiveObjectQuery(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询发布对象列表
    @PostMapping(value = "/ly/adp/base/clue/queryreceiveobject.todo")
    ListResult<Map<String, Object>> taskReceiveObjectQuery2(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询发布对象人员信息
    @PostMapping(value = "/ly/adp/base/clue/querytaskobjectuserlist.do")
    ListResult<Map<String, Object>> queryTaskObjectUserList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询发布对象人员信息
    @PostMapping(value = "/ly/adp/base/clue/querytaskobjectuserlist.todo")
    ListResult<Map<String, Object>> queryTaskObjectUserList2(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    // 发布对象更新
    @PostMapping(value = "/ly/adp/base/clue/receiveobjectupdate.do")
    ListResult<Map<String, Object>> taskReceiveObjectUpdate(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 发布对象更新
    @PostMapping(value = "/ly/adp/base/clue/receiveobjectupdate.todo")
    ListResult<Map<String, Object>> taskReceiveObjectUpdate2(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 发布对象人员临时表到历史表
    @PostMapping(value = "/ly/adp/base/clue/receiveobjectmove.do")
    ListResult<Map<String, Object>> mdmOrgReceiveObjectMove(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 发布对象人员临时表到历史表
    @PostMapping(value = "/ly/adp/base/clue/receiveobjectmove.todo")
    ListResult<Map<String, Object>> mdmOrgReceiveObjectMove2(@RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping(value = "/ly/busicen/base/szlanyou/prc/basedata/MdmSmallCarTypeController/mdmSmallCarTypeQueryListForPage.do")
    ListResult<MdmSmallCarTypeOut> mdmSmallCarTypeQueryListForPage(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) MdmSmallCarTypeIn mdmSmallCarTypeIn) throws Exception;

    @PostMapping(value = "/ly/adp/base/buTestdriverCarset/querycarColorCode.do")
    ListResult<Map<String, Object>> carColorCode(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody ParamPage<BuTestdriverCarset> param);

    // 根据员工ID查员工信息
    @PostMapping(value = "/ly/adp/base/clue/queryEmpInfoByUserId.do")
    ListResult<Map<String, Object>> mdmOrgEmployeeQueryByUserId(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);

    // 根据FILE_PATH 查询OBS桶路径
    @PostMapping(value = "/ly/adp/base/clue/selectOBSFilePath.do")
    String selectOBSFilePath(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestParam(value = "filePath") String filePath);

    // 根据dlrCode/agentCompany/agentCode修改base的门店/城市公司/代理商状态
    @PostMapping(value = "/ly/adp/base/mdmDlrInfo/MdmDlrUpdateBylogout.do")
    OptResult MdmDlrUpdateBylogout(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dateInfo);

    @ApiOperation("用户门店关系查询1")
    @PostMapping("/ly/adp/base/clue/queryuserdlrrelation1.do")
    ListResult<TUscMdmOrgEmployeeVO> queryuserdlrrelation1(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                           @RequestBody(required = false) UserDlrRelationQry qry);
}