package com.ly.adp.csc.otherservice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.csc.idal.mapper.BaseQueryMapper;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.csc.clue.otherservice.ICscSysOrgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CscSysOrgService implements ICscSysOrgService {

	private static final Logger logger = LoggerFactory.getLogger(CscSysOrgService.class);

	@Autowired
	IBasedataFeignClient basedataFeignClient;

	@Autowired
	BaseQueryMapper baseQueryMapper;

	@Override
	public ListResult<Map<String, Object>> getEmpListByPosition(String paramString,
			ParamPage<Map<String, Object>> paramPage) {
		try {
			ParamBase<Map<String, Object>> paramBase = new ParamBase<>();
			paramBase.setParam(paramPage.getParam());
			return basedataFeignClient.selectMdmOrgEmployeeByStation(paramString, paramBase);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public ListResult<Map<String, Object>> getEmpListByIdList(String token, ParamPage<Map<String, Object>> mapParam) {
		try {
			logger.info("basedataFeignClient.getEmpByIdList入参:{}", JSONObject.toJSONString(mapParam.getParam()));
			ListResult<Map<String, Object>> empByIdList = basedataFeignClient.getEmpByIdList(token, mapParam.getParam());
			if (!"1".equals(empByIdList.getResult())) {
				logger.error("调用base服务getEmpListByIdList接口错误，message：{}", empByIdList.getMsg());
			}
			List<Map<String, Object>> rows = empByIdList.getRows();
			logger.info("basedataFeignClient.getEmpByIdList结果:{}", JSONObject.toJSONString(empByIdList));
			ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
			IPage<Map<String, Object>> page = new Page<>(1, -1);
			page.setRecords(rows);
			result = BusicenUtils.page2ListResult(page);
			return result;
		} catch (Exception e) {
			logger.error("getEmpListByIdList异常", e.getMessage(), e);
			throw e;
		}
	}
	@Override
	public ListResult<Map<String, Object>> newGetEmpListByIdList(ParamPage<Map<String, Object>> mapParam) {
		try {
			Map<String, Object> param = mapParam.getParam();
			logger.info("newGetEmpListByIdList参数:{}", JSONObject.toJSONString(param));
			List<Map<String, Object>> maps = baseQueryMapper.newGetEmpByIdList(param);
			if (!CollectionUtils.isEmpty(maps)) {
				logger.info("newGetEmpListByIdList查询结果:{}", JSONObject.toJSONString(maps));
			}
			ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
			result.setRows(maps);
			return result;
		} catch (Exception e) {
			logger.error("查询newGetEmpListByIdList异常", e.getMessage(), e);
			throw e;
		}
	}
	@Override
	public String generateTokenByUserId(String userId) {
		String token = "";
		try {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", userId);
			OptResult result = basedataFeignClient.createMpToken(paramMap);
			if ("1".equals(result.getResult())) {
				String message = result.getMsg();
				JSONObject obj = (JSONObject) JSONObject.parse(URLDecoder.decode(message, "UTF-8"));
				token = obj.getString("token");
			}
		} catch (Exception e) {
			logger.error("CscSysOrgService::generateTokenByUserId", e);
			throw BusicenException.create(e.getMessage());
		}
		return token;
	}

	@Override
	public String generateTokenByUserIdWithoutSuffix(String userId) {
		String token = "";
		try {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", userId);
			OptResult result = basedataFeignClient.createMpTokenWithoutSuffix(paramMap);
			if ("1".equals(result.getResult())) {
				String message = result.getMsg();
				JSONObject obj = (JSONObject) JSONObject.parse(URLDecoder.decode(message, "UTF-8"));
				token = obj.getString("token");
			}
		} catch (Exception e) {
			logger.error("CscSysOrgService::generateTokenByUserIdWithoutSuffix", e);
			throw BusicenException.create(e.getMessage());
		}
		return token;
	}

	@Override
	public Map<String, Object> getOrgDlrInfo(String token, String dlrCode) {
		Map<String, Object> info = new HashMap<>();
		info.put("dlrCode", dlrCode);
		try {
			ListResult<Map<String, Object>> listResult = basedataFeignClient.mdmDlrInfoQuery(token, info);
			if ("1".equals(listResult.getResult())) {
				List<Map<String, Object>> rows = listResult.getRows();
				if (rows != null && !rows.isEmpty()) {
					return rows.get(0);
				}
			}
		} catch (Exception e) {
			return null;
		}
		return null;
	}

	@Override
	public ListResult<Map<String, Object>> getDlrInfoNoToken(ParamPage<Map<String, Object>> mapPage) {
		try {
			Map<String, Object> info = new HashMap<>();
			Map<String, Object> param = mapPage.getParam();
			info.put("dlrCode", param.get("dlrCode"));
			return basedataFeignClient.mdmDlrInfoQuery("", param); // Empty token
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
