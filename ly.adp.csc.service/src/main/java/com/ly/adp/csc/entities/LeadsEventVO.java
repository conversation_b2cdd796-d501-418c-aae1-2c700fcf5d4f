package com.ly.adp.csc.entities;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import net.sourceforge.jtds.jdbc.DateTime;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/9/15
 */
public class LeadsEventVO implements Serializable {

    private static final long serialVersionUID = 2039171359242557647L;

    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("发生时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;


    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public LocalDateTime getEventTime() {
        return eventTime;
    }

    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }

    @Override
    public String toString() {
        return "LeadsEventVO{" +
                "eventName='" + eventName + '\'' +
                ", eventTime=" + eventTime +
                '}';
    }
}
