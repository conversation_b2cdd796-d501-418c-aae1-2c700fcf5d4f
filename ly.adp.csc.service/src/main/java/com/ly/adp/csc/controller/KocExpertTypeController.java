//package com.ly.adp.csc.controller;
//
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.google.common.net.HttpHeaders;
//import com.ly.adp.csc.entities.vo.koc.ExpertTypeInfoVO;
//import com.ly.adp.csc.service.IKocExpertTypeService;
//import com.ly.mp.busicen.common.context.BusicenInvoker;
//import com.ly.mp.component.entities.EntityResult;
//import com.ly.mp.component.entities.ListResult;
//import com.ly.mp.component.entities.OptResult;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//
///**
// * <p>
// * KOC达人类型管理控制器
// * </p>
// *
// * <AUTHOR> System
// * @since 2025-01-25
// */
//@RestController
//@Api(value = "KOC达人类型管理服务", tags = "KOC达人类型管理服务")
//@RequestMapping(value = "/ly/adp/csc/koc/expertType", produces = { MediaType.APPLICATION_JSON_VALUE })
//public class KocExpertTypeController {
//
//    @Autowired
//    private IKocExpertTypeService kocExpertTypeService;
//
//    @ApiOperation(value = "获取所有达人类型", notes = "获取所有达人类型列表（缓存优先）")
//    @PostMapping(value = "/getAllExpertTypes.do")
//    public List<ExpertTypeInfoVO> getAllExpertTypes(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocExpertTypeService.getAllExpertTypes(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "达人类型列表查询", notes = "分页查询达人类型管理列表")
//    @PostMapping(value = "/queryExpertTypeList.do")
//    public ListResult<Map<String, Object>> queryExpertTypeList(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doList(() -> {
//            return kocExpertTypeService.queryExpertTypeList(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "创建达人类型", notes = "创建新的达人类型")
//    @PostMapping(value = "/createExpertType.do")
//    public EntityResult<Map<String, Object>> createExpertType(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocExpertTypeService.createExpertType(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "更新达人类型", notes = "更新达人类型信息")
//    @PostMapping(value = "/updateExpertType.do")
//    public EntityResult<Map<String, Object>> updateExpertType(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocExpertTypeService.updateExpertType(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "删除达人类型", notes = "删除单个达人类型")
//    @PostMapping(value = "/deleteExpertType.do")
//    public OptResult deleteExpertType(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            String typeId = (String) dataInfo.get("typeId");
//            return kocExpertTypeService.deleteExpertType(typeId, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "批量删除达人类型", notes = "批量删除达人类型")
//    @PostMapping(value = "/batchDeleteExpertTypes.do")
//    public OptResult batchDeleteExpertTypes(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            @SuppressWarnings("unchecked")
//            List<String> typeIds = (List<String>) dataInfo.get("typeIds");
//            return kocExpertTypeService.batchDeleteExpertTypes(typeIds, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "批量导入达人类型", notes = "批量导入达人类型")
//    @PostMapping(value = "/batchImportExpertTypes.do")
//    public OptResult batchImportExpertTypes(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> expertTypeList = (List<Map<String, Object>>) dataInfo.get("expertTypeList");
//            return kocExpertTypeService.batchImportExpertTypes(expertTypeList, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取达人类型统计", notes = "获取达人类型统计信息")
//    @PostMapping(value = "/getExpertTypeStatistics.do")
//    public List<Map<String, Object>> getExpertTypeStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocExpertTypeService.getExpertTypeStatistics(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "批量解绑达人类型", notes = "批量解绑达人类型")
//    @PostMapping(value = "/batchUnbindExpertTypes.do")
//    public OptResult batchUnbindExpertTypes(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            return kocExpertTypeService.batchUnbindExpertTypes(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "刷新达人类型缓存", notes = "刷新达人类型缓存")
//    @PostMapping(value = "/refreshExpertTypeCache.do")
//    public OptResult refreshExpertTypeCache(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doOpt(() -> {
//            return kocExpertTypeService.refreshExpertTypeCache(authentication);
//        }).result();
//    }
//
//    // ========== 企微端专用接口 ==========
//
//    @ApiOperation(value = "获取达人类型列表（企微端）", notes = "获取所有可用达人类型列表（企微端专用）")
//    @PostMapping(value = "/wechat/getExpertTypeList.do")
//    public ListResult<ExpertTypeInfoVO> getWechatExpertTypeList(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doList(() -> {
//            List<ExpertTypeInfoVO> expertTypes = kocExpertTypeService.getAllExpertTypes(authentication);
//            ListResult<ExpertTypeInfoVO> result = new ListResult<>();
//            result.setData(expertTypes);
//            result.setTotal(expertTypes.size());
//            result.setSuccess(true);
//            return result;
//        }).result();
//    }
//}
