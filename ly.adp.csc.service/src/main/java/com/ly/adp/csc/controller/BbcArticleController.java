package com.ly.adp.csc.controller;


import com.google.common.net.HttpHeaders;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.impl.BbcArticleBiz;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 论坛文章 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@RestController
@RequestMapping("/ly/adp/csc/bbcArticle")
@Api(value = "论坛文章",tags = {"论坛文章"})
public class BbcArticleController {
   @Resource
    BbcArticleBiz bbcArticleBiz;
    @ApiOperation(value = "帖子查询", notes = "帖子查询")
    @PostMapping(value = "/queryArticle.do")
    public ListResult<Map<String, Object>> queryArticle(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return bbcArticleBiz.queryAruticle(authentication,dateInfo);}).result();
    }
    @ApiOperation(value = "帖子管理", notes = "帖子管理")
    @PostMapping(value = "/managerArticle.do")
    public OptResult managerArticle(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                       @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcArticleBiz.insertAruticle(dateInfo, authentication);}).result();
    }


    @ApiOperation(value = "板块", notes = "板块")
    @PostMapping(value = "/queryArticleBuild.do")
    public EntityResult<List> queryArticleBuild(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> {return bbcArticleBiz.queryArticleBuild(dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "修改帖子", notes = "修改帖子")
    @PostMapping(value = "/updateAruticle.do")
    public OptResult updateAruticle(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                    @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcArticleBiz.updateAruticle(dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "帖子审核列表", notes = "帖子审核列表")
    @PostMapping(value = "/queryArticleList.do")
    public ListResult<Map<String, Object>> queryArticleList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return bbcArticleBiz.queryAruticle(authentication,dateInfo);}).result();
    }
    @ApiOperation(value = "删除帖子", notes = "删除帖子")
    @PostMapping(value = "/delArticle.do")
    public OptResult delArticle(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                    @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcArticleBiz.delAruticle(dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "新增帖子查看数量", notes = "帖子查看数量")
    @PostMapping(value = "/saveArticleCt.do")
    public OptResult saveArticleCt(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcArticleBiz.saveAruticleCount(dateInfo, authentication);}).result();
    }
}

