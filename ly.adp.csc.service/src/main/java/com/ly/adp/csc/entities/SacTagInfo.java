package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 标签信息表
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
@TableName("t_sac_tag_info")
public class SacTagInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @TableId("TAG_ID")
    private String tagId;

    /**
     * 标签名称
     */
    @TableField("TAG_NAME")
    private String tagName;

    /**
     * 父标签ID
     */
    @TableField("PARENT_TAG_ID")
    private String parentTagId;

    /**
     * 标签层级：1,2,3
     */
    @TableField("TAG_LEVEL")
    private Integer tagLevel;

    /**
     * 完整路径
     */
    @TableField("FULL_PATH")
    private String fullPath;

    /**
     * 标签状态：1-上架，0-下架
     */
    @TableField("TAG_STATUS")
    private Integer tagStatus;

    /**
     * 同级排序
     */
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getParentTagId() {
        return parentTagId;
    }

    public void setParentTagId(String parentTagId) {
        this.parentTagId = parentTagId;
    }

    public Integer getTagLevel() {
        return tagLevel;
    }

    public void setTagLevel(Integer tagLevel) {
        this.tagLevel = tagLevel;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public Integer getTagStatus() {
        return tagStatus;
    }

    public void setTagStatus(Integer tagStatus) {
        this.tagStatus = tagStatus;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    @Override
    public String toString() {
        return "SacTagInfo{" +
                "tagId='" + tagId + '\'' +
                ", tagName='" + tagName + '\'' +
                ", parentTagId='" + parentTagId + '\'' +
                ", tagLevel=" + tagLevel +
                ", fullPath='" + fullPath + '\'' +
                ", tagStatus=" + tagStatus +
                ", sortOrder=" + sortOrder +
                ", creator='" + creator + '\'' +
                ", createdName='" + createdName + '\'' +
                ", createdDate=" + createdDate +
                ", modifier='" + modifier + '\'' +
                ", modifyName='" + modifyName + '\'' +
                ", lastUpdatedDate=" + lastUpdatedDate +
                '}';
    }
    
}