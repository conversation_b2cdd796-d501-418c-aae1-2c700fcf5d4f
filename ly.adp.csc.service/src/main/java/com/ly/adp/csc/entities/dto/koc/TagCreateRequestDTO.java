package com.ly.adp.csc.entities.dto.koc;

import java.io.Serializable;

/**
 * 标签创建请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class TagCreateRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 父标签ID
     */
    private String parentTagId;

    /**
     * 同级排序
     */
    private Integer sortOrder;

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getParentTagId() {
        return parentTagId;
    }

    public void setParentTagId(String parentTagId) {
        this.parentTagId = parentTagId;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "TagCreateRequestDTO{" +
                "tagName='" + tagName + '\'' +
                ", parentTagId='" + parentTagId + '\'' +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
