package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 评价中心短信入参返参日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-23
 */
@TableName("t_sac_handle_msg_log")
public class HandleMsgLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评价中心短信入参返参日志ID
     */
    @TableId("LOG_ID")
    private String logId;

    /**
     * 入参
     */
    @TableField("IN_PARAMETER")
    private String inParameter;

    /**
     * 返参
     */
    @TableField("RETURN_PARAMETER")
    private String returnParameter;

    /**
     * 插入时间
     */
    @TableField("INSER_DATE")
    private LocalDateTime inserDate;


    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getInParameter() {
        return inParameter;
    }

    public void setInParameter(String inParameter) {
        this.inParameter = inParameter;
    }

    public String getReturnParameter() {
        return returnParameter;
    }

    public void setReturnParameter(String returnParameter) {
        this.returnParameter = returnParameter;
    }

    public LocalDateTime getInserDate() {
        return inserDate;
    }

    public void setInserDate(LocalDateTime inserDate) {
        this.inserDate = inserDate;
    }

    @Override
    public String toString() {
        return "HandleMsgLog{" +
        "logId=" + logId +
        ", inParameter=" + inParameter +
        ", returnParameter=" + returnParameter +
        ", inserDate=" + inserDate +
        "}";
    }
}
