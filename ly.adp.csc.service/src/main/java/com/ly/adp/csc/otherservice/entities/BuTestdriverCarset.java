package com.ly.adp.csc.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 试驾车申请设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("t_usc_bu_testdriver_carset")
public class BuTestdriverCarset  implements Serializable {


    private static final long serialVersionUID = 1L;
  private String smallCarTypeCode;

    public String getSmallCarTypeCode() {
        return smallCarTypeCode;
    }

    public void setSmallCarTypeCode(String smallCarTypeCode) {
        this.smallCarTypeCode = smallCarTypeCode;
    }

    private String lookupValueName;

    public String getLookupValueName() {
        return lookupValueName;
    }

    public void setLookupValueName(String lookupValueName) {
        this.lookupValueName = lookupValueName;
    }

    //配置数量
     @TableField("TEST_DRIVER_CAR_QUALITY")
    private String testDriverCarQuality;

    public String getTestDriverCarQuality() {
        return testDriverCarQuality;
    }

    public void setTestDriverCarQuality(String testDriverCarQuality) {
        this.testDriverCarQuality = testDriverCarQuality;
    }

    private String smallCarTypeCn;
    private String  carConfigCn;
    private String carColorName;
    private String  carIncolorName;

    public String getSmallCarTypeCn() {
        return smallCarTypeCn;
    }

    public void setSmallCarTypeCn(String smallCarTypeCn) {
        this.smallCarTypeCn = smallCarTypeCn;
    }

    public String getCarConfigCn() {
        return carConfigCn;
    }

    public void setCarConfigCn(String carConfigCn) {
        this.carConfigCn = carConfigCn;
    }

    public String getCarColorName() {
        return carColorName;
    }

    public void setCarColorName(String carColorName) {
        this.carColorName = carColorName;
    }

    public String getCarIncolorName() {
        return carIncolorName;
    }

    public void setCarIncolorName(String carIncolorName) {
        this.carIncolorName = carIncolorName;
    }

    /**
     * 试驾车设置ID
     */
    @TableId("TESTDRIV_CARSET_ID")
    private String testdrivCarsetId;

    /**
     * 门店类型
     */
    @TableField("DLR_TYPE")
    private String dlrType;

    /**
     * 车型
     */
    @TableField("CAR_TYPE_CODE")
    private String carTypeCode;

    /**
     * 配置
     */
    @TableField("CAR_CONFIG_CODE")
    private String carConfigCode;

    /**
     * 颜色
     */
    @TableField("CAR_COLOR_CODE")
    private String carColorCode;

    /**
     * 内饰
     */
    @TableField("CAR_INCOLOR_CODE")
    private String carIncolorCode;

    /**
     * 使用时长
     */
    @TableField("TEST_CAR_EXPIRE")
    private Integer testCarExpire;

    /**
     * and标准
     */
    @TableField("TEST_CAR_PRESTANDARD")
    private String testCarPrestandard;

    /**
     * 备注
     */
    @TableField("SETTING_REMARK")
    private String settingRemark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;


    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    private List<BuTestdriverSetappend> buTestdriverSetappends;
    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    public List<BuTestdriverSetappend> getBuTestdriverSetappends() {
        return buTestdriverSetappends;
    }

    public void setBuTestdriverSetappends(List<BuTestdriverSetappend> buTestdriverSetappends) {
        this.buTestdriverSetappends = buTestdriverSetappends;
    }

    public String getTestdrivCarsetId() {
        return testdrivCarsetId;
    }

    public void setTestdrivCarsetId(String testdrivCarsetId) {
        this.testdrivCarsetId = testdrivCarsetId;
    }

    public String getDlrType() {
        return dlrType;
    }

    public void setDlrType(String dlrType) {
        this.dlrType = dlrType;
    }

    public String getCarTypeCode() {
        return carTypeCode;
    }

    public void setCarTypeCode(String carTypeCode) {
        this.carTypeCode = carTypeCode;
    }

    public String getCarConfigCode() {
        return carConfigCode;
    }

    public void setCarConfigCode(String carConfigCode) {
        this.carConfigCode = carConfigCode;
    }

    public String getCarColorCode() {
        return carColorCode;
    }

    public void setCarColorCode(String carColorCode) {
        this.carColorCode = carColorCode;
    }

    public String getCarIncolorCode() {
        return carIncolorCode;
    }

    public void setCarIncolorCode(String carIncolorCode) {
        this.carIncolorCode = carIncolorCode;
    }

    public Integer getTestCarExpire() {
        return testCarExpire;
    }

    public void setTestCarExpire(Integer testCarExpire) {
        this.testCarExpire = testCarExpire;
    }

    public String getTestCarPrestandard() {
        return testCarPrestandard;
    }

    public void setTestCarPrestandard(String testCarPrestandard) {
        this.testCarPrestandard = testCarPrestandard;
    }

    public String getSettingRemark() {
        return settingRemark;
    }

    public void setSettingRemark(String settingRemark) {
        this.settingRemark = settingRemark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BuTestdriverCarset{" +
        "testdrivCarsetId=" + testdrivCarsetId +
        ", dlrType=" + dlrType +
        ", carTypeCode=" + carTypeCode +
        ", carConfigCode=" + carConfigCode +
        ", carColorCode=" + carColorCode +
        ", carIncolorCode=" + carIncolorCode +
        ", testCarExpire=" + testCarExpire +
        ", testCarPrestandard=" + testCarPrestandard +
        ", settingRemark=" + settingRemark +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
