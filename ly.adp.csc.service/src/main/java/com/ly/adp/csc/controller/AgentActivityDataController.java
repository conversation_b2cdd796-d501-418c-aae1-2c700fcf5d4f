package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.IAgentActivityDataService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "代理商用户运营活动数据校验接口", tags = "代理商用户运营活动数据校验接口")
@RestController
@RequestMapping(value = "/ly/adp/csc/agentdata", produces = { MediaType.APPLICATION_JSON_VALUE })
public class AgentActivityDataController {

	@Autowired
	private IAgentActivityDataService agentActivityDataService;

	@ApiOperation(value = "校验当前门店是否配置品牌大使", notes = "校验当前门店是否配置品牌大使")
	@PostMapping(value = "/checkDlr.do")
	public EntityResult<Boolean> checkDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
		return BusicenInvoker.doEntity(() -> agentActivityDataService.checkDlr(authentication))
				.result();
	}

	@ApiOperation(value = "品牌大使设置管辖门店范围", notes = "品牌大使设置管辖门店范围")
	@PostMapping(value = "/queryBiDlr.do")
	public EntityResult<String> queryBiDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
		return BusicenInvoker.doEntity(() -> agentActivityDataService.queryBiDlr(authentication))
				.result();
	}
}
