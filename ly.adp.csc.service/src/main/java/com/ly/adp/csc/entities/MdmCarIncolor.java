package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 内饰颜色
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-19
 */
@TableName("t_prc_mdm_car_incolor")
public class MdmCarIncolor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内饰颜色ID
     */
    @TableId("CAR_INCOLOR_ID")
    private String carIncolorId;

    /**
     * 内饰颜色编码
     */
    @TableField(value="CAR_INCOLOR_CODE", condition = SqlCondition.LIKE)
    private String carIncolorCode;

    /**
     * 内饰颜色名称
     */
    @TableField(value="CAR_INCOLOR_NAME", condition = SqlCondition.LIKE)
    private String carIncolorName;

    /**
     * 品牌编码
     */
    @TableField("CAR_BRAND_CODE")
    private String carBrandCode;
    @TableField(value="CAR_BRAND_CN",exist=false)
    private String carBrandCn;

    /**
     * 供应状态  供应状态，VE0014
     */
    @TableField("SUPPLY_STATUS")
    private String supplyStatus;
    @TableField(value="SUPPLY_STATUS_CN",exist=false)
    private String supplyStatusCn;
    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人姓名
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 修改人姓名
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;
    @TableField(value="IS_ENABLE_CN",exist=false)
    private String isEnableCn;
    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;
    
    public String getCarBrandCn() {
		return carBrandCn;
	}

	public void setCarBrandCn(String carBrandCn) {
		this.carBrandCn = carBrandCn;
	}

	public String getSupplyStatusCn() {
		return supplyStatusCn;
	}

	public void setSupplyStatusCn(String supplyStatusCn) {
		this.supplyStatusCn = supplyStatusCn;
	}

	public String getIsEnableCn() {
		return isEnableCn;
	}

	public void setIsEnableCn(String isEnableCn) {
		this.isEnableCn = isEnableCn;
	}

	public String getCarIncolorId() {
        return carIncolorId;
    }

    public void setCarIncolorId(String carIncolorId) {
        this.carIncolorId = carIncolorId;
    }
    public String getCarIncolorCode() {
        return carIncolorCode;
    }

    public void setCarIncolorCode(String carIncolorCode) {
        this.carIncolorCode = carIncolorCode;
    }
    public String getCarIncolorName() {
        return carIncolorName;
    }

    public void setCarIncolorName(String carIncolorName) {
        this.carIncolorName = carIncolorName;
    }
    public String getCarBrandCode() {
        return carBrandCode;
    }

    public void setCarBrandCode(String carBrandCode) {
        this.carBrandCode = carBrandCode;
    }
    public String getSupplyStatus() {
        return supplyStatus;
    }

    public void setSupplyStatus(String supplyStatus) {
        this.supplyStatus = supplyStatus;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }
    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }
    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }
    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }
    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

	@Override
	public String toString() {
		return "MdmCarIncolor [carIncolorId=" + carIncolorId + ", carIncolorCode=" + carIncolorCode
				+ ", carIncolorName=" + carIncolorName + ", carBrandCode=" + carBrandCode + ", carBrandCn=" + carBrandCn
				+ ", supplyStatus=" + supplyStatus + ", supplyStatusCn=" + supplyStatusCn + ", mycatOpTime="
				+ mycatOpTime + ", oemId=" + oemId + ", groupId=" + groupId + ", oemCode=" + oemCode + ", groupCode="
				+ groupCode + ", creator=" + creator + ", createdName=" + createdName + ", createdDate=" + createdDate
				+ ", modifier=" + modifier + ", modifyName=" + modifyName + ", lastUpdatedDate=" + lastUpdatedDate
				+ ", isEnable=" + isEnable + ", isEnableCn=" + isEnableCn + ", sdpUserId=" + sdpUserId + ", sdpOrgId="
				+ sdpOrgId + ", updateControlId=" + updateControlId + ", column1=" + column1 + ", column2=" + column2
				+ ", column3=" + column3 + ", column4=" + column4 + ", column5=" + column5 + ", column6=" + column6
				+ ", column7=" + column7 + ", column8=" + column8 + ", column9=" + column9 + ", column10=" + column10
				+ "]";
	}


}
