package com.ly.adp.csc.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 试驾车退役表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@TableName("t_usc_bu_testcar_retire")
public class BuTestcarRetire implements Serializable {


    private static final long serialVersionUID = 1L;
private String orgId;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    private String testCarAppyCode;
    private String smallCarTypeCn;
    private String  carConfigCn;
    private String carColorName;
    private String  carIncolorName;

    private String retireApplyCode;

    public String getRetireApplyCode() {
        return retireApplyCode;
    }

    public void setRetireApplyCode(String retireApplyCode) {
        this.retireApplyCode = retireApplyCode;
    }

    public String getTestCarAppyCode() {
        return testCarAppyCode;
    }

    public void setTestCarAppyCode(String testCarAppyCode) {
        this.testCarAppyCode = testCarAppyCode;
    }

    public String getSmallCarTypeCn() {
        return smallCarTypeCn;
    }

    public void setSmallCarTypeCn(String smallCarTypeCn) {
        this.smallCarTypeCn = smallCarTypeCn;
    }

    public String getCarConfigCn() {
        return carConfigCn;
    }

    public void setCarConfigCn(String carConfigCn) {
        this.carConfigCn = carConfigCn;
    }

    public String getCarColorName() {
        return carColorName;
    }

    public void setCarColorName(String carColorName) {
        this.carColorName = carColorName;
    }

    public String getCarIncolorName() {
        return carIncolorName;
    }

    public void setCarIncolorName(String carIncolorName) {
        this.carIncolorName = carIncolorName;
    }

    /**
     * 试驾车申请ID
     */
    private String testCarAppyid;

    /**
     * 车型
     */
    private String applyCarTypeCode;

    /**
     * 车型配置
     */
    private String applyCartypeConfig;

    /**
     * 颜色
     */
    private String applyCarColorCode;

    /**
     * 内饰
     */
    private String applyCarIncolorCode;


    public String getTestCarAppyid() {
        return testCarAppyid;
    }

    public void setTestCarAppyid(String testCarAppyid) {
        this.testCarAppyid = testCarAppyid;
    }

    public String getApplyCarTypeCode() {
        return applyCarTypeCode;
    }

    public void setApplyCarTypeCode(String applyCarTypeCode) {
        this.applyCarTypeCode = applyCarTypeCode;
    }

    public String getApplyCartypeConfig() {
        return applyCartypeConfig;
    }

    public void setApplyCartypeConfig(String applyCartypeConfig) {
        this.applyCartypeConfig = applyCartypeConfig;
    }

    public String getApplyCarColorCode() {
        return applyCarColorCode;
    }

    public void setApplyCarColorCode(String applyCarColorCode) {
        this.applyCarColorCode = applyCarColorCode;
    }

    public String getApplyCarIncolorCode() {
        return applyCarIncolorCode;
    }

    public void setApplyCarIncolorCode(String applyCarIncolorCode) {
        this.applyCarIncolorCode = applyCarIncolorCode;
    }

    /**
     * 试驾车管理ID
     */
    @TableId("RETIRE_APPLY_ID")
    private String retireApplyId;

    /**
     * 试驾车ID
     */
    @TableField("TEST_CAR_ID")
    private String testCarId;

    /**
     * 申请门店
     */
    @TableField("APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 审批状态
     */
    @TableField("TEST_CAR_STATUS")
    private String testCarStatus;

    /**
     * 申请人
     */
    @TableField("APPLY_NAME")
    private String applyName;

    /**
     * 申请日期
     */
    @TableField("APPLY_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime applyDate;

    /**
     * 业务审批人
     */
    @TableField("BU_AUTHER_NAME")
    private String buAutherName;

    /**
     * 业务审批日期
     */
    @TableField("BU_AUTHER_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime buAutherDate;

    /**
     * 渠道审批人
     */
    @TableField("BD_AUTHER_NAME")
    private String bdAutherName;

    /**
     * 渠道审批日期
     */
    @TableField("BD_AUTHER_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime bdAutherDate;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getRetireApplyId() {
        return retireApplyId;
    }

    public void setRetireApplyId(String retireApplyId) {
        this.retireApplyId = retireApplyId;
    }

    public String getTestCarId() {
        return testCarId;
    }

    public void setTestCarId(String testCarId) {
        this.testCarId = testCarId;
    }

    public String getApplyDlrCode() {
        return applyDlrCode;
    }

    public void setApplyDlrCode(String applyDlrCode) {
        this.applyDlrCode = applyDlrCode;
    }

    public String getTestCarStatus() {
        return testCarStatus;
    }

    public void setTestCarStatus(String testCarStatus) {
        this.testCarStatus = testCarStatus;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public LocalDateTime getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(LocalDateTime applyDate) {
        this.applyDate = applyDate;
    }

    public String getBuAutherName() {
        return buAutherName;
    }

    public void setBuAutherName(String buAutherName) {
        this.buAutherName = buAutherName;
    }

    public LocalDateTime getBuAutherDate() {
        return buAutherDate;
    }

    public void setBuAutherDate(LocalDateTime buAutherDate) {
        this.buAutherDate = buAutherDate;
    }

    public String getBdAutherName() {
        return bdAutherName;
    }

    public void setBdAutherName(String bdAutherName) {
        this.bdAutherName = bdAutherName;
    }

    public LocalDateTime getBdAutherDate() {
        return bdAutherDate;
    }

    public void setBdAutherDate(LocalDateTime bdAutherDate) {
        this.bdAutherDate = bdAutherDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BuTestcarRetire{" +
        "retireApplyId=" + retireApplyId +
        ", testCarId=" + testCarId +
        ", applyDlrCode=" + applyDlrCode +
        ", testCarStatus=" + testCarStatus +
        ", applyName=" + applyName +
        ", applyDate=" + applyDate +
        ", buAutherName=" + buAutherName +
        ", buAutherDate=" + buAutherDate +
        ", bdAutherName=" + bdAutherName +
        ", bdAutherDate=" + bdAutherDate +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
