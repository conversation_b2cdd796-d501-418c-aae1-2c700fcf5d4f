package com.ly.adp.csc.service;

import java.util.Map;

import com.ly.adp.csc.entities.in.SacTestDriveReviewRecordIn;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

public interface ISacTestDriveReviewRecordService {
	
	/**
	 * 试乘试驾跟进记录查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacTestDriveReviewRecordFindAll(SacTestDriveReviewRecordIn dataInfo);
	
	/**
	 * 试乘试驾跟进记录新增
	 * @param info
	 * @return
	 */
	OptResult sacTestDriveReviewRecordSave(SacTestDriveReviewRecordIn dataInfo, String token);
}
