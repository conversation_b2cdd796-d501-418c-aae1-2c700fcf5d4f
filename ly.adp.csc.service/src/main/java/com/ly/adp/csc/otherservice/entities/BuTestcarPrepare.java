package com.ly.adp.csc.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 试驾车整备表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@TableName("t_usc_bu_testcar_prepare")
public class BuTestcarPrepare implements Serializable {

	private String carStatusCode;
	private String carStatusName;

	public String getCarStatusCode() {
		return carStatusCode;
	}

	public void setCarStatusCode(String carStatusCode) {
		this.carStatusCode = carStatusCode;
	}

	public String getCarStatusName() {
		return carStatusName;
	}

	public void setCarStatusName(String carStatusName) {
		this.carStatusName = carStatusName;
	}

	private Integer testcarFrequency;
	private Integer testcarKilometers;

	public Integer getTestcarFrequency() {
		return testcarFrequency;
	}

	public void setTestcarFrequency(Integer testcarFrequency) {
		this.testcarFrequency = testcarFrequency;
	}

	public Integer getTestcarKilometers() {
		return testcarKilometers;
	}

	public void setTestcarKilometers(Integer testcarKilometers) {
		this.testcarKilometers = testcarKilometers;
	}

	private String retireRemark;

	public String getRetireRemark() {
		return retireRemark;
	}

	public void setRetireRemark(String retireRemark) {
		this.retireRemark = retireRemark;
	}

	private String conRetireId;
	private String retireId;

	public String getRetireId() {
		return retireId;
	}

	public void setRetireId(String retireId) {
		this.retireId = retireId;
	}

	public String getConRetireId() {
		return conRetireId;
	}

	public void setConRetireId(String conRetireId) {
		this.conRetireId = conRetireId;
	}

	private BuTestcarRetire retire;

	public BuTestcarRetire getRetire() {
		return retire;
	}

	public void setRetire(BuTestcarRetire retire) {
		this.retire = retire;
	}

	private String applyTestcarTypeName;

	public String getApplyTestcarTypeName() {
		return applyTestcarTypeName;
	}

	public void setApplyTestcarTypeName(String applyTestcarTypeName) {
		this.applyTestcarTypeName = applyTestcarTypeName;
	}

	private String agentCode;

	private String agentCompanyCode;

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentCompanyCode() {
		return agentCompanyCode;
	}

	public void setAgentCompanyCode(String agentCompanyCode) {
		this.agentCompanyCode = agentCompanyCode;
	}

	private String dlrType;

	public String getDlrType() {
		return dlrType;
	}

	public void setDlrType(String dlrType) {
		this.dlrType = dlrType;
	}

	private String licensedCity;

	public String getLicensedCity() {
		return licensedCity;
	}

	public void setLicensedCity(String licensedCity) {
		this.licensedCity = licensedCity;
	}

	private String dlrShortName;
	private String agentCompanyName;
	private String agentName;

	public String getDlrShortName() {
		return dlrShortName;
	}

	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}

	public String getAgentCompanyName() {
		return agentCompanyName;
	}

	public void setAgentCompanyName(String agentCompanyName) {
		this.agentCompanyName = agentCompanyName;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	private String testCarExpire;

	public String getTestCarExpire() {
		return testCarExpire;
	}

	public void setTestCarExpire(String testCarExpire) {
		this.testCarExpire = testCarExpire;
	}

	private String applyQulity;

	public String getApplyQulity() {
		return applyQulity;
	}

	public void setApplyQulity(String applyQulity) {
		this.applyQulity = applyQulity;
	}

	private String applyCustTypename;

	public String getApplyCustTypename() {
		return applyCustTypename;
	}

	public void setApplyCustTypename(String applyCustTypename) {
		this.applyCustTypename = applyCustTypename;
	}

	private String testdrivCarsetId;

	public String getTestdrivCarsetId() {
		return testdrivCarsetId;
	}

	public void setTestdrivCarsetId(String testdrivCarsetId) {
		this.testdrivCarsetId = testdrivCarsetId;
	}

	private String applyCarUsetypeName;

	public String getApplyCarUsetypeName() {
		return applyCarUsetypeName;
	}

	public void setApplyCarUsetypeName(String applyCarUsetypeName) {
		this.applyCarUsetypeName = applyCarUsetypeName;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	private LocalDateTime testCarOnline;

	public LocalDateTime getTestCarOnline() {
		return testCarOnline;
	}

	public void setTestCarOnline(LocalDateTime testCarOnline) {
		this.testCarOnline = testCarOnline;
	}

	private String autherLastDate;
	private String replaceVin;
	private String orgId;
	private String status;
	private String vin;

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getReplaceVin() {
		return replaceVin;
	}

	public void setReplaceVin(String replaceVin) {
		this.replaceVin = replaceVin;
	}

	public String getAutherLastDate() {
		return autherLastDate;
	}

	public void setAutherLastDate(String autherLastDate) {
		this.autherLastDate = autherLastDate;
	}

	private static final long serialVersionUID = 1L;
	private List<BuTestcarPreappend> buTestcarPreappends;

	private String testCarAppyCode;

	public String getTestCarAppyCode() {
		return testCarAppyCode;
	}

	public void setTestCarAppyCode(String testCarAppyCode) {
		this.testCarAppyCode = testCarAppyCode;
	}

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	private LocalDateTime carLicenceLastDate;

	public LocalDateTime getCarLicenceLastDate() {
		return carLicenceLastDate;
	}

	public void setCarLicenceLastDate(LocalDateTime carLicenceLastDate) {
		this.carLicenceLastDate = carLicenceLastDate;
	}

	private String smallCarTypeCn;
	private String carConfigCn;
	private String carColorName;
	private String carIncolorName;

	public String getSmallCarTypeCn() {
		return smallCarTypeCn;
	}

	public void setSmallCarTypeCn(String smallCarTypeCn) {
		this.smallCarTypeCn = smallCarTypeCn;
	}

	public String getCarConfigCn() {
		return carConfigCn;
	}

	public void setCarConfigCn(String carConfigCn) {
		this.carConfigCn = carConfigCn;
	}

	public String getCarColorName() {
		return carColorName;
	}

	public void setCarColorName(String carColorName) {
		this.carColorName = carColorName;
	}

	public String getCarIncolorName() {
		return carIncolorName;
	}

	public void setCarIncolorName(String carIncolorName) {
		this.carIncolorName = carIncolorName;
	}

	/**
	 * 申请类型
	 */
	@TableField("APPLY_TESTCAR_TYPE")
	private String applyTestcarType;

	/**
	 * 车型
	 */
	@TableField("APPLY_CAR_TYPE_CODE")
	private String applyCarTypeCode;

	/**
	 * 车型配置
	 */
	@TableField("APPLY_CARTYPE_CONFIG")
	private String applyCartypeConfig;

	/**
	 * 颜色
	 */
	@TableField("APPLY_CAR_COLOR_CODE")
	private String applyCarColorCode;

	/**
	 * 内饰
	 */
	@TableField("APPLY_CAR_INCOLOR_CODE")
	private String applyCarIncolorCode;
	/**
	 * 车主
	 */
	@TableField("CAR_OWNER")
	private String carOwner;

	/**
	 * 联系电话
	 */
	@TableField("CAR_OWNER_PHONE")
	private String carOwnerPhone;

	/**
	 * 用途
	 */
	@TableField("APPLY_CAR_USETYPE")
	private String applyCarUsetype;

	/**
	 * 客户类型
	 */
	@TableField("APPLY_CUST_TYPE")
	private String applyCustType;

	/**
	 * 备注
	 */
	@TableField("APPLY_REMARK")
	private String applyRemark;

	/**
	 * 试驾整备ID
	 */
	@TableId("TESTCAR_PREPARE_ID")
	private String testcarPrepareId;

	/**
	 * 试驾车申请ID
	 */
	@TableField("TEST_CARAPPLY_ID")
	private String testCarapplyId;

	/**
	 * 零售单号
	 */
	@TableField("SALE_ORDER_CODE")
	private String saleOrderCode;

	/**
	 * 车牌号
	 */
	@TableField("CAR_LICENCE_NO")
	private String carLicenceNo;

	/**
	 * 上牌时间
	 */
	@TableField("CAR_LICENCE_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime carLicenceDate;

	/**
	 * 申请门店
	 */
	@TableField("APPLY_DLR_CODE")
	private String applyDlrCode;

	/**
	 * 审批状态
	 */
	@TableField("RESPONSE_ORDER_STATUS")
	private String responseOrderStatus;

	/**
	 * 审批人
	 */
	@TableField("AUTHER_MAN")
	private String autherMan;

	/**
	 * 审批日期
	 */
	@TableField("AUTHER_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime autherDate;

	/**
	 * 驳回原因
	 */
	@TableField("REJECT_RESON")
	private String rejectReson;

	/**
	 * 创建人
	 */
	@TableField("CREATOR")
	private String creator;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	private LocalDateTime createdDate;

	/**
	 * 最后更新人员
	 */
	@TableField("MODIFIER")
	private String modifier;

	/**
	 * 最后更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GTM+8")
	private LocalDateTime lastUpdatedDate;

	/**
	 * 是否可用
	 */
	@TableField("IS_ENABLE")
	private String isEnable;

	/**
	 * 并发控制字段
	 */
	@TableField("UPDATE_CONTROL_ID")
	private String updateControlId;

	public List<BuTestcarPreappend> getBuTestcarPreappends() {
		return buTestcarPreappends;
	}

	public void setBuTestcarPreappends(List<BuTestcarPreappend> buTestcarPreappends) {
		this.buTestcarPreappends = buTestcarPreappends;
	}

	public String getTestcarPrepareId() {
		return testcarPrepareId;
	}

	public void setTestcarPrepareId(String testcarPrepareId) {
		this.testcarPrepareId = testcarPrepareId;
	}

	public String getTestCarapplyId() {
		return testCarapplyId;
	}

	public void setTestCarapplyId(String testCarapplyId) {
		this.testCarapplyId = testCarapplyId;
	}

	public String getSaleOrderCode() {
		return saleOrderCode;
	}

	public void setSaleOrderCode(String saleOrderCode) {
		this.saleOrderCode = saleOrderCode;
	}

	public String getCarLicenceNo() {
		return carLicenceNo;
	}

	public void setCarLicenceNo(String carLicenceNo) {
		this.carLicenceNo = carLicenceNo;
	}

	public LocalDateTime getCarLicenceDate() {
		return carLicenceDate;
	}

	public void setCarLicenceDate(LocalDateTime carLicenceDate) {
		this.carLicenceDate = carLicenceDate;
	}

	public String getApplyDlrCode() {
		return applyDlrCode;
	}

	public void setApplyDlrCode(String applyDlrCode) {
		this.applyDlrCode = applyDlrCode;
	}

	public String getResponseOrderStatus() {
		return responseOrderStatus;
	}

	public void setResponseOrderStatus(String responseOrderStatus) {
		this.responseOrderStatus = responseOrderStatus;
	}

	public String getAutherMan() {
		return autherMan;
	}

	public void setAutherMan(String autherMan) {
		this.autherMan = autherMan;
	}

	public LocalDateTime getAutherDate() {
		return autherDate;
	}

	public void setAutherDate(LocalDateTime autherDate) {
		this.autherDate = autherDate;
	}

	public String getRejectReson() {
		return rejectReson;
	}

	public void setRejectReson(String rejectReson) {
		this.rejectReson = rejectReson;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	@JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	public String getApplyTestcarType() {
		return applyTestcarType;
	}

	public void setApplyTestcarType(String applyTestcarType) {
		this.applyTestcarType = applyTestcarType;
	}

	public String getApplyCarTypeCode() {
		return applyCarTypeCode;
	}

	public void setApplyCarTypeCode(String applyCarTypeCode) {
		this.applyCarTypeCode = applyCarTypeCode;
	}

	public String getApplyCartypeConfig() {
		return applyCartypeConfig;
	}

	public void setApplyCartypeConfig(String applyCartypeConfig) {
		this.applyCartypeConfig = applyCartypeConfig;
	}

	public String getApplyCarColorCode() {
		return applyCarColorCode;
	}

	public void setApplyCarColorCode(String applyCarColorCode) {
		this.applyCarColorCode = applyCarColorCode;
	}

	public String getApplyCarIncolorCode() {
		return applyCarIncolorCode;
	}

	public void setApplyCarIncolorCode(String applyCarIncolorCode) {
		this.applyCarIncolorCode = applyCarIncolorCode;
	}

	public String getCarOwner() {
		return carOwner;
	}

	public void setCarOwner(String carOwner) {
		this.carOwner = carOwner;
	}

	public String getCarOwnerPhone() {
		return carOwnerPhone;
	}

	public void setCarOwnerPhone(String carOwnerPhone) {
		this.carOwnerPhone = carOwnerPhone;
	}

	public String getApplyCarUsetype() {
		return applyCarUsetype;
	}

	public void setApplyCarUsetype(String applyCarUsetype) {
		this.applyCarUsetype = applyCarUsetype;
	}

	public String getApplyCustType() {
		return applyCustType;
	}

	public void setApplyCustType(String applyCustType) {
		this.applyCustType = applyCustType;
	}

	public String getApplyRemark() {
		return applyRemark;
	}

	public void setApplyRemark(String applyRemark) {
		this.applyRemark = applyRemark;
	}

	@Override
	public String toString() {
		return "BuTestcarPrepare [carStatusCode=" + carStatusCode + ", carStatusName=" + carStatusName
				+ ", testcarFrequency=" + testcarFrequency + ", testcarKilometers=" + testcarKilometers
				+ ", retireRemark=" + retireRemark + ", conRetireId=" + conRetireId + ", retireId=" + retireId
				+ ", retire=" + retire + ", applyTestcarTypeName=" + applyTestcarTypeName + ", agentCode=" + agentCode
				+ ", agentCompanyCode=" + agentCompanyCode + ", dlrType=" + dlrType + ", licensedCity=" + licensedCity
				+ ", dlrShortName=" + dlrShortName + ", agentCompanyName=" + agentCompanyName + ", agentName="
				+ agentName + ", testCarExpire=" + testCarExpire + ", applyQulity=" + applyQulity
				+ ", applyCustTypename=" + applyCustTypename + ", testdrivCarsetId=" + testdrivCarsetId
				+ ", applyCarUsetypeName=" + applyCarUsetypeName + ", testCarOnline=" + testCarOnline
				+ ", autherLastDate=" + autherLastDate + ", replaceVin=" + replaceVin + ", orgId=" + orgId + ", status="
				+ status + ", vin=" + vin + ", buTestcarPreappends=" + buTestcarPreappends + ", testCarAppyCode="
				+ testCarAppyCode + ", carLicenceLastDate=" + carLicenceLastDate + ", smallCarTypeCn=" + smallCarTypeCn
				+ ", carConfigCn=" + carConfigCn + ", carColorName=" + carColorName + ", carIncolorName="
				+ carIncolorName + ", applyTestcarType=" + applyTestcarType + ", applyCarTypeCode=" + applyCarTypeCode
				+ ", applyCartypeConfig=" + applyCartypeConfig + ", applyCarColorCode=" + applyCarColorCode
				+ ", applyCarIncolorCode=" + applyCarIncolorCode + ", carOwner=" + carOwner + ", carOwnerPhone="
				+ carOwnerPhone + ", applyCarUsetype=" + applyCarUsetype + ", applyCustType=" + applyCustType
				+ ", applyRemark=" + applyRemark + ", testcarPrepareId=" + testcarPrepareId + ", testCarapplyId="
				+ testCarapplyId + ", saleOrderCode=" + saleOrderCode + ", carLicenceNo=" + carLicenceNo
				+ ", carLicenceDate=" + carLicenceDate + ", applyDlrCode=" + applyDlrCode + ", responseOrderStatus="
				+ responseOrderStatus + ", autherMan=" + autherMan + ", autherDate=" + autherDate + ", rejectReson="
				+ rejectReson + ", creator=" + creator + ", createdDate=" + createdDate + ", modifier=" + modifier
				+ ", lastUpdatedDate=" + lastUpdatedDate + ", isEnable=" + isEnable + ", updateControlId="
				+ updateControlId + "]";
	}


}
