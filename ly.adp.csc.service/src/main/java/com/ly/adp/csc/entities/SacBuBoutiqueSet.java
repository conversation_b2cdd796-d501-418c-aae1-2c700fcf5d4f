package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 精品基本数据维护表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@TableName("t_sac_bu_boutique_set")
public class SacBuBoutiqueSet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("BOUTIQUE_ID")
    private String boutiqueId;

    /**
     * 精品SKU编码
     */
    @TableField("BOUTIQUE_CODE")
    private String boutiqueCode;

    /**
     * 精品名称
     */
    @TableField("BOUTIQUE_NAME")
    private String boutiqueName;

    /**
     * 门店类型
     */
    @TableField("DLR_TYPE")
    private String dlrType;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人名称
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 最后更新人员名称
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getBoutiqueId() {
        return boutiqueId;
    }

    public void setBoutiqueId(String boutiqueId) {
        this.boutiqueId = boutiqueId;
    }
    public String getBoutiqueCode() {
        return boutiqueCode;
    }

    public void setBoutiqueCode(String boutiqueCode) {
        this.boutiqueCode = boutiqueCode;
    }
    public String getBoutiqueName() {
        return boutiqueName;
    }

    public void setBoutiqueName(String boutiqueName) {
        this.boutiqueName = boutiqueName;
    }
    public String getDlrType() {
        return dlrType;
    }

    public void setDlrType(String dlrType) {
        this.dlrType = dlrType;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SacBuBoutiqueSet{" +
        "boutiqueId=" + boutiqueId +
        ", boutiqueCode=" + boutiqueCode +
        ", boutiqueName=" + boutiqueName +
        ", dlrType=" + dlrType +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
