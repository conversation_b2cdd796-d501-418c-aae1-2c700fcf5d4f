package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.dto.koc.BatchLabelingRequestDTO;
import com.ly.adp.csc.entities.dto.koc.UserLabelingRequestDTO;
import com.ly.adp.csc.entities.dto.koc.wechat.*;
import com.ly.adp.csc.entities.vo.koc.UserTagInfoVO;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * KOC打标服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface IKocLabelingService {

    /**
     * 单个用户打标
     * @param labelingRequest 打标请求
     * @param token 用户token
     * @return 打标结果
     */
    OptResult labelUser(UserLabelingRequestDTO labelingRequest, String token);

    /**
     * 批量用户打标
     * @param batchRequest 批量打标请求
     * @param token 用户token
     * @return 打标结果
     */
    OptResult batchLabelUsers(BatchLabelingRequestDTO batchRequest, String token);

    /**
     * 编辑用户标签
     * @param smartId 用户ID
     * @param tagIds 新的标签ID列表
     * @param token 用户token
     * @return 编辑结果
     */
    OptResult updateUserTags(String smartId, List<String> tagIds, String token);

    /**
     * 编辑用户达人类型
     * @param smartId 用户ID
     * @param expertTypeIds 新的达人类型ID列表
     * @param token 用户token
     * @return 编辑结果
     */
    OptResult updateUserExpertTypes(String smartId, List<String> expertTypeIds, String token);

    /**
     * 添加用户备注
     * @param smartId 用户ID
     * @param remarkContent 备注内容
     * @param token 用户token
     * @return 添加结果
     */
    EntityResult<Map<String, Object>> addUserRemark(String smartId, String remarkContent, String token);

    /**
     * 编辑用户备注
     * @param relId 关系ID
     * @param remarkContent 新的备注内容
     * @param token 用户token
     * @return 编辑结果
     */
    OptResult updateUserRemark(String relId, String remarkContent, String token);

    /**
     * 删除用户备注
     * @param relId 关系ID
     * @param token 用户token
     * @return 删除结果
     */
    OptResult deleteUserRemark(String relId, String token);

    /**
     * 移除用户标签
     * @param smartId 用户ID
     * @param tagIds 要移除的标签ID列表
     * @param token 用户token
     * @return 移除结果
     */
    OptResult removeUserTags(String smartId, List<String> tagIds, String token);

    /**
     * 移除用户达人类型
     * @param smartId 用户ID
     * @param expertTypeIds 要移除的达人类型ID列表
     * @param token 用户token
     * @return 移除结果
     */
    OptResult removeUserExpertTypes(String smartId, List<String> expertTypeIds, String token);

    /**
     * 查询用户打标记录
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 打标记录
     */
    ListResult<Map<String, Object>> queryLabelingRecords(Map<String, Object> dataInfo, String token);

    /**
     * 批量设置用户信息
     * @param dataInfo 批量设置数据
     * @param token 用户token
     * @return 设置结果
     */
    OptResult batchSetUserInfo(Map<String, Object> dataInfo, String token);

    // ========== 企微端专用方法 ==========

    /**
     * 企微端用户打标
     * @param labelingRequest 打标请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatLabelUser(UserLabelingReq labelingRequest, String token);

    /**
     * 企微端编辑用户标签
     * @param request 编辑请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatUpdateUserTags(UpdateUserTagsReq request, String token);

    /**
     * 企微端编辑用户达人类型
     * @param request 编辑请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatUpdateUserExpertTypes(WechatUpdateUserExpertTypesRequestDTO request, String token);

    /**
     * 企微端添加用户备注
     * @param request 备注请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatAddUserRemark(WechatUserRemarkRequestDTO request, String token);

    /**
     * 企微端编辑用户备注
     * @param request 备注请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatUpdateUserRemark(WechatUserRemarkRequestDTO request, String token);

    /**
     * 企微端删除用户备注
     * @param request 备注请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatDeleteUserRemark(WechatUserRemarkRequestDTO request, String token);

    /**
     * 企微端移除用户标签/达人类型
     * @param request 移除请求
     * @param token 用户token
     * @return 用户信息
     */
    UserTagInfoVO wechatRemoveUserLabels(WechatRemoveUserLabelsRequestDTO request, String token);
}
