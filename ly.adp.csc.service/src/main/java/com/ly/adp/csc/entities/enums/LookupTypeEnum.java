package com.ly.adp.csc.entities.enums;

/**
 * 值列表-值枚举
 * <AUTHOR>
 * @Version 1.0.0
 **/
public enum LookupTypeEnum {

    COMMON_CONFIG("COMMON_CONFIG", "常规配置项");

    private final String code;
    private final String description;

    LookupTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static LookupTypeEnum fromCode(String code) {
        for (LookupTypeEnum status : LookupTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid LookupTypeValueEnum code: " + code);
    }
}
