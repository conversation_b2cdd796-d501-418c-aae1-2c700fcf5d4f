package com.ly.adp.csc.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * 封装分页查询
 * 优化mybatisplus 复杂sql查询总条数的性能问题
 * andy.liu
 */
public class SmartPageQueryUtils {

    /*public static <T> IPage<T> executePageQuery(SmartPageQuery smartPageQuery, Page<T> page, Map<String, Object> param) {
//        Page<T> page = new Page<>(pageNumber, pageSize);
        page.setSearchCount(false);
        int totalCount = smartPageQuery.fetchCount(param);
        page.setTotal(totalCount);  
        return smartPageQuery.selectByPage(page, param);
    }*/
    public static <T> List<Map<String, Object>> executeListQuery(SmartPageQuery smartPageQuery, Map<String, Object> param, Page<T> page) {
        // 设置不自动查询总记录数  
        page.setSearchCount(false);
        // 使用自定义方法查询总记录数
        int totalCount = smartPageQuery.fetchCount(param);
        page.setTotal(totalCount);
        return smartPageQuery.queryAllActivityReport(param,page);
    }
}
