package com.ly.adp.csc.gateway;

import com.ly.adp.csc.entities.dto.uc.UcUserInfoDTO;

import java.util.List;

/**
 * UC用户中心网关接口
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface UcUserGateway {
    /**
     * 根据用户ID批量查询用户信息（UC接口）
     * @param smartIdList 用户ID列表
     * @return 用户信息列表
     */
    List<UcUserInfoDTO> getUsersBySmartIds(List<String> smartIdList);

    /**
     * 根据手机号批量查询用户信息（UC接口）
     * @param mobiles 手机号列表
     * @return 用户信息列表
     */
    List<UcUserInfoDTO> getUsersByMobiles(List<String> mobiles);
}
