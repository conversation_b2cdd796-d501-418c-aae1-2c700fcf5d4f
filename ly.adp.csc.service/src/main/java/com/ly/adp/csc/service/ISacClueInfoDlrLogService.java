package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacClueInfoDlrLog;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 店端线索来源日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
public interface ISacClueInfoDlrLogService extends IService<SacClueInfoDlrLog> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacClueInfoDlrLogFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacClueInfoDlrLogSaveInfo(Map<String, Object> dataInfo,String token);
}
