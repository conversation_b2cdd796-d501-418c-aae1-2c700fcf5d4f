package com.ly.adp.csc.otherservice;

import com.ly.adp.csc.entities.dto.uc.UcResponseDTO;
import com.ly.adp.csc.entities.dto.uc.UcUserInfoDTO;
import com.ly.adp.csc.otherservice.config.UcFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * UC用户中心Feign客户端
 * <AUTHOR>
 * @since 2025-07-28
 */
@FeignClient(
    name = "uc-user-service",
    url = "${refer.url.external.uc}",
    configuration = UcFeignConfig.class
)
public interface UcUserFeignClient {

    /**
     * 根据用户ID批量查询用户信息
     * @param smartIdList smartId列表
     * @return UC响应
     */
    @PostMapping("/tob/userBase/v1/by-smartIds")
    UcResponseDTO<UcUserInfoDTO> getUsersBySmartIds(@RequestBody List<String> smartIdList);

    /**
     * 根据手机号批量查询用户信息
     * @param mobiles 手机号列表
     * @return UC响应
     */
    @PostMapping("/tob/userBase/v1/by-mobiles")
    UcResponseDTO<UcUserInfoDTO> getUsersByMobiles(@RequestBody List<String> mobiles);
}
