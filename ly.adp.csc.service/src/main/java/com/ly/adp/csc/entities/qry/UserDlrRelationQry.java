package com.ly.adp.csc.entities.qry;

import com.ly.adp.common.entity.ParamPage;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/5/27
 * @Version 1.0.0
 **/
public class UserDlrRelationQry  extends ParamPage implements Serializable {

    @ApiModelProperty("员工id")
    private String empId;

    @ApiModelProperty("门店编码")
    private String dlrId;

    @ApiModelProperty("员工编码")
    private String empCode;

    @ApiModelProperty("员工名称")
    private String empName;

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getDlrId() {
        return dlrId;
    }

    public void setDlrId(String dlrId) {
        this.dlrId = dlrId;
    }

    public String getEmpCode() {
        return empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }
}
