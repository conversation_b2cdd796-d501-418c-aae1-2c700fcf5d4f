package com.ly.adp.csc.otherservice.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.adp.csc.entities.in.PageInfo;

import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 代理商信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@TableName("t_usc_mdm_agent_info")
public class MdmAgentInfo extends PageInfo implements Serializable {

    //营业执照
    @TableField("BUSINESS_LICENSE")
    private String businessLicense;

    //股东协议
    @TableField("SHAREHOLDER_RESOLUTION")
    private String shareholderResolution;

    private String shareholderAgreement;


    //任意股东
    private List<TUscMdmStockHolder> stockHolderList;

    //代理商协议信息
    private List<Map<String,Object>> agentAgreementList;

    private String agentAgreementId;


    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getShareholderAgreement() {
        return shareholderAgreement;
    }

    public void setShareholderAgreement(String shareholderAgreement) {
        this.shareholderAgreement = shareholderAgreement;
    }

    private List<MdmStockHolder> mdmStockHolders;

    public List<MdmStockHolder> getMdmStockHolders() {
        return mdmStockHolders;
    }

    public void setMdmStockHolders(List<MdmStockHolder> mdmStockHolders) {
        this.mdmStockHolders = mdmStockHolders;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 代理商ID
     */
    @TableId(value = "AGENT_ID", type = IdType.AUTO)
    private String agentId;

    /**
     * 代理商编码
     */
    @TableField("AGENT_CODE")
    private String agentCode;

    /**
     * 代理商名称
     */
    @TableField("AGENT_NAME")
    private String agentName;

    /**
     * 代理省份区域
     */
    @TableField("AGENT_AREA")
    private String agentArea;

    /**
     * 代理开始日期
     */
    @TableField("AGENT_BEGIN_DATE")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime agentBeginDate;

    /**
     * 代理结束日期
     */
    @TableField("AGENT_END_DATE")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime agentEndDate;

    /**
     * 框架合作协议地址
     */
    @TableField("COPRATION_URL")
    private String coprationUrl;

    /**
     * 组织机构代码
     */
    @TableField("GUNO")
    private String guno;

    /**
     * 法人
     */
    @TableField("LEGAL_PERSON")
    private String legalPerson;

    /**
     * 股东一
     */
    @TableField("STOCK_HOLDER")
    private String stockHolder;

    /**
     * 股东一持股比例
     */
    @TableField("STOCK_HOLDER_RATE")
    private BigDecimal stockHolderRate;

    /**
     * 股东二
     */
    @TableField("STOCK_HOLDER2")
    private String stockHolder2;

    /**
     * 股东二持股比例
     */
    @TableField("STOCK_HOLDER2_RATE")
    private BigDecimal stockHolder2Rate;

    /**
     * 股东三
     */
    @TableField("STOCK_HOLDER3")
    private String stockHolder3;

    /**
     * 股东三持股比例
     */
    @TableField("STOCK_HOLDER3_RATE")
    private BigDecimal stockHolder3Rate;

    /**
     * 股东四
     */
    @TableField("STOCK_HOLDER4")
    private String stockHolder4;

    /**
     * 股东四持股比例
     */
    @TableField("STOCK_HOLDER4_RATE")
    private BigDecimal stockHolder4Rate;

    /**
     * 股东五
     */
    @TableField("STOCK_HOLDER5")
    private String stockHolder5;

    /**
     * 股东五持股比例
     */
    @TableField("STOCK_HOLDER5_RATE")
    private BigDecimal stockHolder5Rate;

    /**
     * 股东六
     */
    @TableField("STOCK_HOLDER6")
    private String stockHolder6;

    /**
     * 股东六持股比例
     */
    @TableField("STOCK_HOLDER6_RATE")
    private BigDecimal stockHolder6Rate;

    /**
     * 股东七
     */
    @TableField("STOCK_HOLDER7")
    private String stockHolder7;

    /**
     * 股东七持股比例
     */
    @TableField("STOCK_HOLDER7_RATE")
    private BigDecimal stockHolder7Rate;

    /**
     * 股东八
     */
    @TableField("STOCK_HOLDER8")
    private String stockHolder8;

    /**
     * 股东八持股比例
     */
    @TableField("STOCK_HOLDER8_RATE")
    private BigDecimal stockHolder8Rate;

    /**
     * 股东九
     */
    @TableField("STOCK_HOLDER9")
    private String stockHolder9;

    /**
     * 股东九持股比例
     */
    @TableField("STOCK_HOLDER9_RATE")
    private BigDecimal stockHolder9Rate;

    /**
     * 股东十
     */
    @TableField("STOCK_HOLDER10")
    private String stockHolder10;

    /**
     * 股东十持股比例
     */
    @TableField("STOCK_HOLDER10_RATE")
    private BigDecimal stockHolder10Rate;

    /**
     * 联系人
     */
    @TableField("AGENT_CONTACT_MAN")
    private String agentContactMan;

    /**
     * 联系人电话
     */
    @TableField("AGENT_CONTACT_PHONE")
    private String agentContactPhone;

    /**
     * 联系人邮箱
     */
    @TableField("AGENT_CONTACT_EMAIL")
    private String agentContactEmail;

    /**
     * 公司名称
     */
    @TableField("AGENT_COMPANY_NAME")
    private String agentCompanyName;

    /**
     * 邮件地址
     */
    @TableField("AGENT_MAIL_ADDR")
    private String agentMailAddr;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    @TableField("AGENT_AREA_NAME")
    private String agentAreaName;

    @TableField("ATTACH_URL")
    private String attachUrl;

    @ApiModelProperty(value = "状态名称", required = false, example = "")
    private String enableName;

    @ApiModelProperty(value = "省份ID", required = false, example = "")
    private String provinceId;

    @ApiModelProperty(value = "省份名称", required = false, example = "")
    private String provinceName;

    @ApiModelProperty(value = "城市ID", required = false, example = "")
    private String cityId;

    @ApiModelProperty(value = "城市名称", required = false, example = "")
    private String cityName;

    @ApiModelProperty(value = "区县ID", required = false, example = "")
    private String communityId;

    @ApiModelProperty(value = "区县名称", required = false, example = "")
    private String communityName;
    @ApiModelProperty(value = "用户名称", required = false, example = "")
    private String userName;
    @ApiModelProperty(value = "员工名称", required = false, example = "")
    private String empName;

    public List<TUscMdmStockHolder> getStockHolderList() {
        return stockHolderList;
    }

    public void setStockHolderList(List<TUscMdmStockHolder> stockHolderList) {
        this.stockHolderList = stockHolderList;
    }

    public String getShareholderResolution() {
        return shareholderResolution;
    }

    public void setShareholderResolution(String shareholderResolution) {
        this.shareholderResolution = shareholderResolution;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentArea() {
        return agentArea;
    }

    public void setAgentArea(String agentArea) {
        this.agentArea = agentArea;
    }

    public LocalDateTime getAgentBeginDate() {
        return agentBeginDate;
    }

    public void setAgentBeginDate(LocalDateTime agentBeginDate) {
        this.agentBeginDate = agentBeginDate;
    }

    public LocalDateTime getAgentEndDate() {
        return agentEndDate;
    }

    public void setAgentEndDate(LocalDateTime agentEndDate) {
        this.agentEndDate = agentEndDate;
    }

    public String getCoprationUrl() {
        return coprationUrl;
    }

    public void setCoprationUrl(String coprationUrl) {
        this.coprationUrl = coprationUrl;
    }

    public String getGuno() {
        return guno;
    }

    public void setGuno(String guno) {
        this.guno = guno;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getStockHolder() {
        return stockHolder;
    }

    public void setStockHolder(String stockHolder) {
        this.stockHolder = stockHolder;
    }

    public BigDecimal getStockHolderRate() {
        return stockHolderRate;
    }

    public void setStockHolderRate(BigDecimal stockHolderRate) {
        this.stockHolderRate = stockHolderRate;
    }

    public String getStockHolder2() {
        return stockHolder2;
    }

    public void setStockHolder2(String stockHolder2) {
        this.stockHolder2 = stockHolder2;
    }

    public BigDecimal getStockHolder2Rate() {
        return stockHolder2Rate;
    }

    public void setStockHolder2Rate(BigDecimal stockHolder2Rate) {
        this.stockHolder2Rate = stockHolder2Rate;
    }

    public String getStockHolder3() {
        return stockHolder3;
    }

    public void setStockHolder3(String stockHolder3) {
        this.stockHolder3 = stockHolder3;
    }

    public BigDecimal getStockHolder3Rate() {
        return stockHolder3Rate;
    }

    public void setStockHolder3Rate(BigDecimal stockHolder3Rate) {
        this.stockHolder3Rate = stockHolder3Rate;
    }

    public String getStockHolder4() {
        return stockHolder4;
    }

    public void setStockHolder4(String stockHolder4) {
        this.stockHolder4 = stockHolder4;
    }

    public BigDecimal getStockHolder4Rate() {
        return stockHolder4Rate;
    }

    public void setStockHolder4Rate(BigDecimal stockHolder4Rate) {
        this.stockHolder4Rate = stockHolder4Rate;
    }

    public String getStockHolder5() {
        return stockHolder5;
    }

    public void setStockHolder5(String stockHolder5) {
        this.stockHolder5 = stockHolder5;
    }

    public BigDecimal getStockHolder5Rate() {
        return stockHolder5Rate;
    }

    public void setStockHolder5Rate(BigDecimal stockHolder5Rate) {
        this.stockHolder5Rate = stockHolder5Rate;
    }

    public String getStockHolder6() {
        return stockHolder6;
    }

    public void setStockHolder6(String stockHolder6) {
        this.stockHolder6 = stockHolder6;
    }

    public BigDecimal getStockHolder6Rate() {
        return stockHolder6Rate;
    }

    public void setStockHolder6Rate(BigDecimal stockHolder6Rate) {
        this.stockHolder6Rate = stockHolder6Rate;
    }

    public String getStockHolder7() {
        return stockHolder7;
    }

    public void setStockHolder7(String stockHolder7) {
        this.stockHolder7 = stockHolder7;
    }

    public BigDecimal getStockHolder7Rate() {
        return stockHolder7Rate;
    }

    public void setStockHolder7Rate(BigDecimal stockHolder7Rate) {
        this.stockHolder7Rate = stockHolder7Rate;
    }

    public String getStockHolder8() {
        return stockHolder8;
    }

    public void setStockHolder8(String stockHolder8) {
        this.stockHolder8 = stockHolder8;
    }

    public BigDecimal getStockHolder8Rate() {
        return stockHolder8Rate;
    }

    public void setStockHolder8Rate(BigDecimal stockHolder8Rate) {
        this.stockHolder8Rate = stockHolder8Rate;
    }

    public String getStockHolder9() {
        return stockHolder9;
    }

    public void setStockHolder9(String stockHolder9) {
        this.stockHolder9 = stockHolder9;
    }

    public BigDecimal getStockHolder9Rate() {
        return stockHolder9Rate;
    }

    public void setStockHolder9Rate(BigDecimal stockHolder9Rate) {
        this.stockHolder9Rate = stockHolder9Rate;
    }

    public String getStockHolder10() {
        return stockHolder10;
    }

    public void setStockHolder10(String stockHolder10) {
        this.stockHolder10 = stockHolder10;
    }

    public BigDecimal getStockHolder10Rate() {
        return stockHolder10Rate;
    }

    public void setStockHolder10Rate(BigDecimal stockHolder10Rate) {
        this.stockHolder10Rate = stockHolder10Rate;
    }

    public String getAgentContactMan() {
        return agentContactMan;
    }

    public void setAgentContactMan(String agentContactMan) {
        this.agentContactMan = agentContactMan;
    }

    public String getAgentContactPhone() {
        return agentContactPhone;
    }

    public void setAgentContactPhone(String agentContactPhone) {
        this.agentContactPhone = agentContactPhone;
    }

    public String getAgentContactEmail() {
        return agentContactEmail;
    }

    public void setAgentContactEmail(String agentContactEmail) {
        this.agentContactEmail = agentContactEmail;
    }

    public String getAgentCompanyName() {
        return agentCompanyName;
    }

    public void setAgentCompanyName(String agentCompanyName) {
        this.agentCompanyName = agentCompanyName;
    }

    public String getAgentMailAddr() {
        return agentMailAddr;
    }

    public void setAgentMailAddr(String agentMailAddr) {
        this.agentMailAddr = agentMailAddr;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getAgentAreaName() {
        return agentAreaName;
    }

    public void setAgentAreaName(String agentAreaName) {
        this.agentAreaName = agentAreaName;
    }

    public String getEnableName() {
        return enableName;
    }

    public void setEnableName(String enableName) {
        this.enableName = enableName;
    }


    public String getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(String provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getAttachUrl() {
        return attachUrl;
    }

    public void setAttachUrl(String attachUrl) {
        this.attachUrl = attachUrl;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    @Override
    public String toString() {
        return "MdmAgentInfo{" +
                "agentId=" + agentId +
                ", agentCode=" + agentCode +
                ", agentName=" + agentName +
                ", agentArea=" + agentArea +
                ", agentBeginDate=" + agentBeginDate +
                ", agentEndDate=" + agentEndDate +
                ", coprationUrl=" + coprationUrl +
                ", guno=" + guno +
                ", legalPerson=" + legalPerson +
                ", stockHolder=" + stockHolder +
                ", stockHolderRate=" + stockHolderRate +
                ", stockHolder2=" + stockHolder2 +
                ", stockHolder2Rate=" + stockHolder2Rate +
                ", stockHolder3=" + stockHolder3 +
                ", stockHolder3Rate=" + stockHolder3Rate +
                ", stockHolder4=" + stockHolder4 +
                ", stockHolder4Rate=" + stockHolder4Rate +
                ", stockHolder5=" + stockHolder5 +
                ", stockHolder5Rate=" + stockHolder5Rate +
                ", agentContactMan=" + agentContactMan +
                ", agentContactPhone=" + agentContactPhone +
                ", agentContactEmail=" + agentContactEmail +
                ", agentCompanyName=" + agentCompanyName +
                ", agentMailAddr=" + agentMailAddr +
                ", creator=" + creator +
                ", createdDate=" + createdDate +
                ", modifier=" + modifier +
                ", lastUpdatedDate=" + lastUpdatedDate +
                ", isEnable=" + isEnable +
                ", updateControlId=" + updateControlId +
                "}";
    }

    public List<Map<String, Object>> getAgentAgreementList() {
        return agentAgreementList;
    }

    public void setAgentAgreementList(List<Map<String, Object>> agentAgreementList) {
        this.agentAgreementList = agentAgreementList;
    }

    public String getAgentAgreementId() {
        return agentAgreementId;
    }

    public void setAgentAgreementId(String agentAgreementId) {
        this.agentAgreementId = agentAgreementId;
    }
}
