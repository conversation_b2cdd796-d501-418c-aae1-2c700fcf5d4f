package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 论坛评论表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@TableName("t_sac_bu_bbc_comment")
public class BbcComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @TableId("COMMENT_ID")
    private String commentId;

    /**
     * 文章id
     */
    @TableField("ARTICLE_ID")
    private String articleId;

    /**
     * 父节点评论id
     */
    @TableField("COMMENT_PARENTER_ID")
    private String commentParenterId;

    /**
     * 评论类型 1 楼主 2 官方评论
     */
    @TableField("COMMENT_TYPE")
    private String commentType;

    /**
     * 评论类型名称
     */
    @TableField("COMMENT_TYPE_NAME")
    private String commentTypeName;

    /**
     * 评论内容
     */
    @TableField("COMMENT_CONTENT")
    private String commentContent;

    /**
     * 审核状态 1：待审核 2 通过 3 驳回
     */
    @TableField("COMMENT_STATUS")
    private String commentStatus;

    /**
     * 审核人员用户ID
     */
    @TableField("SH_PERSON_ID")
    private String shPersonId;

    /**
     * 审核人员名称
     */
    @TableField("SH_PERSON_NAME")
    private String shPersonName;

    /**
     * 审核时间
     */
    @TableField("SH_TIME")
    private LocalDateTime shTime;

    /**
     * 是否匿名
     */
    @TableField("IS_ANONYMOUS")
    private String isAnonymous;

    /**
     * 驳回原因
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人名称
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新人员名称
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getArticleId() {
        return articleId;
    }

    public void setArticleId(String articleId) {
        this.articleId = articleId;
    }

    public String getCommentParenterId() {
        return commentParenterId;
    }

    public void setCommentParenterId(String commentParenterId) {
        this.commentParenterId = commentParenterId;
    }

    public String getCommentType() {
        return commentType;
    }

    public void setCommentType(String commentType) {
        this.commentType = commentType;
    }

    public String getCommentTypeName() {
        return commentTypeName;
    }

    public void setCommentTypeName(String commentTypeName) {
        this.commentTypeName = commentTypeName;
    }

    public String getCommentContent() {
        return commentContent;
    }

    public void setCommentContent(String commentContent) {
        this.commentContent = commentContent;
    }

    public String getCommentStatus() {
        return commentStatus;
    }

    public void setCommentStatus(String commentStatus) {
        this.commentStatus = commentStatus;
    }

    public String getShPersonId() {
        return shPersonId;
    }

    public void setShPersonId(String shPersonId) {
        this.shPersonId = shPersonId;
    }

    public String getShPersonName() {
        return shPersonName;
    }

    public void setShPersonName(String shPersonName) {
        this.shPersonName = shPersonName;
    }

    public LocalDateTime getShTime() {
        return shTime;
    }

    public void setShTime(LocalDateTime shTime) {
        this.shTime = shTime;
    }

    public String getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(String isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BbcComment{" +
        "commentId=" + commentId +
        ", articleId=" + articleId +
        ", commentParenterId=" + commentParenterId +
        ", commentType=" + commentType +
        ", commentTypeName=" + commentTypeName +
        ", commentContent=" + commentContent +
        ", commentStatus=" + commentStatus +
        ", shPersonId=" + shPersonId +
        ", shPersonName=" + shPersonName +
        ", shTime=" + shTime +
        ", isAnonymous=" + isAnonymous +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
