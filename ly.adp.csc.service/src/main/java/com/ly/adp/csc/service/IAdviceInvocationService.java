package com.ly.adp.csc.service;

import java.util.Map;

import com.ly.mp.component.entities.OptResult;

public interface IAdviceInvocationService {
	
	/**
	 * 发送通知-试乘试驾跟换门店 后置：csc_message_notice_clue
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	OptResult cscMessageNoticeClue(Map<String, Object> paramMap, String token);
	
	/**
	 * 发送通知-预约通知: 后置csc_message_notice_convention
	 * @param paramMap
	 * @param token
	 * @return
	 */
	@Deprecated
	OptResult cscMessageNoticeConvention(Map<String, Object> paramMap, String token);
	/**
	 * 发送通知-预约通知: 后置csc_message_notice_convention接口优化
	 * @param paramMap
	 * @param token
	 * @return
	 */
	OptResult cscMessageNoticeConvention_performance(Map<String, Object> paramMap, String token);
}
