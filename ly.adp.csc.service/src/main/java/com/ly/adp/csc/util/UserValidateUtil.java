package com.ly.adp.csc.util;

import com.ly.adp.csc.entities.enums.BmStationEnum;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 * @Version 1.0.0
 * 用户校验工具类
 **/
public class UserValidateUtil {

    /**
     * 系统管理员用户名
     */
    private static final String SYSTEM_ADMIN = "xtadmin";

    /**
     * 判断是否系统管理员
     * @param user 用户信息
     * @return true-是系统管理员，false-不是系统管理员
     */
    public static boolean isSystemAdmin(UserBusiEntity user) {
        return StringUtils.equals(SYSTEM_ADMIN, user.getEmpName());
    }

    /**
     * 判断是否产品专家
     * @param user 用户信息
     * @param isolateAdmin 是否隔离系统管理员权限
     * @return true-是产品专家，false-不是产品专家
     */
    public static boolean isProductExpert(UserBusiEntity user, boolean isolateAdmin) {
        if (!isolateAdmin && isSystemAdmin(user)) {
            return true;
        }
        String stationId = user.getStationId();
        return StringUtils.equals(stationId, BmStationEnum.PRODUCT_EXPERT_POS.getCode()) ||
                StringUtils.equals(stationId, BmStationEnum.PRODUCT_EXPERT_BC.getCode());
    }

    /**
     * 判断是否产品专家（默认不隔离系统管理员权限）
     * @param user 用户信息
     * @return true-是产品专家，false-不是产品专家
     */
    public static boolean isProductExpert(UserBusiEntity user) {
        return isProductExpert(user, false);
    }

    /**
     * 判断是否店长
     * @param user 用户信息
     * @param isolateAdmin 是否隔离系统管理员权限
     * @return true-是店长，false-不是店长
     */
    public static boolean isShopManager(UserBusiEntity user, boolean isolateAdmin) {
        if (!isolateAdmin && isSystemAdmin(user)) {
            return true;
        }
        String stationId = user.getStationId();
        return StringUtils.equals(stationId, BmStationEnum.STORE_MANAGER_POS.getCode()) ||
                StringUtils.equals(stationId, BmStationEnum.STORE_MANAGER_BC.getCode());
    }

    /**
     * 判断是否店长（默认不隔离系统管理员权限）
     * @param user 用户信息
     * @return true-是店长，false-不是店长
     */
    public static boolean isDefaultShopManager(UserBusiEntity user) {
        return isShopManager(user, false);
    }

    /**
     * 判断是否资深产品专家
     * @param user 用户信息
     * @param isolateAdmin 是否隔离系统管理员权限
     * @return true-是资深产品专家，false-不是资深产品专家
     */
    public static boolean isSeniorProductExpert(UserBusiEntity user, boolean isolateAdmin) {
        if (!isolateAdmin && isSystemAdmin(user)) {
            return true;
        }
        String stationId = user.getStationId();
        return StringUtils.equals(stationId, BmStationEnum.SENIOR_PRODUCT_EXPER_POS.getCode()) ||
                StringUtils.equals(stationId, BmStationEnum.SENIOR_PRODUCT_EXPER_BC.getCode());
    }

    /**
     * 判断是否资深产品专家（默认不隔离系统管理员权限）
     * @param user 用户信息
     * @return true-是资深产品专家，false-不是资深产品专家
     */
    public static boolean isSeniorProductExpert(UserBusiEntity user) {
        return isSeniorProductExpert(user, false);
    }

    /**
     * 判断是否外呼专员
     * @param user 用户信息
     * @param isolateAdmin 是否隔离系统管理员权限
     * @return true-是外呼专员，false-不是外呼专员
     */
    public static boolean isCallSpecialist(UserBusiEntity user, boolean isolateAdmin) {
        if (!isolateAdmin && isSystemAdmin(user)) {
            return true;
        }
        return StringUtils.equals(user.getStationId(),
                BmStationEnum.OUTBOUND_CALL_SPECIALIST_C.getCode());
    }

    /**
     * 判断是否外呼专员（默认不隔离系统管理员权限）
     * @param user 用户信息
     * @return true-是外呼专员，false-不是外呼专员
     */
    public static boolean isCallSpecialist(UserBusiEntity user) {
        return isCallSpecialist(user, false);
    }

    /**
     * 判断是否产品专家相关岗位（含资深产品专家）
     * @param user 用户信息
     * @param isolateAdmin 是否隔离系统管理员权限
     * @return true-是产品专家相关岗位，false-不是产品专家相关岗位
     */
    public static boolean isProductExpertRelated(UserBusiEntity user, boolean isolateAdmin) {
        if (!isolateAdmin && isSystemAdmin(user)) {
            return true;
        }
        return isProductExpert(user, true) || isSeniorProductExpert(user, true);
    }

    /**
     * 判断是否产品专家相关岗位（含资深产品专家，默认不隔离系统管理员权限）
     * @param user 用户信息
     * @return true-是产品专家相关岗位，false-不是产品专家相关岗位
     */
    public static boolean isProductExpertRelated(UserBusiEntity user) {
        return isProductExpertRelated(user, false);
    }
}
