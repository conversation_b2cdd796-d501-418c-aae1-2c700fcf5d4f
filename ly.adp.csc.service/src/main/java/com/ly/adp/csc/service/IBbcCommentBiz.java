package com.ly.adp.csc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.entities.BbcComment;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 论坛评论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
public interface IBbcCommentBiz extends IService<BbcComment> {
    ListResult<Map<String, Object>> queryBbcComment(String token, ParamPage<Map<String, Object>> paramPage);
    OptResult managerComment(Map<String, Object> dateInfoon, String token);
     OptResult delComment(Map<String, Object> dateInfoon, String token);
    EntityResult<Integer> querycount(Map<String, Object> dateInfoon, String token);

    ListResult<Map<String, Object>> queryCommentStatu(String token, ParamPage<Map<String, Object>> paramPage);
     ListResult<Map<String, Object>> queryBbcChildranComment(String token, ParamPage<Map<String, Object>> paramPage);
    OptResult batchComment(List<Map<String, Object>> dateInfoon, String token);

    ListResult<Map<String, Object>> findStation(Map<String, Object> dateInfo, String authentication);
}
