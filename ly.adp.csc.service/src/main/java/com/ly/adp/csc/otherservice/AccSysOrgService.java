package com.ly.adp.csc.otherservice;

import com.ly.adp.csc.otherservice.entities.MdmOrgDlr;
import com.ly.adp.csc.otherservice.entities.in.MdmOrgDlrIn;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.busi.base.handler.MapUtil;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.csc.clue.service.ISacClueInfoDlrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class AccSysOrgService implements com.ly.mp.acc.manage.otherservice.IAccSysOrgService {
	@Autowired IBasedataFeignClient basedataFeignClient;
	@Autowired
	ISacClueInfoDlrService sacClueInfoDlrService;

	@Override
	public Map<String, Object> mdmDlrInfoFindAll(String token, String dlrCode) {
		MdmOrgDlrIn info = new MdmOrgDlrIn();
		info.setPageIndex(1);
		info.setPageSize(1);
		info.setDlrCode(dlrCode);
		// info.put("dlrCode", dlrCode);
		try {
			ListResult<MdmOrgDlr> listResult = basedataFeignClient.mdmDlrInfoFindAll(token, info);
			if ("1".equals(listResult.getResult())) {
				List<MdmOrgDlr> rows = listResult.getRows();
				if (rows != null && !rows.isEmpty()) {
					return MapUtil.entityToMap(rows.get(0));
				}
			}
		} catch (Exception e) {
			return null;
		}
		return null;
	}

	// userId userName
	@Override
	public String generateToken(Map<String, Object> paramMap) {
		String token = "";
		try {
			OptResult result = basedataFeignClient.createMpTokenInfo(paramMap);
			if ("1".equals(result.getResult())) {
				token = result.getMsg();
			}
		} catch (Exception e) {
			throw BusicenException.create(e.getMessage());
		}
		return token;
	}

	@Override
	public EntityResult<Map<String, Object>> clueDlrSave(ParamBase<Map<String, Object>> queryCondition) {
		//EntityResult<Map<String, Object>> result = new EntityResult<>();
		return sacClueInfoDlrService.saveMap(queryCondition,queryCondition.getParam().get("token").toString());
		//try {
		//} catch (Exception e) {
		//	TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		//	result.setResult("0");
		//	result.setMsg(e.getMessage());
		//	return result;
		//}
	}
}
