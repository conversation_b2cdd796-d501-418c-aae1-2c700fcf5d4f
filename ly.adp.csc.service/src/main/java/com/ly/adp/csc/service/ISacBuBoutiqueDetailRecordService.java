package com.ly.adp.csc.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacBuBoutiqueDetailRecord;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 精品明细记录日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface ISacBuBoutiqueDetailRecordService extends IService<SacBuBoutiqueDetailRecord> {
	/**
	 * 分页查询
	 * 
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacBuBoutiqueDetailRecordFindInfo(Map<String, Object> dataInfo, String token);

	/**
	 * 根据主键判断插入或更新
	 * 
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordSaveInfo(Map<String, Object> dataInfo, String token);

	EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordsaveByList(List<Map<String, Object>> dateInfo, String authentication);
}
