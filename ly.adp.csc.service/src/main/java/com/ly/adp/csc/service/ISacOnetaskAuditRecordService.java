package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnetaskAuditRecord;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 任务审核表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-14
 */
public interface ISacOnetaskAuditRecordService extends IService<SacOnetaskAuditRecord> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskAuditRecordFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskAuditRecordSaveInfo(Map<String, Object> dataInfo,String token);
}
