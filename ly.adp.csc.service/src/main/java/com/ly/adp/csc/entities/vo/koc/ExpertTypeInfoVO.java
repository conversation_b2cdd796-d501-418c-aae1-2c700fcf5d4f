package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;

/**
 * 达人类型信息VO
 * <AUTHOR>
 * @since 2025-07-25
 */
public class ExpertTypeInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 达人类型ID
     */
    private String typeId;

    /**
     * 达人类型名称
     */
    private String typeName;

    /**
     * 达人类型内容
     */
    private String typeContent;

    /**
     * 用户数量（统计用）
     */
    private Integer userCount;

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeContent() {
        return typeContent;
    }

    public void setTypeContent(String typeContent) {
        this.typeContent = typeContent;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    @Override
    public String toString() {
        return "ExpertTypeInfoVO{" +
                "typeId='" + typeId + '\'' +
                ", typeName='" + typeName + '\'' +
                ", typeContent='" + typeContent + '\'' +
                ", userCount=" + userCount +
                '}';
    }
}
