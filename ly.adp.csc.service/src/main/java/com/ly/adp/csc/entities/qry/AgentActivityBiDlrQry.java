package com.ly.adp.csc.entities.qry;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/7
 * @Version 1.0.0
 **/
@ApiModel("品牌大使设置管辖门店范围-查询Qry")
public class AgentActivityBiDlrQry implements Serializable {

    private static final long serialVersionUID = -2005474908713866141L;

    @ApiModelProperty("人员名称")
    @NotNull
    private String empName;

    @ApiModelProperty("岗位id")
    @NotNull
    private String posId;

    public @NotNull String getEmpName() {
        return empName;
    }

    public void setEmpName(@NotNull String empName) {
        this.empName = empName;
    }

    public @NotNull String getPosId() {
        return posId;
    }

    public void setPosId(@NotNull String posId) {
        this.posId = posId;
    }
}
