package com.ly.adp.csc.entities.dto.uc;

import java.io.Serializable;

/**
 * UC用户信息DTO
 * <AUTHOR>
 * @since 2025-07-28
 */
public class UcUserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 生日时间
     */
    private String birthday;

    /**
     * 生日字符串
     */
    private String birthdayStr;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 注册时间
     */
    private Long registerTime;

    /**
     * 用户smartId
     */
    private String smartId;

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBirthdayStr() {
        return birthdayStr;
    }

    public void setBirthdayStr(String birthdayStr) {
        this.birthdayStr = birthdayStr;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Long getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Long registerTime) {
        this.registerTime = registerTime;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    @Override
    public String toString() {
        return "UcUserInfoDTO{" +
                "avatar='" + avatar + '\'' +
                ", birthday='" + birthday + '\'' +
                ", birthdayStr='" + birthdayStr + '\'' +
                ", mobile='" + mobile + '\'' +
                ", nickName='" + nickName + '\'' +
                ", registerTime=" + registerTime +
                ", smartId='" + smartId + '\'' +
                '}';
    }
}
