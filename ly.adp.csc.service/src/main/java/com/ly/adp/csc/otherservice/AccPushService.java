package com.ly.adp.csc.otherservice;

import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.mp.component.entities.OptResult;

@Service
public class AccPushService{
	@Autowired
    IAccPushFeignService aipModelIBiz;

	 static IAccPushFeignService aipModelIBizStaic;
    @PostConstruct
    public void init() {
        aipModelIBizStaic = aipModelIBiz;
    }

    public static Map sendCancleData(String ms, Map<String, Object> mapParam) {
    	Map result = aipModelIBizStaic.sendCancleData(ms, mapParam);
		return result;
    }

}
