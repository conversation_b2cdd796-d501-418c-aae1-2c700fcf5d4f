package com.ly.adp.csc.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ly.adp.csc.entities.enums.PositionEnum;
import com.ly.adp.csc.entities.qry.UserDlrRelationQry;
import com.ly.adp.csc.entities.vo.TUscMdmOrgEmployeeVO;
import com.ly.adp.csc.idal.mapper.BaseQueryMapper;
import com.ly.adp.csc.otherservice.IBasedataFeignClient;
import com.ly.mp.busi.base.context.BusicenException;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.component.entities.ListResult;
import io.seata.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 常用工具类
 * <AUTHOR>
 * @Date 2024/5/27
 * @Version 1.0.0
 **/
@Component
public class CommonUtil {
    //年月日格式校验正则
    private static final String DATE_PATTERN = "^(19|20)\\d\\d([- /.])(0[1-9]|1[012])\\2(0[1-9]|[12][0-9]|3[01])$";
    
    private Logger logger = LoggerFactory.getLogger(CommonUtil.class);

    /**
     * 品牌大使
     */
    public static final String BRAND_AMBASSADOR = "Brand Ambassador";

    @Resource
    private IBasedataFeignClient iBasedataFeignClient;

    @Resource
    private BaseQueryMapper baseQueryMapper;

    /**
     *
     * @param token
     */

    /**
     * 通过登录人获取门店权限设置列表。
     *
     * @return 门店的权限设置列表。
     */
    public TUscMdmOrgEmployeeVO getDlrPermissionSettings(String token){
        // 根据当前登录账号的 【门店权限设置】读取可见门店范围
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        // 数据隔离
        UserDlrRelationQry userDlrRelationQry = new UserDlrRelationQry();
        userDlrRelationQry.setEmpId(userBusiEntity.getEmpID());
        userDlrRelationQry.setPageIndex(-1);
        userDlrRelationQry.setPageSize(-1);
        logger.info("获取门店权限设置-入参:{}", JSON.toJSONString(userDlrRelationQry));
        ListResult<TUscMdmOrgEmployeeVO> queryResult = iBasedataFeignClient.queryuserdlrrelation1(token, userDlrRelationQry);
        logger.info("获取门店权限设置-返回结果:{}", JSON.toJSONString(queryResult));
        if (Objects.isNull(queryResult)) {
            // 查询失败直接中断流程返回
            logger.info("获取门店权限设置失败：{}", JSON.toJSONString(queryResult));
            throw new BusicenException("获取门店权限设置失败，请稍后重试");
        }
        if (CollectionUtils.isEmpty(queryResult.getRows())
                || Objects.isNull(queryResult.getRows().get(0))) {
            // 查询失败直接中断流程返回
            throw new BusicenException(String.format("当前登录人%s未设置门店权限设置", userBusiEntity.getEmpName()));
        }
        return queryResult.getRows().get(0);
    }

    /**
     * 两个按 ',' 分隔的字符串取交集
     * @param str1
     * @param str2
     * @return
     */
    public String getIntersectionAsString(String str1, String str2) {
        // 将字符串按逗号分隔成列表
        List<String> str1List = new ArrayList<>(Arrays.asList(str1.split(",")));
        List<String> str2List = new ArrayList<>(Arrays.asList(str2.split(",")));

        // 获取两个列表的交集
        str1List.retainAll(str2List);

        // 将交集列表转换为按逗号分隔的字符串
        return String.join(",", str1List);
    }

    /**
     * 检查给定的token是否有效。
     *
     * @param userBusiInfo 通过token获取的用户信息
     * @return 如果token失效报错提示
     */
    public void isTokenValid(UserBusiEntity userBusiInfo) {
        if (Objects.isNull(userBusiInfo)) {
            throw new BusicenException("当前token已失效，请重新登陆后操作");
        }
    }

    /**
     * 判断岗位是否为品牌大使
     * @param posId
     * @return
     */
    public boolean isBi(String posId) {
        return PositionEnum.SMART_SYS_0024.getCode().equals(posId);
    }

    /**
     * 查询已设置的门店权限设置信息
     * @param token
     * @return
     */
    public List<TUscMdmOrgEmployeeVO> getPermissionSettings(String token) {
        // 数据隔离
        UserDlrRelationQry userDlrRelationQry = new UserDlrRelationQry();
        userDlrRelationQry.setPageIndex(-1);
        userDlrRelationQry.setPageSize(-1);
        logger.info("获取门店权限设置-入参:{}", JSON.toJSONString(userDlrRelationQry));
        ListResult<TUscMdmOrgEmployeeVO> queryResult = iBasedataFeignClient.queryuserdlrrelation1(token, userDlrRelationQry);
        logger.info("获取门店权限设置-返回结果:{}", JSON.toJSONString(queryResult));
        if (Objects.isNull(queryResult)) {
            // 查询失败直接中断流程返回
            logger.info("获取门店权限设置失败：{}", JSON.toJSONString(queryResult));
            throw new BusicenException("获取门店权限设置失败，请稍后重试");
        }
        // 存已设置了门店设置信息的数据
        List<TUscMdmOrgEmployeeVO> newList = Lists.newArrayList();
        for (TUscMdmOrgEmployeeVO row : queryResult.getRows()) {
            String permissionSettingDlrId = row.getDlrId();
            if (StringUtils.isNotEmpty(permissionSettingDlrId)) {
                newList.add(row);
            }
        }
        return newList;
    }

    /**
     * 查询员工岗位id
     * @param empId
     */
    public String getPosId(String empId){
        Map<String, Object> mapParm = new HashMap<String, Object>();
        mapParm.put("empId", empId);
        logger.info("查询员工岗位信息-入参:{}", JSON.toJSONString(mapParm));
        List<Map<String, Object>> listEmpPosInfo = iBasedataFeignClient.getEmpStationInfo(mapParm).getRows();
        logger.info("查询员工岗位信息-返回结果:{}", JSON.toJSONString(listEmpPosInfo));
        return String.valueOf(CollectionUtils.isEmpty(listEmpPosInfo) ? "" : listEmpPosInfo.get(0).get("positionId"));
    }

    /**
     * 查询员工是否在职
     * @param empId
     */
    public boolean isEmployeeActive(String empId){
        String userStatus = SwitchDbInvoke.invokeTidb(() -> baseQueryMapper.queryEmployeeUserStatus(empId));
        return "1".equals(userStatus) || "在职".equals(userStatus);
    }

    /**
     * 获取线索首次逾期时间
     */
    public Integer getOverdueTime(){
        return SwitchDbInvoke.invokeTidb(() -> baseQueryMapper.getOverdueTime());
    }

    /**
     * 判断给定的时间是否在指定的时间段内（同一天内）。
     *
     * @param beginTimeStr 起始时间字符串，格式为 "HH:mm:ss"。
     * @param endTimeStr 结束时间字符串，格式为 "HH:mm:ss"。
     * @param format 时间格式字符串，用于解析时间。
     * @param time 要判断的时间字符串，格式为 "HH:mm:ss"。
     * @return 如果给定的时间在指定的时间段内，返回 true；否则返回 false。
     */
    public static boolean isCurrentTimeInPeriodDay(String beginTimeStr, String endTimeStr, String format, String time) {
        // 创建日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        // 解析起始和结束时间
        LocalTime startTime = LocalTime.parse(beginTimeStr, formatter);
        LocalTime endTime = LocalTime.parse(endTimeStr, formatter);
        LocalTime now = LocalTime.parse(time, formatter);

        // 判断当前时间是否在指定时间段内
        return (!now.isBefore(startTime) && now.isBefore(endTime) || now.equals(startTime) || now.equals(endTime));
    }

    /**
     * 判断给定的时间是否在两个指定的时间段内（跨天）。
     *
     * @param beginTimeStr 第一个时间段的起始时间字符串，格式为 "HH:mm:ss"。
     * @param endTimeStr 第一个时间段的结束时间字符串，格式为 "HH:mm:ss"。
     * @param format 时间格式字符串，用于解析时间。
     * @param time 要判断的时间字符串，格式为 "HH:mm:ss"。
     * @param s 第二个时间段的起始时间字符串，格式为 "HH:mm:ss"。
     * @param s1 第二个时间段的结束时间字符串，格式为 "HH:mm:ss"。
     * @return 如果给定的时间在任意一个指定的时间段内，返回 true；否则返回 false。
     */
    public static boolean isCurrentTimeInPeriod(String beginTimeStr, String endTimeStr, String format, String time, String s, String s1) {
        // 创建日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        // 解析起始和结束时间
        LocalTime startTime = LocalTime.parse(beginTimeStr, formatter);
        LocalTime endTime = LocalTime.parse(endTimeStr, formatter);
        LocalTime start = LocalTime.parse(s, formatter);
        LocalTime send = LocalTime.parse(s1, formatter);
        LocalTime now = LocalTime.parse(time, formatter);

        // 判断当前时间是否在指定时间段内
        return (!now.isBefore(startTime) && now.isBefore(endTime) || now.equals(startTime) || now.equals(endTime) ||
                (!now.isBefore(start) && now.isBefore(send) || now.equals(start) || now.equals(send)));
    }


    public static boolean isValidDateOnly(String dateString) {
        // 使用Pattern的matcher方法校验日期字符串是否符合预期格式
        return Pattern.matches(DATE_PATTERN, dateString);
    }

    /**
     * 从 dataMap 中安全地获取字符串值
     * @param map map对象
     * @param key 对应键值
     * @return
     */
    public static String safeGetString(Map<String, Object> map, String key) {
        return map != null && map.containsKey(key) ? String.valueOf(map.get(key)) : null;
    }

    /**
     * 值列表查询
     * 通过 typeCode 和 valueCode 获取 valueName
     * @param typeCode
     * @param valueCode
     * @return
     */
    public String queryLookupValueName(String typeCode, String valueCode) {
        return baseQueryMapper.queryLookupValueName(typeCode, valueCode);
    }
}
