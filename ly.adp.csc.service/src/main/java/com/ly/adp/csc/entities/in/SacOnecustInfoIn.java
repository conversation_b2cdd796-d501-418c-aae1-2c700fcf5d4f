package com.ly.adp.csc.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class SacOnecustInfoIn extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	//定义参与排序字段
	String columnArr="CREATED_DATE,LAST_UPDATED_DATE";
	
	@ApiModelProperty(value = "排序列", required = false, example = "")
	private String column;
	@ApiModelProperty(value = "排序方式", required = false, example = "")
	private String sorting;
	@ApiModelProperty(value = "配置ID", required = false, example = "")
	private String configId;
	@ApiModelProperty(value = "对象类型(表编码)", required = false, example = "")
	private String objectType;
	@ApiModelProperty(value = "变更字段编码", required = false, example = "")
	private String changeFiled;
	@ApiModelProperty(value = "变更日志ID", required = false, example = "")
  	private String changeLogId;
	@ApiModelProperty(value = "客户ID", required = false, example = "")
	private String custId;
	@ApiModelProperty(value = "smartID", required = false, example = "")
	private String smartId;
	@ApiModelProperty(value = "姓名", required = false, example = "")
	private String custName;
	@ApiModelProperty(value = "性别", required = false, example = "")
	private String genderCode;
	@ApiModelProperty(value = "昵称", required = false, example = "")
	private String nickName;
	@ApiModelProperty(value = "手机号", required = false, example = "")
	private String phone;
	@ApiModelProperty(value = "邮箱", required = false, example = "")
	private String email;
	@ApiModelProperty(value = "微信账号", required = false, example = "")
	private String wechat;
	@ApiModelProperty(value = "身份证", required = false, example = "")
	private String idCard;
	@ApiModelProperty(value = "行驶证", required = false, example = "")
	private String driverCard;
	@ApiModelProperty(value = "驾照", required = false, example = "")
	private String drivingLicense;
	@ApiModelProperty(value = "护照", required = false, example = "")
	private String passport;
	@ApiModelProperty(value = "现有车品牌（+车型）", required = false, example = "")
	private String existCarBrand;
	@ApiModelProperty(value = "意向车品牌（+车型）", required = false, example = "")
	private String wantCarBrand;
	@ApiModelProperty(value = "消费者生命周期", required = false, example = "")
	private String custLifecycleType;
	@ApiModelProperty(value = "用户活跃度", required = false, example = "")
	private String custActivityType;
	@ApiModelProperty(value = "积分分值", required = false, example = "")
	private String score;
	@ApiModelProperty(value = "成长值分值", required = false, example = "")
	private String growUpScore;
	@ApiModelProperty(value = "用户成分", required = false, example = "")
	private String custComponent;
	@ApiModelProperty(value = "车主角色", required = false, example = "")
	private String carOwnerType;
	@ApiModelProperty(value = "用户来源（最早接触smart）", required = false, example = "")
	private String custSource;
	@ApiModelProperty(value = "积分消耗", required = false, example = "")
	private String scoreCost;
	@ApiModelProperty(value = "勋章", required = false, example = "")
	private String medal;
	@ApiModelProperty(value = "家庭地址_省", required = false, example = "")
	private String homeProvinceName;
	@ApiModelProperty(value = "家庭地址_城市", required = false, example = "")
	private String homeCityName;
	@ApiModelProperty(value = "购车地址_省", required = false, example = "")
	private String buyCarProvinceName;
	@ApiModelProperty(value = "购车地址_城市", required = false, example = "")
	private String buyCarCityName;
	@ApiModelProperty(value = "兴趣爱好（画像类第一位）", required = false, example = "")
	private String userHobbies;
	@ApiModelProperty(value = "生日", required = false, example = "")
	private String birthday;
	@ApiModelProperty(value = "年龄", required = false, example = "")
	private String userAge;
	@ApiModelProperty(value = "学历", required = false, example = "")
	private String educationLevel;
	@ApiModelProperty(value = "家庭情况", required = false, example = "")
	private String familySituation;
	@ApiModelProperty(value = "职业", required = false, example = "")
	private String career;
	@ApiModelProperty(value = "工作职级", required = false, example = "")
	private String jobTitleLevel;
	@ApiModelProperty(value = "月收入（元）", required = false, example = "")
	private String monthlyIncome;
	@ApiModelProperty(value = "拥有机动车辆数量", required = false, example = "")
	private String vehicleHaveCount;
	@ApiModelProperty(value = "拥有房产数量", required = false, example = "")
	private String houseHaveCount;
	@ApiModelProperty(value = "房产情况", required = false, example = "")
	private String houseSituation;
	@ApiModelProperty(value = "进店次数", required = false, example = "")
	private String arrivedNum;
	@ApiModelProperty(value = "进店日期", required = false, example = "")
	private String arrivedDate;
	@ApiModelProperty(value = "试驾车型", required = false, example = "")
	private String driverCarType;
	@ApiModelProperty(value = "试驾反馈", required = false, example = "")
	private String driverFeedback;
	@ApiModelProperty(value = "试驾门店", required = false, example = "")
	private String driverDlrName;
	@ApiModelProperty(value = "参加活动", required = false, example = "")
	private String activityJoin;
	@ApiModelProperty(value = "活动反馈", required = false, example = "")
	private String activityFeedback;
	@ApiModelProperty(value = "销售大使", required = false, example = "")
	private String salesName;
	@ApiModelProperty(value = "最后一次车辆配置日期", required = false, example = "")
	private String endConfigDate;
	@ApiModelProperty(value = "试驾时间", required = false, example = "")
	private String driverTime;
	@ApiModelProperty(value = "下小订时间", required = false, example = "")
	private String smallOrderTime;
	@ApiModelProperty(value = "下大定时间", required = false, example = "")
	private String bigOrderTime;
	@ApiModelProperty(value = "购买意向 ", required = false, example = "")
	private String buyIntention;
	@ApiModelProperty(value = "购车预算", required = false, example = "")
	private String buyBudget;
	@ApiModelProperty(value = "增购 Y/N", required = false, example = "")
	private String isAddBuy;
	@ApiModelProperty(value = "购买用途", required = false, example = "")
	private String buyUse;
	@ApiModelProperty(value = "购车时间", required = false, example = "")
	private String buyDate;
	@ApiModelProperty(value = "购买次数", required = false, example = "")
	private String buyNum;
	@ApiModelProperty(value = "所购车总价", required = false, example = "")
	private String totalBuyAmount;
	@ApiModelProperty(value = "购车方式（全款/贷款）", required = false, example = "")
	private String buyWay;
	@ApiModelProperty(value = "车牌号码", required = false, example = "")
	private String carLicenseCode;
	@ApiModelProperty(value = "车辆VIN码", required = false, example = "")
	private String vin;
	@ApiModelProperty(value = "颜色", required = false, example = "")
	private String carColor;
	@ApiModelProperty(value = "购车门店", required = false, example = "")
	private String buyCarStore;
	@ApiModelProperty(value = "发票信息", required = false, example = "")
	private String receiptInfo;
	@ApiModelProperty(value = "车机交互行为", required = false, example = "")
	private String interactionWay;
	@ApiModelProperty(value = "行驶时间", required = false, example = "")
	private String driveTime;
	@ApiModelProperty(value = "行驶时长", required = false, example = "")
	private String drivingTime;
	@ApiModelProperty(value = "驾驶习惯", required = false, example = "")
	private String drivingHabit;
	@ApiModelProperty(value = "用车频度", required = false, example = "")
	private String carUseFrequency;
	@ApiModelProperty(value = "用车能耗", required = false, example = "")
	private String carUseEngeryCost;
	@ApiModelProperty(value = "金融产品内容", required = false, example = "")
	private String financeProductInfo;
	@ApiModelProperty(value = "金融机构", required = false, example = "")
	private String financeInstitution;
	@ApiModelProperty(value = "申请方式", required = false, example = "")
	private String applyWay;
	@ApiModelProperty(value = "操作时间", required = false, example = "")
	private String operationTime;
	@ApiModelProperty(value = "合同签署方式", required = false, example = "")
	private String signContractWay;
	@ApiModelProperty(value = "贷款", required = false, example = "")
	private String credit;
	@ApiModelProperty(value = "融资租赁", required = false, example = "")
	private String financeLease;
	@ApiModelProperty(value = "贷款期限", required = false, example = "")
	private String creditDeadline;
	@ApiModelProperty(value = "首付", required = false, example = "")
	private String firstPayRatio;
	@ApiModelProperty(value = "车险种类", required = false, example = "")
	private String insureType;
	@ApiModelProperty(value = "保险期限", required = false, example = "")
	private String insureDeadline;
	@ApiModelProperty(value = "保险公司", required = false, example = "")
	private String insureCompany;
	@ApiModelProperty(value = "保险金额", required = false, example = "")
	private String insureAmount;
	@ApiModelProperty(value = "售后内容", required = false, example = "")
	private String csiContent;
	@ApiModelProperty(value = "维修内容", required = false, example = "")
	private String repairContent;
	@ApiModelProperty(value = "购买/更换耗材类型", required = false, example = "")
	private String replaceType;
	@ApiModelProperty(value = "最近一次售后时间", required = false, example = "")
	private String lastCsiTime;
	@ApiModelProperty(value = "售后门店名称", required = false, example = "")
	private String storeDlrName;
	@ApiModelProperty(value = "客户单次维护花费", required = false, example = "")
	private String custSingleCost;
	@ApiModelProperty(value = "客户满意度", required = false, example = "")
	private String custSatisfaction;
	@ApiModelProperty(value = "客户名称（模糊）", required = false, example = "")
	private String custNamePaste;
	@ApiModelProperty(value = "手机号（模糊）", required = false, example = "")
	private String phonePaste;
	@ApiModelProperty(value = "手机号（模糊）", required = false, example = "")
	private String oneCustSource;
	
	public String getOneCustSource() {
		return oneCustSource;
	}
	public void setOneCustSource(String oneCustSource) {
		this.oneCustSource = oneCustSource;
	}
	public String getCustNamePaste() {
		return custNamePaste;
	}
	public void setCustNamePaste(String custNamePaste) {
		this.custNamePaste = custNamePaste;
	}
	public String getPhonePaste() {
		return phonePaste;
	}
	public void setPhonePaste(String phonePaste) {
		this.phonePaste = phonePaste;
	}
	public String getColumnArr() {
		return columnArr;
	}
	public void setColumnArr(String columnArr) {
		this.columnArr = columnArr;
	}
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	public String getSorting() {
		return sorting;
	}
	public void setSorting(String sorting) {
		this.sorting = sorting;
	}
	public String getConfigId() {
		return configId;
	}
	public void setConfigId(String configId) {
		this.configId = configId;
	}
	public String getObjectType() {
		return objectType;
	}
	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}
	public String getChangeFiled() {
		return changeFiled;
	}
	public void setChangeFiled(String changeFiled) {
		this.changeFiled = changeFiled;
	}
	public String getChangeLogId() {
		return changeLogId;
	}
	public void setChangeLogId(String changeLogId) {
		this.changeLogId = changeLogId;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getSmartId() {
		return smartId;
	}
	public void setSmartId(String smartId) {
		this.smartId = smartId;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getGenderCode() {
		return genderCode;
	}
	public void setGenderCode(String genderCode) {
		this.genderCode = genderCode;
	}
	public String getNickName() {
		return nickName;
	}
	public void setNickName(String nickName) {
		this.nickName = nickName;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getWechat() {
		return wechat;
	}
	public void setWechat(String wechat) {
		this.wechat = wechat;
	}
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public String getDriverCard() {
		return driverCard;
	}
	public void setDriverCard(String driverCard) {
		this.driverCard = driverCard;
	}
	public String getDrivingLicense() {
		return drivingLicense;
	}
	public void setDrivingLicense(String drivingLicense) {
		this.drivingLicense = drivingLicense;
	}
	public String getPassport() {
		return passport;
	}
	public void setPassport(String passport) {
		this.passport = passport;
	}
	public String getExistCarBrand() {
		return existCarBrand;
	}
	public void setExistCarBrand(String existCarBrand) {
		this.existCarBrand = existCarBrand;
	}
	public String getWantCarBrand() {
		return wantCarBrand;
	}
	public void setWantCarBrand(String wantCarBrand) {
		this.wantCarBrand = wantCarBrand;
	}
	public String getCustLifecycleType() {
		return custLifecycleType;
	}
	public void setCustLifecycleType(String custLifecycleType) {
		this.custLifecycleType = custLifecycleType;
	}
	public String getCustActivityType() {
		return custActivityType;
	}
	public void setCustActivityType(String custActivityType) {
		this.custActivityType = custActivityType;
	}
	public String getScore() {
		return score;
	}
	public void setScore(String score) {
		this.score = score;
	}
	public String getGrowUpScore() {
		return growUpScore;
	}
	public void setGrowUpScore(String growUpScore) {
		this.growUpScore = growUpScore;
	}
	public String getCustComponent() {
		return custComponent;
	}
	public void setCustComponent(String custComponent) {
		this.custComponent = custComponent;
	}
	public String getCarOwnerType() {
		return carOwnerType;
	}
	public void setCarOwnerType(String carOwnerType) {
		this.carOwnerType = carOwnerType;
	}
	public String getCustSource() {
		return custSource;
	}
	public void setCustSource(String custSource) {
		this.custSource = custSource;
	}
	public String getScoreCost() {
		return scoreCost;
	}
	public void setScoreCost(String scoreCost) {
		this.scoreCost = scoreCost;
	}
	public String getMedal() {
		return medal;
	}
	public void setMedal(String medal) {
		this.medal = medal;
	}
	public String getHomeProvinceName() {
		return homeProvinceName;
	}
	public void setHomeProvinceName(String homeProvinceName) {
		this.homeProvinceName = homeProvinceName;
	}
	public String getHomeCityName() {
		return homeCityName;
	}
	public void setHomeCityName(String homeCityName) {
		this.homeCityName = homeCityName;
	}
	public String getBuyCarProvinceName() {
		return buyCarProvinceName;
	}
	public void setBuyCarProvinceName(String buyCarProvinceName) {
		this.buyCarProvinceName = buyCarProvinceName;
	}
	public String getBuyCarCityName() {
		return buyCarCityName;
	}
	public void setBuyCarCityName(String buyCarCityName) {
		this.buyCarCityName = buyCarCityName;
	}
	public String getUserHobbies() {
		return userHobbies;
	}
	public void setUserHobbies(String userHobbies) {
		this.userHobbies = userHobbies;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getUserAge() {
		return userAge;
	}
	public void setUserAge(String userAge) {
		this.userAge = userAge;
	}
	public String getEducationLevel() {
		return educationLevel;
	}
	public void setEducationLevel(String educationLevel) {
		this.educationLevel = educationLevel;
	}
	public String getFamilySituation() {
		return familySituation;
	}
	public void setFamilySituation(String familySituation) {
		this.familySituation = familySituation;
	}
	public String getCareer() {
		return career;
	}
	public void setCareer(String career) {
		this.career = career;
	}
	public String getJobTitleLevel() {
		return jobTitleLevel;
	}
	public void setJobTitleLevel(String jobTitleLevel) {
		this.jobTitleLevel = jobTitleLevel;
	}
	public String getMonthlyIncome() {
		return monthlyIncome;
	}
	public void setMonthlyIncome(String monthlyIncome) {
		this.monthlyIncome = monthlyIncome;
	}
	public String getVehicleHaveCount() {
		return vehicleHaveCount;
	}
	public void setVehicleHaveCount(String vehicleHaveCount) {
		this.vehicleHaveCount = vehicleHaveCount;
	}
	public String getHouseHaveCount() {
		return houseHaveCount;
	}
	public void setHouseHaveCount(String houseHaveCount) {
		this.houseHaveCount = houseHaveCount;
	}
	public String getHouseSituation() {
		return houseSituation;
	}
	public void setHouseSituation(String houseSituation) {
		this.houseSituation = houseSituation;
	}
	public String getArrivedNum() {
		return arrivedNum;
	}
	public void setArrivedNum(String arrivedNum) {
		this.arrivedNum = arrivedNum;
	}
	public String getArrivedDate() {
		return arrivedDate;
	}
	public void setArrivedDate(String arrivedDate) {
		this.arrivedDate = arrivedDate;
	}
	public String getDriverCarType() {
		return driverCarType;
	}
	public void setDriverCarType(String driverCarType) {
		this.driverCarType = driverCarType;
	}
	public String getDriverFeedback() {
		return driverFeedback;
	}
	public void setDriverFeedback(String driverFeedback) {
		this.driverFeedback = driverFeedback;
	}
	public String getDriverDlrName() {
		return driverDlrName;
	}
	public void setDriverDlrName(String driverDlrName) {
		this.driverDlrName = driverDlrName;
	}
	public String getActivityJoin() {
		return activityJoin;
	}
	public void setActivityJoin(String activityJoin) {
		this.activityJoin = activityJoin;
	}
	public String getActivityFeedback() {
		return activityFeedback;
	}
	public void setActivityFeedback(String activityFeedback) {
		this.activityFeedback = activityFeedback;
	}
	public String getSalesName() {
		return salesName;
	}
	public void setSalesName(String salesName) {
		this.salesName = salesName;
	}
	public String getEndConfigDate() {
		return endConfigDate;
	}
	public void setEndConfigDate(String endConfigDate) {
		this.endConfigDate = endConfigDate;
	}
	public String getDriverTime() {
		return driverTime;
	}
	public void setDriverTime(String driverTime) {
		this.driverTime = driverTime;
	}
	public String getSmallOrderTime() {
		return smallOrderTime;
	}
	public void setSmallOrderTime(String smallOrderTime) {
		this.smallOrderTime = smallOrderTime;
	}
	public String getBigOrderTime() {
		return bigOrderTime;
	}
	public void setBigOrderTime(String bigOrderTime) {
		this.bigOrderTime = bigOrderTime;
	}
	public String getBuyIntention() {
		return buyIntention;
	}
	public void setBuyIntention(String buyIntention) {
		this.buyIntention = buyIntention;
	}
	public String getBuyBudget() {
		return buyBudget;
	}
	public void setBuyBudget(String buyBudget) {
		this.buyBudget = buyBudget;
	}
	public String getIsAddBuy() {
		return isAddBuy;
	}
	public void setIsAddBuy(String isAddBuy) {
		this.isAddBuy = isAddBuy;
	}
	public String getBuyUse() {
		return buyUse;
	}
	public void setBuyUse(String buyUse) {
		this.buyUse = buyUse;
	}
	public String getBuyDate() {
		return buyDate;
	}
	public void setBuyDate(String buyDate) {
		this.buyDate = buyDate;
	}
	public String getBuyNum() {
		return buyNum;
	}
	public void setBuyNum(String buyNum) {
		this.buyNum = buyNum;
	}
	public String getTotalBuyAmount() {
		return totalBuyAmount;
	}
	public void setTotalBuyAmount(String totalBuyAmount) {
		this.totalBuyAmount = totalBuyAmount;
	}
	public String getBuyWay() {
		return buyWay;
	}
	public void setBuyWay(String buyWay) {
		this.buyWay = buyWay;
	}
	public String getCarLicenseCode() {
		return carLicenseCode;
	}
	public void setCarLicenseCode(String carLicenseCode) {
		this.carLicenseCode = carLicenseCode;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public String getCarColor() {
		return carColor;
	}
	public void setCarColor(String carColor) {
		this.carColor = carColor;
	}
	public String getBuyCarStore() {
		return buyCarStore;
	}
	public void setBuyCarStore(String buyCarStore) {
		this.buyCarStore = buyCarStore;
	}
	public String getReceiptInfo() {
		return receiptInfo;
	}
	public void setReceiptInfo(String receiptInfo) {
		this.receiptInfo = receiptInfo;
	}
	public String getInteractionWay() {
		return interactionWay;
	}
	public void setInteractionWay(String interactionWay) {
		this.interactionWay = interactionWay;
	}
	public String getDriveTime() {
		return driveTime;
	}
	public void setDriveTime(String driveTime) {
		this.driveTime = driveTime;
	}
	public String getDrivingTime() {
		return drivingTime;
	}
	public void setDrivingTime(String drivingTime) {
		this.drivingTime = drivingTime;
	}
	public String getDrivingHabit() {
		return drivingHabit;
	}
	public void setDrivingHabit(String drivingHabit) {
		this.drivingHabit = drivingHabit;
	}
	public String getCarUseFrequency() {
		return carUseFrequency;
	}
	public void setCarUseFrequency(String carUseFrequency) {
		this.carUseFrequency = carUseFrequency;
	}
	public String getCarUseEngeryCost() {
		return carUseEngeryCost;
	}
	public void setCarUseEngeryCost(String carUseEngeryCost) {
		this.carUseEngeryCost = carUseEngeryCost;
	}
	public String getFinanceProductInfo() {
		return financeProductInfo;
	}
	public void setFinanceProductInfo(String financeProductInfo) {
		this.financeProductInfo = financeProductInfo;
	}
	public String getFinanceInstitution() {
		return financeInstitution;
	}
	public void setFinanceInstitution(String financeInstitution) {
		this.financeInstitution = financeInstitution;
	}
	public String getApplyWay() {
		return applyWay;
	}
	public void setApplyWay(String applyWay) {
		this.applyWay = applyWay;
	}
	public String getOperationTime() {
		return operationTime;
	}
	public void setOperationTime(String operationTime) {
		this.operationTime = operationTime;
	}
	public String getSignContractWay() {
		return signContractWay;
	}
	public void setSignContractWay(String signContractWay) {
		this.signContractWay = signContractWay;
	}
	public String getCredit() {
		return credit;
	}
	public void setCredit(String credit) {
		this.credit = credit;
	}
	public String getFinanceLease() {
		return financeLease;
	}
	public void setFinanceLease(String financeLease) {
		this.financeLease = financeLease;
	}
	public String getCreditDeadline() {
		return creditDeadline;
	}
	public void setCreditDeadline(String creditDeadline) {
		this.creditDeadline = creditDeadline;
	}
	public String getFirstPayRatio() {
		return firstPayRatio;
	}
	public void setFirstPayRatio(String firstPayRatio) {
		this.firstPayRatio = firstPayRatio;
	}
	public String getInsureType() {
		return insureType;
	}
	public void setInsureType(String insureType) {
		this.insureType = insureType;
	}
	public String getInsureDeadline() {
		return insureDeadline;
	}
	public void setInsureDeadline(String insureDeadline) {
		this.insureDeadline = insureDeadline;
	}
	public String getInsureCompany() {
		return insureCompany;
	}
	public void setInsureCompany(String insureCompany) {
		this.insureCompany = insureCompany;
	}
	public String getInsureAmount() {
		return insureAmount;
	}
	public void setInsureAmount(String insureAmount) {
		this.insureAmount = insureAmount;
	}
	public String getCsiContent() {
		return csiContent;
	}
	public void setCsiContent(String csiContent) {
		this.csiContent = csiContent;
	}
	public String getRepairContent() {
		return repairContent;
	}
	public void setRepairContent(String repairContent) {
		this.repairContent = repairContent;
	}
	public String getReplaceType() {
		return replaceType;
	}
	public void setReplaceType(String replaceType) {
		this.replaceType = replaceType;
	}
	public String getLastCsiTime() {
		return lastCsiTime;
	}
	public void setLastCsiTime(String lastCsiTime) {
		this.lastCsiTime = lastCsiTime;
	}
	public String getStoreDlrName() {
		return storeDlrName;
	}
	public void setStoreDlrName(String storeDlrName) {
		this.storeDlrName = storeDlrName;
	}
	public String getCustSingleCost() {
		return custSingleCost;
	}
	public void setCustSingleCost(String custSingleCost) {
		this.custSingleCost = custSingleCost;
	}
	public String getCustSatisfaction() {
		return custSatisfaction;
	}
	public void setCustSatisfaction(String custSatisfaction) {
		this.custSatisfaction = custSatisfaction;
	}
}
