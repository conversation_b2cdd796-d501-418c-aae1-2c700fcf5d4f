package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.qry.PCUserTagQry;
import com.ly.adp.csc.entities.qry.UserTagQry;
import com.ly.adp.csc.entities.vo.koc.PCUserTagInfoVO;
import com.ly.adp.csc.entities.vo.koc.UserTagInfoVO;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * KOC用户查询服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface IKocUserService {

    /**
     * 分页查询用户列表（PC端管理）
     * @param pcUserTagQry 查询参数
     * @param token 用户token
     * @return 分页结果
     */
    ListResult<PCUserTagInfoVO> queryUserList(PCUserTagQry pcUserTagQry, String token);

    /**
     * 企微端用户查询
     * @param userTagQry 查询请求
     * @param token 用户token
     * @return 用户信息列表
     */
    ListResult<UserTagInfoVO> searchWechatUsers(UserTagQry userTagQry, String token);
}
