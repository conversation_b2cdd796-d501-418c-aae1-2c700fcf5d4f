package com.ly.adp.csc.service;

import java.util.Map;

import com.ly.adp.csc.entities.in.SacClueMsgRecordIn;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

public interface ISacClueMsgRecordService {
	/**
	 * CSC-消息查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacMsgRecordFindInfo(SacClueMsgRecordIn dataInfo);
	/**
	 * CSC-消息维护
	 * @param info
	 * @return
	 */
	OptResult sacMsgRecordSaveInfo(Map<String, Object> paramMap,String token);
	/**
	 * CSC-消息报表查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacMsgRecordReport(Map<String, Object> dataInfo,String token);
	
}
