package com.ly.adp.csc.service;

import java.util.Map;

import com.ly.adp.csc.entities.in.SacBasisLogIn;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

public interface ISacBasisLogService {

    /**
     *  CSC-基础日志查询
     * @param dataInfo
     * @return
     */
    ListResult<Map<String, Object>> sacBasisLogFindInfo(SacBasisLogIn dataInfo);

    /**
     * CSC-基础日志记录
     * @param dataInfo
     * @param token
     * @return
     */
    OptResult sacBasisLogSaveInfo(Map<String, Object> dataInfo, String token);

    OptResult sacSwitcchDlrLogInsertOne(Map<String, Object> dataInfo, String token);
}
