package com.ly.adp.csc.service;

import java.util.List;
import java.util.Map;

import com.ly.adp.csc.entities.vo.koc.ExpertTypeInfoVO;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

/**
 * <p>
 * KOC达人类型管理服务接口
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
public interface IKocExpertTypeService {

    /**
     * 获取所有达人类型列表（缓存优先）
     * @param token 用户token
     * @return 达人类型列表
     */
    List<ExpertTypeInfoVO> getAllExpertTypes(String token);

    /**
     * 分页查询达人类型管理列表
     * @param dataInfo 查询参数
     * @param token 用户token
     * @return 分页结果
     */
    ListResult<Map<String, Object>> queryExpertTypeList(Map<String, Object> dataInfo, String token);

    /**
     * 创建达人类型
     * @param dataInfo 创建数据
     * @param token 用户token
     * @return 创建结果
     */
    EntityResult<Map<String, Object>> createExpertType(Map<String, Object> dataInfo, String token);

    /**
     * 更新达人类型
     * @param dataInfo 更新数据
     * @param token 用户token
     * @return 更新结果
     */
    EntityResult<Map<String, Object>> updateExpertType(Map<String, Object> dataInfo, String token);

    /**
     * 删除达人类型
     * @param typeId 达人类型ID
     * @param token 用户token
     * @return 删除结果
     */
    OptResult deleteExpertType(String typeId, String token);

    /**
     * 批量删除达人类型
     * @param typeIds 达人类型ID列表
     * @param token 用户token
     * @return 删除结果
     */
    OptResult batchDeleteExpertTypes(List<String> typeIds, String token);

    /**
     * 批量导入达人类型
     * @param expertTypeList 达人类型列表
     * @param token 用户token
     * @return 导入结果
     */
    OptResult batchImportExpertTypes(List<Map<String, Object>> expertTypeList, String token);

    /**
     * 查询达人类型统计信息
     * @param token 用户token
     * @return 统计信息
     */
    List<Map<String, Object>> getExpertTypeStatistics(String token);

    /**
     * 批量解绑达人类型
     * @param dataInfo 解绑数据
     * @param token 用户token
     * @return 解绑结果
     */
    OptResult batchUnbindExpertTypes(Map<String, Object> dataInfo, String token);

    /**
     * 刷新达人类型缓存
     * @param token 用户token
     * @return 操作结果
     */
    OptResult refreshExpertTypeCache(String token);
}
