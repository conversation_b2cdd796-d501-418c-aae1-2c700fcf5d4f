package com.ly.adp.csc.entities.dto.koc;

import java.io.Serializable;
import java.util.List;

/**
 * 批量打标请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class BatchLabelingRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID列表
     */
    private List<String> userIds;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 达人类型ID列表
     */
    private List<String> expertTypeIds;

    /**
     * 批量操作ID
     */
    private String batchId;

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }

    public List<String> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<String> tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getExpertTypeIds() {
        return expertTypeIds;
    }

    public void setExpertTypeIds(List<String> expertTypeIds) {
        this.expertTypeIds = expertTypeIds;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    @Override
    public String toString() {
        return "BatchLabelingRequestDTO{" +
                "userIds=" + userIds +
                ", tagIds=" + tagIds +
                ", expertTypeIds=" + expertTypeIds +
                ", batchId='" + batchId + '\'' +
                '}';
    }
}
