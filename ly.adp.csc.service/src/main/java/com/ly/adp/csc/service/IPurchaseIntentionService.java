package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.MaturityVO;
import com.ly.adp.csc.otherservice.entities.PurchaseIntentionDto;
import com.ly.adp.csc.otherservice.entities.PurchaseIntentionVO;
import com.ly.mp.component.entities.ListResult;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/4/20
 */
public interface IPurchaseIntentionService {
    ListResult<PurchaseIntentionVO> purchaseIntentionQuery(PurchaseIntentionDto purchaseIntention);

    ListResult<MaturityVO> queryMaturity(PurchaseIntentionDto purchaseIntention);
}
