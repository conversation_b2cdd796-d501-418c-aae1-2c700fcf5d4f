package com.ly.adp.csc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnetaskDetail;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-14
 */
public interface ISacOnetaskDetailService extends IService<SacOnetaskDetail> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskDetailFindInfo(Map<String, Object> dataInfo,String token);

	ListResult<Map<String, Object>> sacOnetaskDetailFindInfo_performance(Map<String, Object> dataInfo);
	ListResult<Map<String, Object>> sacOnetaskDetailFindInfoNew(Map<String, Object> dataInfo);
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskDetailSaveInfo(Map<String, Object> dataInfo,String token);

    void sacOnetaskDetailBatchSave(List<Map<String, Object>> taskDetailMapList, String token);
}
