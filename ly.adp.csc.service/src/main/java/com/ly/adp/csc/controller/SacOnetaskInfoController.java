package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.req.CommonSaveTaskReq;
import com.ly.adp.csc.entities.req.SendFinishTaskMqReq;
import com.ly.adp.csc.service.*;
import com.ly.mp.busi.base.handler.OptResultBuilder;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
/**
 * <p>
 * 任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Api(value = "CSC任务信息服务", tags = "CSC任务信息服务")
@RestController
@RequestMapping(value = "/ly/adp/csc/sacOnetaskInfo", produces = { MediaType.APPLICATION_JSON_VALUE })
public class  SacOnetaskInfoController {
	@Autowired
	ISacOnetaskInfoService sacOnetaskInfoService;
	@Autowired
	ISacOnetaskDetailService sacOnetaskDetailService;
	@Autowired
	ISacOnetaskAuditRecordService sacOnetaskAuditRecordService;
	@Autowired
	ISacOnetaskExecutePlanService sacOnetaskExecutePlanService;
	@Autowired
	ISacOnetaskLeadInTempService sacOnetaskLeadInTempService;
	
	@ApiOperation(value = "任务表查询", notes = "任务表查询")
	@PostMapping(value = "/sacOnetaskInfoquery.do")
	public ListResult<Map<String, Object>> sacOnetaskInfoFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskInfoService.sacOnetaskInfoFindInfo(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "任务表维护", notes = "任务表维护")
	@PostMapping(value = "/sacOnetaskInfosave.do")
	public EntityResult<Map<String, Object>> sacOnetaskInfoSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacOnetaskInfoService.sacOnetaskInfoSaveInfo(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "任务详情表查询", notes = "任务详情表查询")
	@PostMapping(value = "/sacOnetaskDetailquery.do")
	public ListResult<Map<String, Object>> sacOnetaskDetailFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskDetailService.sacOnetaskDetailFindInfo(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "任务详情表维护", notes = "任务详情表维护")
	@PostMapping(value = "/sacOnetaskDetailsave.do")
	public EntityResult<Map<String, Object>> sacOnetaskDetailSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacOnetaskDetailService.sacOnetaskDetailSaveInfo(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "任务执行周期表查询", notes = "任务执行周期表查询")
	@PostMapping(value = "/sacOnetaskExecutePlanquery.do")
	public ListResult<Map<String, Object>> sacOnetaskExecutePlanFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskExecutePlanService.sacOnetaskExecutePlanFindInfo(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "任务执行周期表维护", notes = "任务执行周期表维护")
	@PostMapping(value = "/sacOnetaskExecutePlansave.do")
	public EntityResult<Map<String, Object>> sacOnetaskExecutePlanSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacOnetaskExecutePlanService.sacOnetaskExecutePlanSaveInfo(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "任务导入临时表导入", notes = "任务导入临时表导入")
	@PostMapping(value = "/taskInfoImport.do")
	public Result taskInfoImport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,String taskTypeCode,String taskType,String taskId,@RequestBody(required = true) MultipartFile uploadfile) {
		return sacOnetaskLeadInTempService.taskInfoImport(authentication,taskTypeCode, taskType, taskId, uploadfile);
	}

	@ApiOperation(value = "任务导入临时表导入1", notes = "任务导入临时表导入1")
	@PostMapping(value = "/taskInfoImport1.do")
	public Result taskInfoImport1(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,String taskTypeCode,String taskType,String taskId,@RequestBody(required = true) MultipartFile uploadfile) {
		return sacOnetaskLeadInTempService.taskInfoImport1(authentication,taskTypeCode, taskType, taskId, uploadfile);
	}
	
	@ApiOperation(value = "任务导入临时表查询", notes = "任务导入临时表查询")
	@PostMapping(value = "/sacOnetaskLeadInTempquery.do")
	public ListResult<Map<String, Object>> sacOnetaskLeadInTempFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskLeadInTempService.sacOnetaskLeadInTempFindInfo(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "任务导入临时表定时", notes = "任务导入临时表定时")
	@PostMapping(value = "/taskInfoDbjob.do")
	public void taskInfoDbjob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
		sacOnetaskLeadInTempService.taskInfoDbjob();
	}
	
	@ApiOperation(value = "任务发送消息表定时", notes = "任务发送消息表定时")
	@PostMapping(value = "/taskMsgDbjob.do")
	public void taskMsgDbjob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
		sacOnetaskLeadInTempService.taskMsgDbjob();
	}
	
	@ApiOperation(value = "试驾换店任务", notes = "试驾换店任务")
	@PostMapping(value = "/sacOnetaskDriveUse.do")
	public EntityResult<Map<String, Object>> sacOnetaskDriveUse(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacOnetaskInfoService.sacOnetaskDriveUse(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "试驾换店任务", notes = "试驾换店任务")
	@PostMapping(value = "/sacOnetaskDriveUseNotDo")
	public EntityResult<Map<String, Object>> sacOnetaskDriveUseNotDo(@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> sacOnetaskInfoService.sacOnetaskDriveUse(dateInfo, "")).result();
	}
	
	@ApiOperation(value = "任务消息查询", notes = "任务消息查询")
	@PostMapping(value = "/sacOnetaskInfoMsg.do")
	public ListResult<Map<String, Object>> sacOnetaskInfoMsg(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskInfoService.sacOnetaskInfoMsg(dateInfo,authentication)).result();
	}

	@ApiOperation(value = "任务消息查询-优化性能", notes = "任务消息查询-优化性能")
	@PostMapping(value = "/sacOnetaskInfoMsg-performance.do")
	public ListResult<Map<String, Object>> sacOnetaskInfoMsg_performance(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacOnetaskInfoService.sacOnetaskInfoMsg_performance(dateInfo, authentication)).result();
	}

	@ApiOperation(value = "任务消息查询-优化性能", notes = "任务消息查询-优化性能")
	@PostMapping(value = "/sacOnetaskInfoMsgNew.do")
	public ListResult<Map<String, Object>> sacOnetaskInfoMsgNew(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> sacOnetaskInfoService.sacOnetaskInfoMsgNew(dateInfo, authentication)).result();
	}

	@ApiOperation(value = "产品专家下拉框查询", notes = "产品专家下拉框查询")
	@PostMapping(value = "/selectProductNameDlr.do")
	public ListResult<Map<String, Object>> selectProductNameDlr(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> sacOnetaskInfoService.selectProductNameDlr(dateInfo,authentication)).result();
	}
	
	@ApiOperation(value = "任务详情完成", notes = "任务详情完成")
	@PostMapping(value = "/sacOnetaskFinishUpdate.do")
	public OptResult sacOnetaskFinishUpdate(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doOpt(() -> sacOnetaskInfoService.sacOnetaskFinishUpdate(dateInfo, authentication)).result();
	}
	
	@ApiOperation(value = "发布对象导入定时任务", notes = "发布对象导入定时任务")
	@PostMapping(value = "/taskPublishObjImportJob.do")
	public OptResult taskPublishJob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
		sacOnetaskInfoService.taskPublishJob();
		return OptResultBuilder.createOk().build();
	}
	
	@ApiOperation(value = "任务周期重复执行定时任务", notes = "任务周期重复执行定时任务")
	@PostMapping(value = "/taskCycleExecuteJob.do")
	public OptResult taskCycleExecuteJob(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
		sacOnetaskInfoService.cycleExecuteTask();
		return OptResultBuilder.createOk().build();
	}

	@ApiOperation(value = "通用任务表维护接口", notes = "通用任务表维护接口")
	@PostMapping(value = "/commonSaveTask.do")
	public EntityResult<String> commonSaveTask(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication
			, @RequestBody(required = false) CommonSaveTaskReq req) {
		return sacOnetaskLeadInTempService.commonSaveTask(authentication, req);
	}

	@ApiOperation(value = "任务状态消息推送", notes = "任务状态消息推送")
	@PostMapping(value = "/sendFinishTaskMq.do")
	public OptResult sendFinishTaskMq(@RequestHeader(HttpHeaders.AUTHORIZATION) String token
			, @RequestBody(required = false) SendFinishTaskMqReq sendFinishTaskMqReq) {
		sacOnetaskInfoService.sendFinishTaskMq(sendFinishTaskMqReq);
		return OptResultBuilder.createOk().build();
	}
}
