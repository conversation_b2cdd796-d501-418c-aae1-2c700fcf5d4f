package com.ly.adp.csc.otherservice.entities.in;

import java.io.Serializable;

import com.ly.adp.csc.entities.in.PageInfo;

import io.swagger.annotations.ApiModelProperty;

public class MdmOrgDlrIn extends PageInfo implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "是否限定当前经销商权限(1:限定只查当前经销商，一网可以查询下属二网)")
    private String specCurAuthorOnly;
	
    @ApiModelProperty(value = "用户ID")
    private String userId;
    public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	@ApiModelProperty(value = "管理员账号")
    private String userName;

	@ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "管理员密码")
    private String password;

    @ApiModelProperty(value = "管理员角色")
    private String roleId;

    @ApiModelProperty(value = "专营店ID")
    private String dlrId;

    private String dlrAgentCompanyArea;

    @ApiModelProperty(value = "专营店编码")
    private String dlrCode;

    @ApiModelProperty(value = "专营店简称")
    private String dlrShortName;

    @ApiModelProperty(value = "专营店全称")
    private String dlrFullName;

    @ApiModelProperty(value = "专营店英文名")
    private String dlrEnName;

    @ApiModelProperty(value = "专营店曾用名")
    private String dlrNameOld;

    @ApiModelProperty(value = "公司曾用名")
    private String compNameOld;

    @ApiModelProperty(value = "公司拼音")
    private String compSpell;

    @ApiModelProperty(value = "公司类型  P:PV;D:DLR;")
    private String compType;

    @ApiModelProperty(value = "上级网点ID")
    private String parentDlrId;

    @ApiModelProperty(value = "专营店ANSWER编码")
    private String dlrAnswerCode;

    @ApiModelProperty(value = "SAP专营店ID")
    private String sapDlrId;

    @ApiModelProperty(value = "名称缩写代码")
    private String dlrSymbol;

    @ApiModelProperty(value = "组织机构代号")
    private String guno;

    @ApiModelProperty(value = "专营店硬体级别")
    private String dlrHardwareClass;

    @ApiModelProperty(value = "展厅面积")
    private String showAcreage;

    @ApiModelProperty(value = "车间面积")
    private String factoryAcreage;

    @ApiModelProperty(value = "占地面积")
    private String coverAcreage;

    @ApiModelProperty(value = "总建筑面积")
    private String tatolAcreage;

    @ApiModelProperty(value = "经营范围")
    private String fareRange;

    @ApiModelProperty(value = "专营店级别")
    private String dlrLevel;

    @ApiModelProperty(value = "联络地址")
    private String linkAddr;

    @ApiModelProperty(value = "小区ID")
    private String smallAreaId;

    @ApiModelProperty(value = "大区ID")
    private String bigAreaId;

    @ApiModelProperty(value = "省份ID")
    private String provinceId;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "区县ID")
    private String countyId;

    @ApiModelProperty(value = "传真")
    private String fax;

    @ApiModelProperty(value = "电话号码")
    private String phone;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "邮编")
    private String zip;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "紧急救援电话")
    private String urgSosTel;

    @ApiModelProperty(value = "销售热线电话")
    private String saleTel;

    @ApiModelProperty(value = "售前传真")
    private String saleFax;

    @ApiModelProperty(value = "销售邮箱")
    private String saleEmail;

    @ApiModelProperty(value = "服务热线电话")
    private String serviceTel;

    @ApiModelProperty(value = "服务传真")
    private String serviceFax;

    @ApiModelProperty(value = "售后邮箱")
    private String serviceEmail;

    @ApiModelProperty(value = "法人")
    private String legalPerson;

    @ApiModelProperty(value = "法人代表证件编码")
    private String legalPersonCard;

    @ApiModelProperty(value = "签约公司法人代表")
    private String sMaster;

    @ApiModelProperty(value = "法人代表联系方式")
    private String sMasterConn;

    @ApiModelProperty(value = "通讯地址及邮编")
    private String sAddr;

    @ApiModelProperty(value = "店长姓名")
    private String managerName;

    @ApiModelProperty(value = "店长电话")
    private String managerTel;

    @ApiModelProperty(value = "结算资格")
    private String balanceCertificate;

    @ApiModelProperty(value = "汽车维修资格")
    private String maintainCertificate;

    @ApiModelProperty(value = "建店联系人")
    private String ceo;

    @ApiModelProperty(value = "建店联系人手机号码")
    private String ceoConn;

    @ApiModelProperty(value = "组织机构模型编号")
    private String deptModelId;

    @ApiModelProperty(value = "认证标志")
    private String certificateFlag;

    @ApiModelProperty(value = "专营店状态：一网  0 停业，1 营业，2、在建 3、取消 4、整改；直营二网 1营业，2、在建 3、取消<br>7、建店撤销，自动撤销，停业，升级，已批准一级店")
    private String dlrStatus;

    @ApiModelProperty(value = "专营店关联关系  1-深圳风神专营店；2-关联专营店；3-非关联专营店")
    private String dlrReleation;

    @ApiModelProperty(value = "专营店类型 MDM.T_MDM_LOOKUP_VALUE(lookup_type_code =5005)")
    private String dlrType;

    @ApiModelProperty(value = "关联关系维护状态  0-未维护；1-已维护")
    private String releationStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "品牌编码")
    private String carBrandCode;

    @ApiModelProperty(value = "DOQD标记")
    private String doqdFlag;

    @ApiModelProperty(value = "专营店分类 MDM.T_MDM_LOOKUP_VALUE(lookup_type_code =5003):  0:一级店 1:二级网点 2:二手车店")
    private String dlrSort;

    @ApiModelProperty(value = "所属PV公司代码 例如：4000,4815，4911")
    private String pvCompCode;

    @ApiModelProperty(value = "是否同步信息 1 是 2 否")
    private String isSynchronous;

    @ApiModelProperty(value = "区域")
    private String areaId;

    @ApiModelProperty(value = "专营店类别(值列表: DB0062)  1. 4S店；2. 直营二网；3. 虚拟4S店；4. 备件采购类；5. 服务类；6. 钣喷中心；7. 二手车中心；8. 配送中心；9. 服务二网）")
    private String orgType;

    @ApiModelProperty(value = "连体店")
    private String linkDlrId;

    @ApiModelProperty(value = "微信号")
    private String wechat;

    @ApiModelProperty(value = "经度")
    private String lng;

    @ApiModelProperty(value = "纬度")
    private String lat;

    @ApiModelProperty(value = "是否保险认证店")
    private String isSecurity;

    @ApiModelProperty(value = "专营店4S级别")
    private String dlr4sLevel;

    @ApiModelProperty(value = "公司名称")
    private String compName;

    @ApiModelProperty(value = "V模块网点编码")
    private String vDlrCode;

    @ApiModelProperty(value = "P模块网点编码")
    private String pDlrCode;

    @ApiModelProperty(value = "S模块网点编码")
    private String sDlrCode;

    @ApiModelProperty(value = "UC模块网点编码")
    private String ucDlrCode;

    @ApiModelProperty(value = "AI模块网点编码")
    private String aiDlrCode;

    @ApiModelProperty(value = "CAP模块网点编码")
    private String capDlrCode;

    @ApiModelProperty(value = "MDS大区ID MDS临时使用的大区ID")
    private String mdsBigAreaId;

    @ApiModelProperty(value = "排放标准")
    private String emissionStandard;

    @ApiModelProperty(value = "专属模块")
    private String belongModule;

    @ApiModelProperty(value = "气候状态")
    private String climateStatus;

    @ApiModelProperty(value = "新E3S上线标记(未上线0,上线1,旧店2)")
    private String onlineFlag;

    @ApiModelProperty(value = "打印模板")
    private String printTemplet;

    @ApiModelProperty(value = "特需网点标记 0:一般网点，1:特需网点")
    private String spFlag;

    @ApiModelProperty(value = "是否OKCare店")
    private String isOkCare;

    @ApiModelProperty(value = "是否开通快速保养")
    private String isSsaFast;

    @ApiModelProperty(value = "是否开通自助保养")
    private String isSsaSelf;

    @ApiModelProperty(value = "是否开通极速钣喷")
    private String isSsaSpray;

    @ApiModelProperty(value = "电子档案维修企业注册区域编码")
    private String companyAreaCode;

    @ApiModelProperty(value = "电子档案维修企业唯一标识")
    private String companyUniqueCode;

    @ApiModelProperty(value = "电子档案企业许可名称")
    private String companyName;

    @ApiModelProperty(value = "是否需要发送维修记录到电子档案系统：0不需要，1需要")
    private String isSend;

    @ApiModelProperty(value = "电子档案许可证号")
    private String transportLicense;

    @ApiModelProperty(value = "是否开通自助保养")
    private String isSsaFalf;

    @ApiModelProperty(value = "厂商标识ID")
    private String oemId;

    @ApiModelProperty(value = "集团标识ID")
    private String groupId;

    @ApiModelProperty(value = "厂商标识")
    private String oemCode;

    @ApiModelProperty(value = "集团标识")
    private String groupCode;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人姓名")
    private String createdName;

    @ApiModelProperty(value = "最后更新人员")
    private String modifier;

    @ApiModelProperty(value = "修改人姓名")
    private String modifyName;

    @ApiModelProperty(value = "是否可用")
    private String isEnable;

    @ApiModelProperty(value = "SDP用户ID")
    private String sdpUserId;

    @ApiModelProperty(value = "SDP组织ID")
    private String sdpOrgId;

    @ApiModelProperty(value = "并发控制字段")
    private String updateControlId;

    @ApiModelProperty(value = "扩展字段1")
    private String column1;

    @ApiModelProperty(value = "扩展字段2")
    private String column2;

    @ApiModelProperty(value = "扩展字段3")
    private String column3;

    @ApiModelProperty(value = "扩展字段4")
    private String column4;

    @ApiModelProperty(value = "扩展字段5")
    private String column5;

    @ApiModelProperty(value = "扩展字段6")
    private String column6;

    @ApiModelProperty(value = "扩展字段7")
    private String column7;

    @ApiModelProperty(value = "扩展字段8")
    private String column8;

    @ApiModelProperty(value = "扩展字段9")
    private String column9;

    @ApiModelProperty(value = "扩展字段10")
    private String column10;

    @ApiModelProperty(value = "高级搜索模糊匹配")
    private String queryParam;

    @ApiModelProperty(value = "救援热线")
    private String rescuHotline;

    @ApiModelProperty(value = "营业时间")
    private String dlrBussDate;
    
    @ApiModelProperty(value = "注册资金")
    private String registerMoney;
    @ApiModelProperty(value = "是否NCH店")
    private String isNch;
    @ApiModelProperty(value = "是否NCH授权销售店")
    private String isNchSaleAuth;
    @ApiModelProperty(value = "是否NCH授权售后店")
    private String isNchServiceAuth;  
    @ApiModelProperty(value = "组织id")
    private String orgTreeId;  
    
    @ApiModelProperty(value = "集团名称")
    private String orgName;  
    @ApiModelProperty(value = "组织树类型")
    private String treeType;
    
    @ApiModelProperty(value = " 售后大区id")
	private String serviceBigAreaId;
	
    @ApiModelProperty(value = " 售后小区id")
	private String serviceSmallAreaId;
	

    @ApiModelProperty(value = "售后服务状态")
	private String saServiceStatus;
	
    @ApiModelProperty(value = "销售服务状态")
	private String caServiceStatus;
	
	
    @ApiModelProperty(value = "是否具备EV销售资质")
	private String hasEvSale;
	
    @ApiModelProperty(value = "是否具备EV售后资质")
	private String hasEvService;
   
    @ApiModelProperty(value = "初始化品牌编码")
	private String initBrandCode;
    
    @ApiModelProperty(value = "业务类型")
	private String userType;
    
    @ApiModelProperty(value = "城市公司ID")
    private String agentCompanyId;

    @ApiModelProperty(value = "城市公司名称")
    private String agentCompanyName;

    @ApiModelProperty(value = "城市公司编码")
    private String agentCompanyCode;
    
    @ApiModelProperty(value = "代理商ID")
    private String agentId;

    @ApiModelProperty(value = "代理商名称")
    private String agentName;

    @ApiModelProperty(value = "代理商编码")
    private String agentCode;

    @ApiModelProperty(value = "代理公司区域")
    private String agentCompanyArea;
	
    @ApiModelProperty(value = "门店创建开始时间")
    private String createdStartDate;
    
    @ApiModelProperty(value = "门店创建结束时间")
    private String createdEndDate;

    public String getAgentCompanyId() {
        return agentCompanyId;
    }

    public void setAgentCompanyId(String agentCompanyId) {
        this.agentCompanyId = agentCompanyId;
    }

    public String getAgentCompanyCode() {
        return agentCompanyCode;
    }

    public void setAgentCompanyCode(String agentCompanyCode) {
        this.agentCompanyCode = agentCompanyCode;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentCompanyArea() {
        return agentCompanyArea;
    }

    public void setAgentCompanyArea(String agentCompanyArea) {
        this.agentCompanyArea = agentCompanyArea;
    }

    public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getServiceBigAreaId() {
		return serviceBigAreaId;
	}

	public void setServiceBigAreaId(String serviceBigAreaId) {
		this.serviceBigAreaId = serviceBigAreaId;
	}

	public String getServiceSmallAreaId() {
		return serviceSmallAreaId;
	}

	public void setServiceSmallAreaId(String serviceSmallAreaId) {
		this.serviceSmallAreaId = serviceSmallAreaId;
	}

	public String getSaServiceStatus() {
		return saServiceStatus;
	}

	public void setSaServiceStatus(String saServiceStatus) {
		this.saServiceStatus = saServiceStatus;
	}

	public String getCaServiceStatus() {
		return caServiceStatus;
	}

	public void setCaServiceStatus(String caServiceStatus) {
		this.caServiceStatus = caServiceStatus;
	}

	public String getHasEvSale() {
		return hasEvSale;
	}

	public void setHasEvSale(String hasEvSale) {
		this.hasEvSale = hasEvSale;
	}

	public String getHasEvService() {
		return hasEvService;
	}

	public void setHasEvService(String hasEvService) {
		this.hasEvService = hasEvService;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgTreeId() {
		return orgTreeId;
	}

	public void setOrgTreeId(String orgTreeId) {
		this.orgTreeId = orgTreeId;
	}

	public String getIsNchSaleAuth() {
		return isNchSaleAuth;
	}

	public void setIsNchSaleAuth(String isNchSaleAuth) {
		this.isNchSaleAuth = isNchSaleAuth;
	}

	public String getIsNchServiceAuth() {
		return isNchServiceAuth;
	}

	public void setIsNchServiceAuth(String isNchServiceAuth) {
		this.isNchServiceAuth = isNchServiceAuth;
	}

	public String getSpecCurAuthorOnly() {
		return specCurAuthorOnly;
	}

	public void setSpecCurAuthorOnly(String specCurAuthorOnly) {
		this.specCurAuthorOnly = specCurAuthorOnly;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getDlrId() {
		return dlrId;
	}

	public void setDlrId(String dlrId) {
		this.dlrId = dlrId;
	}

	public String getDlrCode() {
		return dlrCode;
	}

	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}

	public String getDlrShortName() {
		return dlrShortName;
	}

	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}

	public String getDlrFullName() {
		return dlrFullName;
	}

	public void setDlrFullName(String dlrFullName) {
		this.dlrFullName = dlrFullName;
	}

	public String getDlrEnName() {
		return dlrEnName;
	}

	public void setDlrEnName(String dlrEnName) {
		this.dlrEnName = dlrEnName;
	}

	public String getDlrNameOld() {
		return dlrNameOld;
	}

	public void setDlrNameOld(String dlrNameOld) {
		this.dlrNameOld = dlrNameOld;
	}

	public String getCompNameOld() {
		return compNameOld;
	}

	public void setCompNameOld(String compNameOld) {
		this.compNameOld = compNameOld;
	}

	public String getCompSpell() {
		return compSpell;
	}

	public void setCompSpell(String compSpell) {
		this.compSpell = compSpell;
	}

	public String getCompType() {
		return compType;
	}

	public void setCompType(String compType) {
		this.compType = compType;
	}

	public String getParentDlrId() {
		return parentDlrId;
	}

	public void setParentDlrId(String parentDlrId) {
		this.parentDlrId = parentDlrId;
	}

	public String getDlrAnswerCode() {
		return dlrAnswerCode;
	}

	public void setDlrAnswerCode(String dlrAnswerCode) {
		this.dlrAnswerCode = dlrAnswerCode;
	}

	public String getSapDlrId() {
		return sapDlrId;
	}

	public void setSapDlrId(String sapDlrId) {
		this.sapDlrId = sapDlrId;
	}

	public String getDlrSymbol() {
		return dlrSymbol;
	}

	public void setDlrSymbol(String dlrSymbol) {
		this.dlrSymbol = dlrSymbol;
	}

	public String getGuno() {
		return guno;
	}

	public void setGuno(String guno) {
		this.guno = guno;
	}

	public String getDlrHardwareClass() {
		return dlrHardwareClass;
	}

	public void setDlrHardwareClass(String dlrHardwareClass) {
		this.dlrHardwareClass = dlrHardwareClass;
	}

	public String getShowAcreage() {
		return showAcreage;
	}

	public void setShowAcreage(String showAcreage) {
		this.showAcreage = showAcreage;
	}

	public String getFactoryAcreage() {
		return factoryAcreage;
	}

	public void setFactoryAcreage(String factoryAcreage) {
		this.factoryAcreage = factoryAcreage;
	}

	public String getCoverAcreage() {
		return coverAcreage;
	}

	public void setCoverAcreage(String coverAcreage) {
		this.coverAcreage = coverAcreage;
	}

	public String getTatolAcreage() {
		return tatolAcreage;
	}

	public void setTatolAcreage(String tatolAcreage) {
		this.tatolAcreage = tatolAcreage;
	}

	public String getFareRange() {
		return fareRange;
	}

	public void setFareRange(String fareRange) {
		this.fareRange = fareRange;
	}

	public String getDlrLevel() {
		return dlrLevel;
	}

	public void setDlrLevel(String dlrLevel) {
		this.dlrLevel = dlrLevel;
	}

	public String getLinkAddr() {
		return linkAddr;
	}

	public void setLinkAddr(String linkAddr) {
		this.linkAddr = linkAddr;
	}

	public String getSmallAreaId() {
		return smallAreaId;
	}

	public void setSmallAreaId(String smallAreaId) {
		this.smallAreaId = smallAreaId;
	}

	public String getBigAreaId() {
		return bigAreaId;
	}

	public void setBigAreaId(String bigAreaId) {
		this.bigAreaId = bigAreaId;
	}

	public String getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}

	public String getCountyId() {
		return countyId;
	}

	public void setCountyId(String countyId) {
		this.countyId = countyId;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUrgSosTel() {
		return urgSosTel;
	}

	public void setUrgSosTel(String urgSosTel) {
		this.urgSosTel = urgSosTel;
	}

	public String getSaleTel() {
		return saleTel;
	}

	public void setSaleTel(String saleTel) {
		this.saleTel = saleTel;
	}

	public String getSaleFax() {
		return saleFax;
	}

	public void setSaleFax(String saleFax) {
		this.saleFax = saleFax;
	}

	public String getSaleEmail() {
		return saleEmail;
	}

	public void setSaleEmail(String saleEmail) {
		this.saleEmail = saleEmail;
	}

	public String getServiceTel() {
		return serviceTel;
	}

	public void setServiceTel(String serviceTel) {
		this.serviceTel = serviceTel;
	}

	public String getServiceFax() {
		return serviceFax;
	}

	public void setServiceFax(String serviceFax) {
		this.serviceFax = serviceFax;
	}

	public String getServiceEmail() {
		return serviceEmail;
	}

	public void setServiceEmail(String serviceEmail) {
		this.serviceEmail = serviceEmail;
	}

	public String getLegalPerson() {
		return legalPerson;
	}

	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}

	public String getLegalPersonCard() {
		return legalPersonCard;
	}

	public void setLegalPersonCard(String legalPersonCard) {
		this.legalPersonCard = legalPersonCard;
	}

	public String getsMaster() {
		return sMaster;
	}

	public void setsMaster(String sMaster) {
		this.sMaster = sMaster;
	}

	public String getsMasterConn() {
		return sMasterConn;
	}

	public void setsMasterConn(String sMasterConn) {
		this.sMasterConn = sMasterConn;
	}

	public String getsAddr() {
		return sAddr;
	}

	public void setsAddr(String sAddr) {
		this.sAddr = sAddr;
	}

	public String getManagerName() {
		return managerName;
	}

	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}

	public String getManagerTel() {
		return managerTel;
	}

	public void setManagerTel(String managerTel) {
		this.managerTel = managerTel;
	}

	public String getBalanceCertificate() {
		return balanceCertificate;
	}

	public void setBalanceCertificate(String balanceCertificate) {
		this.balanceCertificate = balanceCertificate;
	}

	public String getMaintainCertificate() {
		return maintainCertificate;
	}

	public void setMaintainCertificate(String maintainCertificate) {
		this.maintainCertificate = maintainCertificate;
	}

	public String getCeo() {
		return ceo;
	}

	public void setCeo(String ceo) {
		this.ceo = ceo;
	}

	public String getCeoConn() {
		return ceoConn;
	}

	public void setCeoConn(String ceoConn) {
		this.ceoConn = ceoConn;
	}

	public String getDeptModelId() {
		return deptModelId;
	}

	public void setDeptModelId(String deptModelId) {
		this.deptModelId = deptModelId;
	}

	public String getCertificateFlag() {
		return certificateFlag;
	}

	public void setCertificateFlag(String certificateFlag) {
		this.certificateFlag = certificateFlag;
	}

	public String getDlrStatus() {
		return dlrStatus;
	}

	public void setDlrStatus(String dlrStatus) {
		this.dlrStatus = dlrStatus;
	}

	public String getDlrReleation() {
		return dlrReleation;
	}

	public void setDlrReleation(String dlrReleation) {
		this.dlrReleation = dlrReleation;
	}

	public String getDlrType() {
		return dlrType;
	}

	public void setDlrType(String dlrType) {
		this.dlrType = dlrType;
	}

	public String getReleationStatus() {
		return releationStatus;
	}

	public void setReleationStatus(String releationStatus) {
		this.releationStatus = releationStatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCarBrandCode() {
		return carBrandCode;
	}

	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}

	public String getDoqdFlag() {
		return doqdFlag;
	}

	public void setDoqdFlag(String doqdFlag) {
		this.doqdFlag = doqdFlag;
	}

	public String getDlrSort() {
		return dlrSort;
	}

	public void setDlrSort(String dlrSort) {
		this.dlrSort = dlrSort;
	}

	public String getPvCompCode() {
		return pvCompCode;
	}

	public void setPvCompCode(String pvCompCode) {
		this.pvCompCode = pvCompCode;
	}

	public String getIsSynchronous() {
		return isSynchronous;
	}

	public void setIsSynchronous(String isSynchronous) {
		this.isSynchronous = isSynchronous;
	}

	public String getAreaId() {
		return areaId;
	}

	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	public String getLinkDlrId() {
		return linkDlrId;
	}

	public void setLinkDlrId(String linkDlrId) {
		this.linkDlrId = linkDlrId;
	}

	public String getWechat() {
		return wechat;
	}

	public void setWechat(String wechat) {
		this.wechat = wechat;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getIsSecurity() {
		return isSecurity;
	}

	public void setIsSecurity(String isSecurity) {
		this.isSecurity = isSecurity;
	}

	public String getDlr4sLevel() {
		return dlr4sLevel;
	}

	public void setDlr4sLevel(String dlr4sLevel) {
		this.dlr4sLevel = dlr4sLevel;
	}

	public String getCompName() {
		return compName;
	}

	public void setCompName(String compName) {
		this.compName = compName;
	}

	public String getvDlrCode() {
		return vDlrCode;
	}

	public void setvDlrCode(String vDlrCode) {
		this.vDlrCode = vDlrCode;
	}

	public String getpDlrCode() {
		return pDlrCode;
	}

	public void setpDlrCode(String pDlrCode) {
		this.pDlrCode = pDlrCode;
	}

	public String getsDlrCode() {
		return sDlrCode;
	}

	public void setsDlrCode(String sDlrCode) {
		this.sDlrCode = sDlrCode;
	}

	public String getUcDlrCode() {
		return ucDlrCode;
	}

	public void setUcDlrCode(String ucDlrCode) {
		this.ucDlrCode = ucDlrCode;
	}

	public String getAiDlrCode() {
		return aiDlrCode;
	}

	public void setAiDlrCode(String aiDlrCode) {
		this.aiDlrCode = aiDlrCode;
	}

	public String getCapDlrCode() {
		return capDlrCode;
	}

	public void setCapDlrCode(String capDlrCode) {
		this.capDlrCode = capDlrCode;
	}

	public String getMdsBigAreaId() {
		return mdsBigAreaId;
	}

	public void setMdsBigAreaId(String mdsBigAreaId) {
		this.mdsBigAreaId = mdsBigAreaId;
	}

	public String getEmissionStandard() {
		return emissionStandard;
	}

	public void setEmissionStandard(String emissionStandard) {
		this.emissionStandard = emissionStandard;
	}

	public String getBelongModule() {
		return belongModule;
	}

	public void setBelongModule(String belongModule) {
		this.belongModule = belongModule;
	}

	public String getClimateStatus() {
		return climateStatus;
	}

	public void setClimateStatus(String climateStatus) {
		this.climateStatus = climateStatus;
	}

	public String getOnlineFlag() {
		return onlineFlag;
	}

	public void setOnlineFlag(String onlineFlag) {
		this.onlineFlag = onlineFlag;
	}

	public String getPrintTemplet() {
		return printTemplet;
	}

	public void setPrintTemplet(String printTemplet) {
		this.printTemplet = printTemplet;
	}

	public String getSpFlag() {
		return spFlag;
	}

	public void setSpFlag(String spFlag) {
		this.spFlag = spFlag;
	}

	public String getIsOkCare() {
		return isOkCare;
	}

	public void setIsOkCare(String isOkCare) {
		this.isOkCare = isOkCare;
	}

	public String getIsSsaFast() {
		return isSsaFast;
	}

	public void setIsSsaFast(String isSsaFast) {
		this.isSsaFast = isSsaFast;
	}

	public String getIsSsaSelf() {
		return isSsaSelf;
	}

	public void setIsSsaSelf(String isSsaSelf) {
		this.isSsaSelf = isSsaSelf;
	}

	public String getIsSsaSpray() {
		return isSsaSpray;
	}

	public void setIsSsaSpray(String isSsaSpray) {
		this.isSsaSpray = isSsaSpray;
	}

	public String getCompanyAreaCode() {
		return companyAreaCode;
	}

	public void setCompanyAreaCode(String companyAreaCode) {
		this.companyAreaCode = companyAreaCode;
	}

	public String getCompanyUniqueCode() {
		return companyUniqueCode;
	}

	public void setCompanyUniqueCode(String companyUniqueCode) {
		this.companyUniqueCode = companyUniqueCode;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getIsSend() {
		return isSend;
	}

	public void setIsSend(String isSend) {
		this.isSend = isSend;
	}

	public String getTransportLicense() {
		return transportLicense;
	}

	public void setTransportLicense(String transportLicense) {
		this.transportLicense = transportLicense;
	}

	public String getIsSsaFalf() {
		return isSsaFalf;
	}

	public void setIsSsaFalf(String isSsaFalf) {
		this.isSsaFalf = isSsaFalf;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getOemCode() {
		return oemCode;
	}

	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getSdpUserId() {
		return sdpUserId;
	}

	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}

	public String getSdpOrgId() {
		return sdpOrgId;
	}

	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	public String getColumn1() {
		return column1;
	}

	public void setColumn1(String column1) {
		this.column1 = column1;
	}

	public String getColumn2() {
		return column2;
	}

	public void setColumn2(String column2) {
		this.column2 = column2;
	}

	public String getColumn3() {
		return column3;
	}

	public void setColumn3(String column3) {
		this.column3 = column3;
	}

	public String getColumn4() {
		return column4;
	}

	public void setColumn4(String column4) {
		this.column4 = column4;
	}

	public String getColumn5() {
		return column5;
	}

	public void setColumn5(String column5) {
		this.column5 = column5;
	}

	public String getColumn6() {
		return column6;
	}

	public void setColumn6(String column6) {
		this.column6 = column6;
	}

	public String getColumn7() {
		return column7;
	}

	public void setColumn7(String column7) {
		this.column7 = column7;
	}

	public String getColumn8() {
		return column8;
	}

	public void setColumn8(String column8) {
		this.column8 = column8;
	}

	public String getColumn9() {
		return column9;
	}

	public void setColumn9(String column9) {
		this.column9 = column9;
	}

	public String getColumn10() {
		return column10;
	}

	public void setColumn10(String column10) {
		this.column10 = column10;
	}

	public String getQueryParam() {
		return queryParam;
	}

	public void setQueryParam(String queryParam) {
		this.queryParam = queryParam;
	}

	public String getRescuHotline() {
		return rescuHotline;
	}

	public void setRescuHotline(String rescuHotline) {
		this.rescuHotline = rescuHotline;
	}

	public String getDlrBussDate() {
		return dlrBussDate;
	}

	public void setDlrBussDate(String dlrBussDate) {
		this.dlrBussDate = dlrBussDate;
	}

	public String getRegisterMoney() {
		return registerMoney;
	}

	public void setRegisterMoney(String registerMoney) {
		this.registerMoney = registerMoney;
	}

	public String getIsNch() {
		return isNch;
	}

	public void setIsNch(String isNch) {
		this.isNch = isNch;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getTreeType() {
		return treeType;
	}

	public void setTreeType(String treeType) {
		this.treeType = treeType;
	}

	public String getInitBrandCode() {
		return initBrandCode;
	}

	public void setInitBrandCode(String initBrandCode) {
		this.initBrandCode = initBrandCode;
	}

	public String getAgentCompanyName() {
		return agentCompanyName;
	}

	public void setAgentCompanyName(String agentCompanyName) {
		this.agentCompanyName = agentCompanyName;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getCreatedStartDate() {
		return createdStartDate;
	}

	public void setCreatedStartDate(String createdStartDate) {
		this.createdStartDate = createdStartDate;
	}

	public String getCreatedEndDate() {
		return createdEndDate;
	}

	public void setCreatedEndDate(String createdEndDate) {
		this.createdEndDate = createdEndDate;
	}


    public String getDlrAgentCompanyArea() {
        return dlrAgentCompanyArea;
    }

    public void setDlrAgentCompanyArea(String dlrAgentCompanyArea) {
        this.dlrAgentCompanyArea = dlrAgentCompanyArea;
    }
}
