package com.ly.adp.csc.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacComplaintsDealRecordService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(value = "投诉工单处理", tags = "投诉工单处理")
@RestController
@RequestMapping(value = "/ly/sac/complaints", produces = { MediaType.APPLICATION_JSON_VALUE })
public class ComplaintsController {

	@Autowired
	ISacComplaintsDealRecordService sacComplaintsDealRecordService;

	@ApiOperation(value = "投诉工单处理记录维护", notes = "投诉工单处理记录维护")
	@PostMapping(value = "/complaintsrecordsave.do")
	public OptResult sacBasisLogSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker
				.doOpt(() -> sacComplaintsDealRecordService.insertSacComplaintsDealRecord(dateInfo, authentication))
				.result();
	}

	@ApiOperation(value = "投诉工单处理记录查询", notes = "投诉工单处理记录查询")
	@PostMapping(value = "/queryrecord.do")
	public ListResult<Map<String, Object>> cscMessageNoticeConvention(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(
				() -> sacComplaintsDealRecordService.sacComplaintsDealRecordFindByPage(dateInfo, authentication))
				.result();
	}
}
