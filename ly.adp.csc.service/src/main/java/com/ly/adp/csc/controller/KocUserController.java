package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.qry.PCUserTagQry;
import com.ly.adp.csc.entities.qry.UserTagQry;
import com.ly.adp.csc.entities.vo.koc.PCUserTagInfoVO;
import com.ly.adp.csc.entities.vo.koc.UserTagInfoVO;
import com.ly.adp.csc.service.IKocUserService;
import com.ly.mp.component.entities.ListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * KOC用户查询控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@Api(value = "KOC用户查询服务", tags = "KOC用户查询服务")
@RequestMapping(value = "/ly/adp/csc/koc/user", produces = { MediaType.APPLICATION_JSON_VALUE })
public class KocUserController {

    @Autowired
    private IKocUserService kocUserService;

    @ApiOperation(value = "用户标签信息查询（企微端）", notes = "根据关键词查询用户标签信息")
    @PostMapping(value = "/weChat/search.do")
    public ListResult<UserTagInfoVO> searchUsers(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody UserTagQry userTagQry) {
        return kocUserService.searchWechatUsers(userTagQry, authentication);
    }


    @ApiOperation(value = "用户信息查询（PC）", notes = "查询用户标签信息")
    @PostMapping(value = "/pc/search.do")
    public ListResult<PCUserTagInfoVO> pcSearchUsers(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody PCUserTagQry pcUserTagQry) {
        return kocUserService.queryUserList(pcUserTagQry, authentication);
    }
}
