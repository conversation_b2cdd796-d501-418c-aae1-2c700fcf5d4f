package com.ly.adp.csc.controller;


import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacUserGroupDetailService;
import com.ly.adp.csc.service.ISacUserGroupService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 用户分组主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@RestController
@Api(value = "CSC用户分组服务", tags = "CSC用户分组服务")
@RequestMapping(value = "/ly/adp/csc/sacUserGroup", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacUserGroupController {
	@Autowired
	ISacUserGroupService sacUserGroupService;
	@Autowired
	ISacUserGroupDetailService sacUserGroupDetailService;
	
	@ApiOperation(value = "用户分组主表查询", notes = "用户分组主表查询")
	@PostMapping(value = "/sacUserGroupquery.do")
	public ListResult<Map<String, Object>> sacUserGroupFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacUserGroupService.sacUserGroupFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "用户分组主表维护", notes = "用户分组主表维护")
	@PostMapping(value = "/sacUserGroupsave.do")
	public EntityResult<Map<String, Object>> sacUserGroupSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacUserGroupService.sacUserGroupSaveInfo(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "用户分组明细表查询", notes = "用户分组明细表查询")
	@PostMapping(value = "/sacUserGroupDetailquery.do")
	public ListResult<Map<String, Object>> sacUserGroupDetailFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacUserGroupDetailService.sacUserGroupDetailFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "用户分组明细表维护", notes = "用户分组明细表维护")
	@PostMapping(value = "/sacUserGroupDetailsave.do")
	public EntityResult<Map<String, Object>> sacUserGroupDetailSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacUserGroupDetailService.sacUserGroupDetailSaveInfo(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "用户分组删除", notes = "用户分组明细表删除")
	@PostMapping(value = "/sacUserGroupDelete.do")
	public OptResult sacUserGroupDelete(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> {return sacUserGroupDetailService.sacUserGroupDelete(dateInfo, authentication);}).result();
	}

	@ApiOperation(value = "用户分组明细表删除", notes = "用户分组明细表删除")
	@PostMapping(value = "/sacUserGroupDetailDelete.do")
	public OptResult sacUserGroupDetailDelete(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) List<Map<String, Object>> list) {
		return BusicenInvoker.doOpt(() -> {return sacUserGroupDetailService.sacUserGroupDetailDelete(list, authentication);}).result();
	}
}
