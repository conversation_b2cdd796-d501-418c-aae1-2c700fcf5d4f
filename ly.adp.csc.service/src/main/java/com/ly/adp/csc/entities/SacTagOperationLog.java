package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 标签操作日志表
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
@TableName("t_sac_tag_operation_log")
public class SacTagOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId("LOG_ID")
    private String logId;

    /**
     * 操作类型,见值列表 TAG_OP_TYPE
     */
    @TableField("OPERATION_TYPE")
    private Integer operationType;

    /**
     * 操作目标ID（标签ID/类型ID/备注ID）
     */
    @TableField("TARGET_ID")
    private String targetId;

    /**
     * 用户smartId
     */
    @TableField("SMART_ID")
    private String smartId;

    /**
     * 批量操作ID
     */
    @TableField("BATCH_ID")
    private String batchId;

    /**
     * 操作人
     */
    @TableField("OPERATOR")
    private String operator;

    /**
     * 操作时间
     */
    @TableField(value = "OPERATION_DATE", fill = FieldFill.INSERT)
    private LocalDateTime operationDate;

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public LocalDateTime getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(LocalDateTime operationDate) {
        this.operationDate = operationDate;
    }

    @Override
    public String toString() {
        return "SacTagOperationLog{" +
                "logId='" + logId + '\'' +
                ", operationType=" + operationType +
                ", targetId='" + targetId + '\'' +
                ", smartId='" + smartId + '\'' +
                ", batchId='" + batchId + '\'' +
                ", operator='" + operator + '\'' +
                ", operationDate=" + operationDate +
                '}';
    }

}
