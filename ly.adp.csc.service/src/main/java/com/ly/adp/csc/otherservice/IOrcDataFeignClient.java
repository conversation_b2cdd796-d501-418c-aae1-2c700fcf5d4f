package com.ly.adp.csc.otherservice;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.ly.mp.component.entities.OptResult;

@FeignClient(name = "${refer.name.adp.orc:ly.adp.orc}", url = "${refer.url.adp.orc}")
public interface IOrcDataFeignClient {
	// 线索分配调整-更新销售顾问
	@PostMapping("/ly/adp/orc/veBuSaleOrderToC/updateSalesAdvisor.do")
	OptResult updateSalesAdvisor(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo);
}