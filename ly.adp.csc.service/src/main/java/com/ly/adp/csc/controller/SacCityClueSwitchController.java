package com.ly.adp.csc.controller;


import java.util.Map;

import java.util.Map;

import com.ly.adp.csc.service.ISacCityClueSwitchService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 城市线索开关表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@RestController
@Api(value = "城市线索开关表服务", tags = "城市线索开关表服务")
@RequestMapping(value = "/ly/adp/csc/sacCityClueSwitch", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacCityClueSwitchController {
	@Autowired
	ISacCityClueSwitchService sacCityClueSwitchService;
	
	@ApiOperation(value = "城市线索开关表查询", notes = "城市线索开关表查询")
	@PostMapping(value = "/sacCityClueSwitchquery.do")
	public ListResult<Map<String, Object>> sacCityClueSwitchFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacCityClueSwitchService.sacCityClueSwitchFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "城市线索开关表维护", notes = "城市线索开关表维护")
	@PostMapping(value = "/sacCityClueSwitchsave.do")
	public EntityResult<Map<String, Object>> sacCityClueSwitchSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacCityClueSwitchService.sacCityClueSwitchSaveInfo(dateInfo, authentication);}).result();
	}
}
