package com.ly.adp.csc.entities.vo;

import java.io.Serializable;
import java.util.Objects;

/**
 *
 * 客戶试驾信息VO
 * <AUTHOR>
 */
public class CustomerTestDriveVO implements Serializable {

    private static final long serialVersionUID = -8963662147747239431L;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * smartID
     */
    private String smartID;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 门店
     */
    private String dlrName;

    /**
     * 车型
     */
    private String smallCarTypeName;

    /**
     * 产品专家
     */
    private String salesConsultantName;

    /**
     * 试驾类型
     */
    private String testType;

    /**
     * 试驾类型:已开始、已结束...
     */
    private String testStatus;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 开始时间
     */
    private String starTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 接待时间
     */
    private String receiverTime;

    @Override
    public String toString() {
        return "CustomerTestDriveVO{" +
                "plateNumber='" + plateNumber + '\'' +
                ", smartID='" + smartID + '\'' +
                ", customerName='" + customerName + '\'' +
                ", dlrName='" + dlrName + '\'' +
                ", smallCarTypeName='" + smallCarTypeName + '\'' +
                ", salesConsultantName='" + salesConsultantName + '\'' +
                ", testType='" + testType + '\'' +
                ", testStatus='" + testStatus + '\'' +
                ", vin='" + vin + '\'' +
                ", starTime='" + starTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", receiverTime='" + receiverTime + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerTestDriveVO that = (CustomerTestDriveVO) o;
        return Objects.equals(plateNumber, that.plateNumber) && Objects.equals(smartID, that.smartID) && Objects.equals(customerName, that.customerName) && Objects.equals(dlrName, that.dlrName) && Objects.equals(smallCarTypeName, that.smallCarTypeName) && Objects.equals(salesConsultantName, that.salesConsultantName) && Objects.equals(testType, that.testType) && Objects.equals(testStatus, that.testStatus) && Objects.equals(vin, that.vin) && Objects.equals(starTime, that.starTime) && Objects.equals(endTime, that.endTime) && Objects.equals(receiverTime, that.receiverTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(plateNumber, smartID, customerName, dlrName, smallCarTypeName, salesConsultantName, testType, testStatus, vin, starTime, endTime, receiverTime);
    }

    public CustomerTestDriveVO() {
    }

    public CustomerTestDriveVO(String plateNumber, String smartID, String customerName, String dlrName, String smallCarTypeName, String salesConsultantName, String testType, String testStatus, String vin, String starTime, String endTime, String receiverTime) {
        this.plateNumber = plateNumber;
        this.smartID = smartID;
        this.customerName = customerName;
        this.dlrName = dlrName;
        this.smallCarTypeName = smallCarTypeName;
        this.salesConsultantName = salesConsultantName;
        this.testType = testType;
        this.testStatus = testStatus;
        this.vin = vin;
        this.starTime = starTime;
        this.endTime = endTime;
        this.receiverTime = receiverTime;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getSmartID() {
        return smartID;
    }

    public void setSmartID(String smartID) {
        this.smartID = smartID;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }

    public String getSmallCarTypeName() {
        return smallCarTypeName;
    }

    public void setSmallCarTypeName(String smallCarTypeName) {
        this.smallCarTypeName = smallCarTypeName;
    }

    public String getSalesConsultantName() {
        return salesConsultantName;
    }

    public void setSalesConsultantName(String salesConsultantName) {
        this.salesConsultantName = salesConsultantName;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public String getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(String testStatus) {
        this.testStatus = testStatus;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getStarTime() {
        return starTime;
    }

    public void setStarTime(String starTime) {
        this.starTime = starTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getReceiverTime() {
        return receiverTime;
    }

    public void setReceiverTime(String receiverTime) {
        this.receiverTime = receiverTime;
    }
}
