package com.ly.adp.csc.entities.qry;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/30
 * @Version 1.0.0
 **/
@ApiModel("公共审批查询入参")
public class CommonAuditQry implements Serializable {

    private static final long serialVersionUID = 6066477325842834313L;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "审核节点编码")
    private String nodeCode;

    @ApiModelProperty(value = "审核状态编码，多个状态以逗号分隔，例如'1,2'表示审核通过和驳回")
    private String shStatus;

    @ApiModelProperty(value = "所属组织")
    private String orgCode;

    @ApiModelProperty(value = "单据业务类型,例如AGENT")
    private String businessType;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动开始时间")
    private String beginTimeMin;

    @ApiModelProperty(value = "活动开始时间")
    private String beginTimeMax;

    @ApiModelProperty(value = "活动大类")
    private String activityTypeCode;

    @ApiModelProperty(value = "活动类型")
    private String inCreateTypeCode;

    @ApiModelProperty(value = "活动小类")
    private String activitySubtypeCode;

    @ApiModelProperty(value = "区域id")
    private String areaId;

    @ApiModelProperty(value = "代理商id")
    private String agentId;

    @ApiModelProperty(value = "城市公司id")
    private String companyId;

    @ApiModelProperty(value = "活动门店")
    private String dlrCodeIn;

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getShStatus() {
        return shStatus;
    }

    public void setShStatus(String shStatus) {
        this.shStatus = shStatus;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getBeginTimeMin() {
        return beginTimeMin;
    }

    public void setBeginTimeMin(String beginTimeMin) {
        this.beginTimeMin = beginTimeMin;
    }

    public String getBeginTimeMax() {
        return beginTimeMax;
    }

    public void setBeginTimeMax(String beginTimeMax) {
        this.beginTimeMax = beginTimeMax;
    }

    public String getActivityTypeCode() {
        return activityTypeCode;
    }

    public void setActivityTypeCode(String activityTypeCode) {
        this.activityTypeCode = activityTypeCode;
    }

    public String getInCreateTypeCode() {
        return inCreateTypeCode;
    }

    public void setInCreateTypeCode(String inCreateTypeCode) {
        this.inCreateTypeCode = inCreateTypeCode;
    }

    public String getActivitySubtypeCode() {
        return activitySubtypeCode;
    }

    public void setActivitySubtypeCode(String activitySubtypeCode) {
        this.activitySubtypeCode = activitySubtypeCode;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getDlrCodeIn() {
        return dlrCodeIn;
    }

    public void setDlrCodeIn(String dlrCodeIn) {
        this.dlrCodeIn = dlrCodeIn;
    }
}
