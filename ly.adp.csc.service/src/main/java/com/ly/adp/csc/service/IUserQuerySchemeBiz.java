package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.UserQueryScheme;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.Map;

/**
 * <p>
 * 用户查询 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
public interface IUserQuerySchemeBiz extends IService<UserQueryScheme> {
    ListResult<Map<String, Object>> query(String token, Map<String, Object> map);
     OptResult updateScheme(Map<String, Object> dateInfoon, String token);
    OptResult delScheme(Map<String, Object> dateInfoon, String token);

    OptResult script(String sql);
}
