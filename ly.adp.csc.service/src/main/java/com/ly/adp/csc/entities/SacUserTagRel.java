package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.adp.csc.enums.UserRelationTypeEnum;
import com.ly.mp.component.helper.StringHelper;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户标签关系表（标签、达人类型、备注）
 * </p>
 *
 * <AUTHOR> System
 * @since 2025-01-25
 */
@TableName("t_sac_user_tag_rel")
public class SacUserTagRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @TableId("REL_ID")
    private String relId;

    /**
     * 用户smartId
     */
    @TableField("SMART_ID")
    private String smartId;

    /**
     * 用户手机号
     */
    @TableField(value = "PHONE")
    private String phone;

    /**
     * 用户昵称
     */
    @TableField(value = "NICK_NAME")
    private String nickName;

    /**
     * 关系类型：1-标签 2-达人类型 3-备注
     */
    @TableField("REL_TYPE")
    private Integer relType;

    /**
     * 引用ID（标签ID/类型ID）
     */
    @TableField("REF_ID")
    private String refId;

    /**
     * 备注内容（仅当REL_TYPE=3时有效）
     */
    @TableField("REMARK_CONTENT")
    private String remarkContent;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    // ========== 查询相关字段（不映射到数据库） ==========

    /**
     * smartId列表（用于批量查询）
     */
    @TableField(exist = false)
    private List<String> smartIdList;

    /**
     * 关系类型枚举（用于查询条件）
     */
    @TableField(exist = false)
    private UserRelationTypeEnum relationTypeEnum;

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getRelType() {
        return relType;
    }

    public void setRelType(Integer relType) {
        this.relType = relType;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getRemarkContent() {
        return remarkContent;
    }

    public void setRemarkContent(String remarkContent) {
        this.remarkContent = remarkContent;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public List<String> getSmartIdList() {
        return smartIdList;
    }

    public void setSmartIdList(List<String> smartIdList) {
        this.smartIdList = smartIdList;
    }

    public UserRelationTypeEnum getRelationTypeEnum() {
        return relationTypeEnum;
    }

    public void setRelationTypeEnum(UserRelationTypeEnum relationTypeEnum) {
        this.relationTypeEnum = relationTypeEnum;
        // 同步设置relType字段
        if (relationTypeEnum != null) {
            this.relType = relationTypeEnum.getCode();
        }
    }

    @Override
    public String toString() {
        return "SacUserTagRel{" +
                "relId='" + relId + '\'' +
                ", smartId='" + smartId + '\'' +
                ", phone='" + phone + '\'' +
                ", nickName='" + nickName + '\'' +
                ", relType=" + relType +
                ", refId='" + refId + '\'' +
                ", remarkContent='" + remarkContent + '\'' +
                ", creator='" + creator + '\'' +
                ", createdName='" + createdName + '\'' +
                ", createdDate=" + createdDate +
                ", modifier='" + modifier + '\'' +
                ", modifyName='" + modifyName + '\'' +
                ", lastUpdatedDate=" + lastUpdatedDate +
                '}';
    }

    // ========== 查询构建方法 ==========

    /**
     * 构建查询条件
     * @return QueryWrapper
     */
    public LambdaQueryWrapper<SacUserTagRel> buildQueryWrapper() {
        LambdaQueryWrapper<SacUserTagRel> queryWrapper = new LambdaQueryWrapper<>();

        // 单个smartId查询
        if (!StringHelper.IsEmptyOrNull(this.smartId)) {
            queryWrapper.eq(SacUserTagRel::getSmartId, this.smartId);
        }

        // 批量smartId查询
        if (this.smartIdList != null && !this.smartIdList.isEmpty()) {
            queryWrapper.in(SacUserTagRel::getSmartId, this.smartIdList);
        }

        // 关系类型查询
        if (this.relType != null) {
            queryWrapper.eq(SacUserTagRel::getRelType, this.relType);
        }

        // 引用ID查询
        if (!StringHelper.IsEmptyOrNull(this.refId)) {
            queryWrapper.eq(SacUserTagRel::getRefId, this.refId);
        }

        // 手机号查询
        if (!StringHelper.IsEmptyOrNull(this.phone)) {
            queryWrapper.eq(SacUserTagRel::getPhone, this.phone);
        }

        // 用户昵称模糊查询
        if (!StringHelper.IsEmptyOrNull(this.nickName)) {
            queryWrapper.like(SacUserTagRel::getNickName, this.nickName);
        }

        // 备注内容模糊查询
        if (!StringHelper.IsEmptyOrNull(this.remarkContent)) {
            queryWrapper.like(SacUserTagRel::getRemarkContent, this.remarkContent);
        }

        // 创建人查询
        if (!StringHelper.IsEmptyOrNull(this.creator)) {
            queryWrapper.eq(SacUserTagRel::getCreator, this.creator);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(SacUserTagRel::getCreatedDate);

        return queryWrapper;
    }

    /**
     * 构建批量查询条件（根据smartId列表和关系类型）
     * @param smartIds smartId列表
     * @param relationType 关系类型
     * @return LambdaQueryWrapper
     */
    public static LambdaQueryWrapper<SacUserTagRel> buildBatchQueryWrapper(List<String> smartIds, UserRelationTypeEnum relationType) {
        LambdaQueryWrapper<SacUserTagRel> queryWrapper = new LambdaQueryWrapper<>();

        // smartId列表查询
        if (smartIds != null && !smartIds.isEmpty()) {
            queryWrapper.in(SacUserTagRel::getSmartId, smartIds);
        }

        // 关系类型查询
        if (relationType != null) {
            queryWrapper.eq(SacUserTagRel::getRelType, relationType.getCode());
        }

        // 按smartId和创建时间排序
        queryWrapper.orderByAsc(SacUserTagRel::getSmartId).orderByDesc(SacUserTagRel::getCreatedDate);

        return queryWrapper;
    }

    /**
     * 构建标签查询条件
     * @param smartIds smartId列表
     * @return QueryWrapper
     */
    public static LambdaQueryWrapper<SacUserTagRel> buildTagQueryWrapper(List<String> smartIds) {
        return buildBatchQueryWrapper(smartIds, UserRelationTypeEnum.TAG);
    }

    /**
     * 构建达人类型查询条件
     * @param smartIds smartId列表
     * @return QueryWrapper
     */
    public static LambdaQueryWrapper<SacUserTagRel> buildExpertTypeQueryWrapper(List<String> smartIds) {
        return buildBatchQueryWrapper(smartIds, UserRelationTypeEnum.EXPERT_TYPE);
    }

    /**
     * 构建备注查询条件
     * @param smartIds smartId列表
     * @return QueryWrapper
     */
    public static LambdaQueryWrapper<SacUserTagRel> buildRemarkQueryWrapper(List<String> smartIds) {
        return buildBatchQueryWrapper(smartIds, UserRelationTypeEnum.REMARK);
    }

    // ========== 便捷构建方法 ==========

    /**
     * 创建标签关系实例
     * @param smartId smartId
     * @param tagId 标签ID
     * @return SacUserTagRel
     */
    public static SacUserTagRel createTagRelation(String smartId, String tagId) {
        SacUserTagRel relation = new SacUserTagRel();
        relation.setSmartId(smartId);
        relation.setRefId(tagId);
        relation.setRelationTypeEnum(UserRelationTypeEnum.TAG);
        return relation;
    }

    /**
     * 创建达人类型关系实例
     * @param smartId smartId
     * @param expertTypeId 达人类型ID
     * @return SacUserTagRel
     */
    public static SacUserTagRel createExpertTypeRelation(String smartId, String expertTypeId) {
        SacUserTagRel relation = new SacUserTagRel();
        relation.setSmartId(smartId);
        relation.setRefId(expertTypeId);
        relation.setRelationTypeEnum(UserRelationTypeEnum.EXPERT_TYPE);
        return relation;
    }

    /**
     * 创建备注关系实例
     * @param smartId smartId
     * @param remarkContent 备注内容
     * @return SacUserTagRel
     */
    public static SacUserTagRel createRemarkRelation(String smartId, String remarkContent) {
        SacUserTagRel relation = new SacUserTagRel();
        relation.setSmartId(smartId);
        relation.setRemarkContent(remarkContent);
        relation.setRelationTypeEnum(UserRelationTypeEnum.REMARK);
        return relation;
    }
}
