package com.ly.adp.csc.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * <p>
 * 员工履历表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */

public class TUscMdmEmployeeCareer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工履历ID
     */
    @TableId("EMPLOYEE_ID")
    private String employeeId;

    /**
     * 就职公司
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 职位
     */
    @TableField("DUTY")
    private String duty;

    /**
     * 薪资
     */
    @TableField("SALARY")
    private String salary;

    /**
     * 在职日期开始
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    @TableField("ON_DUTY_BEGIN")
    private LocalDateTime onDutyBegin;

    /**
     * 就职日期结束
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    @TableField("ON_DUTY_END")
    private LocalDateTime onDutyEnd;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

   /**
     * 职员id
     * @return
     */

    @TableField("EMP_ID")
    private String empId;

    public String getEmpCareerId() {
        return empCareerId;
    }

    public void setEmpCareerId(String empCareerId) {
        this.empCareerId = empCareerId;
    }

    /**
     * 履历id
     */
    @TableField("EMP_CAREER_ID")
    private String empCareerId;


    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getSalary() {
        return salary;
    }

    public void setSalary(String salary) {
        this.salary = salary;
    }

    public LocalDateTime getOnDutyBegin() {
        return onDutyBegin;
    }

    public void setOnDutyBegin(LocalDateTime onDutyBegin) {
        this.onDutyBegin = onDutyBegin;
    }

    public LocalDateTime getOnDutyEnd() {
        return onDutyEnd;
    }

    public void setOnDutyEnd(LocalDateTime onDutyEnd) {
        this.onDutyEnd = onDutyEnd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "TUscMdmEmployeeCareer{" +
        "employeeId=" + employeeId +
        ", companyName=" + companyName +
        ", duty=" + duty +
        ", salary=" + salary +
        ", onDutyBegin=" + onDutyBegin +
        ", onDutyEnd=" + onDutyEnd +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
