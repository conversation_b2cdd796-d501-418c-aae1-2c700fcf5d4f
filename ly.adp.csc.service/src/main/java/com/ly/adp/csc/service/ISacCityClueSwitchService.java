package com.ly.adp.csc.service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacCityClueSwitch;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 城市线索开关表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
public interface ISacCityClueSwitchService extends IService<SacCityClueSwitch> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacCityClueSwitchFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacCityClueSwitchSaveInfo(Map<String, Object> dataInfo,String token);
}
