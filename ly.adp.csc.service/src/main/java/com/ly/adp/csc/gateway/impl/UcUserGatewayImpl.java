package com.ly.adp.csc.gateway.impl;

import com.ly.adp.csc.entities.dto.uc.UcResponseDTO;
import com.ly.adp.csc.entities.dto.uc.UcUserInfoDTO;
import com.ly.adp.csc.gateway.UcUserGateway;
import com.ly.adp.csc.otherservice.UcUserFeignClient;
import com.ly.mp.busicen.common.context.BusicenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * UC用户中心网关实现类
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class UcUserGatewayImpl implements UcUserGateway {

    private static final Logger log = LoggerFactory.getLogger(UcUserGatewayImpl.class);

    @Autowired
    private UcUserFeignClient ucUserFeignClient;

    @Override
    public List<UcUserInfoDTO> getUsersBySmartIds(List<String> smartIdList) {
        return callUcInterface(smartIdList, "smartIdList", ucUserFeignClient::getUsersBySmartIds);
    }

    @Override
    public List<UcUserInfoDTO> getUsersByMobiles(List<String> mobiles) {
        return callUcInterface(mobiles, "mobiles", ucUserFeignClient::getUsersByMobiles);
    }

    /**
     * 调用UC接口的通用方法
     * @param params 查询参数列表
     * @param paramName 参数名称（用于日志）
     * @param apiCall UC接口调用函数
     * @return 用户信息列表
     */
    private List<UcUserInfoDTO> callUcInterface(List<String> params, String paramName,
                                               Function<List<String>, UcResponseDTO<UcUserInfoDTO>> apiCall) {
        // 参数校验
        if (params == null || params.isEmpty()) {
            return new ArrayList<>();
        }

        log.info("开始调用UC接口查询用户信息，{}: {}, 数量: {}", paramName, params, params.size());

        try {
            // 调用UC接口并计时
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            UcResponseDTO<UcUserInfoDTO> ucResponse = apiCall.apply(params);
            stopWatch.stop();

            log.info("UC接口调用完成，耗时: {}ms, ucResponse: {}", stopWatch.getTotalTimeMillis(), ucResponse);

            return processUcResponse(ucResponse, params, paramName);

        } catch (Exception e) {
            log.error("调用UC接口查询用户信息异常，{}: {}, 异常信息: {}", paramName, params, e.getMessage(), e);
            throw BusicenException.create("查询用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 处理UC接口响应
     * @param ucResponse UC接口响应
     * @param params 查询参数
     * @param paramName 参数名称
     * @return 用户信息列表
     */
    private List<UcUserInfoDTO> processUcResponse(UcResponseDTO<UcUserInfoDTO> ucResponse,
                                                 List<String> params, String paramName) {
        // 空值检查
        if (ucResponse == null) {
            log.warn("UC接口返回null响应，{}: {}", paramName, params);
            return new ArrayList<>();
        }

        // 成功响应处理
        if (ucResponse.isSuccess()) {
            List<UcUserInfoDTO> userData = ucResponse.getData();
            int resultCount = userData != null ? userData.size() : 0;
            log.info("UC接口调用成功，{}: {}, 返回用户数: {}", paramName, params, resultCount);
            return userData != null ? userData : new ArrayList<>();
        }

        // 失败响应处理
        log.warn("UC接口返回失败状态，{}: {}, status: {}, msg: {}, code: {}",
                paramName, params, ucResponse.getStatus(), ucResponse.getMsg(), ucResponse.getCode());
        return new ArrayList<>();
    }
}
