package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacServerClass;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 服务类别 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface ISacServerClassService extends IService<SacServerClass> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacServerClassFindInfo(Map<String, Object> dataInfo,String token);
}
