package com.ly.adp.csc.entities.dto;

import java.io.Serializable;

/**
 * @Description: 接口开关
 * @Author: rik.ren
 * @Date: 2025/1/6 15:03
 **/
public class SwitchStatus implements Serializable {
    private String interfaceName;
    private String statusName;
    private String timeLine;

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getTimeLine() {
        return timeLine;
    }

    public void setTimeLine(String timeLine) {
        this.timeLine = timeLine;
    }
}
