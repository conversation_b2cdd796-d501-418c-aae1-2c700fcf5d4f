//package com.ly.adp.csc.controller;
//
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.google.common.net.HttpHeaders;
//import com.ly.adp.csc.entities.dto.koc.TagCreateRequestDTO;
//import com.ly.adp.csc.entities.vo.koc.TagInfoVO;
//import com.ly.adp.csc.service.IKocTagService;
//import com.ly.mp.busicen.common.context.BusicenInvoker;
//import com.ly.mp.component.entities.EntityResult;
//import com.ly.mp.component.entities.ListResult;
//import com.ly.mp.component.entities.OptResult;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//
///**
// * <p>
// * KOC标签管理控制器
// * </p>
// *
// * <AUTHOR> System
// * @since 2025-01-25
// */
//@RestController
//@Api(value = "KOC标签管理服务", tags = "KOC标签管理服务")
//@RequestMapping(value = "/ly/adp/csc/koc/tag", produces = { MediaType.APPLICATION_JSON_VALUE })
//public class KocTagController {
//
//    @Autowired
//    private IKocTagService kocTagService;
//
//    @ApiOperation(value = "获取标签树", notes = "获取标签树形结构")
//    @PostMapping(value = "/getTagTree.do")
//    public List<TagInfoVO> getTagTree(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocTagService.getTagTree(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取标签扁平列表", notes = "获取标签扁平列表（缓存优先）")
//    @PostMapping(value = "/getTagFlatList.do")
//    public List<TagInfoVO> getTagFlatList(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocTagService.getTagFlatList(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "标签列表查询", notes = "分页查询标签管理列表")
//    @PostMapping(value = "/queryTagList.do")
//    public ListResult<Map<String, Object>> queryTagList(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody(required = false) Map<String, Object> dataInfo) {
//        return BusicenInvoker.doList(() -> {
//            return kocTagService.queryTagList(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "创建标签", notes = "创建新标签")
//    @PostMapping(value = "/createTag.do")
//    public EntityResult<Map<String, Object>> createTag(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody TagCreateRequestDTO createRequest) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocTagService.createTag(createRequest, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "更新标签", notes = "更新标签信息")
//    @PostMapping(value = "/updateTag.do")
//    public EntityResult<Map<String, Object>> updateTag(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocTagService.updateTag(dataInfo, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "删除标签", notes = "删除单个标签")
//    @PostMapping(value = "/deleteTag.do")
//    public OptResult deleteTag(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            String tagId = (String) dataInfo.get("tagId");
//            return kocTagService.deleteTag(tagId, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "批量删除标签", notes = "批量删除标签")
//    @PostMapping(value = "/batchDeleteTags.do")
//    public OptResult batchDeleteTags(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            @SuppressWarnings("unchecked")
//            List<String> tagIds = (List<String>) dataInfo.get("tagIds");
//            return kocTagService.batchDeleteTags(tagIds, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "更新标签状态", notes = "上架/下架标签")
//    @PostMapping(value = "/updateTagStatus.do")
//    public OptResult updateTagStatus(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            String tagId = (String) dataInfo.get("tagId");
//            Integer status = (Integer) dataInfo.get("status");
//            return kocTagService.updateTagStatus(tagId, status, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "批量导入标签", notes = "批量导入标签")
//    @PostMapping(value = "/batchImportTags.do")
//    public OptResult batchImportTags(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
//            @RequestBody Map<String, Object> dataInfo) {
//        return BusicenInvoker.doOpt(() -> {
//            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> tagList = (List<Map<String, Object>>) dataInfo.get("tagList");
//            return kocTagService.batchImportTags(tagList, authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "获取标签统计", notes = "获取标签统计信息")
//    @PostMapping(value = "/getTagStatistics.do")
//    public List<Map<String, Object>> getTagStatistics(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doEntity(() -> {
//            return kocTagService.getTagStatistics(authentication);
//        }).result();
//    }
//
//    @ApiOperation(value = "刷新标签缓存", notes = "刷新标签缓存")
//    @PostMapping(value = "/refreshTagCache.do")
//    public OptResult refreshTagCache(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doOpt(() -> {
//            return kocTagService.refreshTagCache(authentication);
//        }).result();
//    }
//
//    // ========== 企微端专用接口 ==========
//
//    @ApiOperation(value = "获取标签列表（企微端）", notes = "获取所有可用标签列表（企微端专用）")
//    @PostMapping(value = "/wechat/getTagList.do")
//    public ListResult<TagInfoVO> getWechatTagList(
//            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication) {
//        return BusicenInvoker.doList(() -> {
//            List<TagInfoVO> tags = kocTagService.getTagFlatList(authentication);
//            ListResult<TagInfoVO> result = new ListResult<>();
//            result.setData(tags);
//            result.setTotal(tags.size());
//            result.setSuccess(true);
//            return result;
//        }).result();
//    }
//}
