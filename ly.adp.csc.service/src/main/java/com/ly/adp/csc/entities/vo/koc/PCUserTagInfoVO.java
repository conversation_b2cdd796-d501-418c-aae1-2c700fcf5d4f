package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PC用户标签信息VO
 * <AUTHOR>
 * @since 2025/7/28
 **/
public class PCUserTagInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户smartId
     */
    private String smartId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 标签列表
     */
    private List<TagVO> tags;

    /**
     * 达人类型列表
     */
    private List<ExpertTypeVO> expertTypes;

    /**
     * 备注列表
     */
    private List<RemarkVO> remarks;

    /**
     * 标签最近一次修改时间
     */
    private LocalDateTime tagUpdatedTime;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<TagVO> getTags() {
        return tags;
    }

    public void setTags(List<TagVO> tags) {
        this.tags = tags;
    }

    public List<ExpertTypeVO> getExpertTypes() {
        return expertTypes;
    }

    public void setExpertTypes(List<ExpertTypeVO> expertTypes) {
        this.expertTypes = expertTypes;
    }

    public List<RemarkVO> getRemarks() {
        return remarks;
    }

    public void setRemarks(List<RemarkVO> remarks) {
        this.remarks = remarks;
    }

    public LocalDateTime getTagUpdatedTime() {
        return tagUpdatedTime;
    }

    public void setTagUpdatedTime(LocalDateTime tagUpdatedTime) {
        this.tagUpdatedTime = tagUpdatedTime;
    }

    @Override
    public String toString() {
        return "PCUserTagInfoVO{" +
                "smartId='" + smartId + '\'' +
                ", nickName='" + nickName + '\'' +
                ", phone='" + phone + '\'' +
                ", avatar='" + avatar + '\'' +
                ", tags=" + tags +
                ", expertTypes=" + expertTypes +
                ", remarks=" + remarks +
                ", tagUpdatedTime=" + tagUpdatedTime +
                '}';
    }
}
