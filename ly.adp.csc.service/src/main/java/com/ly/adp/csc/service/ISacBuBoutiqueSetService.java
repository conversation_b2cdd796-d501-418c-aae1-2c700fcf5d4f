package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.entities.SacBuBoutiqueSet;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 精品基本数据维护表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
public interface ISacBuBoutiqueSetService extends IService<SacBuBoutiqueSet> {
	/**
	 * 分页查询
	 * @param dataInfo
	 * @param token
	 * @return
	 */
	ListResult<Map<String, Object>> sacBuBoutiqueSetFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param dataInfo
	 * @return
	 */
	OptResult sacBuBoutiqueSetSaveInfo(ParamBase<Map<String, Object>> dataInfo, String token);

	EntityResult<Map<String, Object>> insertSacBuBoutiqueDlrInfo(Map<String, Object> dataInfo, String token);

	ListResult<Map<String, Object>> querySacBuBoutiqueDlrInfo(Map<String, Object> dataInfo, String token);

	ListResult<Map<String, Object>> sacBuBoutiqueDetailQuery(Map<String, Object> dateInfo, String authentication);

	/**
	 * 精品基础数据批量上传
	 *
	 * @param info
	 * @return
	 */
	Result sacBuBoutiqueSetImport(String authentication, MultipartFile uploadfile);
	/**
	 * 精品基础数据批量上传
	 *
	 * @param info
	 * @return
	 */
	Result sacBuBoutiqueDetailImport(String authentication, MultipartFile uploadfile);

	OptResult sacBuBoutiquePutOnOrDownUpdate(Map<String, Object> dataInfo, String authentication);

	EntityResult<Map<String, Object>> sacBuBoutiqueDetailInsert(Map<String, Object> dataInfo, String authentication);

	OptResult sacBuBoutiquePutDownAll(Map<String, Object> dataInfo, String authentication);

	OptResult sacBuBoutiqueDetailUpdate(Map<String, Object> dataInfo, String authentication);
	
	/**
	 * 精品明细单表查询
	 * @param dataInfo
	 * @param authentication
	 * @return
	 */
	ListResult<Map<String, Object>> querySacBuBoutiqueDetail(Map<String, Object> dataInfo, String authentication);

	OptResult sacBuBoutiquePutOnSave(Map<String, Object> dateInfo, String authentication);

	ListResult<Map<String, Object>> querySacBuBoutiqueDlrInfoOne(Map<String, Object> dataInfo, String token);

	ListResult<Map<String, Object>> querySacBuBoutiqueReceiving(ParamPage<Map<String, Object>> dateInfo, String authentication);

    OptResult exportSacBuBoutiqueDetailQuery(Map<String, Object> info, HttpServletResponse response, String authentication);

	OptResult exportSacBuBoutiqueDetailQueryByElchee(Map<String, Object> info, HttpServletResponse response, String authentication);

	ListResult<Map<String, Object>> querySacBuBoutiqueStatement(ParamPage<Map<String, Object>> dateInfo, String authentication);

	OptResult exportquerySacBuBoutiqueStatement(ParamPage<Map<String, Object>> info, HttpServletResponse response, String authentication);
}
