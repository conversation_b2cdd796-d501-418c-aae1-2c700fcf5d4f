package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.BuLogout;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 退网信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface IBuLogoutBiz extends IService<BuLogout> {

    ListResult<Map<String, Object>> sacBuLogOutQueryByDlr(ParamPage<Map<String, Object>> dateInfo, String authentication);

    OptResult sacBuLogOutSave(ParamBase<Map<String, Object>> paramBase, String authentication);

    ListResult<Map<String, Object>> sacBuLogOutQueryByCompanySponsor(ParamPage<Map<String, Object>> dateInfo, String authentication);

    ListResult<Map<String, Object>> sacBuLogOutQueryByAgentSponsor(ParamPage<Map<String, Object>> dateInfo, String authentication);

    ListResult<Map<String, Object>> sacBuLogOutQuery(ParamPage<Map<String, Object>> dateInfo, String authentication);

    EntityResult<Map<String, Object>> sacBuLogOutAppPower(ParamBase<Map<String, Object>> dateInfo, String authentication);

    OptResult sacBuLogOutClearing(ParamBase<Map<String, Object>> dateInfo, String authentication);

    ListResult<Map<String, Object>> sacBuLogOutClearingQueryByDlr(ParamPage<Map<String, Object>> dateInfo, String token);

    OptResult sacBuLogOutAuditByCompany(ParamBase<Map<String, Object>> dateInfo, String authentication);

    OptResult ifDlrLogout(Map<String, Object> param, String authentication);

    OptResult CreatdRecord(Map<String, Object> param,String authentication);

    ListResult<Map<String, Object>> sacBuLogOutRecordQuery(ParamPage<Map<String, Object>> dateInfo, String authentication);

    OptResult exportSacBuLogOutQueryByDlr(ParamPage<Map<String, Object>> info, HttpServletResponse response, String authentication);

    OptResult exportSacBuLogOutClearingQueryByDlr(ParamPage<Map<String, Object>> info, HttpServletResponse response, String authentication);

    OptResult exportsacBuLogOutQueryByDlrLogout(ParamPage<Map<String, Object>> info, HttpServletResponse response, String authentication);

    ListResult<Map<String, Object>> sacBuLogOutQueryByDlrLogout(ParamPage<Map<String, Object>> dateInfo, String authentication);

}
