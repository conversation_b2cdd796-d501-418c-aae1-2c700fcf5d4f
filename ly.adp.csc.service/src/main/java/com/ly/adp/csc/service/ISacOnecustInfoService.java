package com.ly.adp.csc.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnecustInfo;
import com.ly.adp.csc.entities.in.SacOnecustInfoIn;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.List;
import java.util.Map;

public interface ISacOnecustInfoService extends IService<SacOnecustInfo> {
    /**
     * CSC-客户信息查询
     *
     * @param pageInfo
     * @param info
     * @return
     */
    ListResult<Map<String, Object>> sacOnecustInfoFindInfo(SacOnecustInfoIn dataInfo);
    /**
     * CSC-客户信息查询，无分页
     *
     * @param pageInfo
     * @param info
     * @return
     */
    List<Map<String, Object>> sacOnecustInfoFindInfoNoPage(SacOnecustInfoIn dataInfo);

    /**
     * CSC-客户信息维护
     *
     * @param info
     * @return
     */
    EntityResult<Map<String, Object>> sacOnecustInfoSaveInfo(Map<String, Object> dataInfo, String token);

    /**
     * CSC-客户信息变更记录查询
     *
     * @param pageInfo
     * @param info
     * @return
     */
    ListResult<Map<String, Object>> sacOnecustChangeLogFindInfo(SacOnecustInfoIn dataInfo, String token);

    /**
     * CSC_客户信息变更记录定时（接口表到业务表）
     *
     * @param info
     * @return
     */
    void SacOnecustChangeLogDbjob();

    /**
     * 查看是新建还是修改客户信息
     *
     * @param param
     * @param token
     * @return
     */
    Map<String, Object> checkRepeak(Map<String, Object> param, String token);

    /**
     * 客户信息刷新保存
     *
     * @param param
     * @param token
     * @return
     */
    EntityResult<Map<String, Object>> SacOnecustRefresh(Map<String, Object> dataInfo, String token);

    /**
     * CDP参数加密解密
     *
     * @param param
     * @param token
     * @return
     */
    OptResult crackCipher(Map<String, Object> dataInfo);

    EntityResult<Map<String, Object>> updateOneCustInfo(Map<String, Object> dateInfo, String authentication);

    EntityResult<Map<String, Object>> updateOneCustInfoS(Map<String, Object> dateInfo, String authentication);

    EntityResult<Map<String, Object>> getEcToken(String authentication);

    EntityResult<Map<String, Object>> queryVehicleVersions(String authentication);

    EntityResult<Map<String, Object>> queryCalculate(Map<String, Object> dateInfo, String authentication);

    EntityResult<Map<String, Object>> queryFinanceScheme(Map<String, Object> dateInfo, String authentication);

    ListResult<Map<String, Object>> queryOneCustSupplementInfo(Map<String, Object> param);

    EntityResult<Map<String, Object>> getCdpToken(String authentication);

    OptResult customerTagWhitelis(Map<String, Object> dateInfo);

    EntityResult<Map<String, Object>> queryClueLevel(Map<String, Object> dateInfo);

    OptResult updateClueLevel(Map<String, Object> dateInfo);

    List<SacOnecustInfo> querySacOnecustInfo(SacOnecustInfo param, SFunction<SacOnecustInfo, ?>... columns);
}
