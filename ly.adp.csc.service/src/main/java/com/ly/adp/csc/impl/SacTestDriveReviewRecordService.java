package com.ly.adp.csc.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.csc.entities.in.SacTestDriveReviewRecordIn;
import com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper;
import com.ly.adp.csc.service.ISacTestDriveReviewRecordService;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.busicen.common.util.BusicenUtils.SOU;
import com.ly.mp.busicen.common.util.OptResultBuilder;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

@Service
@Transactional
public class SacTestDriveReviewRecordService implements ISacTestDriveReviewRecordService {

	private static final Logger logger = LoggerFactory.getLogger(SacTestDriveReviewRecordService.class);

	@Autowired
	SacTestDriveReviewRecordMapper sacTestDriveReviewRecordMapper;

	@Override
	public ListResult<Map<String, Object>> sacTestDriveReviewRecordFindAll(SacTestDriveReviewRecordIn dataInfo) {
		ListResult<Map<String, Object>> result = new ListResult<>();
		try {
			//参数校验
			if (StringUtils.isEmpty(dataInfo.getTestDriveSheetId()) 
					&& StringUtils.isEmpty(dataInfo.getTestDriveOrderNo())) {
				throw BusicenException.create("ID和单号不能同时为空");
			}
			IPage<Map<String, Object>> page = new Page<>(dataInfo.getPageIndex(), dataInfo.getPageSize());
			Map<String, Object> customerMap = BusicenUtils.entityToMap(dataInfo);
			List<Map<String, Object>> list = sacTestDriveReviewRecordMapper.sacTestDriveReviewRecordFindAll(page, customerMap);
			page.setRecords(list);
			result = BusicenUtils.page2ListResult(page);
		} catch (Exception e) {
			logger.error("sacTestDriveReviewRecordFindAll", e);
			result.setMsg("查询失败");
			result.setResult("0");
			throw e;
		}
		return result;
	}

	@Override
	@Transactional
	public OptResult sacTestDriveReviewRecordSave(SacTestDriveReviewRecordIn dataInfo, String token) {
		OptResult result = OptResultBuilder.create().build();
		try {
			if (StringUtils.isEmpty(dataInfo.getTestDriveSheetId())) {
				throw BusicenException.create("试乘试驾单ID不能为空");
			}
			if (StringUtils.isEmpty(dataInfo.getTestDriveOrderNo())) {
				throw BusicenException.create("试乘试驾单号不能为空");
			}
			if (StringUtils.isEmpty(dataInfo.getTestDriveDesc())) {
				throw BusicenException.create("试乘试驾单跟进内容不能为空");
			}
			Map<String, Object> paramMap = BusicenUtils.entityToMap(dataInfo);
			BusicenUtils.invokeUserInfo(paramMap, SOU.Save, token);
			int count = sacTestDriveReviewRecordMapper.createTestDriveReviewRecordInfo(paramMap);
			if (count > 0) {
				result.setMsg("新增成功");
				result.setResult(String.valueOf(count));
			}
		} catch (Exception e) {
			logger.error("sacTestDriveReviewRecordSave", e);
			throw e;
		}
		return result;
	}
}
