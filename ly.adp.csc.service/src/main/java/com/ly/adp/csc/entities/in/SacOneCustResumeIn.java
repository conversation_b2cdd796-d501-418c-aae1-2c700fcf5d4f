package com.ly.adp.csc.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class SacOneCustResumeIn extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	// 定义参与排序字段
	String columnArr = "CLUE_LEVEL_CODE,BUSS_TIME,CREATED_DATE,LAST_UPDATED_DATE";
	@ApiModelProperty(value = "订单号", required = false, example = "")
	private String saleOrderCode;

	public String getSaleOrderCode() {
		return saleOrderCode;
	}

	public void setSaleOrderCode(String saleOrderCode) {
		this.saleOrderCode = saleOrderCode;
	}

	@ApiModelProperty(value = "排序列", required = false, example = "")
	private String column;
	@ApiModelProperty(value = "排序方式", required = false, example = "")
	private String sorting;
	@ApiModelProperty(value = "客户ID", required = false, example = "")
	private String custId;
	@ApiModelProperty(value = "SMARTID", required = false, example = "")
	private String smartId;
	@ApiModelProperty(value = "意向级别", required = false, example = "")
	private String clueLevelCode;
	@ApiModelProperty(value = "数据所属门店编码", required = false, example = "")
	private String dlrCodeOwner;
	@ApiModelProperty(value = "数据所属门店名称", required = false, example = "")
	private String dlrNameOwner;
	@ApiModelProperty(value = "场景编码", required = false, example = "")
	private String senceCode;
	@ApiModelProperty(value = "场景名称", required = false, example = "")
	private String senceName;
	@ApiModelProperty(value = "客户履历内容", required = false, example = "")
	private String resumeDesc;
	@ApiModelProperty(value = "关联单据ID", required = false, example = "")
	private String relationBillId;
	@ApiModelProperty(value = "作业时间", required = false, example = "")
	private String bussTime;
	@ApiModelProperty(value = "手机号", required = false, example = "")
	private String phone;
	@ApiModelProperty(value = "作业开始时间", required = false, example = "")
	private String bussTimeStart;
	@ApiModelProperty(value = "作业结束时间", required = false, example = "")
	private String bussTimeEnd;
	@ApiModelProperty(value = "创建开始时间", required = false, example = "")
	private String createdDateStart;
	@ApiModelProperty(value = "创建结束时间", required = false, example = "")
	private String createdDateEnd;
	@ApiModelProperty(value = "履历内容模糊", required = false, example = "")
	private String resumeDescPaste;
	@ApiModelProperty(value = "履历状态多选", required = false, example = "")
	private String senceCodeIn;
	
	public String getResumeDescPaste() {
		return resumeDescPaste;
	}
	public void setResumeDescPaste(String resumeDescPaste) {
		this.resumeDescPaste = resumeDescPaste;
	}
	public String getSenceCodeIn() {
		return senceCodeIn;
	}
	public void setSenceCodeIn(String senceCodeIn) {
		this.senceCodeIn = senceCodeIn;
	}
	public String getBussTimeStart() {
		return bussTimeStart;
	}
	public void setBussTimeStart(String bussTimeStart) {
		this.bussTimeStart = bussTimeStart;
	}
	public String getBussTimeEnd() {
		return bussTimeEnd;
	}
	public void setBussTimeEnd(String bussTimeEnd) {
		this.bussTimeEnd = bussTimeEnd;
	}
	public String getCreatedDateStart() {
		return createdDateStart;
	}
	public void setCreatedDateStart(String createdDateStart) {
		this.createdDateStart = createdDateStart;
	}
	public String getCreatedDateEnd() {
		return createdDateEnd;
	}
	public void setCreatedDateEnd(String createdDateEnd) {
		this.createdDateEnd = createdDateEnd;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getBussTime() {
		return bussTime;
	}
	public void setBussTime(String bussTime) {
		this.bussTime = bussTime;
	}
	public String getColumnArr() {
		return columnArr;
	}
	public void setColumnArr(String columnArr) {
		this.columnArr = columnArr;
	}
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	public String getSorting() {
		return sorting;
	}
	public void setSorting(String sorting) {
		this.sorting = sorting;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getSmartId() {
		return smartId;
	}
	public void setSmartId(String smartId) {
		this.smartId = smartId;
	}
	public String getClueLevelCode() {
		return clueLevelCode;
	}
	public void setClueLevelCode(String clueLevelCode) {
		this.clueLevelCode = clueLevelCode;
	}
	public String getDlrCodeOwner() {
		return dlrCodeOwner;
	}
	public void setDlrCodeOwner(String dlrCodeOwner) {
		this.dlrCodeOwner = dlrCodeOwner;
	}
	public String getDlrNameOwner() {
		return dlrNameOwner;
	}
	public void setDlrNameOwner(String dlrNameOwner) {
		this.dlrNameOwner = dlrNameOwner;
	}
	public String getSenceCode() {
		return senceCode;
	}
	public void setSenceCode(String senceCode) {
		this.senceCode = senceCode;
	}
	public String getSenceName() {
		return senceName;
	}
	public void setSenceName(String senceName) {
		this.senceName = senceName;
	}
	public String getResumeDesc() {
		return resumeDesc;
	}
	public void setResumeDesc(String resumeDesc) {
		this.resumeDesc = resumeDesc;
	}
	public String getRelationBillId() {
		return relationBillId;
	}
	public void setRelationBillId(String relationBillId) {
		this.relationBillId = relationBillId;
	}
}
