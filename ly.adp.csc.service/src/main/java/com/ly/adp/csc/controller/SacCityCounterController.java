package com.ly.adp.csc.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacCityCounterService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 城市计数 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@RestController
@Api(value = "CSC城市计数服务", tags = "CSC城市计数服务")
@RequestMapping(value = "/ly/adp/csc/cityCounter", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacCityCounterController {
	@Autowired
	ISacCityCounterService sacCityCounterService;
	
	@ApiOperation(value = "城市计数查询", notes = "城市计数查询")
	@PostMapping(value = "/sacCityCounterquery.do")
	public ListResult<Map<String, Object>> sacCityCounterFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo){
		return BusicenInvoker.doList(() -> {return sacCityCounterService.sacCityCounterFindInfo(dateInfo,authentication);}).result();
	}
	
	@ApiOperation(value = "城市计数维护", notes = "城市计数维护")
	@PostMapping(value = "/sacCityCountersave.do")
	public EntityResult<Map<String, Object>> sacCityCounterSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacCityCounterService.sacCityCounterSaveInfo(dateInfo, authentication);}).result();
	}
	
	@ApiOperation(value = "城市计数通用维护", notes = "城市计数通用维护")
	@PostMapping(value = "/sacCityCounterGeneral.do")
	public EntityResult<Map<String, Object>> sacCityCounterGeneral(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {return sacCityCounterService.sacCityCounterGeneral(dateInfo, authentication);}).result();
	}
}
