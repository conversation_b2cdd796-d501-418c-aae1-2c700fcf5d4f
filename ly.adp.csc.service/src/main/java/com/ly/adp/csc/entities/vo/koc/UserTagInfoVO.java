package com.ly.adp.csc.entities.vo.koc;

import com.ly.adp.csc.entities.dto.uc.UcUserInfoDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 用户标签信息VO(企微端)
 * <AUTHOR>
 * @since 2025-07-28
 */
public class UserTagInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户smartId
     */
    private String smartId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 标签列表
     */
    private List<TagVO> tags;

    /**
     * 达人类型列表
     */
    private List<ExpertTypeVO> expertTypes;

    /**
     * 备注列表
     */
    private List<RemarkVO> remarks;

    /**
     * 构建用户标签信息VO
     */
    public static UserTagInfoVO buildUserTagInfoVO(UcUserInfoDTO ucUser) {
        UserTagInfoVO userInfo = new UserTagInfoVO();
        userInfo.setSmartId(ucUser.getSmartId());
        userInfo.setNickName(ucUser.getNickName());
        userInfo.setPhone(ucUser.getMobile());
        userInfo.setAvatar(ucUser.getAvatar());
        return userInfo;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<TagVO> getTags() {
        return tags;
    }

    public void setTags(List<TagVO> tags) {
        this.tags = tags;
    }

    public List<ExpertTypeVO> getExpertTypes() {
        return expertTypes;
    }

    public void setExpertTypes(List<ExpertTypeVO> expertTypes) {
        this.expertTypes = expertTypes;
    }

    public List<RemarkVO> getRemarks() {
        return remarks;
    }

    public void setRemarks(List<RemarkVO> remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "UserTagInfoVO{" +
                "smartId='" + smartId + '\'' +
                ", nickName='" + nickName + '\'' +
                ", phone='" + phone + '\'' +
                ", avatar='" + avatar + '\'' +
                ", tags=" + tags +
                ", expertTypes=" + expertTypes +
                ", remarks=" + remarks +
                '}';
    }
}
