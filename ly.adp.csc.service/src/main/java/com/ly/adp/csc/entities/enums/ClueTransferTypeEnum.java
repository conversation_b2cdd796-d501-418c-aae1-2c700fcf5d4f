package com.ly.adp.csc.entities.enums;

/**
 * 线索移交类型枚举
 * 
 * <AUTHOR>
 * @date 2025/4/21
 */
public enum ClueTransferTypeEnum {

    /**
     * 线索移交-已处理
     */
    PROCESSED("1", "线索移交-已处理"),

    /**
     * 线索移交-员工换店
     */
    EMPLOYEE_CHANGE_STORE("2", "线索移交-员工换店"),

    /**
     * 线索移交-员工离职
     */
    EMPLOYEE_DIMISSION("3", "线索移交-员工离职"),
    
    /**
     * 线索移交-离职员工线索自动划转到店长
     */
    AUTO_TRANSFER_TO_MANAGER("4", "线索移交-离职员工线索自动划转到店长");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    ClueTransferTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ClueTransferTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ClueTransferTypeEnum value : ClueTransferTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}