package com.ly.adp.csc.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.dto.SwitchStatus;
import com.ly.adp.csc.entities.qry.CustomerTestDriveQry;
import com.ly.adp.csc.entities.vo.ClueReactivationStatVO;
import com.ly.adp.csc.entities.vo.CustomerTestDriveVO;
import com.ly.adp.csc.service.ISacBasisLogService;
import com.ly.adp.csc.service.ISacIndexPageInfoService;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Api(value = "CSC首页信息服务", tags = "CSC首页信息服务")
@RestController
@RequestMapping(value = "/ly/sac/indexpage/", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacIndexPageController {

    @Autowired
    ISacIndexPageInfoService sacIndexPageService;

    @Autowired
    ISacBasisLogService sacBasisLogService;

    @ApiOperation(value = "查询首页显示信息", notes = "查询首页显示信息")
    @PostMapping(value = "/queryinfobydlr.do")
    public EntityResult<Map<String, Object>> sacTestDriveReviewRecordFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.sacIndexPageInfoQueryByDlr(token, dataInfo)).result();
    }

    @ApiOperation(value = "查询首页消息显示", notes = "查询首页消息显示")
    @PostMapping(value = "/queryinfoMsgbydlr.do")
    public EntityResult<Map<String, Object>> queryinfoMsgbydlr(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryinfoMsgbydlr(token, dataInfo)).result();
    }

    @ApiOperation(value = "根据岗位获取员工信息", notes = "根据岗位获取员工信息")
    @PostMapping(value = "/locationempinfo.do")
    public ListResult<Map<String, Object>> locationEmpInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                           @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.locationEmpInfo(token, dataInfo)).result();
    }

    @ApiOperation(value = "根据SmartID查询客户试驾信息", notes = "根据SmartID查询客户试驾信息")
    @PostMapping(value = "/queryCustomerTestDrive")
    public ListResult<CustomerTestDriveVO> queryCustomerTestDriveInfo(@RequestBody(required = false) CustomerTestDriveQry dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryCustomerTestDriveInfo(dataInfo)).result();
    }

    @ApiOperation(value = "查询首页线索热度比例", notes = "查询首页线索热度比例（已停用，不再维护）")
    @PostMapping(value = "/queryHeatRatio.do")
    public EntityResult<Map<String, Object>> queryHeatRatio(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryHeatRatio(token, dataInfo)).result();
    }

    @ApiOperation(value = "查询首页线索热度比例-接口优化", notes = "查询首页线索热度比例-接口优化")
    @PostMapping(value = "/queryHeatRatio-performance.do")
    public EntityResult<Map<String, Object>> queryHeatRatio_performance(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryHeatRatio_performance(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-线索指标", notes = "看板指标-线索指标")
    @PostMapping(value = "/queryClueDlrReport.do")
    public EntityResult<Map<String, Object>> queryClueDlrReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryAdpReportForm(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-线索指标", notes = "看板指标-线索指标")
    @PostMapping(value = "/queryClueDlrReport-performance.do")
    public EntityResult<Map<String, Object>> queryAdpReportForm_performance(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryAdpReportForm_performance(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-试驾指标", notes = "看板指标-试驾指标")
    @PostMapping(value = "/queryTestDriveReport.do")
    public EntityResult<Map<String, Object>> queryTestDriveReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryTestDriveReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-活动指标", notes = "看板指标-活动指标")
    @PostMapping(value = "/queryActivityReport.do")
    public EntityResult<Map<String, Object>> queryActivityReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryActivityReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-报表展板-线索和订单数量", notes = "看板指标-报表展板-线索和订单数量")
    @PostMapping(value = "/queryReportBoardClueAndOrder.do")
    public EntityResult<Map<String, Object>> queryReportBoardClueAndOrder(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryReportBoardClueAndOrder(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-报表展板-门店数量", notes = "看板指标-报表展板-门店数量")
    @PostMapping(value = "/queryReportBoardDlr.do")
    public EntityResult<Map<String, Object>> queryReportBoardDlr(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryReportBoardDlr(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-报表展板-活动数量", notes = "看板指标-报表展板-活动数量")
    @PostMapping(value = "/queryReportBoardAct.do")
    public EntityResult<Map<String, Object>> queryReportBoardAct(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryReportBoardAct(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-报表展板-活动数量-接口性能优化", notes = "看板指标-报表展板-活动数量-接口性能优化")
    @PostMapping(value = "/queryReportBoardAct-performance.do")
    public EntityResult<Map<String, Object>> queryReportBoardAct_performance(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryReportBoardAct_performance(token, dataInfo)).result();
    }

    @ApiOperation(value = "看板指标-报表展板-展车试驾车数量", notes = "看板指标-报表展板-展车试驾车数量")
    @PostMapping(value = "/queryReportBoardCar.do")
    public EntityResult<Map<String, Object>> queryReportBoardCar(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryReportBoardCar(token, dataInfo)).result();
    }


    @ApiOperation(value = "看板指标-用户指标", notes = "看板指标-用户指标")
    @PostMapping(value = "/queryUserBoardCar.do")
    public EntityResult<Map<String, Object>> queryUserBoardCar(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryUserBoardCar(token, dataInfo)).result();
    }

    @ApiOperation(value = "用户分组界面线索统计", notes = "用户分组界面线索统计")
    @PostMapping(value = "/queryUserGroupClueReport.do")
    public EntityResult<Map<String, Object>> queryUserGroupClueReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doEntity(() -> sacIndexPageService.queryUserGroupClueReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "切店日志记录新增", notes = "切店日志记录新增")
    @PostMapping(value = "/userSwitchDlrLogRecord.do")
    public OptResult switchDlrLogRecord(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doOpt(() -> sacBasisLogService.sacSwitcchDlrLogInsertOne(dataInfo, token)).result();
    }

    @ApiOperation(value = "销售顾问日报汇总", notes = "销售顾问日报汇总")
    @PostMapping(value = "/querySaleConstantReport.do")
    public ListResult<Map<String, Object>> querySaleConstantReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.querySaleConstantReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "销售顾问日报汇总-管理", notes = "销售顾问日报汇总-管理")
    @PostMapping(value = "/querySaleConstantManagerReport.do")
    public ListResult<Map<String, Object>> querySaleConstantManagerReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.querySaleConstantManagerReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "销售顾问日报汇总-管理", notes = "销售顾问日报汇总-管理")
    @PostMapping(value = "/querySaleConstantManagerReportToDo.todo")
    public ListResult<Map<String, Object>> querySaleConstantManagerReportToDo(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.querySaleConstantManagerReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "试驾总览-导出", notes = "试驾总览-导出")
    @RequestMapping(value = "/exportQuerytestCarReport.do", method = RequestMethod.POST)
    public OptResult exportQuerytestCarReport(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                              @RequestBody(required = false) Map<String, Object> param,
                                              HttpServletResponse response) throws Exception {
        return sacIndexPageService.exportQuerytestCarReport(param, response, authentication);
    }

    @ApiOperation(value = "试驾总览", notes = "试驾总览")
    @PostMapping(value = "/querytestCarReport.do")
    public ListResult<Map<String, Object>> querytestCarReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.querytestCarReport(token, dataInfo)).result();
    }
    @ApiOperation(value = "离职战败线索移交查询优化性能", notes = "离职战败线索移交查询优化性能")
    @RequestMapping(value = "/switchStatus.todo", method = RequestMethod.POST)
    public EntityResult<Boolean> switchStatus(
            @RequestBody SwitchStatus dataInfo) {
        return com.ly.mp.busi.base.context.BusicenInvoker.doEntity(() -> sacIndexPageService.switchStatus(dataInfo)).result();
    }

    @ApiOperation(value = "日报管理-导出", notes = "城市报表-导出")
    @RequestMapping(value = "/exportSaleConstantManagerReport.do", method = RequestMethod.POST)
    public OptResult exportSaleConstantManagerReport(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                                     @RequestBody(required = false) Map<String, Object> param,
                                                     HttpServletResponse response) throws Exception {
        return sacIndexPageService.exportSaleConstantManagerReport(param, authentication, response);
    }

    @ApiOperation(value = "日报管理-导出", notes = "城市报表-导出")
    @RequestMapping(value = "/exportSaleConstantManagerReport-performance.do", method = RequestMethod.POST)
    public OptResult exportSaleConstantManagerReport_performance(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                                     @RequestBody(required = false) Map<String, Object> param,
                                                     HttpServletResponse response) throws Exception {
        return sacIndexPageService.exportSaleConstantManagerReport_performance(param, authentication, response);
    }

    @ApiOperation(value = "日报-导出", notes = "城市报表-导出")
    @RequestMapping(value = "/exportSaleConstantReport.do", method = RequestMethod.POST)
    public OptResult exportSaleConstantReport(@RequestHeader(org.springframework.http.HttpHeaders.AUTHORIZATION) String authentication,
                                              @RequestBody(required = false) Map<String, Object> param,
                                              HttpServletResponse response) throws Exception {
        return sacIndexPageService.export(param, authentication, response);
    }

    @ApiOperation(value = "线索指标统计报表", notes = "线索指标统计报表")
    @PostMapping(value = "/queryClueStatisticalReport.do")
    public ListResult<Map<String, Object>> queryClueStatisticalReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryClueStatisticalReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "线索指标统计报表-性能优化", notes = "线索指标统计报表-性能优化")
    @PostMapping(value = "/queryClueStatisticalReport-performance.do")
    public ListResult<Map<String, Object>> queryClueStatisticalReport_performance(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) Map<String, Object> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryClueStatisticalReport_performance(token, dataInfo)).result();
    }

    @ApiOperation(value = "线索指标统计报表导出", notes = "线索指标统计报表导出")
    @PostMapping(value = "/queryClueStatisticalReportExport.do")
    public OptResult queryClueStatisticalReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryClueStatisticalReportExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "线索指标统计报表导出-性能优化", notes = "线索指标统计报表导出-性能优化")
    @PostMapping(value = "/queryClueStatisticalReportExport-performance.do")
    public OptResult queryClueStatisticalReportExport_performance(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryClueStatisticalReportExport_performance(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "城市与休眠线索报表", notes = "城市与休眠线索报表")
    @PostMapping(value = "/queryClueDormancyReport.do")
    public ListResult<Map<String, Object>> queryClueDormancyReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryClueDormancyReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "城市与休眠线索报表导出", notes = "城市与休眠线索报表导出")
    @PostMapping(value = "/queryClueDormancyReportExport.do")
    public OptResult queryClueDormancyReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                   @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryClueDormancyReportExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "市场类活动报表", notes = "市场类活动报表")
    @PostMapping(value = "/queryMarketActivityReport.do")
    public ListResult<Map<String, Object>> queryMarketActivityReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryMarketActivityReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "市场类活动报表导出", notes = "市场类活动报表导出")
    @PostMapping(value = "/queryMarketActivityReportExport.do")
    public OptResult queryMarketActivityReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryMarketActivityReportExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "活动报表", notes = "活动报表")
    @PostMapping(value = "/queryAllActivityReport.do")
    public ListResult<Map<String, Object>> queryAllActivityReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryAllActivityReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "活动报表导出", notes = "活动报表导出")
    @PostMapping(value = "/queryActivityReportExport.do")
    public OptResult queryActivityReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryActivityReportExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "代理商线下活动报表", notes = "代理商线下活动报表")
    @PostMapping(value = "/queryAgentOfflineActivityReport.do")
    public ListResult<Map<String, Object>> queryAgentOfflineActivityReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryAgentOfflineActivityReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "代理商线下活动报表导出", notes = "代理商线下活动报表导出")
    @PostMapping(value = "/queryAgentOfflineActivityReportExport.do")
    public OptResult queryAgentOfflineActivityReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                           @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo,
                                                           HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryAgentOfflineActivityReportExport(dateInfo, token, response)).result();
    }

    @ApiOperation(value = "代理商线上投放报表", notes = "代理商线上投放报表")
    @PostMapping(value = "/queryAgentOnlineActivityReport.do")
    public ListResult<Map<String, Object>> queryAgentOnlineActivityReport(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo) {
        return BusicenInvoker.doList(() -> sacIndexPageService.queryAgentOnlineActivityReport(token, dataInfo)).result();
    }

    @ApiOperation(value = "代理商线上投放报表导出", notes = "代理商线上投放报表导出")
    @PostMapping(value = "/queryAgentOnlineActivityReportExport.do")
    public OptResult queryAgentOnlineActivityReportExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                          @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo,
                                                          HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.queryAgentOnlineActivityReportExport(dateInfo, token, response)).result();
    }

    @ApiOperation(value = "评价报表导出", notes = "评价报表导出")
    @PostMapping(value = "/getEvaluationDetailsExport.do")
    public OptResult getEvaluationDetailsExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                @RequestBody Map<String, Object> dateInfo,
                                                HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacIndexPageService.getEvaluationDetailsExport(dateInfo, token, response)).result();
    }

    @ApiOperation(value = "评价报表查询", notes = "评价报表查询")
    @PostMapping(value = "/getEvaluationDetails.do")
    public ListResult<Map<String, Object>> getEvaluationDetails(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                                @RequestBody Map<String, Object> dateInfo,
                                                                HttpServletResponse response) {
        return sacIndexPageService.getEvaluationDetails(dateInfo, token, response);
    }

    @ApiOperation(value = "首页待跟进看板（外呼专员）", notes = "首页待跟进看板（外呼专员）")
    @PostMapping(value = "/callReactivation.do")
    public EntityResult<ClueReactivationStatVO> getReactivationStat(@RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
        return sacIndexPageService.getReactivationStat(token);
    }
}