package com.ly.adp.csc.entities.req;

import com.ly.mp.csc.clue.helper.LocalDateTimeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/
@ApiModel("任务维护入参-req")
public class CommonSaveTaskReq implements Serializable {

    private static final long serialVersionUID = -8141239381523227116L;

    /**
     * 任务结束时间
     */
    @ApiModelProperty("任务结束时间")
    private String bussEndTime;

    /**
     * 任务开始时间
     */
    @ApiModelProperty("任务开始时间")
    private String bussStartTime;

    /**
     * 是否必须跟进，1是，0否
     */
    @ApiModelProperty("是否必须跟进")
    private String isRequiredFollowUp;

    /**
     * 任务创建人，任务创建人，例如：CDP
     */
    @ApiModelProperty("任务创建人")
    private String taskCreator;

    /**
     * 任务描述
     */
    @ApiModelProperty("任务描述")
    private String taskDescribe;

    /**
     * 任务id，任务主键，默认uuid
     */
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 任务线索对象List
     */
    @ApiModelProperty("任务线索对象List")
    private List<CommonSaveTask> taskList;

    /**
     * 任务标题
     */
    @ApiModelProperty("任务标题")
    private String taskTitle;

    /**
     * 任务类型编码，1通知，2任务。当前场景传2任务
     */
    @ApiModelProperty("任务类型编码")
    private String taskTypeCode;

    /**
     * 任务类型名称
     */
    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    public String getBussEndTime() {
        return bussEndTime;
    }

    public void setBussEndTime(String bussEndTime) {
        this.bussEndTime = bussEndTime;
    }

    public String getBussStartTime() {
        return bussStartTime;
    }

    public void setBussStartTime(String bussStartTime) {
        this.bussStartTime = bussStartTime;
    }

    public String getIsRequiredFollowUp() {
        return isRequiredFollowUp;
    }

    public void setIsRequiredFollowUp(String isRequiredFollowUp) {
        this.isRequiredFollowUp = isRequiredFollowUp;
    }

    public String getTaskCreator() {
        return taskCreator;
    }

    public void setTaskCreator(String taskCreator) {
        this.taskCreator = taskCreator;
    }

    public String getTaskDescribe() {
        return taskDescribe;
    }

    public void setTaskDescribe(String taskDescribe) {
        this.taskDescribe = taskDescribe;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<CommonSaveTask> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<CommonSaveTask> taskList) {
        this.taskList = taskList;
    }

    public String getTaskTitle() {
        return taskTitle;
    }

    public void setTaskTitle(String taskTitle) {
        this.taskTitle = taskTitle;
    }

    public String getTaskTypeCode() {
        return taskTypeCode;
    }

    public void setTaskTypeCode(String taskTypeCode) {
        this.taskTypeCode = taskTypeCode;
    }

    public String getTaskTypeName() {
        return taskTypeName;
    }

    public void setTaskTypeName(String taskTypeName) {
        this.taskTypeName = taskTypeName;
    }

    public Map<String, Object> mapToSaveTask() {
        Map<String, Object> map = new HashMap<>();
        map.put("isSave", "1");
        map.put("taskId", this.taskId);
        map.put("taskTitle", this.taskTitle);
        map.put("taskDescribe", this.taskDescribe);
        map.put("taskTypeCode", this.taskTypeCode);
        map.put("taskTypeName", this.taskTypeName);
        map.put("bussStartTime", this.bussStartTime);
        map.put("bussEndTime", this.bussEndTime);
        map.put("isEnable", "1");
        map.put("bussTime", LocalDateTimeUtil.getCurrentDateTimeAsString());
        map.put("taskFilePath", "");
        map.put("column1", "");
        map.put("column2", "");
        map.put("column3", "否");
        map.put("taskList", convertTaskListToMapList(this.taskList));
        map.put("taskStateCode", "3");
        map.put("taskStateName", "已发布");
        map.put("taskAttestationIs", this.isRequiredFollowUp);
        map.put("endTime", this.bussEndTime);
        map.put("taskCreator", this.taskCreator);
        map.put("taskRepeatIs", "0");
        return map;
    }

    public List<Map<String, Object>> convertTaskListToMapList(List<CommonSaveTask> taskList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (CommonSaveTask task : taskList) {
            Map<String, Object> map = new HashMap<>();
            map.put("phone", task.getPhone());
            map.put("custId", task.getCustId());
            map.put("custName", task.getCustName());
            map.put("reviewId", task.getReviewId());
            map.put("serverOrder", task.getServerOrder());
            map.put("taskPersonCode", task.getTaskPersonCode());
            map.put("taskPersonDlrCode", task.getTaskPersonDlrCode());
            map.put("taskPersonDlrName", task.getTaskPersonDlrName());
            map.put("taskPersonId", task.getTaskPersonId());
            map.put("taskPersonName", task.getTaskPersonName());
            mapList.add(map);
        }
        return mapList;
    }

    public static class CommonSaveTask implements Serializable {

        private static final long serialVersionUID = -7567907258444148003L;

        /**
         * 客户名称
         */
        @ApiModelProperty("客户名称")
        private String custName;

        /**
         * 客户手机号
         */
        @ApiModelProperty("客户手机号")
        private String phone;

        @ApiModelProperty("客户ID")
        private String custId;

        @ApiModelProperty("线索单号")
        private String serverOrder;

        @ApiModelProperty("回访ID")
        private String reviewId;

        @ApiModelProperty("完成人员id")
        private String taskPersonId;

        @ApiModelProperty("完成人员编码")
        private String taskPersonCode;

        @ApiModelProperty("完成人员")
        private String taskPersonName;

        @ApiModelProperty("完成人员专营店编码")
        private String taskPersonDlrCode;

        @ApiModelProperty("完成人员专营店")
        private String taskPersonDlrName;

        @ApiModelProperty("smartId")
        private String smartId;

        public String getCustName() {
            return custName;
        }

        public void setCustName(String custName) {
            this.custName = custName;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getSmartId() {
            return smartId;
        }

        public void setSmartId(String smartId) {
            this.smartId = smartId;
        }

        public String getCustId() {
            return custId;
        }

        public void setCustId(String custId) {
            this.custId = custId;
        }

        public String getServerOrder() {
            return serverOrder;
        }

        public void setServerOrder(String serverOrder) {
            this.serverOrder = serverOrder;
        }

        public String getReviewId() {
            return reviewId;
        }

        public void setReviewId(String reviewId) {
            this.reviewId = reviewId;
        }

        public String getTaskPersonId() {
            return taskPersonId;
        }

        public void setTaskPersonId(String taskPersonId) {
            this.taskPersonId = taskPersonId;
        }

        public String getTaskPersonCode() {
            return taskPersonCode;
        }

        public void setTaskPersonCode(String taskPersonCode) {
            this.taskPersonCode = taskPersonCode;
        }

        public String getTaskPersonName() {
            return taskPersonName;
        }

        public void setTaskPersonName(String taskPersonName) {
            this.taskPersonName = taskPersonName;
        }

        public String getTaskPersonDlrCode() {
            return taskPersonDlrCode;
        }

        public void setTaskPersonDlrCode(String taskPersonDlrCode) {
            this.taskPersonDlrCode = taskPersonDlrCode;
        }

        public String getTaskPersonDlrName() {
            return taskPersonDlrName;
        }

        public void setTaskPersonDlrName(String taskPersonDlrName) {
            this.taskPersonDlrName = taskPersonDlrName;
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("custName", this.custName);
            if (StringUtils.isNotEmpty(this.phone)) {
                map.put("phone", this.phone);
            }
            if (StringUtils.isNotEmpty(this.smartId)) {
                map.put("smartId", this.smartId);
            }
            return map;
        }
    }
}
