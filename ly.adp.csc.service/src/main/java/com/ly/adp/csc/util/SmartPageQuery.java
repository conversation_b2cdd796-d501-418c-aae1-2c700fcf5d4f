package com.ly.adp.csc.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * 封装分页查询
 * 优化mybatisplus 复杂sql查询总条数的性能问题
 * 需要在xxmapper文件类新增sql fetchCount
 * andy.liu
 */
public interface SmartPageQuery<T> {
    //    <T> IPage<T> selectByPage(Page<T> page, Map<String, Object> param);
    List<Map<String, Object>> queryAllActivityReport(Map<String, Object> param, Page<Map<String, Object>> page);
    int fetchCount(Map<String, Object> param);
}  
