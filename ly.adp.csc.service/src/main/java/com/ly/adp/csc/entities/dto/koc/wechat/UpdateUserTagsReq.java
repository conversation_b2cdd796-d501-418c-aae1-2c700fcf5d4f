package com.ly.adp.csc.entities.dto.koc.wechat;

import java.io.Serializable;
import java.util.List;

/**
 * 企微端编辑用户标签请求DTO
 * <AUTHOR>
 * @since 2025-07-31
 */
public class UpdateUserTagsReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String smartId;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public List<String> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<String> tagIds) {
        this.tagIds = tagIds;
    }

    @Override
    public String toString() {
        return "UpdateUserTagsReq{" +
                "smartId='" + smartId + '\'' +
                ", tagIds=" + tagIds +
                '}';
    }
}
