package com.ly.adp.csc.entities.dto.uc;

import java.io.Serializable;
import java.util.List;

/**
 * UC接口响应DTO
 * <AUTHOR>
 * @since 2025-07-28
 */
public class UcResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String SUCCESS_CODE = "0";

    /**
     * 响应码： 0 成功
     */
    private String code;

    /**
     * 数据列表
     */
    private List<T> data;

    /**
     * 请求码响应码信息
     */
    private String msg;

    /**
     * 请求状态是否成功：'成功'：'SUCCEED','失败'：'FAILED',可用值:SUCCEED
     */
    private String status;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }

    @Override
    public String toString() {
        return "UcResponseDTO{" +
                "code=" + code +
                ", data=" + data +
                ", msg='" + msg + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
