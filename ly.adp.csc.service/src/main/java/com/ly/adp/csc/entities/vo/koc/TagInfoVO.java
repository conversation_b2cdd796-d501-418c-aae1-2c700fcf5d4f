package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;
import java.util.List;

/**
 * 标签信息VO
 * <AUTHOR>
 * @since 2025-07-25
 */
public class TagInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签层级
     */
    private Integer level;

    /**
     * 父标签ID
     */
    private String parentTagId;

    /**
     * 完整路径
     */
    private String fullPath;

    /**
     * 路径数组
     */
    private String[] pathArray;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户数量（统计用）
     */
    private Integer userCount;

    /**
     * 子标签列表（树形结构用）
     */
    private List<TagInfoVO> children;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getParentTagId() {
        return parentTagId;
    }

    public void setParentTagId(String parentTagId) {
        this.parentTagId = parentTagId;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String[] getPathArray() {
        return pathArray;
    }

    public void setPathArray(String[] pathArray) {
        this.pathArray = pathArray;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public List<TagInfoVO> getChildren() {
        return children;
    }

    public void setChildren(List<TagInfoVO> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "TagInfoVO{" +
                "tagId='" + tagId + '\'' +
                ", tagName='" + tagName + '\'' +
                ", level=" + level +
                ", parentTagId='" + parentTagId + '\'' +
                ", fullPath='" + fullPath + '\'' +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                ", userCount=" + userCount +
                '}';
    }
}
