package com.ly.adp.csc.entities.qry;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2024/8/14
 * @Version 1.0.0
 **/
@ApiModel("获取线索信息入参-qry")
public class GetClueInfoQry {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "客户id")
    private String custId;

    @ApiModelProperty(value = "线索单号")
    private String serverOrder;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getServerOrder() {
        return serverOrder;
    }

    public void setServerOrder(String serverOrder) {
        this.serverOrder = serverOrder;
    }
}
