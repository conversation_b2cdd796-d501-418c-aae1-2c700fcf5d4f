package com.ly.adp.csc.controller;


import com.google.common.net.HttpHeaders;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.csc.impl.BbcArticleBiz;
import com.ly.adp.csc.impl.BbcCommentBiz;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 论坛评论表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@RestController
@RequestMapping("/ly/adp/csc/bbcComment")
@Api(value = "论坛评论",tags = {"论坛评论"})
public class BbcCommentController {
    @Resource
    BbcCommentBiz bbcCommentBiz;
    @ApiOperation(value = "评论列表", notes = "评论列表")
    @PostMapping(value = "/queryBbcComment.do")
    public ListResult<Map<String, Object>> sacBuBoutiqueApplyFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return bbcCommentBiz.queryBbcComment(authentication,dateInfo);}).result();
    }

    @ApiOperation(value = "子评论列表", notes = "子评论列表")
    @PostMapping(value = "/queryBbcChildranComment.do")
    public ListResult<Map<String, Object>> queryBbcChildranComment(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return bbcCommentBiz.queryBbcChildranComment(authentication,dateInfo);}).result();
    }

    @ApiOperation(value = "待审核评论列表", notes = "待审核评论列表")
    @PostMapping(value = "/queryCommentStatu.do")
    public ListResult<Map<String, Object>> queryCommentStatu(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) ParamPage<Map<String, Object>> dateInfo){
        return BusicenInvoker.doList(() -> {return bbcCommentBiz.queryCommentStatu(authentication,dateInfo);}).result();
    }
    @ApiOperation(value = "管理回复", notes = "管理回复")
    @PostMapping(value = "/managerComment.do")
    public OptResult managerComment(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcCommentBiz.managerComment(dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "回复审批", notes = "回复审批")
    @PostMapping(value = "/batchComment.do")
    public OptResult batchComment(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                  @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcCommentBiz.batchComment((List<Map<String, Object>>) dateInfo.get("param"), authentication);}).result();
    }
    @ApiOperation(value = "待审核数量", notes = "待审核数量")
    @PostMapping(value = "/queryCount.do")
    public EntityResult<Integer> queryCount(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                     @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> {return bbcCommentBiz.querycount( dateInfo, authentication);}).result();
    }
    @ApiOperation(value = "删除回复", notes = "删除回复")
    @PostMapping(value = "/delComment.do")
    public OptResult delComment(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doOpt(() -> {return bbcCommentBiz.delComment(dateInfo, authentication);}).result();
    }

    @ApiOperation(value = "评论列表岗位查询", notes = "评论列表岗位查询")
    @PostMapping(value = "/findStation.do")
    public ListResult<Map<String, Object>> findStation(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                @RequestBody(required = false) Map<String, Object> dateInfo) {
        return bbcCommentBiz.findStation(dateInfo, authentication);
    }
}

