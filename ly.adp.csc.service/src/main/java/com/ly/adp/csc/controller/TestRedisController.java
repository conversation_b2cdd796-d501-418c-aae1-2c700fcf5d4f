package com.ly.adp.csc.controller;

import com.ly.mp.csc.clue.entities.dto.ClueDelayedMqDTO;
import com.ly.mp.csc.clue.util.CommonUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/ly/adp/csc/redis", produces = { MediaType.APPLICATION_JSON_VALUE })
public class TestRedisController {

	@Resource
	StringRedisTemplate stringRedisTemplate;

	@GetMapping("/test/set/{key}/{value}")
	public void set(@PathVariable("key") String key, @PathVariable("value") String value) {
		stringRedisTemplate.opsForValue().set(key, value);
	}

	@GetMapping("/test/incr/{key}")
	public String incr(@PathVariable("key") String key) {
		return String.valueOf(stringRedisTemplate.opsForValue().increment(key));
	}

	@GetMapping("/test/get/{key}")
	public String get(@PathVariable("key") String key) {
		return stringRedisTemplate.opsForValue().get(key);
	}

	@GetMapping("/test/del/{key}")
	public void del(@PathVariable("key") String key) {
		stringRedisTemplate.delete(key);
	}

	@GetMapping("/test")
	public void test(@RequestParam String json) {
		ClueDelayedMqDTO clueDelayedMqDTO = CommonUtil.jsonStringToObj(json, ClueDelayedMqDTO.class);
		System.out.println(clueDelayedMqDTO);
	}
}
