package com.ly.adp.csc.entities.enums;

/**
 * 标签操作类型枚举
 * <p>
 * 定义了标签系统中所有可能的操作类型，包括标签管理、达人类型管理、用户关联操作和备注管理等功能。
 * 每个操作类型都有唯一的编码和描述信息，用于系统日志记录、权限控制和业务流程追踪。
 * </p>
 *
 * <h3>操作分类：</h3>
 * <ul>
 *   <li><b>标签管理操作</b>：创建、编辑、删除、上下架标签</li>
 *   <li><b>达人类型管理</b>：添加、编辑、删除达人类型</li>
 *   <li><b>用户关联操作</b>：为用户添加/移除标签和达人类型</li>
 *   <li><b>备注管理操作</b>：添加、编辑、移除备注信息</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @version 1.0
 */
public enum TagOperationTypeEnum {

    // ==================== 标签管理操作 ====================

    /**
     * 创建标签操作
     * <p>用于新建标签时的操作记录，包括标签名称、分类、描述等基本信息的创建</p>
     */
    CREATE_TAG(1, "创建标签"),

    /**
     * 编辑标签操作
     * <p>用于修改已存在标签的基本信息，如标签名称、描述、分类等属性的更新</p>
     */
    EDIT_TAG(2, "编辑标签"),

    /**
     * 删除标签操作
     * <p>用于永久删除标签的操作记录，删除后标签将无法恢复</p>
     */
    DELETE_TAG(3, "删除标签"),

    /**
     * 下架标签操作
     * <p>用于将标签设置为不可用状态，下架后的标签不会在前端展示，但数据仍然保留</p>
     */
    DISABLE_TAG(4, "下架标签"),

    /**
     * 上架标签操作
     * <p>用于将已下架的标签重新设置为可用状态，上架后标签将重新在前端展示</p>
     */
    ENABLE_TAG(5, "上架标签"),

    // ==================== 达人类型管理操作 ====================

    /**
     * 添加达人类型操作
     * <p>用于创建新的达人类型分类，定义不同类型的专家用户分类</p>
     */
    ADD_EXPERT_TYPE(6, "添加达人类型"),

    /**
     * 编辑达人类型操作
     * <p>用于修改已存在的达人类型信息，包括类型名称、描述等属性的更新</p>
     */
    EDIT_EXPERT_TYPE(7, "编辑达人类型"),

    /**
     * 删除达人类型操作
     * <p>用于删除不再使用的达人类型，删除前需确保没有用户关联该类型</p>
     */
    DELETE_EXPERT_TYPE(8, "删除达人类型"),

    // ==================== 用户关联操作 ====================

    /**
     * 添加标签到用户操作
     * <p>用于为指定用户添加标签，建立用户与标签的关联关系</p>
     */
    ADD_TAG_TO_USER(9, "添加标签到用户"),

    /**
     * 从用户移除标签操作
     * <p>用于解除用户与指定标签的关联关系，移除用户身上的标签</p>
     */
    REMOVE_TAG_FROM_USER(10, "从用户移除标签"),

    /**
     * 添加达人类别到用户操作
     * <p>用于为指定用户添加达人类型，标识用户的专业领域或特长</p>
     */
    ADD_EXPERT_TYPE_TO_USER(11, "添加达人类别到用户"),

    /**
     * 从用户移除达人类别操作
     * <p>用于解除用户与指定达人类型的关联关系，移除用户的达人身份</p>
     */
    REMOVE_EXPERT_TYPE_FROM_USER(12, "从用户移除达人类别"),

    // ==================== 备注管理操作 ====================

    /**
     * 添加备注操作
     * <p>用于为用户、标签或其他实体添加备注信息，记录额外的说明或注意事项</p>
     */
    ADD_REMARK(13, "添加备注"),

    /**
     * 移除备注操作
     * <p>用于删除已存在的备注信息，清除不再需要的说明内容</p>
     */
    REMOVE_REMARK(14, "移除备注"),

    /**
     * 编辑备注操作
     * <p>用于修改已存在的备注内容，更新说明信息或注意事项</p>
     */
    EDIT_REMARK(15, "编辑备注");

    /**
     * 操作类型编码
     * <p>每个操作类型的唯一标识符，用于数据库存储和系统间数据传输</p>
     */
    private final Integer code;

    /**
     * 操作类型描述
     * <p>操作类型的中文描述信息，用于日志记录和用户界面展示</p>
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code 操作类型编码，必须唯一
     * @param description 操作类型描述，不能为空
     */
    TagOperationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取操作类型编码
     *
     * @return 操作类型的唯一编码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取操作类型描述
     *
     * @return 操作类型的中文描述信息
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取对应的枚举实例
     * <p>
     * 通过操作类型编码查找对应的枚举值，常用于数据库查询结果转换为枚举对象。
     * 如果找不到对应的枚举值，返回null。
     * </p>
     *
     * @param code 操作类型编码
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static TagOperationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (TagOperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为标签管理相关操作
     * <p>包括创建、编辑、删除、上下架标签等操作</p>
     *
     * @return 如果是标签管理操作返回true，否则返回false
     */
    public boolean isTagManagementOperation() {
        return this == CREATE_TAG || this == EDIT_TAG || this == DELETE_TAG
            || this == DISABLE_TAG || this == ENABLE_TAG;
    }

    /**
     * 判断是否为达人类型管理相关操作
     * <p>包括添加、编辑、删除达人类型等操作</p>
     *
     * @return 如果是达人类型管理操作返回true，否则返回false
     */
    public boolean isExpertTypeManagementOperation() {
        return this == ADD_EXPERT_TYPE || this == EDIT_EXPERT_TYPE || this == DELETE_EXPERT_TYPE;
    }

    /**
     * 判断是否为用户关联相关操作
     * <p>包括为用户添加/移除标签和达人类型等操作</p>
     *
     * @return 如果是用户关联操作返回true，否则返回false
     */
    public boolean isUserRelationOperation() {
        return this == ADD_TAG_TO_USER || this == REMOVE_TAG_FROM_USER
            || this == ADD_EXPERT_TYPE_TO_USER || this == REMOVE_EXPERT_TYPE_FROM_USER;
    }

    /**
     * 判断是否为备注管理相关操作
     * <p>包括添加、编辑、移除备注等操作</p>
     *
     * @return 如果是备注管理操作返回true，否则返回false
     */
    public boolean isRemarkManagementOperation() {
        return this == ADD_REMARK || this == REMOVE_REMARK || this == EDIT_REMARK;
    }

    /**
     * 获取操作类型的分类名称
     * <p>根据操作类型返回对应的分类名称，用于日志分类和统计分析</p>
     *
     * @return 操作类型的分类名称
     */
    public String getCategoryName() {
        if (isTagManagementOperation()) {
            return "标签管理";
        } else if (isExpertTypeManagementOperation()) {
            return "达人类型管理";
        } else if (isUserRelationOperation()) {
            return "用户关联";
        } else if (isRemarkManagementOperation()) {
            return "备注管理";
        } else {
            return "其他操作";
        }
    }
}
