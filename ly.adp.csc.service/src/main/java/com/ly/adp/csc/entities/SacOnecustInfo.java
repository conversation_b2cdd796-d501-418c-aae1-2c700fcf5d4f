package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 潜客客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@TableName("t_sac_onecust_info")
public class SacOnecustInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @TableId("CUST_ID")
    private String custId;

    /**
     * smartID
     */
    @TableField("SMART_ID")
    private String smartId;

    /**
     * 姓名
     */
    @TableField("CUST_NAME")
    private String custName;

    /**
     * 性别编码
     */
    @TableField("GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     */
    @TableField("GENDER_NAME")
    private String genderName;

    /**
     * 昵称
     */
    @TableField("NICK_NAME")
    private String nickName;

    /**
     * 手机号
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 手机号
     */
    @TableField("PHONE_STANDBY")
    private String phoneStandby;

    /**
     * 邮箱
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 微信账号
     */
    @TableField("WECHAT")
    private String wechat;

    /**
     * 身份证
     */
    @TableField("ID_CARD")
    private String idCard;

    /**
     * 行驶证
     */
    @TableField("DRIVER_CARD")
    private String driverCard;

    /**
     * 驾照
     */
    @TableField("DRIVING_LICENSE")
    private String drivingLicense;

    /**
     * 护照
     */
    @TableField("PASSPORT")
    private String passport;

    /**
     * cl_sp（管理来源）
     */
    @TableField("source5")
    private String source5;

    /**
     * cl_sr（管理来源)
     */
    @TableField("source6")
    private String source6;

    /**
     * 关注车型版型
     */
    @TableField("attr83")
    private String attr83;

    /**
     * 投诉标签
     */
    @TableField("COMPLAINTS_LABEL")
    private String complaintsLabel;

    /**
     * 空闲标签
     */
    @TableField("FREE_LABEL")
    private String freeLabel;

    /**
     * 兴趣爱好编码（画像类第一位）
     */
    @TableField("USER_HOBBIES_CODE")
    private String userHobbiesCode;

    /**
     * 兴趣爱好名称（画像类第一位）
     */
    @TableField("USER_HOBBIES_NAME")
    private String userHobbiesName;

    /**
     * 特征编码
     */
    @TableField("CHARACTERISTICS_CODE")
    private String characteristicsCode;

    /**
     * 特征名称
     */
    @TableField("CHARACTERISTICS_NAME")
    private String characteristicsName;

    /**
     * 客户来源
     */
    @TableField("ONE_CUST_SOURCE")
    private String oneCustSource;

    /**
     * 扩展信息
     */
    @TableField("EXTEND_JSON")
    private String extendJson;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    /**
     * 客户ID
     */
    @TableField(exist = false)
    private List<String> listCustId;

    public String getSource5() {
        return source5;
    }

    public void setSource5(String source5) {
        this.source5 = source5;
    }

    public String getSource6() {
        return source6;
    }

    public void setSource6(String source6) {
        this.source6 = source6;
    }

    public String getAttr83() {
        return attr83;
    }

    public void setAttr83(String attr83) {
        this.attr83 = attr83;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneStandby() {
        return phoneStandby;
    }

    public void setPhoneStandby(String phoneStandby) {
        this.phoneStandby = phoneStandby;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getDriverCard() {
        return driverCard;
    }

    public void setDriverCard(String driverCard) {
        this.driverCard = driverCard;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getPassport() {
        return passport;
    }

    public void setPassport(String passport) {
        this.passport = passport;
    }

    public String getComplaintsLabel() {
        return complaintsLabel;
    }

    public void setComplaintsLabel(String complaintsLabel) {
        this.complaintsLabel = complaintsLabel;
    }

    public String getFreeLabel() {
        return freeLabel;
    }

    public void setFreeLabel(String freeLabel) {
        this.freeLabel = freeLabel;
    }

    public String getUserHobbiesCode() {
        return userHobbiesCode;
    }

    public void setUserHobbiesCode(String userHobbiesCode) {
        this.userHobbiesCode = userHobbiesCode;
    }

    public String getUserHobbiesName() {
        return userHobbiesName;
    }

    public void setUserHobbiesName(String userHobbiesName) {
        this.userHobbiesName = userHobbiesName;
    }

    public String getCharacteristicsCode() {
        return characteristicsCode;
    }

    public void setCharacteristicsCode(String characteristicsCode) {
        this.characteristicsCode = characteristicsCode;
    }

    public String getCharacteristicsName() {
        return characteristicsName;
    }

    public void setCharacteristicsName(String characteristicsName) {
        this.characteristicsName = characteristicsName;
    }

    public String getOneCustSource() {
        return oneCustSource;
    }

    public void setOneCustSource(String oneCustSource) {
        this.oneCustSource = oneCustSource;
    }

    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }

    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public List<String> getListCustId() {
        return listCustId;
    }

    public void setListCustId(List<String> listCustId) {
        this.listCustId = listCustId;
    }

    @Override
    public String toString() {
        return "SacOnecustInfo{" +
                "custId='" + custId + '\'' +
                ", smartId='" + smartId + '\'' +
                ", custName='" + custName + '\'' +
                ", genderCode='" + genderCode + '\'' +
                ", genderName='" + genderName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", phone='" + phone + '\'' +
                ", phoneStandby='" + phoneStandby + '\'' +
                ", email='" + email + '\'' +
                ", wechat='" + wechat + '\'' +
                ", idCard='" + idCard + '\'' +
                ", driverCard='" + driverCard + '\'' +
                ", drivingLicense='" + drivingLicense + '\'' +
                ", passport='" + passport + '\'' +
                ", source5='" + source5 + '\'' +
                ", source6='" + source6 + '\'' +
                ", attr83='" + attr83 + '\'' +
                ", complaintsLabel='" + complaintsLabel + '\'' +
                ", freeLabel='" + freeLabel + '\'' +
                ", userHobbiesCode='" + userHobbiesCode + '\'' +
                ", userHobbiesName='" + userHobbiesName + '\'' +
                ", characteristicsCode='" + characteristicsCode + '\'' +
                ", characteristicsName='" + characteristicsName + '\'' +
                ", oneCustSource='" + oneCustSource + '\'' +
                ", extendJson='" + extendJson + '\'' +
                ", column1='" + column1 + '\'' +
                ", column2='" + column2 + '\'' +
                ", column3='" + column3 + '\'' +
                ", column4='" + column4 + '\'' +
                ", column5='" + column5 + '\'' +
                ", column6='" + column6 + '\'' +
                ", column7='" + column7 + '\'' +
                ", column8='" + column8 + '\'' +
                ", column9='" + column9 + '\'' +
                ", column10='" + column10 + '\'' +
                ", mycatOpTime=" + mycatOpTime +
                ", oemId='" + oemId + '\'' +
                ", groupId='" + groupId + '\'' +
                ", oemCode='" + oemCode + '\'' +
                ", groupCode='" + groupCode + '\'' +
                ", creator='" + creator + '\'' +
                ", createdName='" + createdName + '\'' +
                ", createdDate=" + createdDate +
                ", modifier='" + modifier + '\'' +
                ", modifyName='" + modifyName + '\'' +
                ", lastUpdatedDate=" + lastUpdatedDate +
                ", isEnable='" + isEnable + '\'' +
                ", sdpUserId='" + sdpUserId + '\'' +
                ", sdpOrgId='" + sdpOrgId + '\'' +
                ", updateControlId='" + updateControlId + '\'' +
                '}';
    }

    public QueryWrapper buildQueryWrapper(SFunction<SacOnecustInfo, ?>... columns) {
        QueryWrapper<SacOnecustInfo> queryWrapper = new QueryWrapper<>();
        if (CollectionUtils.isNotEmpty(this.listCustId)) {
            queryWrapper.lambda().in(SacOnecustInfo::getCustId, this.getListCustId());
        }
        if (StringUtils.isNotEmpty(this.phone)) {
            queryWrapper.lambda().eq(SacOnecustInfo::getPhone, this.phone);
        }
        if (StringUtils.isNotEmpty(this.custId)) {
            queryWrapper.lambda().eq(SacOnecustInfo::getCustId, this.custId);
        }
        if (StringUtils.isNotEmpty(this.custName)) {
            queryWrapper.lambda().eq(SacOnecustInfo::getCustName, this.custName);
        }
        if (Objects.nonNull(columns)) {
            queryWrapper.lambda().select(columns);
        }
        return queryWrapper;
    }
}
