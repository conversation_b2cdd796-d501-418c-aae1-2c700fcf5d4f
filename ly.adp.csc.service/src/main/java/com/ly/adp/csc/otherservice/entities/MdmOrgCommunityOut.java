package com.ly.adp.csc.otherservice.entities;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class MdmOrgCommunityOut implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "区县ID")
    private String countyId;

    @ApiModelProperty(value = "城市ID  T_MDM_ORG_CITY CITY_ID")
    private String cityId;

    @ApiModelProperty(value = "区县编码")
    private String countyCode;

    @ApiModelProperty(value = "区县名称")
    private String countyName;

    @ApiModelProperty(value = "厂商标识ID")
    private String oemId;

    @ApiModelProperty(value = "集团标识ID")
    private String groupId;

    @ApiModelProperty(value = "厂商标识")
    private String oemCode;

    @ApiModelProperty(value = "集团标识")
    private String groupCode;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人姓名")
    private String createdName;

    @ApiModelProperty(value = "最后更新人员")
    private String modifier;

    @ApiModelProperty(value = "修改人姓名")
    private String modifyName;

    @ApiModelProperty(value = "是否可用")
    private String isEnable;

    @ApiModelProperty(value = "SDP用户ID")
    private String sdpUserId;

    @ApiModelProperty(value = "SDP组织ID")
    private String sdpOrgId;

    @ApiModelProperty(value = "并发控制字段")
    private String updateControlId;

    @ApiModelProperty(value = "扩展字段1")
    private String column1;

    @ApiModelProperty(value = "扩展字段2")
    private String column2;

    @ApiModelProperty(value = "扩展字段3")
    private String column3;

    @ApiModelProperty(value = "扩展字段4")
    private String column4;

    @ApiModelProperty(value = "扩展字段5")
    private String column5;

    @ApiModelProperty(value = "扩展字段6")
    private String column6;

    @ApiModelProperty(value = "扩展字段7")
    private String column7;

    @ApiModelProperty(value = "扩展字段8")
    private String column8;

    @ApiModelProperty(value = "扩展字段9")
    private String column9;

    @ApiModelProperty(value = "扩展字段10")
    private String column10;
    
    @ApiModelProperty(value = "创建时间")
    private String createdDate;

    @ApiModelProperty(value = "状态名称")
    private String isEnableName;
    
	public String getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
	}

	public String getCountyId() {
		return countyId;
	}

	public void setCountyId(String countyId) {
		this.countyId = countyId;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getOemCode() {
		return oemCode;
	}

	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getSdpUserId() {
		return sdpUserId;
	}

	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}

	public String getSdpOrgId() {
		return sdpOrgId;
	}

	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	public String getColumn1() {
		return column1;
	}

	public void setColumn1(String column1) {
		this.column1 = column1;
	}

	public String getColumn2() {
		return column2;
	}

	public void setColumn2(String column2) {
		this.column2 = column2;
	}

	public String getColumn3() {
		return column3;
	}

	public void setColumn3(String column3) {
		this.column3 = column3;
	}

	public String getColumn4() {
		return column4;
	}

	public void setColumn4(String column4) {
		this.column4 = column4;
	}

	public String getColumn5() {
		return column5;
	}

	public void setColumn5(String column5) {
		this.column5 = column5;
	}

	public String getColumn6() {
		return column6;
	}

	public void setColumn6(String column6) {
		this.column6 = column6;
	}

	public String getColumn7() {
		return column7;
	}

	public void setColumn7(String column7) {
		this.column7 = column7;
	}

	public String getColumn8() {
		return column8;
	}

	public void setColumn8(String column8) {
		this.column8 = column8;
	}

	public String getColumn9() {
		return column9;
	}

	public void setColumn9(String column9) {
		this.column9 = column9;
	}

	public String getColumn10() {
		return column10;
	}

	public void setColumn10(String column10) {
		this.column10 = column10;
	}

	public String getIsEnableName() {
		return isEnableName;
	}

	public void setIsEnableName(String isEnableName) {
		this.isEnableName = isEnableName;
	}

}
