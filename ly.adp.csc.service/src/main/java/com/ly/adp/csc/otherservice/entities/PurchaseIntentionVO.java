package com.ly.adp.csc.otherservice.entities;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/4/20
 */
public class PurchaseIntentionVO implements Serializable {
    private static final long serialVersionUID = 2040843874092288065L;
    @ApiModelProperty("分数")
    private BigDecimal proba;
    @ApiModelProperty("热度")
    private String ml_intention;

    public BigDecimal getProba() {
        return proba;
    }

    public void setProba(BigDecimal proba) {
        this.proba = proba;
    }

    public String getMl_intention() {
        return ml_intention;
    }

    public void setMl_intention(String ml_intention) {
        this.ml_intention = ml_intention;
    }
}
