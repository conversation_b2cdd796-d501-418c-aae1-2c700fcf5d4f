package com.ly.adp.csc.otherservice;

import com.ly.adp.csc.entities.dto.SalesConsultantDetailResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
@FeignClient(name = "biFeign", url = "${refer.bi.baseurl:}")
public interface BIFeign {

    // 配置apig
    @GetMapping("/adp/sales_consultant_summery")
    SalesConsultantDetailResponseDTO saleConsultantSum(@RequestParam("v") String v, @RequestParam("page_num") Integer page_num,
                                                          @RequestParam("page_size") Integer page_size,
                                                          @RequestParam("createdDateStart") String createdDateStart,
                                                          @RequestParam("createdDateEnd") String createdDateEnd,
                                                          @RequestParam("createdDateStart%2CcreatedDateEnd") String createdDateStarts,
                                                          @RequestParam("dlrCode") String dlrCode,
                                                          @RequestParam("userId") String userId);

    @GetMapping("/adp/activity")
    SalesConsultantDetailResponseDTO queryAgentOnlineActivityReport(@RequestParam("v") String v,
                                                                          @RequestParam("page_num") Integer page_num,
                                                                          @RequestParam("page_size") Integer page_size,
                                                                          @RequestParam("areaId") String areaId,
                                                                          @RequestParam("provinceCode") String provinceCode,
                                                                          @RequestParam("cityCode") String cityCode,
                                                                          @RequestParam("agentId") String agentId,
                                                                          @RequestParam("agentCompanyId") String agentCompanyId,
                                                                          @RequestParam("dlrCode") String dlrCode,
                                                                          @RequestParam("activityID") String activityID,
                                                                          @RequestParam("activityName") String activityName,
                                                                          @RequestParam("actBeginTimeStart") String actBeginTimeStart,
                                                                          @RequestParam("act_activity_type_code") Integer activityTypeCode);

    @GetMapping("/adp/activity")
    SalesConsultantDetailResponseDTO queryAgentOnlineActivityReportExpprt(@RequestParam("v") String v,
                                                                     @RequestParam("page_num") Integer page_num,
                                                       @RequestParam("page_size") Integer page_size,
                                                       @RequestParam("areaId") String areaId,
                                                       @RequestParam("provinceCode") String provinceCode,
                                                       @RequestParam("cityCode") String cityCode,
                                                       @RequestParam("agentId") String agentId,
                                                       @RequestParam("agentCompanyId") String agentCompanyId,
                                                       @RequestParam("dlrCode") String dlrCode,
                                                       @RequestParam("activityName") String activityName,
                                                       @RequestParam("actBeginTimeStart") String actBeginTimeStart,
                                                       @RequestParam("hxStatus") String hxStatus,
                                                       @RequestParam("act_activity_type_code") Integer activityTypeCode);
}
