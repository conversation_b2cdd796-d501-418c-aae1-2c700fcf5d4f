package com.ly.adp.csc.otherservice.entities.in;

import java.io.Serializable;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.adp.csc.entities.in.PageInfo;
import com.ly.adp.csc.otherservice.entities.TUscMdmEmployeeCareer;

import io.swagger.annotations.ApiModelProperty;

public class MdmOrgEmployeeIn extends PageInfo implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "业务岗位名称")
    private String stationName;

    @ApiModelProperty(value = "第一岗位")
    private String stationId;

    @ApiModelProperty(value = "查询用是否第一岗位")
    private String keyStationId;

    @ApiModelProperty(value = "系统岗位编码：")
    private String positionCode;

    @ApiModelProperty(value = "系统岗位名称")
    private String positionName;

    @ApiModelProperty(value = "#所属组织")
    private String orgName;
    @ApiModelProperty(value = "#所属组织")
    private String groupName;

    @ApiModelProperty(value = "所属经销商")
    private String dlrShortName;

    @ApiModelProperty(value = "性别名称")
    private String genderName;

    @ApiModelProperty(value = "品牌")
    private String carBrandCode;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "职员ID")
    private String empId;

    @ApiModelProperty(value = "部门ID  T_MDM_ORG_DEPT DEPT_ID")
    private String deptId;

    @ApiModelProperty(value = "专营店编码")
    private String dlrCode;

    @ApiModelProperty(value = "职员编码")
    private String empCode;

    @ApiModelProperty(value = "职员姓名")
    private String empName;

    @ApiModelProperty(value = "工作电话")
    private String workTel;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "性别编码")
    private String genderCode;

    @ApiModelProperty(value = "学历编码")
    private String degreeCode;

    @ApiModelProperty(value = "个人通信地址")
    private String personAddr;

    @ApiModelProperty(value = "邮编")
    private String zip;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "传真号")
    private String fax;

    @ApiModelProperty(value = "国籍编码")
    private String nationalityCode;

    @ApiModelProperty(value = "婚姻状况编码")
    private String marriagedCode;

    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    @ApiModelProperty(value = "毕业院校")
    private String school;

    @ApiModelProperty(value = "专业")
    private String degreepro;

    @ApiModelProperty(value = "技能特长")
    private String skillSpecial;

    @ApiModelProperty(value = "家庭电话")
    private String familyPhone;

    @ApiModelProperty(value = "紧急联络人")
    private String secondMan;

    @ApiModelProperty(value = "紧急联络人电话")
    private String secondManTel;

    @ApiModelProperty(value = "民族编码")
    private String nationCode;

    @ApiModelProperty(value = "是否有驾照")
    private String isDriver;
    @ApiModelProperty(value = "是否NCH")
    private String isNch;

    @ApiModelProperty(value = "招聘方式")
    private String employType;

    @ApiModelProperty(value = "政治面貌编码")
    private String politicsCode;

    @ApiModelProperty(value = "证件类型编码")
    private String credTypeCode;

    @ApiModelProperty(value = "证件号")
    private String credNo;

    @ApiModelProperty(value = "职称")
    private String classes;

    @ApiModelProperty(value = "职员照片")
    private String empPic;

    @ApiModelProperty(value = "自我评价")
    private String selfEstimate;

    @ApiModelProperty(value = "专营店ID")
    private String dlrId;

    @ApiModelProperty(value = "二级网点ID 如属于一级店则为空")
    private String secDlrId;

    @ApiModelProperty(value = "二级网点编码 如属于一级店则为空")
    private String secDlrCode;

    @ApiModelProperty(value = "直属上司ID")
    private String headManager;

    @ApiModelProperty(value = "是否冻结")
    private String isFrozen;
    @ApiModelProperty(value = "状态（1在职，2离职")
    private String userStatus;
    private String userStatus1;
    @ApiModelProperty(value = "一网编码  一网DLRCODE")
    private String oldDlrCode;

    @ApiModelProperty(value = "一网ID  一网DLR_ID")
    private String oldDlrId;

    @ApiModelProperty(value = "厂商标识ID")
    private String oemId;

    @ApiModelProperty(value = "集团标识ID")
    private String groupId;

    @ApiModelProperty(value = "厂商标识")
    private String oemCode;

    @ApiModelProperty(value = "集团标识")
    private String groupCode;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人姓名")
    private String createdName;
    
    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private String createdDate;

    @ApiModelProperty(value = "最后更新人员")
    private String modifier;

    @ApiModelProperty(value = "修改人姓名")
    private String modifyName;

    @ApiModelProperty(value = "是否可用")
    private String isEnable;

    @ApiModelProperty(value = "SDP用户ID")
    private String sdpUserId;

    @ApiModelProperty(value = "SDP组织ID")
    private String sdpOrgId;

    @ApiModelProperty(value = "并发控制字段")
    private String updateControlId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "是否培训合格")
    private String column1;

    @ApiModelProperty(value = "兼职岗位")
    private String column2;

    @ApiModelProperty(value = "省份")
    private String column3;

    @ApiModelProperty(value = "城市")
    private String column4;

    @ApiModelProperty(value = "区县")
    private String column5;

    @ApiModelProperty(value = "扩展字段6")
    private String column6;

    @ApiModelProperty(value = "扩展字段7")
    private String column7;

    @ApiModelProperty(value = "扩展字段8")
    private String column8;

    @ApiModelProperty(value = "扩展字段9")
    private String column9;

    @ApiModelProperty(value = "扩展字段10")
    private String column10;

    @ApiModelProperty(value = "是否销售顾问销售经理")
    private String isCsr;

    @ApiModelProperty(value = "离职原因编码")
    private String resignationReasonCode;

    @ApiModelProperty(value = "离职原因名称")
    private String resignationReasonName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "生效日期")
    private String effectDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "失效日期")
    private String diseffectDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "关闭日期")
    private String disableDate;
	
	@ApiModelProperty(value = "生日日期")
    private String birthDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "离职日期")
	private String leaveDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "驾照日期")
    private String driverDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "雇佣日期")
	private String employDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "工作日期")   
    private String businessDate;

	@ApiModelProperty(value = "是否显示手机号")   
    private String showPhone;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "生效日期") 
    private String activeDate;
	
	@ApiModelProperty(value = "关闭说明") 
    private String userDesc;
	
	@ApiModelProperty(value = "是否专营店") 
    private String isDlr;
	@ApiModelProperty(value = "是否集团") 
    private String isGroup;
	
	@ApiModelProperty(value = "专营店账号id") 
    private String dlrUserId;
	
	@ApiModelProperty(value = "专营店账号Name") 
    private String dlrUserName;

	
	@ApiModelProperty(value = "专营店账号Name") 
    private String adminRoleId;
	
	private String initBrandCode; //初始化品牌编码

	@ApiModelProperty(value = "灰度") 
    private String grayScale;

	@ApiModelProperty(value = "灰度筛选字段") 
    private String isGrayScale;
	
	@ApiModelProperty(value = "员工过滤Id") 
    private String filterId;

    @ApiModelProperty(value = "换店还是修改")
    private String type;

    @ApiModelProperty(value = "岗级")
    private String duty;
    @ApiModelProperty(value = "岗级名字")
    private String dutyName;

    @ApiModelProperty(value = "审核状态")
    private String status;

	@ApiModelProperty(value = "员工用户名称")
	private String employeeName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "结束时间")
	private String EndTime;

    @ApiModelProperty(value = "是否留学")
    private String overseasStudy;
    
    @ApiModelProperty(value = "是否过滤发起离职/换店申请的员工")
    private String filterFlag;
    
	public String getFilterFlag() {
		return filterFlag;
	}

	public void setFilterFlag(String filterFlag) {
		this.filterFlag = filterFlag;
	}

	//员工履历
	private List<TUscMdmEmployeeCareer> list;



    public String getOverseasStudy() {
        return overseasStudy;
    }

    public void setOverseasStudy(String overseasStudy) {
        this.overseasStudy = overseasStudy;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<TUscMdmEmployeeCareer> getList() {
        return list;
    }

    public void setList(List<TUscMdmEmployeeCareer> list) {
        this.list = list;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEndTime() {
        return EndTime;
    }

    public void setEndTime(String endTime) {
        EndTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKeyStationId() {
        return keyStationId;
    }

    public void setKeyStationId(String keyStationId) {
        this.keyStationId = keyStationId;
    }

	public String getFilterId() {
		return filterId;
	}

	public void setFilterId(String filterId) {
		this.filterId = filterId;
	}

	public String getGrayScale() {
		return grayScale;
	}

	public void setGrayScale(String grayScale) {
		this.grayScale = grayScale;
	}

	public String getIsGrayScale() {
		return isGrayScale;
	}

	public void setIsGrayScale(String isGrayScale) {
		this.isGrayScale = isGrayScale;
	}

	public String getAdminRoleId() {
		return adminRoleId;
	}

	public void setAdminRoleId(String adminRoleId) {
		this.adminRoleId = adminRoleId;
	}

	public String getDlrUserId() {
		return dlrUserId;
	}

	public void setDlrUserId(String dlrUserId) {
		this.dlrUserId = dlrUserId;
	}

	public String getDlrUserName() {
		return dlrUserName;
	}

	public void setDlrUserName(String dlrUserName) {
		this.dlrUserName = dlrUserName;
	}

	public String getIsGroup() {
		return isGroup;
	}

	public void setIsGroup(String isGroup) {
		this.isGroup = isGroup;
	}

	public String getIsDlr() {
		return isDlr;
	}

	public void setIsDlr(String isDlr) {
		this.isDlr = isDlr;
	}

	public String getUserDesc() {
		return userDesc;
	}

	public void setUserDesc(String userDesc) {
		this.userDesc = userDesc;
	}


	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getActiveDate() {
		return activeDate;
	}

	public void setActiveDate(String activeDate) {
		this.activeDate = activeDate;
	}

	public String getDisableDate() {
		return disableDate;
	}

	public void setDisableDate(String disableDate) {
		this.disableDate = disableDate;
	}

	public String getIsNch() {
		return isNch;
	}

	public void setIsNch(String isNch) {
		this.isNch = isNch;
	}

	public String getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getPositionCode() {
		return positionCode;
	}

	public void setPositionCode(String positionCode) {
		this.positionCode = positionCode;
	}

	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getDlrShortName() {
		return dlrShortName;
	}

	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}

	public String getGenderName() {
		return genderName;
	}

	public void setGenderName(String genderName) {
		this.genderName = genderName;
	}

	public String getCarBrandCode() {
		return carBrandCode;
	}

	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getEmpId() {
		return empId;
	}

	public void setEmpId(String empId) {
		this.empId = empId;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getDlrCode() {
		return dlrCode;
	}

	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}

	public String getEmpCode() {
		return empCode;
	}

	public void setEmpCode(String empCode) {
		this.empCode = empCode;
	}

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public String getWorkTel() {
		return workTel;
	}

	public void setWorkTel(String workTel) {
		this.workTel = workTel;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getGenderCode() {
		return genderCode;
	}

	public void setGenderCode(String genderCode) {
		this.genderCode = genderCode;
	}

	public String getDegreeCode() {
		return degreeCode;
	}

	public void setDegreeCode(String degreeCode) {
		this.degreeCode = degreeCode;
	}

	public String getPersonAddr() {
		return personAddr;
	}

	public void setPersonAddr(String personAddr) {
		this.personAddr = personAddr;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getNationalityCode() {
		return nationalityCode;
	}

	public void setNationalityCode(String nationalityCode) {
		this.nationalityCode = nationalityCode;
	}

	public String getMarriagedCode() {
		return marriagedCode;
	}

	public void setMarriagedCode(String marriagedCode) {
		this.marriagedCode = marriagedCode;
	}

	public String getNativePlace() {
		return nativePlace;
	}

	public void setNativePlace(String nativePlace) {
		this.nativePlace = nativePlace;
	}

	public String getSchool() {
		return school;
	}

	public void setSchool(String school) {
		this.school = school;
	}

	public String getDegreepro() {
		return degreepro;
	}

	public void setDegreepro(String degreepro) {
		this.degreepro = degreepro;
	}

	public String getSkillSpecial() {
		return skillSpecial;
	}

	public void setSkillSpecial(String skillSpecial) {
		this.skillSpecial = skillSpecial;
	}

	public String getFamilyPhone() {
		return familyPhone;
	}

	public void setFamilyPhone(String familyPhone) {
		this.familyPhone = familyPhone;
	}

	public String getSecondMan() {
		return secondMan;
	}

	public void setSecondMan(String secondMan) {
		this.secondMan = secondMan;
	}

	public String getSecondManTel() {
		return secondManTel;
	}

	public void setSecondManTel(String secondManTel) {
		this.secondManTel = secondManTel;
	}

	public String getNationCode() {
		return nationCode;
	}

	public void setNationCode(String nationCode) {
		this.nationCode = nationCode;
	}

	public String getIsDriver() {
		return isDriver;
	}

	public void setIsDriver(String isDriver) {
		this.isDriver = isDriver;
	}

	public String getEmployType() {
		return employType;
	}

	public void setEmployType(String employType) {
		this.employType = employType;
	}

	public String getPoliticsCode() {
		return politicsCode;
	}

	public void setPoliticsCode(String politicsCode) {
		this.politicsCode = politicsCode;
	}

	public String getCredTypeCode() {
		return credTypeCode;
	}

	public void setCredTypeCode(String credTypeCode) {
		this.credTypeCode = credTypeCode;
	}

	public String getCredNo() {
		return credNo;
	}

	public void setCredNo(String credNo) {
		this.credNo = credNo;
	}

	public String getClasses() {
		return classes;
	}

	public void setClasses(String classes) {
		this.classes = classes;
	}

	public String getEmpPic() {
		return empPic;
	}

	public void setEmpPic(String empPic) {
		this.empPic = empPic;
	}

	public String getSelfEstimate() {
		return selfEstimate;
	}

	public void setSelfEstimate(String selfEstimate) {
		this.selfEstimate = selfEstimate;
	}

	public String getDlrId() {
		return dlrId;
	}

	public void setDlrId(String dlrId) {
		this.dlrId = dlrId;
	}

	public String getSecDlrId() {
		return secDlrId;
	}

	public void setSecDlrId(String secDlrId) {
		this.secDlrId = secDlrId;
	}

	public String getSecDlrCode() {
		return secDlrCode;
	}

	public void setSecDlrCode(String secDlrCode) {
		this.secDlrCode = secDlrCode;
	}

	public String getHeadManager() {
		return headManager;
	}

	public void setHeadManager(String headManager) {
		this.headManager = headManager;
	}

	public String getIsFrozen() {
		return isFrozen;
	}

	public void setIsFrozen(String isFrozen) {
		this.isFrozen = isFrozen;
	}

	public String getOldDlrCode() {
		return oldDlrCode;
	}

	public void setOldDlrCode(String oldDlrCode) {
		this.oldDlrCode = oldDlrCode;
	}

	public String getOldDlrId() {
		return oldDlrId;
	}

	public void setOldDlrId(String oldDlrId) {
		this.oldDlrId = oldDlrId;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getOemCode() {
		return oemCode;
	}

	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getSdpUserId() {
		return sdpUserId;
	}

	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}

	public String getSdpOrgId() {
		return sdpOrgId;
	}

	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getColumn1() {
		return column1;
	}

	public void setColumn1(String column1) {
		this.column1 = column1;
	}

	public String getColumn2() {
		return column2;
	}

	public void setColumn2(String column2) {
		this.column2 = column2;
	}

	public String getColumn3() {
		return column3;
	}

	public void setColumn3(String column3) {
		this.column3 = column3;
	}

	public String getColumn4() {
		return column4;
	}

	public void setColumn4(String column4) {
		this.column4 = column4;
	}

	public String getColumn5() {
		return column5;
	}

	public void setColumn5(String column5) {
		this.column5 = column5;
	}

	public String getColumn6() {
		return column6;
	}

	public void setColumn6(String column6) {
		this.column6 = column6;
	}

	public String getColumn7() {
		return column7;
	}

	public void setColumn7(String column7) {
		this.column7 = column7;
	}

	public String getColumn8() {
		return column8;
	}

	public void setColumn8(String column8) {
		this.column8 = column8;
	}

	public String getColumn9() {
		return column9;
	}

	public void setColumn9(String column9) {
		this.column9 = column9;
	}

	public String getColumn10() {
		return column10;
	}

	public void setColumn10(String column10) {
		this.column10 = column10;
	}

	public String getIsCsr() {
		return isCsr;
	}

	public void setIsCsr(String isCsr) {
		this.isCsr = isCsr;
	}

	public String getResignationReasonCode() {
		return resignationReasonCode;
	}

	public void setResignationReasonCode(String resignationReasonCode) {
		this.resignationReasonCode = resignationReasonCode;
	}

	public String getResignationReasonName() {
		return resignationReasonName;
	}

	public void setResignationReasonName(String resignationReasonName) {
		this.resignationReasonName = resignationReasonName;
	}

	public String getEffectDate() {
		return effectDate;
	}

	public void setEffectDate(String effectDate) {
		this.effectDate = effectDate;
	}

	public String getDiseffectDate() {
		return diseffectDate;
	}

	public void setDiseffectDate(String diseffectDate) {
		this.diseffectDate = diseffectDate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getLeaveDate() {
		return leaveDate;
	}

	public void setLeaveDate(String leaveDate) {
		this.leaveDate = leaveDate;
	}

	public String getDriverDate() {
		return driverDate;
	}

	public void setDriverDate(String driverDate) {
		this.driverDate = driverDate;
	}

	public String getEmployDate() {
		return employDate;
	}

	public void setEmployDate(String employDate) {
		this.employDate = employDate;
	}

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getBusinessDate() {
		return businessDate;
	}

    public String getUserStatus1() {
        return userStatus1;
    }

    public void setUserStatus1(String userStatus1) {
        this.userStatus1 = userStatus1;
    }

    public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public String getShowPhone() {
		return showPhone;
	}

	public void setShowPhone(String showPhone) {
		this.showPhone = showPhone;
	}

	public String getInitBrandCode() {
		return initBrandCode;
	}

	public void setInitBrandCode(String initBrandCode) {
		this.initBrandCode = initBrandCode;
	}

    
}
