package com.ly.adp.csc.entities.dto.koc.wechat;

import java.io.Serializable;

/**
 * 企微端用户备注操作请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class WechatUserRemarkRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（添加备注时使用）
     */
    private String smartId;

    /**
     * 关系ID（编辑/删除备注时使用）
     */
    private String relId;

    /**
     * 备注内容
     */
    private String remarkContent;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public String getRemarkContent() {
        return remarkContent;
    }

    public void setRemarkContent(String remarkContent) {
        this.remarkContent = remarkContent;
    }

    @Override
    public String toString() {
        return "WechatUserRemarkRequestDTO{" +
                "smartId='" + smartId + '\'' +
                ", relId='" + relId + '\'' +
                ", remarkContent='" + remarkContent + '\'' +
                '}';
    }
}
