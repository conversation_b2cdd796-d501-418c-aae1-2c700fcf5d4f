package com.ly.adp.csc.distjob;

import com.ly.adp.csc.service.*;
import com.ly.mp.acc.manage.service.IAccBuActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class CscDistJob {

    private static final Logger logger = LoggerFactory.getLogger(CscDistJob.class);
    @Autowired
    ISacOnecustInfoService sacOnecustInfoService;
    @Autowired
    ISacOnetaskInfoService sacOnetaskInfoService;
    @Autowired
    ISacComplaintsInfoService sacComplaintsInfoService;
    @Autowired
    ISacOnetaskLeadInTempService sacOnetaskLeadInTempService;
    @Autowired
    ISacClueCenterService sacClueCenterService;
    @Autowired
    IAccBuActivityService accBuActivityService;
    @Autowired
    ISacTestDriveLongApplyService sacTestDriveLongApplyService;

    // 新增员工离职线索分配服务
    @Autowired
    ISacEmployeeDimissionClueService sacEmployeeDimissionClueService;

    public void cscDistjob001() {
        // 客户信息变更记录定时（接口表到业务表）

        logger.info("ADP_CSC_DISTJOB_DS001进来{}", LocalDateTime.now());
        sacOnecustInfoService.SacOnecustChangeLogDbjob();
    }

    public void cscDistjob002() {
        // 任务人员导入定时调用（接口表到业务表）

        logger.info("ADP_CSC_DISTJOB_DS002进来{}", LocalDateTime.now());
        sacOnetaskLeadInTempService.taskInfoDbjob();
    }

    public void cscDistjob003() {
        // 任务发送消息定时调用

        logger.info("ADP_CSC_DISTJOB_DS003进来{}", LocalDateTime.now());
        sacOnetaskLeadInTempService.taskMsgDbjob();
    }

    public void cscDistjob004() {
        // 诉工单异常关单定时调用

        logger.info("ADP_CSC_DISTJOB_DS004进来{}", LocalDateTime.now());
        sacComplaintsInfoService.complaintsDbjob();
    }


    public void cscDistjob005() {
        // 任务发布对象导入定时调用

        logger.info("ADP_CSC_DISTJOB_DS005进来{}", LocalDateTime.now());
        sacOnetaskInfoService.taskPublishJob();
    }

    public void cscDistjob006() {
        // 任务周期重复执行定时调用

        logger.info("ADP_CSC_DISTJOB_DS006进来{}", LocalDateTime.now());
        sacOnetaskInfoService.cycleExecuteTask();
    }

    public void cscDistjob007() {
        // 超24小时线索自动分配定时调用

        logger.info("ADP_CSC_DISTJOB_DS007进来{}", LocalDateTime.now());
        sacClueCenterService.clueAutoAsgnDbjob();
    }

    public void cscDistjob008() {
        // 活动状态更新：获取活动类型为非APP的活动，以及已审核的，到了发布时间的活动，更新发布状态为已发布

        logger.info("ADP_CSC_DISTJOB_DS008进来{}", LocalDateTime.now());
        accBuActivityService.updateActivityStatusDistJob();
    }

    public void cscDistjob009() {
        // 活动定时发送短信

        logger.info("ADP_CSC_DISTJOB_DS009进来{}", LocalDateTime.now());
        sacTestDriveLongApplyService.sendMessageJoB();
    }

    public void cscDistjob010() {
        // 客户生日和交车纪念日刷新跟进时间

        logger.info("ADP_CSC_DISTJOB_DS010进来{}", LocalDateTime.now());
        sacClueCenterService.clueAutoFollowTimeDistJob();
    }

    public void cscDistjob011() {
        // 接口创建的任务状态 已发布 -》 已结束定时推送mq

        logger.info("ADP_CSC_DISTJOB_DS011进来{}", LocalDateTime.now());
        sacOnetaskInfoService.sendFinishTaskMqJob();
    }
    
    /**
     * 员工离职线索自动分配给店长定时任务
     * 每天凌晨4点执行，处理前一天离职审核通过的员工名下的线索
     */
    public void cscDistjob012() {
        // 员工离职线索自动分配给店长
        logger.info("ADP_CSC_DISTJOB_DS012进来{}", LocalDateTime.now());
        sacEmployeeDimissionClueService.transferDimissionEmployeeClues();
    }
}
