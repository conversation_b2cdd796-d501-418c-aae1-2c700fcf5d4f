package com.ly.adp.csc.entities;

import java.io.Serializable;
import java.util.Map;

import io.swagger.annotations.ApiModel;

@ApiModel("短信接口返回")
public class MessageResponseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	private String message;
	private String code;
	private Map<String, Object> result;
	private String success;

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Map<String, Object> getResult() {
		return result;
	}

	public void setResult(Map<String, Object> result) {
		this.result = result;
	}

	public String getSuccess() {
		return success;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

}
