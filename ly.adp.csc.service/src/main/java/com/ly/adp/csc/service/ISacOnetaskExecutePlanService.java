package com.ly.adp.csc.service;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacOnetaskExecutePlan;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * <p>
 * 任务执行周期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-14
 */
public interface ISacOnetaskExecutePlanService extends IService<SacOnetaskExecutePlan> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacOnetaskExecutePlanFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacOnetaskExecutePlanSaveInfo(Map<String, Object> dataInfo,String token);
}
