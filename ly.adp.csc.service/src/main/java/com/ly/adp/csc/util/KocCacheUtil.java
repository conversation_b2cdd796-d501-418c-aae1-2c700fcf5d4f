package com.ly.adp.csc.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * KOC缓存工具类
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class KocCacheUtil {

    private static final Logger log = LoggerFactory.getLogger(KocCacheUtil.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 缓存Key常量
    public static final String TAG_LIST_KEY = "tag:list";
    public static final String EXPERT_TYPES_KEY = "expert:types";
    public static final String USER_TAGS_KEY_PREFIX = "user:tags:";
    public static final String USER_EXPERTS_KEY_PREFIX = "user:experts:";
    public static final String USER_FULL_KEY_PREFIX = "user:full:";

    // 缓存TTL常量（秒）
    public static final long TAG_LIST_TTL = 7200L; // 2小时
    public static final long EXPERT_TYPES_TTL = 7200L; // 2小时
    public static final long USER_TAGS_TTL = 3600L; // 1小时
    public static final long USER_EXPERTS_TTL = 3600L; // 1小时
    public static final long USER_FULL_TTL = 1800L; // 30分钟

    /**
     * 获取缓存
     */
    public Object get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("获取缓存失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 设置缓存
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("设置缓存成功，key: {}, timeout: {}", key, timeout);
        } catch (Exception e) {
            log.error("设置缓存失败，key: {}", key, e);
        }
    }

    /**
     * 删除缓存
     */
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
            log.debug("删除缓存成功，key: {}", key);
        } catch (Exception e) {
            log.error("删除缓存失败，key: {}", key, e);
        }
    }

    /**
     * 批量删除缓存
     */
    public void deletePattern(String pattern) {
        try {
             Set<String> keys = redisTemplate.keys(pattern);
             if (keys != null && !keys.isEmpty()) {
                 redisTemplate.delete(keys);
             }
            log.debug("批量删除缓存成功，pattern: {}", pattern);
        } catch (Exception e) {
            log.error("批量删除缓存失败，pattern: {}", pattern, e);
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean exists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查缓存存在失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取标签列表缓存
     */
    public Object getTagListCache() {
        return get(TAG_LIST_KEY);
    }

    /**
     * 设置标签列表缓存
     */
    public void setTagListCache(Object tagList) {
        set(TAG_LIST_KEY, tagList, TAG_LIST_TTL, TimeUnit.SECONDS);
    }

    /**
     * 获取达人类型列表缓存
     */
    public Object getExpertTypesCache() {
        return get(EXPERT_TYPES_KEY);
    }

    /**
     * 设置达人类型列表缓存
     */
    public void setExpertTypesCache(Object expertTypes) {
        set(EXPERT_TYPES_KEY, expertTypes, EXPERT_TYPES_TTL, TimeUnit.SECONDS);
    }

    /**
     * 获取用户标签缓存
     */
    public Object getUserTagsCache(String smartId) {
        return get(USER_TAGS_KEY_PREFIX + smartId);
    }

    /**
     * 设置用户标签缓存
     */
    public void setUserTagsCache(String smartId, Object userTags) {
        set(USER_TAGS_KEY_PREFIX + smartId, userTags, USER_TAGS_TTL, TimeUnit.SECONDS);
    }

    /**
     * 获取用户达人类型缓存
     */
    public Object getUserExpertsCache(String smartId) {
        return get(USER_EXPERTS_KEY_PREFIX + smartId);
    }

    /**
     * 设置用户达人类型缓存
     */
    public void setUserExpertsCache(String smartId, Object userExperts) {
        set(USER_EXPERTS_KEY_PREFIX + smartId, userExperts, USER_EXPERTS_TTL, TimeUnit.SECONDS);
    }

    /**
     * 获取用户完整信息缓存
     */
    public Object getUserFullCache(String smartId) {
        return get(USER_FULL_KEY_PREFIX + smartId);
    }

    /**
     * 设置用户完整信息缓存
     */
    public void setUserFullCache(String smartId, Object userFullInfo) {
        set(USER_FULL_KEY_PREFIX + smartId, userFullInfo, USER_FULL_TTL, TimeUnit.SECONDS);
    }

    /**
     * 清除用户相关缓存
     */
    public void clearUserCaches(String smartId) {
        delete(USER_TAGS_KEY_PREFIX + smartId);
        delete(USER_EXPERTS_KEY_PREFIX + smartId);
        delete(USER_FULL_KEY_PREFIX + smartId);
    }

    /**
     * 清除所有用户缓存
     */
    public void clearAllUserCaches() {
        deletePattern(USER_TAGS_KEY_PREFIX + "*");
        deletePattern(USER_EXPERTS_KEY_PREFIX + "*");
        deletePattern(USER_FULL_KEY_PREFIX + "*");
    }

    /**
     * 刷新标签缓存
     */
    public void refreshTagCache() {
        delete(TAG_LIST_KEY);
        log.info("标签缓存已刷新");
    }

    /**
     * 刷新达人类型缓存
     */
    public void refreshExpertTypeCache() {
        delete(EXPERT_TYPES_KEY);
        log.info("达人类型缓存已刷新");
    }

    /**
     * 刷新所有缓存
     */
    public void refreshAllCaches() {
        refreshTagCache();
        refreshExpertTypeCache();
        clearAllUserCaches();
        log.info("所有缓存已刷新");
    }
}
