package com.ly.adp.csc.entities.dto;

/**
 * <AUTHOR>
 * @Date 2024/6/24
 * @Version 1.0.0
 **/
public class TaskClueMqDto {
    /**
     * 线索名称
     */
    private String clueName;
    /**
     * 完成人
     */
    private String completer;
    /**
     * 完成时间
     */
    private String completionTime;
    /**
     * 是否超期，1 是 0 否
     */
    private String isOverdue;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 线索完成状态，1 已完成 0 未完成
     */
    private String status;

    /**
     * 任务id
     */
    private String taskId;

    public String getClueName() { return clueName; }
    public void setClueName(String value) { this.clueName = value; }

    public String getCompleter() { return completer; }
    public void setCompleter(String value) { this.completer = value; }

    public String getCompletionTime() { return completionTime; }
    public void setCompletionTime(String value) { this.completionTime = value; }

    public String getIsOverdue() { return isOverdue; }
    public void setIsOverdue(String value) { this.isOverdue = value; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String value) { this.phoneNumber = value; }

    public String getStatus() { return status; }
    public void setStatus(String value) { this.status = value; }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
}
