package com.ly.adp.csc.entities.dto.indto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/7
 * @Version 1.0.0
 **/
@ApiModel("校验当前门店是否配置品牌大使-入参DTO")
public class AgentActivityCheckDlrInDto implements Serializable {

    private static final long serialVersionUID = -2823106819140218336L;

    @ApiModelProperty("门店编码")
    @NotNull
    private String dlrCode;

    public @NotNull String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(@NotNull String dlrCode) {
        this.dlrCode = dlrCode;
    }
}
