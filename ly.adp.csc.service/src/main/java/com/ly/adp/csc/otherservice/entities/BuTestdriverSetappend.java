package com.ly.adp.csc.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 试驾车申请设置附件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("t_usc_bu_testdriver_setappend")
public class BuTestdriverSetappend implements Serializable {
   private String onOrder;

    public String getOnOrder() {
        return onOrder;
    }

    public void setOnOrder(String onOrder) {
        this.onOrder = onOrder;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 试驾设置附件ID
     */
    @TableId("TEST_CAR_APPEND_ID")
    private String testCarAppendId;

    /**
     * 附件类型
     */
    @TableField("APPEND_TYPE_CODE")
    private String appendTypeCode;

    /**
     * 附件地址
     */
    @TableField("APPEND_URL")
    private String appendUrl;

    /**
     * 试驾车设置ID
     */
    @TableField("TESTDRIV_CARSET_ID")
    private String testdrivCarsetId;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getTestCarAppendId() {
        return testCarAppendId;
    }

    public void setTestCarAppendId(String testCarAppendId) {
        this.testCarAppendId = testCarAppendId;
    }

    public String getAppendTypeCode() {
        return appendTypeCode;
    }

    public void setAppendTypeCode(String appendTypeCode) {
        this.appendTypeCode = appendTypeCode;
    }

    public String getAppendUrl() {
        return appendUrl;
    }

    public void setAppendUrl(String appendUrl) {
        this.appendUrl = appendUrl;
    }

    public String getTestdrivCarsetId() {
        return testdrivCarsetId;
    }

    public void setTestdrivCarsetId(String testdrivCarsetId) {
        this.testdrivCarsetId = testdrivCarsetId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BuTestdriverSetappend{" +
        "testCarAppendId=" + testCarAppendId +
        ", appendTypeCode=" + appendTypeCode +
        ", appendUrl=" + appendUrl +
        ", testdrivCarsetId=" + testdrivCarsetId +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
