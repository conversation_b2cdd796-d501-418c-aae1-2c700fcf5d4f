package com.ly.adp.csc.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.ISacBuBoutiqueDetailRecordService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 精品明细记录日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@RestController
@Api(value = "精品明细记录日志表服务", tags = "精品明细记录日志表服务")
@RequestMapping(value = "/ly/adp/csc/sacBuBoutiqueDetailRecord", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SacBuBoutiqueDetailRecordController {
	@Autowired
	ISacBuBoutiqueDetailRecordService sacBuBoutiqueDetailRecordService;

	@ApiOperation(value = "精品明细记录日志表查询", notes = "精品明细记录日志表查询")
	@PostMapping(value = "/sacBuBoutiqueDetailRecordquery.do")
	public ListResult<Map<String, Object>> sacBuBoutiqueDetailRecordFindInfo(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doList(() -> {
			return sacBuBoutiqueDetailRecordService.sacBuBoutiqueDetailRecordFindInfo(dateInfo, authentication);
		}).result();
	}

	@ApiOperation(value = "精品明细记录日志表维护", notes = "精品明细记录日志表维护")
	@PostMapping(value = "/sacBuBoutiqueDetailRecordsave.do")
	public EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordSaveInfo(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doEntity(() -> {
			return sacBuBoutiqueDetailRecordService.sacBuBoutiqueDetailRecordSaveInfo(dateInfo, authentication);
		}).result();
	}

	@ApiOperation(value = "精品明细记录日志表维护多个", notes = "精品明细记录日志表维护多个")
	@PostMapping(value = "/sacBuBoutiqueDetailRecordsaveByList.do")
	public EntityResult<Map<String, Object>> sacBuBoutiqueDetailRecordsaveByList(
			@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) List<Map<String, Object>> dateInfo) {
		return BusicenInvoker.doEntity(() -> {
			return sacBuBoutiqueDetailRecordService.sacBuBoutiqueDetailRecordsaveByList(dateInfo, authentication);
		}).result();
	}
}
