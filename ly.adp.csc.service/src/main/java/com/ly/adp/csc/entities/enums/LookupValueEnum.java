package com.ly.adp.csc.entities.enums;

/**
 * 值列表-值枚举
 * <AUTHOR>
 * @Version 1.0.0
 **/
public enum LookupValueEnum {

    SWITCH_001("SWITCH_001", "SP自动审批开关"),
    CONFIG_001("CONFIG_001", "产品专家每日任务下发限制数");

    private final String code;
    private final String description;

    LookupValueEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static LookupValueEnum fromCode(String code) {
        for (LookupValueEnum status : LookupValueEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid LookupTypeValueEnum code: " + code);
    }
}
