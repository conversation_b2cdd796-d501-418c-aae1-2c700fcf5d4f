package com.ly.adp.csc.enums;

/**
 * 用户关系类型枚举
 * <AUTHOR>
 * @since 2025-07-28
 */
public enum UserRelationTypeEnum {

    /**
     * 标签
     */
    TAG(1, "标签"),

    /**
     * 达人类型
     */
    EXPERT_TYPE(2, "达人类型"),

    /**
     * 备注
     */
    REMARK(3, "备注");

    private final Integer code;
    private final String desc;

    UserRelationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static UserRelationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserRelationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
