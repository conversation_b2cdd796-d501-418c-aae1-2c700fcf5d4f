package com.ly.adp.csc.service;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.csc.entities.SacComplaintsInfo;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

/**
 * <p>
 * 投诉工单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
public interface ISacComplaintsInfoService extends IService<SacComplaintsInfo> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacComplaintsInfoFindInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacComplaintsInfoSaveInfo(Map<String, Object> dataInfo,String token);
	
	/**
	 *投诉工单异常关单定时调用
	 * @param info
	 * @return
	 */
	void complaintsDbjob();
	
	/**
	 * 投诉审核节点查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacComplaintsInfoAuditQuery(ParamPage<Map<String, Object>> dataInfo,String token);

	/**
	 * 投诉通用查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacComplaintsInfoFind(Map<String, Object> dataInfo,String token);
	
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacServerClassReport(Map<String, Object> dataInfo,String token);
	
	/**
	 * 投诉工单表查询导出
	 * @param info
	 * @return
	 */
	OptResult querycomplaintsListExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);
	
	/**
	 * 投诉审核节点查询导出
	 * @param info
	 * @return
	 */
	OptResult sacComplaintsInfoAuditQueryExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);
	
	/**
	 * 公共审核工单查询导出
	 * @param info
	 * @return
	 */
	OptResult queryAuditRecordListExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

	OptResult complaintsVoid(Map<String, Object> dateInfo, String authentication);

	EntityResult<Map<String, Object>> findAuditId(Map<String, Object> dataInfo);
}
