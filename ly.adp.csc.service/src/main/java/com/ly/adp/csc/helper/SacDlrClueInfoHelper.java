package com.ly.adp.csc.helper;

import com.google.common.collect.Sets;
import com.ly.adp.csc.entities.EntireDlrClueInfoExport;
import com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/13
 */
@Component
public class SacDlrClueInfoHelper {

    private Logger logger = LoggerFactory.getLogger(SacDlrClueInfoHelper.class);

    @Autowired
    SacPcDlrClueInfoMapper sacPcDlrClueInfoMapper;

    private static final String PHONE = "phone";
    private static final String CUST_ID = "custId";
    private static final String DL_SIGN_NUM = "dlSignNum";
    private static final String YH_SIGN_NUM = "yhSignNum";
    private static final String USER_GROUP_NAME = "userGroupName";
    private static final Set<String> NON_CLUE_CONDITION_SET = Sets.newHashSet("attr83", "isCompleteCarApply", "chooseTimes", "saleOrderState", "isOverdue", "planStartTime", "planEndTime", "assignStatus", "reviewStatus", "source5", "source6", "yhSignNum", "dlSignNum", "isFirstOverdue");

    /**
     * 后置填充 PC 端查询结果
     *
     * @param list  查询结果
     * @param param 查询参数
     */
    public void fillPCList(List<Map<String, Object>> list, Map<String, Object> param) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return;
        }

        StopWatch stopWatch = new StopWatch("后置填充PC端查询结果");
        // 传参情况下连表时拼装 无需填充
        // 活动
        if (StringHelper.IsEmptyOrNull(param.get(DL_SIGN_NUM)) && StringHelper.IsEmptyOrNull(param.get(YH_SIGN_NUM))) {
            stopWatch.start("handleActivity");
            handleActivity(list);
            stopWatch.stop();
        }
        stopWatch.start("handleUserGroup");
        // 用户标签
        handleUserGroup(list);
        stopWatch.stop();
        logger.info("全部线索-后置填充PC端查询结果耗时:{}", stopWatch.prettyPrint());
    }
    /**
     * 后置填充 PC 端查询结果
     *
     * @param list  查询结果
     * @param param 查询参数
     */
    public void fillPCListEntity(List<EntireDlrClueInfoExport> list, Map<String, Object> param) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return;
        }

        // 传参情况下连表时拼装 无需填充
        // 活动
        if (StringHelper.IsEmptyOrNull(param.get(DL_SIGN_NUM)) && StringHelper.IsEmptyOrNull(param.get(YH_SIGN_NUM))) {
            handleActivityEntity(list);
        }

        // 用户标签
        handleUserGroupEntity(list);
    }

    /**
     * 处理活动次数逻辑 | 根据 phone 填充 | 默认值 0
     *
     * @param list 查询结果
     */
    private void handleActivity(List<Map<String, Object>> list) {
        List<Object> phones = list.stream()
                                  .map(SacDlrClueInfoHelper::getPhone)
                                  .filter(o -> !StringHelper.IsEmptyOrNull(o))
                                  .distinct()
                                  .collect(Collectors.toList());

        // phone -> 活动次数
        Map<Object, Map<String, Object>> activityMap = new HashMap<>();
        if (!phones.isEmpty()) {
            activityMap = sacPcDlrClueInfoMapper.entireDlrClueInfoActivity(phones)
                                                .stream()
                                                .collect(Collectors.toMap(SacDlrClueInfoHelper::getPhone, Function.identity(), (m1, m2) -> m2));
        }

        for (Map<String, Object> clue : list) {
            Object phone = getPhone(clue);
            if (!StringHelper.IsEmptyOrNull(phone)) {
                Object dlSignSum = Optional.of(activityMap)
                                           .map(am -> am.get(phone))
                                           .map(m -> m.get(DL_SIGN_NUM))
                                           .orElse(0);

                Object yhSignSum = Optional.of(activityMap)
                                           .map(am -> am.get(phone))
                                           .map(m -> m.get(YH_SIGN_NUM))
                                           .orElse(0);

                clue.put(DL_SIGN_NUM, dlSignSum);
                clue.put(YH_SIGN_NUM, yhSignSum);
            } else {
                clue.put(DL_SIGN_NUM, 0);
                clue.put(YH_SIGN_NUM, 0);
            }
        }
    }
    private void handleActivityEntity(List<EntireDlrClueInfoExport> list) {
        List<Object> phones = list.stream()
                                  .map(SacDlrClueInfoHelper::getPhone)
                                  .filter(o -> !StringHelper.IsEmptyOrNull(o))
                                  .distinct()
                                  .collect(Collectors.toList());

        // phone -> 活动次数
        Map<Object, Map<String, Object>> activityMap = new HashMap<>();
        if (!phones.isEmpty()) {
            activityMap = sacPcDlrClueInfoMapper.entireDlrClueInfoActivity(phones)
                                                .stream()
                                                .collect(Collectors.toMap(SacDlrClueInfoHelper::getPhone, Function.identity(), (m1, m2) -> m2));
        }

        for (EntireDlrClueInfoExport clue : list) {
            Object phone = getPhone(clue);
            if (!StringHelper.IsEmptyOrNull(phone)) {
                Object dlSignSum = Optional.of(activityMap)
                                           .map(am -> am.get(phone))
                                           .map(m -> m.get(DL_SIGN_NUM))
                                           .orElse(0);

                Object yhSignSum = Optional.of(activityMap)
                                           .map(am -> am.get(phone))
                                           .map(m -> m.get(YH_SIGN_NUM))
                                           .orElse(0);
                clue.setDlSignNum(dlSignSum.toString());
                clue.setYhSignNum(yhSignSum.toString());
            } else {
                clue.setDlSignNum("0");
                clue.setYhSignNum("0");
            }
        }
    }

    /**
     * 处理用户标签逻辑 | 根据 custId 填充 | 无默认值
     *
     * @param list 查询结果
     */
    private void handleUserGroup(List<Map<String, Object>> list) {
        List<Object> custIds = list.stream()
                                   .map(m -> m.get(CUST_ID))
                                   .filter(o -> !StringHelper.IsEmptyOrNull(o))
                                   .distinct()
                                   .collect(Collectors.toList());

        // custId -> 用户标签
        Map<Object, Map<String, Object>> userGroupMap = new HashMap<>();
        if (!custIds.isEmpty()) {
            userGroupMap = sacPcDlrClueInfoMapper.entireDlrClueInfoUserGroup(custIds)
                                                 .stream()
                                                 .collect(Collectors.toMap(m -> m.get(CUST_ID), Function.identity(), (m1, m2) -> m2));
        }

        for (Map<String, Object> clue : list) {
            Object custId = clue.get(CUST_ID);
            if (!StringHelper.IsEmptyOrNull(custId)) {
                Object userGroupName = Optional.of(userGroupMap)
                                               .map(um -> um.get(custId))
                                               .map(m -> m.get(USER_GROUP_NAME))
                                               .orElse(null);

                if (Objects.nonNull(userGroupName)) {
                    clue.put(USER_GROUP_NAME, userGroupName);
                }
            }
        }
    }

    /**
     * 处理用户标签逻辑 | 根据 custId 填充 | 无默认值
     *
     * @param list 查询结果
     */
    private void handleUserGroupEntity(List<EntireDlrClueInfoExport> list) {
        List<Object> custIds = list.stream()
                                   .map(m -> m.getCustId())
                                   .filter(o -> !StringHelper.IsEmptyOrNull(o))
                                   .distinct()
                                   .collect(Collectors.toList());

        // custId -> 用户标签
        Map<Object, Map<String, Object>> userGroupMap = new HashMap<>();
        if (!custIds.isEmpty()) {
            userGroupMap = sacPcDlrClueInfoMapper.entireDlrClueInfoUserGroup(custIds)
                                                 .stream()
                                                 .collect(Collectors.toMap(m -> m.get(CUST_ID), Function.identity(), (m1, m2) -> m2));
        }

        for (EntireDlrClueInfoExport clue : list) {
//            Object custId = clue.get(CUST_ID);
            Object custId = clue.getCustId();
            if (!StringHelper.IsEmptyOrNull(custId)) {
                Object userGroupName = Optional.of(userGroupMap)
                                               .map(um -> um.get(custId))
                                               .map(m -> m.get(USER_GROUP_NAME))
                                               .orElse(null);

                if (Objects.nonNull(userGroupName)) {
//                    clue.put(USER_GROUP_NAME, userGroupName);
                    clue.setUserGroupName(userGroupName.toString());
                }
            }
        }
    }

    /**
     * 根据手机号对结果去重
     *
     * @param infos 查询结果
     * @return 去重后的结果
     */
    public List<Map<String, Object>> distinctByPhone(List<Map<String, Object>> infos) {
        Deque<Map<String, Object>> deque = new LinkedBlockingDeque<>(infos.size());
        Set<String> phoneSet = new HashSet<>();
        // 逆序遍历 保留最后一条(即创建时间较早的)
        for (int i = infos.size() - 1; i >= 0; i--) {
            Map<String, Object> info = infos.get(i);
            Object phone = getPhone(info);
            if (Objects.isNull(phone)) {
                continue;
            }

            String phoneStr = phone.toString();
            if (!phoneSet.contains(phoneStr)) {
                deque.addFirst(info);
                phoneSet.add(phoneStr);
            }
        }
        return new ArrayList<>(deque);
    }
    /**
     * 根据手机号对结果去重
     *
     * @param infos 查询结果
     * @return 去重后的结果
     */
    public List<EntireDlrClueInfoExport> distinctByPhoneEntity(List<EntireDlrClueInfoExport> infos) {
        Deque<EntireDlrClueInfoExport> deque = new LinkedBlockingDeque<>(infos.size());
        Set<String> phoneSet = new HashSet<>();
        // 逆序遍历 保留最后一条(即创建时间较早的)
        for (int i = infos.size() - 1; i >= 0; i--) {
            EntireDlrClueInfoExport info = infos.get(i);
            Object phone = getPhone(info);
            if (Objects.isNull(phone)) {
                continue;
            }

            String phoneStr = phone.toString();
            if (!phoneSet.contains(phoneStr)) {
                deque.addFirst(info);
                phoneSet.add(phoneStr);
            }
        }
        return new ArrayList<>(deque);
    }

    public static Object getPhone(Map<String, Object> map) {
        return map.get(PHONE);
    }
    public static Object getPhone(EntireDlrClueInfoExport paramObject) {
        return paramObject.getPhone();
    }

    /**
     * 是否包含线索表之外的条件
     *
     * @param params 入参
     * @return boolean
     */
    public static boolean haveNonClueCondition(Map<String, Object> params) {
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (NON_CLUE_CONDITION_SET.contains(entry.getKey()) && !StringHelper.IsEmptyOrNull(entry.getValue())) {
                return true;
            }
        }
        return false;
    }
}
