package com.ly.adp.csc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.entities.dto.koc.wechat.UserLabelingReq;
import com.ly.adp.csc.entities.dto.koc.wechat.UpdateUserTagsReq;
import com.ly.adp.csc.entities.dto.koc.wechat.WechatUpdateUserExpertTypesRequestDTO;
import com.ly.adp.csc.entities.dto.koc.wechat.WechatUserRemarkRequestDTO;
import com.ly.adp.csc.entities.dto.koc.wechat.WechatRemoveUserLabelsRequestDTO;
import com.ly.adp.csc.entities.vo.koc.UserTagInfoVO;
import com.ly.adp.csc.service.IKocLabelingService;
import com.ly.mp.component.entities.EntityResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * KOC打标控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@RestController
@Api(value = "KOC打标服务", tags = "KOC打标服务")
@RequestMapping(value = "/ly/adp/csc/koc", produces = { MediaType.APPLICATION_JSON_VALUE })
public class KocLabelingController {

    @Autowired
    private IKocLabelingService kocLabelingService;

    @ApiOperation(value = "用户打标", notes = "为用户添加标签、达人类型、备注")
    @PostMapping(value = "/labelUser.do")
    public EntityResult<UserTagInfoVO> labelUser(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody UserLabelingReq userLabelingReq) {
        return kocLabelingService.wechatLabelUser(userLabelingReq, authentication);
    }

    @ApiOperation(value = "编辑用户标签", notes = "编辑用户的标签")
    @PostMapping(value = "/updateUserTags.do")
    public EntityResult<UserTagInfoVO> updateUserTags(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody UpdateUserTagsReq request) {
        return kocLabelingService.wechatUpdateUserTags(request, authentication);
    }

    @ApiOperation(value = "编辑用户达人类型", notes = "编辑用户的达人类型")
    @PostMapping(value = "/updateUserExpertTypes.do")
    public EntityResult<UserTagInfoVO> updateUserExpertTypes(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody WechatUpdateUserExpertTypesRequestDTO request) {
        return kocLabelingService.wechatUpdateUserExpertTypes(request, authentication);
    }

    @ApiOperation(value = "添加用户备注", notes = "为用户添加备注")
    @PostMapping(value = "/addUserRemark.do")
    public EntityResult<UserTagInfoVO> addUserRemark(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody WechatUserRemarkRequestDTO request) {
        return kocLabelingService.wechatAddUserRemark(request, authentication);
    }

    @ApiOperation(value = "编辑用户备注", notes = "编辑用户备注")
    @PostMapping(value = "/updateUserRemark.do")
    public EntityResult<UserTagInfoVO> updateUserRemark(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody WechatUserRemarkRequestDTO request) {
        return kocLabelingService.wechatUpdateUserRemark(request, authentication);
    }

    @ApiOperation(value = "删除用户备注", notes = "删除用户备注")
    @PostMapping(value = "/deleteUserRemark.do")
    public EntityResult<UserTagInfoVO> deleteUserRemark(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody WechatUserRemarkRequestDTO request) {
        return kocLabelingService.wechatDeleteUserRemark(request, authentication);
    }

    @ApiOperation(value = "移除用户标签/达人类型", notes = "移除用户的指定标签或达人类型")
    @PostMapping(value = "/removeUserLabels.do")
    public EntityResult<UserTagInfoVO> removeUserLabels(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody WechatRemoveUserLabelsRequestDTO request) {
        return kocLabelingService.wechatRemoveUserLabels(request, authentication);
    }
}
