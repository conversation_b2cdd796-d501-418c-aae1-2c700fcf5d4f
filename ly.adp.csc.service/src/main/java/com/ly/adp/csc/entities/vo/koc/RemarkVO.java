package com.ly.adp.csc.entities.vo.koc;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 企微端备注VO
 * <AUTHOR>
 * @since 2025-07-29
 */
public class RemarkVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    private String relId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public String toString() {
        return "WechatRemarkVO{" +
                "relId='" + relId + '\'' +
                ", content='" + content + '\'' +
                ", createdName='" + createdName + '\'' +
                ", createdDate=" + createdDate +
                '}';
    }
}
