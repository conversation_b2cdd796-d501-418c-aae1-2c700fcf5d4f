package com.ly.adp.csc.service;

import com.ly.adp.csc.entities.qry.GetClueInfoQry;
import com.ly.adp.csc.entities.vo.ClueDetailCustomInfoVO;
import com.ly.adp.csc.entities.vo.ClueInfoVO;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface ISacPcDlrClueInfoService {
    /**
     * PC门店全部回访单查询
     *
     * @param token     token
     * @param paramPage 输入参数
     * @return ListResult
     */
    ListResult<Map<String, Object>> entireReviewInfo(ParamPage<Map<String, Object>> paramPage, String token);

    /**
     * PC门店全部线索单查询
     *
     * @param token     token
     * @param paramPage 输入参数
     * @return ListResult
     */
    ListResult<Map<String, Object>> entireDlrClueInfo(ParamPage<Map<String, Object>> paramPage, String token);
    ListResult<Map<String, Object>> entireDlrClueInfoNew(ParamPage<Map<String, Object>> paramPage, String token);
    List<Map<String, Object>> entireDlrClueInfo1(ParamPage<Map<String, Object>> paramPage, String token);

    EntityResult<String> importPhone(HttpServletRequest request, String authentication);

    /**
     * PC门店全部线索查询导出
     *
     * @param dataInfo
     * @param token
     * @param response
     * @return
     */
    void entireDlrClueInfoExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

    void defeatEntireDlrClueInfoExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

    ListResult<Map<String, Object>> getEvaluatedPercent(Map<String, Object> paramPage);

    ListResult<Map<String, Object>> findEmployee(String token, Map<String, Object> dataInfo);

    EntityResult<ClueInfoVO> getClueInfo(String token, GetClueInfoQry qry);

    /**
     * 试驾单获取线索基本信息
     *
     * @param qry
     * @return
     */
    EntityResult<ClueDetailCustomInfoVO> getClueDetailAndCustomInfo(GetClueInfoQry qry);
}
