package com.ly.adp.csc.gateway.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.adp.csc.entities.enums.OrgTypeEnum;
import com.ly.adp.csc.gateway.SacEmployeeDimissionClueGateway;
import com.ly.mp.csc.clue.DBNameConstant;
import com.ly.mp.csc.clue.entities.SacClueInfoDlr;
import com.ly.mp.csc.clue.entities.copyEntity.MdmOrgEmployee;
import com.ly.mp.csc.clue.entities.dto.TransferEmployeeClueDTO;
import com.ly.mp.csc.clue.enums.BizPositionEnum;
import com.ly.mp.csc.clue.enums.ClueStatusEnum;
import com.ly.mp.csc.clue.enums.UserStatus;
import com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper;
import com.ly.mp.csc.clue.idal.mapper.SacReviewMapper;
import com.ly.mp.csc.clue.service.IMdmOrgEmployeeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Version 1.0.0
 **/
@Service
public class SacEmployeeDimissionClueGatewayImpl implements SacEmployeeDimissionClueGateway {

    private static final Logger logger = LoggerFactory.getLogger(SacEmployeeDimissionClueGatewayImpl.class);

    // 批处理大小 - 每批次处理的线索数量
    private static final int BATCH_SIZE = 1000;

    /**
     * 代理店长标识
     */
    private static final String DEPUTY_SM_FLAG = "1";

    @Autowired
    private SacClueInfoDlrMapper sacClueInfoDlrMapper;

    @Autowired
    private SacReviewMapper reviewMapper;

    @Autowired
    private IMdmOrgEmployeeService employeeService;

    /**
     * 查询各门店的店长或代理店长
     * @param dlrCodes 门店编码集合
     * @return 门店编码 -> 店长信息的映射
     */
    @Override
    public Map<String, MdmOrgEmployee> querySMInfo(Set<String> dlrCodes) {
        Map<String, MdmOrgEmployee> result = new HashMap<>();

        if (CollectionUtils.isEmpty(dlrCodes)) {
            return result;
        }

        // 查询所有门店的店长和代理店长
        MdmOrgEmployee mdmOrgEmployee = new MdmOrgEmployee();
        mdmOrgEmployee.setListDlrCode(new ArrayList<>(dlrCodes));
        mdmOrgEmployee.setUserStatus(UserStatus.EMPLOYED.getCode());
        List<MdmOrgEmployee> managers =  employeeService.queryMdmOrgEmployee(mdmOrgEmployee, MdmOrgEmployee::getEmpName, MdmOrgEmployee::getUserId, MdmOrgEmployee::getDlrCode, MdmOrgEmployee::getStationId, MdmOrgEmployee::getRemark);
        if (CollectionUtils.isEmpty(managers)) {
            return result;
        }

        // 过滤出店长和代理店长，remark 为 1 表示代理店长
        List<MdmOrgEmployee> smManagers = managers.stream()
                .filter(d -> BizPositionEnum.smStationList().contains(d.getStationId()) || DEPUTY_SM_FLAG.equals(d.getRemark()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(smManagers)) {
            return result;
        }

        // 按门店分组，并取每个门店中ID最大的店长
        return smManagers.stream()
                .filter(manager -> StringUtils.isNotBlank(manager.getDlrCode()))
                .collect(Collectors.toMap(
                        MdmOrgEmployee::getDlrCode,
                        manager -> manager,
                        (existing, replacement) -> existing.getUserId().compareTo(replacement.getUserId()) > 0 ? existing : replacement,
                        HashMap::new
                ));
    }

    /**
     * 查询前一天离职审核通过的门店员工
     *
     * @return 离职门店员工信息
     */
    @Override
    public List<MdmOrgEmployee> queryYesterdayDimissionEmployee() {
        MdmOrgEmployee mdmOrgEmployee = new MdmOrgEmployee();
        mdmOrgEmployee.setOrgType(OrgTypeEnum.DLR.getCode());
        mdmOrgEmployee.setUserStatus(UserStatus.RESIGNATION.getCode());
        mdmOrgEmployee.setYesterdayDimissionFlag(Boolean.TRUE);
        return employeeService.queryMdmOrgEmployee(mdmOrgEmployee, MdmOrgEmployee::getUserId, MdmOrgEmployee::getDlrCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processEmployeeCluesByBatch(
            List<String> allEmployeeIds,
            Map<String, MdmOrgEmployee> dlrManagerMap) {

        if (CollectionUtils.isEmpty(allEmployeeIds) || MapUtils.isEmpty(dlrManagerMap)) {
            logger.info("没有需要处理的员工或门店不存在店长信息，跳过处理");
            return;
        }

        // 批次线索的最后一条线索id，初始为null
        String lastId = null;
        int totalProcessed = 0;
        int batchSize = BATCH_SIZE;
        boolean hasMoreClues = true;

        while (hasMoreClues) {
            // 通过 sortedEmployeeIds 查询出线索数据 1000 一批
            List<SacClueInfoDlr> employeeClues = getEmployeeClues(allEmployeeIds, lastId, batchSize);

            if (CollectionUtils.isEmpty(employeeClues)) {
                logger.info("没有更多线索需要处理，总共处理了 {} 条线索", totalProcessed);
                break;
            }

            // 构建线索更新参数，批量更新
            List<TransferEmployeeClueDTO> clueUpdateBatch = new ArrayList<>(batchSize);
            for (SacClueInfoDlr employeeClue : employeeClues) {
                // 店长信息
                MdmOrgEmployee mdmOrgEmployee = dlrManagerMap.get(employeeClue.getDlrCode());
                if (Objects.isNull(mdmOrgEmployee)) {
                    logger.warn("未找到门店 {} 对应的店长信息，跳过处理", employeeClue.getDlrCode());
                    continue;
                }
                TransferEmployeeClueDTO employeeClueDTO = new TransferEmployeeClueDTO();
                employeeClueDTO.setClueId(employeeClue.getId());
                employeeClueDTO.setReviewId(employeeClue.getReviewId());
                employeeClueDTO.setStatusCode(employeeClue.getStatusCode());
                employeeClueDTO.setSmId(mdmOrgEmployee.getUserId());
                employeeClueDTO.setSmName(mdmOrgEmployee.getEmpName());
                clueUpdateBatch.add(employeeClueDTO);
            }

            // 批量更新线索
            if (CollectionUtils.isNotEmpty(clueUpdateBatch)) {
                int updated = batchUpdateClues(clueUpdateBatch);
                totalProcessed += updated;
                logger.info("批量更新线索完成，本批次更新{}条，累计更新{}条", updated, totalProcessed);
            }

            // 更新lastId为当前批次的最后一条记录ID
            lastId = employeeClues.get(employeeClues.size() - 1).getId();

            // 如果获取的线索数量小于批处理大小，说明没有更多数据了
            if (employeeClues.size() < batchSize) {
                logger.info("所有线索处理完毕，共处理{}条", totalProcessed);
                hasMoreClues = false;
            }
        }
    }


    /**
     * 获取员工的线索列表（使用游标分页优化深分页问题）
     * @param allEmployeeIds 员工ID列表
     * @param lastId 上次查询的最后一条记录ID，首次查询传null或空字符串
     * @param limit 每页记录数
     * @return 线索列表
     */
    public List<SacClueInfoDlr> getEmployeeClues(List<String> allEmployeeIds, String lastId, int limit) {
        try {
            if (CollectionUtils.isEmpty(allEmployeeIds)) {
                return Collections.emptyList();
            }

            LambdaQueryWrapper<SacClueInfoDlr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(
                            SacClueInfoDlr::getId,
                            SacClueInfoDlr::getReviewId,
                            SacClueInfoDlr::getDlrCode,
                            SacClueInfoDlr::getStatusCode,
                            SacClueInfoDlr::getReviewPersonId
                    )
                    .in(SacClueInfoDlr::getReviewPersonId, allEmployeeIds);

            // 使用游标分页，只有当lastId不为空时才添加条件
            if (StringUtils.isNotBlank(lastId)) {
                queryWrapper.gt(SacClueInfoDlr::getId, lastId);
            }

            queryWrapper.orderByAsc(SacClueInfoDlr::getId)
                    .last("LIMIT " + limit);

            return sacClueInfoDlrMapper.selectList(queryWrapper);
        } catch (Exception e) {
            logger.error("获取员工线索列表异常", e);
            return Collections.emptyList();
        }
    }

    public int batchUpdateClues(List<TransferEmployeeClueDTO> clueUpdateBatch) {
        List<TransferEmployeeClueDTO> normalClue = clueUpdateBatch.stream().filter(d -> ClueStatusEnum.isNonDefeatedClue(d.getStatusCode())).collect(Collectors.toList());
        List<TransferEmployeeClueDTO> defeatClue = clueUpdateBatch.stream().filter(d -> ClueStatusEnum.isDefeatedClue(d.getStatusCode())).collect(Collectors.toList());
        int normalCLueCount = 0, defeatCLueCount = 0;
        if (CollectionUtils.isNotEmpty(normalClue)) {
            // 更新正常线索
            normalCLueCount = sacClueInfoDlrMapper.transferEmployeeDimissionClues(normalClue, DBNameConstant.ADP_LEADS);
        }

        if (CollectionUtils.isNotEmpty(defeatClue)) {
            // 更新战败线索
            defeatCLueCount = sacClueInfoDlrMapper.transferEmployeeDimissionClues(defeatClue, DBNameConstant.CSC);
        }
        // 更新回访
        int reviewCount = reviewMapper.transferEmployeeDimissionReview(clueUpdateBatch);
        logger.info("离职员工线索分配完成, 转移正常线索{}条, 战败线索{}条, 回访{}条", normalCLueCount, defeatCLueCount, reviewCount);
        return reviewCount;
    }
}
