package com.ly.adp.csc.otherservice;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.adp.csc.otherservice.entities.BuTestcarPrepare;
import com.ly.adp.csc.otherservice.entities.MdsLookupValue;
import com.ly.adp.csc.otherservice.in.MdsLookupValueIn;
import com.ly.adp.csc.otherservice.util.ListResultUtil;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.csc.clue.otherservice.ICscSysBaseDataService;

@Service
public class CscSysBaseDataService implements ICscSysBaseDataService {

    @Autowired
    IBasedataFeignClient basedataFeignClient;

    private static final Logger LOG = LoggerFactory.getLogger(CscSysBaseDataService.class);

    @Override
    public ListResult<Map<String, Object>> generateOrderCode(String dlrId, String billTypeId, String token) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        try {
            OptResult optResult = basedataFeignClient.generateOrderCode(dlrId, billTypeId);
            result.setMsg(optResult.getMsg());
            result.setResult(optResult.getResult());
        } catch (Exception e) {
            LOG.error("CscSysBaseDataService::generateOrderCode", e);
            throw BusicenException.create(e.getMessage());
        }
        return result;
    }

    @Override
    public ListResult<Map<String, Object>> generateOrderCodeNoToken(String dlrId, String billTypeId) {
        ListResult<Map<String, Object>> result = new ListResult<>();

        Map<String, Object> map = new HashMap<>();
        map.put("dlrId", dlrId);
        map.put("billTypeId", billTypeId);
        OptResult optResult = basedataFeignClient.generateOrderCodeForFeign("", map);
        result.setMsg(optResult.getMsg());
        result.setResult(optResult.getResult());

        return result;
    }

    @Override
    public ListResult<Map<String, Object>> mdslookupvaluefindbypage(String lookupTypeCode, String token) {
        MdsLookupValueIn mdsLookupValue = new MdsLookupValueIn();
        mdsLookupValue.setPageIndex(1);
        mdsLookupValue.setPageSize(Integer.MAX_VALUE);
        mdsLookupValue.setLookupTypeCode(lookupTypeCode);
        ListResult<MdsLookupValue> listResult = basedataFeignClient.mdsLookupValueFindByPage(token, mdsLookupValue);
        return ListResultUtil.ListResultOfMap(listResult);
    }

    @Override
    public EntityResult<Map<String, Object>> inset(String authentication, ParamPage<Map<String, Object>> param) {
        //转换参数类型
        ParamPage<BuTestcarPrepare> newParam = new ParamPage<BuTestcarPrepare>();
        newParam.setPageIndex(param.getPageIndex());
        newParam.setPageSize(param.getPageSize());
        BuTestcarPrepare buTestcarPrepare1 = new BuTestcarPrepare();
        BeanUtils.copyProperties(param.getParam(),buTestcarPrepare1);
      //  BuTestcarPrepare buTestcarPrepare = BusicenUtils.map2Object(param.getParam(), BuTestcarPrepare.class);
        newParam.setParam(buTestcarPrepare1);
        EntityResult<BuTestcarPrepare> result = basedataFeignClient.inset(authentication, newParam);
        //转换结果类型
        EntityResult<Map<String, Object>> newResult = new EntityResult<>();
        newResult.setMsg(result.getMsg());
        newResult.setExtInfo(result.getExtInfo());
        newResult.setResult(result.getResult());
        newResult.setRows(BusicenUtils.entityToMap(result.getRows()));
        return newResult;
    }

    /**
     * 获取管辖的门店信息
     */
    public ListResult<Map<String, Object>> querymanagedlr(String authentication, Map<String, Object> dateInfo) {
        return basedataFeignClient.querymanagedlr(authentication, dateInfo);
    }
}
