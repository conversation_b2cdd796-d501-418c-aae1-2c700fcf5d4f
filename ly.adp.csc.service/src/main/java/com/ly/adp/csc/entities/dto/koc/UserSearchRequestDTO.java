package com.ly.adp.csc.entities.dto.koc;

import java.io.Serializable;

/**
 * 用户查询请求DTO
 * <AUTHOR> System
 * @since 2025-01-25
 */
public class UserSearchRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号(可选)
     */
    private String phone;

    /**
     * 用户ID(可选)
     */
    private String smartId;

    /**
     * 用户名(可选)
     */
    private String userName;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return "UserSearchRequestDTO{" +
                "phone='" + phone + '\'' +
                ", smartId='" + smartId + '\'' +
                ", userName='" + userName + '\'' +
                '}';
    }
}
