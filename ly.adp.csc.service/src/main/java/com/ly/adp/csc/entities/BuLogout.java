package com.ly.adp.csc.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 退网信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@TableName("t_sac_bu_logout")
public class BuLogout implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退网ID
     */
    @TableId("LOGOUT_ID")
    private String logoutId;

    /**
     * 类型：1门店 2城市公司 3代理商
     */
    @TableField("TYPE")
    private String type;

    /**
     * 门店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 门店名称
     */
    @TableField("DLR_NAME")
    private String dlrName;

    /**
     * 城市公司编码
     */
    @TableField("AGENT_COMPANY_CODE")
    private String agentCompanyCode;

    /**
     * 城市公司名称
     */
    @TableField("AGENT_COMPANY_NAME")
    private String agentCompanyName;

    /**
     * 代理商编码
     */
    @TableField("AGENT_CODE")
    private String agentCode;

    /**
     * 代理商名称
     */
    @TableField("AGENT_NAME")
    private String agentName;

    /**
     * 退网状态 0正常 1待审批 2通过 3已退网 B驳回
     */
    @TableField("LOGOUT_STATUS")
    private String logoutStatus;

    /**
     * BPM审核截图
     */
    @TableField("BPM_URL")
    private String bpmUrl;

    /**
     * RO清算
     */
    @TableField("RO_URL")
    private String roUrl;

    /**
     * CS清算
     */
    @TableField("CS_URL")
    private String csUrl;

    /**
     * CUD清算
     */
    @TableField("CUD_URL")
    private String cudUrl;

    /**
     * SP清算
     */
    @TableField("SP_URL")
    private String spUrl;

    /**
     * 财务清算
     */
    @TableField("FINANCIAL_URL")
    private String financialUrl;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人名称
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新人员名称
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getLogoutId() {
        return logoutId;
    }

    public void setLogoutId(String logoutId) {
        this.logoutId = logoutId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }

    public String getAgentCompanyCode() {
        return agentCompanyCode;
    }

    public void setAgentCompanyCode(String agentCompanyCode) {
        this.agentCompanyCode = agentCompanyCode;
    }

    public String getAgentCompanyName() {
        return agentCompanyName;
    }

    public void setAgentCompanyName(String agentCompanyName) {
        this.agentCompanyName = agentCompanyName;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getLogoutStatus() {
        return logoutStatus;
    }

    public void setLogoutStatus(String logoutStatus) {
        this.logoutStatus = logoutStatus;
    }

    public String getBpmUrl() {
        return bpmUrl;
    }

    public void setBpmUrl(String bpmUrl) {
        this.bpmUrl = bpmUrl;
    }

    public String getRoUrl() {
        return roUrl;
    }

    public void setRoUrl(String roUrl) {
        this.roUrl = roUrl;
    }

    public String getCsUrl() {
        return csUrl;
    }

    public void setCsUrl(String csUrl) {
        this.csUrl = csUrl;
    }

    public String getCudUrl() {
        return cudUrl;
    }

    public void setCudUrl(String cudUrl) {
        this.cudUrl = cudUrl;
    }

    public String getSpUrl() {
        return spUrl;
    }

    public void setSpUrl(String spUrl) {
        this.spUrl = spUrl;
    }

    public String getFinancialUrl() {
        return financialUrl;
    }

    public void setFinancialUrl(String financialUrl) {
        this.financialUrl = financialUrl;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BuLogout{" +
        "logoutId=" + logoutId +
        ", type=" + type +
        ", dlrCode=" + dlrCode +
        ", dlrName=" + dlrName +
        ", agentCompanyCode=" + agentCompanyCode +
        ", agentCompanyName=" + agentCompanyName +
        ", agentCode=" + agentCode +
        ", agentName=" + agentName +
        ", logoutStatus=" + logoutStatus +
        ", bpmUrl=" + bpmUrl +
        ", roUrl=" + roUrl +
        ", csUrl=" + csUrl +
        ", cudUrl=" + cudUrl +
        ", spUrl=" + spUrl +
        ", financialUrl=" + financialUrl +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", remark=" + remark +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
