package com.ly.adp.csc.entities.enums;

/**
 * 组织类型枚举
 * <AUTHOR>
 * @Version 1.0.0
 **/
public enum OrgTypeEnum {

    AREA("0", "大使、区域"),
    DLR("1", "门店"),
    AGENT("2", "代理商"),
    CITYCOP("3", "城市公司");

    private final String code;
    private final String description;

    OrgTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
