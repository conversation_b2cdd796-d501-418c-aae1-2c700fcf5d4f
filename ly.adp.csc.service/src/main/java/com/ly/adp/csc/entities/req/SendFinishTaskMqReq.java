package com.ly.adp.csc.entities.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/8
 * @Version 1.0.0
 **/
@ApiModel("任务状态消息推送")
public class SendFinishTaskMqReq implements Serializable {

    private static final long serialVersionUID = -4790751588809061078L;

    @ApiModelProperty("任务id")
    String taskId;

    @ApiModelProperty("任务状态 1-已结束 2-已取消")
    String taskStatus;

    @ApiModelProperty("任务结束时间")
    String bussEndTime;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getBussEndTime() {
        return bussEndTime;
    }

    public void setBussEndTime(String bussEndTime) {
        this.bussEndTime = bussEndTime;
    }
}
