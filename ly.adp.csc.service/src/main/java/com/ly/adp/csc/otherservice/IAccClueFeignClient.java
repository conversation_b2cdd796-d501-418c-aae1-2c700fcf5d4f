package com.ly.adp.csc.otherservice;

import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@FeignClient(name = "${refer.name.adp.csc:ly.adp.clue}", url = "${refer.url.adp.clue}")
public interface IAccClueFeignClient {

    @RequestMapping(value = "/ly/sac/sacattachment/attachmentadd.do", method = RequestMethod.POST)
    EntityResult<Map<String, Object>> sacAttachmentAdd(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                       @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/cluedlr/cluedlrcheckrepeat.do")
    EntityResult<Map<String, Object>> clueDlrCheckRepeat(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                         @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/cluedlr/cluedlrcheckrepeat2.do")
    EntityResult<Map<String, Object>> clueDlrCheckRepeat2(@RequestParam(value = "authentication") String authentication,
                                                          @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/cluedlr/cluedlrsave.do")
    EntityResult<Map<String, Object>> clueDlrSave(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                  @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/cluedlr/cluedlrsave2.do")
    EntityResult<Map<String, Object>> clueDlrSave2(@RequestParam(value = "authentication") String authentication,
                                                   @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/innerconfig/queryConfigList.do")
    ListResult<Map<String, Object>> queryConfigList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                    @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo);
}