package com.ly.adp.csc.otherservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.csc.clue.entities.SacClueInfoDlr;
import com.ly.mp.csc.clue.entities.dto.ParamQry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
@FeignClient(name = "dwsFeign", url = "${refer.dws.baseurl:}")
public interface DwsFeign {

    @PostMapping("/api/activity/agentOfflinePage")
    Page<Map<String, Object>> agentOfflinePage(@RequestBody ParamQry qry);

    /**
     * 查询热度
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryHeatRatio")
    EntityResult<Map<String, Object>> queryHeatRatio(@RequestBody ParamQry qry);

    /**
     * 看板指标-报表展板-活动数量-接口性能优化
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryReportBoardAct")
    EntityResult<Map<String, Object>> queryReportBoardAct(@RequestBody ParamQry qry);

    /**
     * 看板指标-报表展板-活动数量-接口性能优化
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryClueDlrReport")
    EntityResult<Map<String, Object>> queryClueDlrReport(@RequestBody ParamQry qry);

    /**
     * 看板指标-试驾指标 接口性能优化
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryTestDriveReport")
    EntityResult<Map<String, Object>> queryTestDriveReport(@RequestBody ParamQry qry);

    /**
     * 代理商线上投放报表
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryAgentOnlineActivityReport")
    Page<Map<String, Object>> queryAgentOnlineActivityReport(@RequestBody ParamQry qry);

    /**
     * 线索指标统计报表
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryClueStatisticalReport")
    Page<Map<String, Object>> queryClueStatisticalReport(@RequestBody ParamQry qry);

    /**
     * 线索指标统计报表导出
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/sacClueInfoDlr/queryClueStatisticalReportExport")
    Page<Map<String, Object>> queryClueStatisticalReportExport(@RequestBody ParamQry qry);
}
