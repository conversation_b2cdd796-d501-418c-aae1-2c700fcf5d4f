package com.ly.adp.csc.entities.enums;

/**
 * 业务岗位枚举（后续变更需同步）
 **/
public enum BmStationEnum {
    CUSTOMER_DEVELOPMENT_SUPERVISOR_POS("smart_bm_0004", "用户发展主管"),
    STORE_MANAGER_POS("smart_bm_0005", "店长"),
    PRODUCT_EXPERT_POS("smart_bm_0007", "产品专家"),
    GRAVITY_EXPERT_POS("smart_bm_0008", "引力专家"),
    BRAND_DIRECTOR("smart_bm_0012_A", "品牌总"),
    ADMINISTRATION_MANAGER_A("smart_bm_0013_A", "综合管理"),
    ADMINISTRATION_MANAGER_C("smart_bm_0013_C", "综合管理"),
    MARKETING_MANAGER_A("smart_bm_0014_A", "市场经理"),
    MARKETING_MANAGER_C("smart_bm_0014_C", "市场经理"),
    CUSTOMER_DEVELOPMENT_SUPERVISOR_BC("smart_bm_0015", "用户发展主管"),
    STORE_MANAGER_BC("smart_bm_0016", "店长"),
    PRODUCT_EXPERT_BC("smart_bm_0018", "产品专家"),
    GRAVITY_EXPERT_BC("smart_bm_0019", "引力专家"),
    TRAINER_A("smart_bm_0020_A", "内训师"),
    TRAINER_C("smart_bm_0020_C", "内训师"),
    FINANCING_SPECIALIST_A("smart_bm_0021_A", "金融/二手车主管"),
    FINANCING_SPECIALIST_C("smart_bm_0021_C", "金融/二手车主管"),
    FLEET_MANAGER_A("smart_bm_0022_A", "大客户经理"),
    FLEET_MANAGER_C("smart_bm_0022_C", "大客户经理"),
    DC_MANAGER_DC("smart_bm_0023", "交付经理"),
    FINANCE_MANAGER_DC("smart_bm_0024", "财务经理"),
    CASHIER_DC("smart_bm_0025", "收银"),
    DELIVERY_SPECIALIST_DC("smart_bm_0029", "交付专员"),
    STOCK_SPECIALIST_DC("smart_bm_0031", "仓储专员"),
    STOCK_SUPERVISOR_DC("smart_bm_0033", "仓储主管"),
    SERVICE_MANAGER_POL("smart_bm_0035", "服务经理"),
    SERVICE_ADVISOR_POL("smart_bm_0036", "服务顾问"),
    SERVICE_SUPERVISOR_POL("smart_bm_0037", "前台主管"),
    APPOINTMENT_SPECIALI_POL("smart_bm_0038", "预约专员"),
    CLAIMS_SPECIALIST_POL("smart_bm_0040", "事故专员"),
    TECHNICAL_EXPERT_POL("smart_bm_0041", "技术专家"),
    PARTS_SPECIALIST_POL("smart_bm_0042", "备件经理"),
    WARRANTY_SPECIALIST_POL("smart_bm_0043", "质保专员"),
    ME_TECHNICIAN_POL("smart_bm_0044", "机电技师"),
    BODY_TECHNICIAN_POL("smart_bm_0045", "钣金技师"),
    PAINTS_TECHNICIAN_POL("smart_bm_0046", "油漆技师"),
    WORKSHOP_FORMAN_M_POL("smart_bm_0047", "车间经理"),
    HV_TECHNICIAN_POL("smart_bm_0048", "高压技术员"),
    DIAGNOSIS_TECHNICIAN_POL("smart_bm_0049", "诊断技术员"),
    QUALITY_INSPECTION_POL("smart_bm_0050", "质量总检"),
    TECHNICAL_TRAINER_POL("smart_bm_0051", "技术内训师"),
    WORKSHOP_FORMAN_POL("smart_bm_0052", "车间调度"),
    SPARE_PARTS_PLANNER_POL("smart_bm_0053", "备件计划员"),
    CAR_CARE_POL("smart_bm_0054", "洗车工"),
    CAR_CARE_ASSISTANT_POL("smart_bm_0055", "汽车护理"),
    SERVICE_EXPERIENCE_E_POL("smart_bm_0056", "服务体验专家"),
    INFORMATION_SPECIALI_POL("smart_bm_0058", "售后信息专员"),
    REST_AREA_SPECIALIST_POL("smart_bm_0059", "客休专员"),
    SENIOR_PRODUCT_EXPER_POS("smart_bm_0061", "资深产品专家"),
    MARKETING_SUPERVISOR_A("smart_bm_0063_A", "市场主管"),
    MARKETING_SUPERVISOR_C("smart_bm_0063_C", "市场主管"),
    SENIOR_PRODUCT_EXPER_BC("smart_bm_0064", "资深产品专家"),
    CITY_MANAGER_A("smart_bm_0065A", "城市总经理"),
    CITY_MANAGER_C("smart_bm_0065C", "城市总经理"),
    DERIVATIVES_SUPERVIS_DC("smart_bm_0067", "衍生业务主管"),
    DELIVERY_ASSISTANT_DC("smart_bm_0068", "交付助理"),
    POL001_POL("smart_bm_0069", "服务助理"),
    POL002_POL("smart_bm_0070", "服务内训师"),
    NEW_MEDIA_OPERATIONS_DIRECTOR_A("smart_bm_0073_A", "新媒体运营主管"),
    NEW_MEDIA_OPERATIONS_DIRECTOR_C("smart_bm_0073_C", "新媒体运营主管"),
    OUTBOUND_CALL_SPECIALIST_A("smart_bm_0075_A", "外呼专员"),
    OUTBOUND_CALL_SPECIALIST_C("smart_bm_0075_C", "外呼专员"),
    BILLING_CLERK_POL("smart_bm_0076", "开票员"),
    BILLING_CLERK_DC("smart_bm_0077", "开票员"),
    SERVICE_PARTNER("smart_bm_0078", "服务伙伴"),
    NEW_MEDIA_LEADER("smart_bm_0080_C", "新媒体负责人"),
    NEW_MEDIA_OPERATION("smart_bm_0081_C", "新媒体运营"),
    LIVE_STREAMER("smart_bm_0082_C", "主播"),
    DC_MANAGER_POL("smart_bm_0701", "交付经理"),
    FINANCE_MANAGER_POL("smart_bm_0702", "财务经理"),
    CASHIER_POL("smart_bm_0703", "收银"),
    DELIVERY_SPECIALIST_POL("smart_bm_0704", "交付专员"),
    STOCK_SPECIALIST_POL("smart_bm_0705", "仓储专员"),
    STOCK_SUPERVISOR_POL("smart_bm_0706", "仓储主管"),
    DERIVATIVES_SUPERVIS_POL("smart_bm_0707", "衍生业务主管"),
    DELIVERY_ASSISTANT_POL("smart_bm_0708", "交付助理"),
    SERVICE_MANAGER_DC("smart_bm_0711", "服务经理"),
    SERVICE_ADVISOR_DC("smart_bm_0712", "服务顾问"),
    SERVICE_SUPERVISOR_DC("smart_bm_0713", "前台主管"),
    APPOINTMENT_SPECIALI_DC("smart_bm_0714", "预约专员"),
    CLAIMS_SPECIALIST_DC("smart_bm_0715", "事故专员"),
    TECHNICAL_EXPERT_DC("smart_bm_0716", "技术专家"),
    PARTS_SPECIALIST_DC("smart_bm_0717", "备件经理"),
    WARRANTY_SPECIALIST_DC("smart_bm_0718", "质保专员"),
    ME_TECHNICIAN_DC("smart_bm_0719", "机电技师"),
    BODY_TECHNICIAN_DC("smart_bm_0720", "钣金技师"),
    PAINTS_TECHNICIAN_DC("smart_bm_0721", "油漆技师"),
    WORKSHOP_FORMAN_M_DC("smart_bm_0722", "车间经理"),
    HV_TECHNICIAN_DC("smart_bm_0723", "高压技术员"),
    DIAGNOSIS_TECHNICIAN_DC("smart_bm_0724", "诊断技术员"),
    QUALITY_INSPECTION_DC("smart_bm_0725", "质量总检"),
    TECHNICAL_TRAINER_DC("smart_bm_0726", "技术内训师"),
    WORKSHOP_FORMAN_DC("smart_bm_0727", "车间调度"),
    SPARE_PARTS_PLANNER_DC("smart_bm_0728", "备件计划员"),
    CAR_CARE_DC("smart_bm_0729", "洗车工"),
    CAR_CARE_ASSISTANT_DC("smart_bm_0730", "汽车护理"),
    SERVICE_EXPERIENCE_E_DC("smart_bm_0731", "服务体验专家"),
    CRM_CONSULTANT("smart_bm_0732_A", "客户关怀顾问"),
    INFORMATION_SPECIALI_DC("smart_bm_0733", "售后信息专员"),
    REST_AREA_SPECIALIST_DC("smart_bm_0734", "客休专员"),
    POL001_DC("smart_bm_0735", "服务助理"),
    POL002_DC("smart_bm_0736", "服务内训师"),
    OUTBOUND_SPECIALIST("smart_bm_0737", "外呼专员"),
    A_ADMIN("smart_bm_a999", "代理商管理员");

    private final String code;
    private final String name;

    BmStationEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BmStationEnum fromCode(String code) {
        for (BmStationEnum station : BmStationEnum.values()) {
            if (station.getCode().equals(code)) {
                return station;
            }
        }
        throw new IllegalArgumentException("Invalid BmStationEnum code: " + code);
    }

    @Override
    public String toString() {
        return this.name;
    }
}