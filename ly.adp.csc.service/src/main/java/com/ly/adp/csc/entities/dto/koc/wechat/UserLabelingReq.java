package com.ly.adp.csc.entities.dto.koc.wechat;

import java.io.Serializable;
import java.util.List;

/**
 * 企微端用户打标请求DTO
 * <AUTHOR>
 * @since 2025-07-31
 */
public class UserLabelingReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * smartId
     */
    private String smartId;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 达人类型ID列表
     */
    private List<String> expertTypeIds;

    /**
     * 备注内容
     */
    private String remark;

    public String getSmartId() {
        return smartId;
    }

    public void setSmartId(String smartId) {
        this.smartId = smartId;
    }

    public List<String> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<String> tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getExpertTypeIds() {
        return expertTypeIds;
    }

    public void setExpertTypeIds(List<String> expertTypeIds) {
        this.expertTypeIds = expertTypeIds;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "UserLabelingReq{" +
                "smartId='" + smartId + '\'' +
                ", tagIds=" + tagIds +
                ", expertTypeIds=" + expertTypeIds +
                ", remark='" + remark + '\'' +
                '}';
    }
}
