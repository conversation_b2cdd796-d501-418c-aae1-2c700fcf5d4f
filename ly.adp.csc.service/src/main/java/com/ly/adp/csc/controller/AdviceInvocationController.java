package com.ly.adp.csc.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;
import com.ly.adp.csc.service.IAdviceInvocationService;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.OptResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(value = "前后置接口调用服务", tags = "前后置接口调用服务")
@RestController
@RequestMapping(value = "/ly/adp/csc/invokeAdvice", produces = { MediaType.APPLICATION_JSON_VALUE })
public class AdviceInvocationController {

	@Autowired
	IAdviceInvocationService adviceInvocationService;

	@ApiOperation(value = "试乘试驾跟换门店发送通知-后置", notes = "试乘试驾跟换门店发送通知-后置")
	@PostMapping(value = "/messageNoticeClue.do")
	public OptResult sacBasisLogSaveInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> adviceInvocationService.cscMessageNoticeClue(dateInfo, authentication))
				.result();
	}

	@ApiOperation(value = "发送通知-预约通知-后置", notes = "发送通知-预约通知-后置")
	@PostMapping(value = "/messageNoticeConvention.do")
	public OptResult cscMessageNoticeConvention(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
			@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> adviceInvocationService.cscMessageNoticeConvention(dateInfo, authentication))
				.result();
	}

	@ApiOperation(value = "发送通知-预约通知-后置-优化接口性能", notes = "发送通知-预约通知-后置-优化接口性能")
	@PostMapping(value = "/messageNoticeConvention-performance.do")
	public OptResult cscMessageNoticeConvention_performance(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
												@RequestBody(required = false) Map<String, Object> dateInfo) {
		return BusicenInvoker.doOpt(() -> adviceInvocationService.cscMessageNoticeConvention_performance(dateInfo, authentication))
				.result();
	}
}
