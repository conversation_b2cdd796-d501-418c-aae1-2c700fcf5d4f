# 是否开启Feign token兼容模式
mp.component.enable.feign.token=true
# allinone 地址
mp.component.token.url=http://**************
# base地址
ref.url.adp.base=http://**************

#seata.service.default.grouplist=**************:8091
service.vgroupMapping.default_tx_group=*************:8091
refer.url.xapi.api=**************:8061
#refer.url.xapi.api=http://localhost:8061
refer.url.adp.base=**************:8080
refer.url.adp.api=**************:8061
refer.url.adp.orc=**************:8086
#refer.url.adp.api=http://localhost:8061
#refer.url.adp.clue=http://localhost:8085
refer.url.adp.clue=http://**************:8085
refer.url.mp.allinone=http://**************:8100
refer.url.image.url=http://**************

#CDP加密策略
AESCipherKey=SmartUatTid5

#rabbitmq 适配
bucn.rabbitmq.init=true

bucn.rabbitmq.master=master

bucn.rabbitmq.pool.master.host=**************

bucn.rabbitmq.pool.master.port=5672

bucn.rabbitmq.pool.master.username=adp

bucn.rabbitmq.pool.master.password=adp@smart2021

bucn.rabbitmq.pool.master.virtualhost=/adp/public

bucn.rabbitmq.config.minIdle=8

bucn.rabbitmq.config.maxIdle=200

bucn.rabbitmq.config.maxTotal=230



spring.application.name=ly-adp-csc-service
server.servlet.context-path=/
bucn.cdata.enable.mybatis=false
#bucn.cdata.enable.interceptor=false
#bucn.cdata.enable.properties=false
#bucn.cdata.enable.sqlwrap=false

#分布式的端口
server.port=8085
# tomcat最大线程数，默认为200
server.tomcat.max-threads=500
# tomcat的URI编码
server.tomcat.uri-encoding=UTF-8
# 存放Tomcat的日志、Dump等文件的临时文件夹，默认为系统的tmp文件夹（如：C:\Users\<USER>\AppData\Local\Temp）
server.tomcat.basedir=./springboot/project
# 打开Tomcat的Access日志，并可以设置日志格式的方法：
server.tomcat.access-log-enabled=true
#server.tomcat.access-log-pattern=
# accesslog目录，默认在basedir/logs
#server.tomcat.accesslog.directory=
# 日志文件目录
logging.path=./springboot/project
# 日志文件名称，默认为spring.log
logging.file=myapp.log
logging.config=classpath:logback-spring.xml
#logging.level.org.springframework.web=INFO
# -------------------------
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.locale=zh_CN
spring.jackson.time-zone=GMT+8
http.mappers.json-pretty-print=true 
http.mappers.json-sort-keys=true
spring.mvc.locale=zh_CN 
spring.mvc.date-format=dd/MM/yyyy
#ActiveProfile
spring.profiles.active=sit
#Redis配置
#多个servers用逗号(",")隔开,不需要配置redis从机的IP,只需要redis主机IP
#sentinel模式的格式masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,例如mymaster?**************:63793,**************:63794
#redis.session.servers=**************:6379,**************:6379,**************:6379
redis.session.servers=*************:6379,**************:6379
#redis密码,所有redis服务密码必须一样
redis.session.password=adb@smart2021
#redis.servers=**************:6379,**************:6379,**************:6379
redis.servers=*************:6379,**************:6379
redis.password=adb@smart2021
#最大连接线程数
redis.pool.maxActive=10000
#连接超时时间(单位:秒)
redis.pool.timeout=3000
#缓存时间(单位:秒)
redis.pool.expires=86400
#在获取一个jedis实例时，是否提前进行alidate操作；如果为true，则得到的jedis实例均是可用的；
redis.pool.testOnBorrow=true
#在return给pool时，是否提前进行validate操作；
redis.pool.testOnReturn=true
#
#session time out (单位:秒)
session.timeout=7200
#
#默认为false，不写日志，true 写日志
#程序运行日志
RunLog=true
#服务调用日志
InvokingLog=true
#业务数据日志
BssLog=true
#通过MQ转发记录日志
# NONE:不写日志，MQ:写MQ，FILE:写文件，ALL:所有方式（MQ和文件），FILE方式时，平台的日志分析功能无法使用
logStorageType=MQ
#
# 日志归档
# 是否启用日志归档
log.job.filing.enable=true
# 归档多久之前的数据
log.job.filing.interval=30
# 归档触发的时机，每月1号00:04:00
log.job.filing.cron=0 4 0 1 * ?
# 服务调用日志按功能统计作业，每隔2小时跑一次
log.job.invoking.func.cron=0 0 0/2 * * ?
# 服务调用日志按用户统计作业，每隔2小时跑一次
log.job.invoking.user.cron=0 0 0/2 * * ?
# 系统运行日志按功能统计作业，每隔2小时跑一次
log.job.run.func.cron=0 0 0/2 * * ?
#-----------------------------------
#  redisson分布式锁配置
# 是否启用分布式锁 true:启用 false:不启用,默认不启用, 如果没用到分布式锁 不要启用
redisson.redis.enable=true
# 集群时，需要所有主从节点地址
redisson.redis.servers=*************:6379,**************:6379
redisson.redis.password=adb@smart2021
# 监控锁的看门狗超时（宕机或进程挂了释放锁的超时时间），单位：毫秒。默认值：30000
redisson.redis.lockWatchdogTimeout=20000
# 集群状态扫描间隔时间，单位是毫秒。默认值： 1000
redisson.redis.scanInterval=1000
# 多主节点的环境里，每个 主节点的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64
redisson.redis.masterConnectionPoolSize=64
# 多从节点的环境里，每个 从服务节点里用于普通操作（非 发布和订阅）连接的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64
redisson.redis.slaveConnectionPoolSize=64
#-----------------邮箱配置-------------------
#必配项
#邮件服务器地址
mp.component.mailServer=mail-hd.dfl.com.cn
#
#必配项
#邮件服务器地址端口
mp.component.mailServerPort=25
#必配项
#发送提醒邮件的邮箱帐号
mp.component.mailAccount=<EMAIL>
#
#必配项
#发送提醒邮件的邮箱密码
mp.component.mailAccountPWD=1qaz@wsx
#
#发送提醒邮件显示的邮箱名(邮箱帐号或邮箱账号@之前的部分)
mp.component.mailAccountName=lanyoutest
# 系统管理员邮箱,多个使用逗号分隔
mp.component.adminEmail=<EMAIL>
#-----------------邮箱配置-------------------
#-----------------找回密码邮件配置-------------------
mp.component.findpwd.title=找回密码邮件
#第一个参数为用户名，第二个参数为设置新密码的链接
mp.component.findpwd.content=<br/>    尊敬的用户 {0},您好<br/><br/>您收到这封电子邮件是因为您申请了找回密码，如果不是您本人申请，请不用理会这封邮件。<br/><br/>        请点击链接  {1} 设置新密码。<br/><br/><br/><br/>注：请您收到邮件在2小时内使用，否则该链接失效。
#-----------------找回密码邮件配置-------------------
#-----------------MQ配置--------------------
#是否启用AMQ(true,false)
mp.component.amqOpen=false
# MQ类型 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (默认使用1:RabbitMQ, 如果没有设置amqType, 为兼容之前版本使用ActiveMQ)
mp.component.amqType=1
mp.component.amqUrl=172.26.165.86:9876;172.26.165.93:9876
# MQ端口，只对RabbitMQ有用
mp.component.amqPort=5672
mp.component.amqUser=rocketadmin
mp.component.amqPwd=Mp@2020
#队列，以“队列键:队列名:队列数量;队列键:队列名:队列数量”为格式，队列数量未配时，默认为1（注：队列键与代码绑定，确定后不能修改）
mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue
#是否启用待办消息, 默认为false, 如果没用到待办消息, 不要启用
mp.component.pendingMsg=true
#是否启用公告消息, 默认为false, 如果没用到公告消息, 不要启用
mp.component.noticeMsg=true
#是否启用CC消息, 默认为false, 如果没用到CC消息, 不要启用
mp.component.ccMsg=false
#-----------------MQ配置--------------------
#-----------------短信网关配置--------------------
mp.component.smsServerID=*************
mp.component.smsLoginName=E3S_PV
mp.component.smsLoginPWD=d9b0910c7fb545b98791af8641cf8f7e
mp.component.smsServerPort=8011
#-----------------短信网关配置--------------------
#--------------------AdDomain配置--------------------
#是否启用AD域(true,false)
mp.component.adOpen=true
#
#多个域，adHost、adDomain、adFilterProc、adPort、adUser、adPwd用☆分割
#AD域IP
mp.component.adHost=**************☆*************
#AD域后缀,必须是@xxx.xxx;如@lymp.com
mp.component.adDomain=@lymp.com☆@lyeap.lanyou.com
#过滤组织
mp.component.adFilterOrg=
#获取域某个属性字段的内容
#设置域属性字段域与用户表中的那个字段关联
mp.component.adFilterProc=SamAccountName☆SamAccountName
mp.component.adTableField=user_name
#AD域端口,默认端口389
mp.component.adPort=389☆389
#
mp.component.adUser=admin☆administrator
#
mp.component.adPwd=abc123!!☆abc123!!
#
#--------------------AdDomain配置--------------------
#--------------------平台配置--------------------
#项目名称
mp.component.itemname=MPJAVA-oracle-nacos
mp.component.itemcode=f0ae9afe071411e8b5fb0050569d5d3a
#是否Debug模式(true 是; false 否), Debug模式不需要Token强制验证
mp.component.isdebug=false
#网站域名(http://*************/或http://www.qq.com)
mp.component.rootUrl=http://*************:8088/
#  滑动验证使用图片的存放目录
mp.component.verifyPicDir=/home/<USER>/dockerfiles/verifyPIC
#  默认租户id, 单租户时设置为租户的id(兼容旧版本), 多租户时不要设置默认租户id
mp.component.defaultTenancyId=AADDC13F38953414E0533FF11AAC5574
#--------------------平台配置--------------------
#-----------------监控平台配置--------------------
#是否开启监控跟踪日志
mp.component.traceOpen=false
#是否开启SQL监控跟踪日志
mp.component.traceSqlOpen=false
#小于耗时阈值的不记录(单位:毫秒)
mp.component.traceThreshold=10
#监控URL地址
mp.component.traceUrl=http://172.26.165.242:18000/mc/api/sendv10
#-----------------监控平台配置--------------------
# cc notify url
mp.component.cc.notifyUrl=http://testroute.szlanyou.com/msgpush_mp2.cgi
mp.component.cc.sysname=sys_mp2.1_dff
#
#
#
#用于标记MP 待办列表、已办列表是否取消显示系统待办标签（如【审批】、【驳回】、【抄送】。。）
#默认为false，不取消，标记为true的时候取消
#pendingtitle_no_des=false
mp.wfengine.pendingTitleNoDes=true
#工作流节点初始化后调用的函数
#--------------------------------------paras-----------------------
# node_guid, node_from, node_type, autor_guid,
# autor_name, Pend_Autor, gather_pending_id, gather_value,
# orderauditor_delaytime, step_path_guid, step_guid, pending_type,
# extProV1, business_no, last_handler
#----------------------------------------------
mp.component.wfengine.onPendingCreatedInsert=
mp.component.wfengine.onPendingCreatedUpdate=
#工作流节点完成后调用的函数
#--------------------------------------paras-----------------------
# node_guid, node_from, node_type, autor_guid,
# autor_name, Pend_Autor, gather_pending_id, gather_value,
# orderauditor_delaytime, step_path_guid, step_guid, pending_type,
# extProV1, business_no, last_handler
#----------------------------------------------
mp.component.wfengine.onNodeClosedInsert=
mp.component.wfengine.onNodeClosedUpdate=
# 批量处理线程数, 默认20
mp.component.wfengine.batchProcessThreadCount=20
#-----------------微信公众平台配置--------------------
mp.component.wxgzAppID=wx31ae6672f98297fe
mp.component.wxgzAppSecret=da095df4ae2c15dbee46d714d32f5222
mp.component.wxgzToken=szmplanyou
mp.component.wxgzReply=欢迎关注联友MP公众号
mp.component.wxgzLoginCallUrl=https://XXX.XXX/mp/login/wxgzlogincallback.do
mp.component.wxgzResponseUrl=https://mdp1.szlanyou.com/wxweb/wxindex.html
#-----------------微信公众平台配置--------------------
#-----------------企业微信公众平台配置--------------------
mp.component.ewxgzAppID=wwde346d80e24185f9
mp.component.ewxgzAppSecret=P2zzu7ngoSGjClmyvrVFSPHoJduMHY1-Dz8fC9HJz38
mp.component.ewxgzBindSecret=YjYvnd_9VUVW-vQmhG2XKI5CCCmNW1huA2fuSqt5MSk
mp.component.ewxgzToken=szmplanyou
mp.component.ewxgzReply=欢迎关注联友MP企业微信
mp.component.ewxgzLoginCallUrl=http://web01.dawnpro.cc/mp/login/ewxgzlogincallback.do
mp.component.ewxgzResponseUrl=http://web01.dawnpro.cc/wxweb/wxindex.html
mp.component.ewxgzBindResponseUrl=http://web01.dawnpro.cc/wxweb/wxbindindex.html
#-----------------企业微信公众平台配置--------------------
#登陆时用户名是否使用lower函数
mp.component.ignoreUserNameCase=true
#--------------------人脸识别配置--------------------
mp.component.facecompareURL=http://**********:10001/face/recog/group/compare
mp.component.faceQueryURL=http://**********:10001/face/clustering/face/query
mp.component.faceCreateURL=http://**********:10001/face/clustering/face/create
mp.component.faceEditUrl=http://**********:10001/face/clustering/face/edit
mp.component.faceDelUrl=http://**********:10001/face/clustering/face/delete
mp.component.faceGroup=LYAI
mp.component.faceScore=0.9
mp.component.faceNums=5
#刷脸登录时，0关闭，1总成功，2总失败；
mp.component.faceTestModule=1
#--------------------人脸识别配置--------------------
#Alipay 二维码支付与条码支付
mp.pay.alipay.qr.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.alipay.qr.support[0]=true
mp.pay.alipay.qr.alipayPublicKey[0]=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgR4kDsDe2A5ISVrqHZKj0SHm3iPGiw5P3LwEu1Rn4kSVAodRgcAgTr7jL7nAj8LF5f2Ow4Xk7H4OIecngOSZLOce/xXz/PIsUxuoQeIAox7pTuK9swTr7sZFl0COXHTSm5BSadR5jWkdQNeqTjZUxWIrrhZd2kTVUlzJhRbwkCACfAXWdS0lLDOGjcYfbOyo0zC8O/njG3e+MmU0eErQyQ+x0wdffTNHfJ2y5PvIrm988+pZvdu8uJzQTUpxjFmwNripI/Bjthng+8MBfPAI7/Zk9/hfn3RtMm1E2pu/btu6BjYkffp2D3oEcUEYTIyB0FInDgrYYLdewYyqmX2IjQIDAQAB
mp.pay.alipay.qr.appId[0]=2017110109661663
mp.pay.alipay.qr.privateKey[0]=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCkDLs+MtzdX4ptcaZ+E59Wt670Jtsr7vuiWFV/ZUQ4MmD0yaPOCglfgixCSCiHzqFmRn4+VI69+YFMdI7lCv0e9xNCmQjgN4lmow9PlIeOsXO8Ka8QKxVhYULfsZRdp6fF5SS0lkm/kmHOkIDCP8Tlipe1AA5xZhKqIhx7fPPY3Y64MehD7vqc8d8E7p9DykveX92DcYZOwYk/9GWkSlmiMa0uWbv/9e1pQaSy2l4iRWfxTsxLtq3FC1Zc/kJW2aEAXFZo3bftYfQY8BoVhVURk8b7ZY2VvV+osAYFpCAlra4m7UjGiBLqT7gsrW3EA1lm8F5YIAYhZKclmXtVJztzAgMBAAECggEAW/I20Dm5yOnPsF/OrUNaP0RcbsOIfCtKJkfUQ78CaWzzIsARa138uuc+3zeKX/PUSnqgL1c9WgUKD0wU+xMZo81foigb7W+zNy+VWUkqYTPcZk4GrM05Aod879ucCJH7WtN/qyfA1fq5jwk24ajUNsNjHDOX5L8NHwZXaG80TQubRUAIZoljCH+/WVA1IH6lFAhceQOTVRGhaCOFxDJgW5XgKL8ki5pzvf3D3Z25cor2ixkAwDeaQnedJvPrvfGg5XGebRAd9HQb2bA+/mlCFKesPTEiJk8jX39wCIqdhS+RJZtNVRqHPl0pWqTi+RxdMzLXjrVxdQSYyxX0ZPf2QQKBgQDNpeX8RtyY57Fev6bsW3Ne8nKjvyfc5FBx7e3krcC/JCWLI1YndjIVNHyOHn4V+3rwk7QS8BwU9NJaPHuuk92AV2RysDfvlfbmD8DxFw5uNaD+ByWlKPSf/vjWdORLRf4diogP4DYTaZcH/7NpnvuiyQae6buU93cxbAGaYnl5qwKBgQDMN280D+eUIB2EBtZcAhmclmqBCkm+NT6diencNVhBmtt14WzrsTgD2/9BfMr2YnlVuxvo77cSEQZjwg2jxz2XnsaE6zMoH/uug8LoGrPlAYPhtBDa6H75C2gvF3acT78FTGIw5V+Zk3rw9u8pGK9aZpqHPz6OI6suo6mOhXrNWQKBgCDuo0DeHC+EUvwoVtPc5UHcM42TbA/MpDFKd+E6DfbOFBEPDJnvLKAGsreTnH9qsUpbbOfneafFePYoX2oalcsas6RGIf8FFe/LsAsrtQzjG6/ydw3W3C3PCAxX1cNUJxiV+aoJLr+3Fg+a3CFa61MrPBswtPBrHLRWZn9Rq8BXAoGBAJY6VWj0JkS2V2Avc3Od699gW5rvyY3ON3DG6q2e5HzmgXk3Stwbs3xLU3yGY/xaNq4VhhOWfJMiyROLxmLsB+hI2fsf1rM2y/v2W+RI+HuH4M+hmiCflgB5Hrw9w3h7xacNKNKVef1NG8y1qvwNd7nF7vl9UfRYUu5tYSdCFDOJAoGAH3Qv4LS7hPJY7w+s7SFA+9nMdCxRhGlKGZpkBfzdG1sPZb1EVGnRYHl5o1c23hC9rJG5YT+RNTpuev8pBqC8Sbv0sK8bl5cKxb2M4J3xNDfUt9UvoX3k4PtYGqWHkc7s6FSGVcadm3RUDg1e0vl04LVs02FZrdDS83LJZoNb8IA=
#Alipay app pay properties
mp.pay.alipay.app.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.alipay.app.support[0]=true
mp.pay.alipay.app.timeoutExpress[0]=30m
mp.pay.alipay.app.appId[0]=2017020705549022
mp.pay.alipay.app.privateKey[0]=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCIqo9sVoyc4XuTyZjnOqGQxDqlmtZd8FIs3RFpdc6V2/P+1WjGKa0ALGb5ds4+Ai13rxvk2qfz6N+0jkTlbdxdlFPUpJOukkhDvip2JB4jBv5AVj6iZNCwTvXC4bt1VNqdXWJPKJW3iMZtwM5q+JR9Mm8rHh5peRORTHEYYCLjhp5yT/Qd/FQjnrIaCQ6ZlAWqH1O6IjYA2egi2lK+BioaOSSfSf0lDRu91f8/zw3AaUgbNeDpCTVl4niMU3FMwAwIeNd8JMjedpl6YsOLi3Ozc3UqwWAJRWQ+Y5AZIBnBqTnnT6qJbcvwrY3WWcEWN7VB+kabC9roD2LZcR1h1U0fAgMBAAECggEAAgmQ48uSgxA1eGmL9v5/xBm4yhk50uKvRu3wwYK5FrDVvVnPGw0qABOpYVStFG2R4fPhVZMQ5+jz1Mw+KLFvlJGOgmAFt5eErkXaejJlpJ4JEkp22pLoLYivzzzwP3qJW8k1mvp+6OKIGsDCEwQrqoYi9CCZ0Xa1nPfnw7ZDLuMgPACWQ75fPUngnRbB7Uwty131NROK6FNt7/16ByQipevTMuhcOO9eF0s4XcoubStg0w5uL03UfIZ8x4ldD0O78Dafd2SeSRae45nVMdpoefI0GNnY+gRNOyhK/+S6akXaXINRxTDLypfA3gQ6XEzufuT8x7D2XypTHfjuaLRkCQKBgQDZGL96IlZQzYRHJWW6SViDbcJHbB4V3Oy+kpSoIq3SNrOpvkApE3/tHtxJxzSWVFt+5tlSTz5IyR4Ae1wWDxR5A46LQqYFCENGbdG1nz3djqTahuhhwGtN3PMyTgV8MqFY2vQv+oHJJsjVaf9pCD55g76aekhIk3QFOfzNBKLmMwKBgQChKBaQbj/TvIZl/GbMC43C0DYeHRUpWIFmgX15LxXYAUS6XAtd0TNM18DwKZcA5OY4tsi1+W2ynIMk/198naBBBMSmW4lUjWG1g1Zp+ghTO/MDsswy/MqQGkra/kU+tG4qUeDGrKUBo36r6AEn9xnHL9N4l8qhO4gp7QBl8hSZZQKBgC4pK/WdPXs658h09DdzBwYTHX8wRwlhC1nOMxu5G/qZtQP/twbE5auWp0JswArC7x2Bmm38+YJieSWjFUZ/eFvu3K1Rw5lIU32zNicHMBFfFkB89QZr8qUAuRlWK6Zn4ZTSIZ/eBSCvRX7TZgKARUBzOeEA5UPBTqcZ2F4DgVuvAoGAbXkU8uHhu8y6I3d0wTEsCGV2DbjF0kNMC7z05ihFF2mtLUcvdXiR96YsazhlWncjqO0JpQweJ5HISI6tZ7KP1PsPNs7BmE0+TZY9UlpF43y61Q1VR2GPnJovtVm64iChIWBjZ7KJmHZeqxo8BtEFkth7N9UtEZ+mHIzhk18B/T0CgYB2or6GPH+ZrnHvIHqQGh0a1vaBGuiUTwGIWPYHJSI92YsXQ2+pDodq/gM+aM99qtmEEJjsTn7iV446sds8LF8tcbS2QJucDltdvTm1dh8qU9ESHJU79wzaRFk+IKdieWNqD11kaidRNSz0i+BSgh6Y79jRmwiJ0G4rYRZpKdiTEA==
mp.pay.alipay.app.alipayPublicKey[0]=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgR4kDsDe2A5ISVrqHZKj0SHm3iPGiw5P3LwEu1Rn4kSVAodRgcAgTr7jL7nAj8LF5f2Ow4Xk7H4OIecngOSZLOce/xXz/PIsUxuoQeIAox7pTuK9swTr7sZFl0COXHTSm5BSadR5jWkdQNeqTjZUxWIrrhZd2kTVUlzJhRbwkCACfAXWdS0lLDOGjcYfbOyo0zC8O/njG3e+MmU0eErQyQ+x0wdffTNHfJ2y5PvIrm988+pZvdu8uJzQTUpxjFmwNripI/Bjthng+8MBfPAI7/Zk9/hfn3RtMm1E2pu/btu6BjYkffp2D3oEcUEYTIyB0FInDgrYYLdewYyqmX2IjQIDAQAB
#Weixin 扫码与条码支付
mp.pay.weixin.qr.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.weixin.qr.support[0]=true
mp.pay.weixin.qr.appid[0]=wxa29f9b6dc75ece95
mp.pay.weixin.qr.mch_id[0]=1491587722
mp.pay.weixin.qr.certLocalPath[0]=/home/<USER>/certs/weixinqr_cert.p12
mp.pay.weixin.qr.certPassword[0]=1491587722
mp.pay.weixin.qr.key[0]=D15AF265019C48cc8D3F307C30E532DB
mp.pay.weixin.qr.spbill_create_ip[0]=**************
#Weixin app pay properties
mp.pay.weixin.app.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.weixin.app.support[0]=true
mp.pay.weixin.app.appid[0]=wxccf7dc136735ee23
mp.pay.weixin.app.mch_id[0]=1464996002
mp.pay.weixin.app.certLocalPath[0]=/home/<USER>/certs/weixinapp_cert.p12
mp.pay.weixin.app.certPassword[0]=1464996002
mp.pay.weixin.app.key[0]=D15AF265019C48cc8D3F307C30E532DB
mp.pay.weixin.app.spbill_create_ip[0]=**************
mp.pay.weixin.app.gateway[0]=http://mdp1.szlanyou.com:8083/mp/pay/weixin/app/gateway.do
#------------------文件上传配置--------------------
#是否使用nfs保存上传文件,默认为false
mp.component.useNfs=false
#mount nfs命令
mp.component.mountNfsCmd=mount -t nfs *************:/springboot/upload /springboot/fileupload/upload -o nolock,rsize=1024,wsize=1024,timeo=15
#上传文件类型
mp.component.uploadFileFormat=doc|docx|xlsx|xls|pptx|ppt|txt|rar|zip|7z|jpg|png|gif|ico|icon|jpeg|bmp|xml|pdf|apk|aip|mp3|mp4|
#上传文件保存路径
mp.component.uploadFilePath=upload
#上传文件保存模式, 1 类别/日期(默认方式) 2 日期/类别
mp.component.uploadSaveMode=1
# 是否启用水印
mp.component.watermarkEnable=true
# 需要添加水印的路径, 默认所有路径。 如只需要在公告的文件添加水印，路径为：mp/file/notice/upload.do，则配置mp.component.watermarkCategories=notice
mp.component.watermarkCategories=notice
# 需要添加水印的文件类型, 小写
mp.component.watermarkFileTypes=pdf,docx,xlsx,png,jpg,gif,bmp
# pdf水印字体文件路径，（pdfbox默认不支持中文，水印包含中文时，需要配置包含中文的字体路径）
mp.component.watermarkPdfFont=
# 水印字体名称(如：宋体), pdf不受此影响
mp.component.watermarkWordFont=宋体
# 水印内容
mp.component.watermarkContent=mp_watermark
# 水印颜色
mp.component.watermarkColor=#aaaaaa
# 水印字体大小，单位 pt
mp.component.watermarkFontSize=30
# 水印旋转角度（逆时针）
mp.component.watermarkRotate=45
# 水印透明度
mp.component.watermarkAlpha=0.5
# 水印间的横向间隔（像素）
mp.component.watermarkSpaceX=200
# 水印间的纵向间隔（像素）
mp.component.watermarkSpaceY=100
# 是否限制excel内容（总行数、总单元格数），由于poi的限制，加载excel需要大量内存，当excel中内容过多时，容易导致oom
mp.component.watermarkExcelLimit=true
# excel文件的最大sheet数
mp.component.watermarkExcelMaxSheets=100
# excel文件的最大总行数
mp.component.watermarkExcelMaxRows=50000
# excel文件的最大总单元格数
mp.component.watermarkExcelMaxCells=1000000
#
#-----------------用户风险配置--------------------
# 检查在线用户状态作业
mp.component.onlineUser.checkCron=0 0/10 * * * ?
# 剔除用户操作后多少分钟之后真正剔除，提前通知用户保存
mp.component.onlineUser.timesInMinuteBerforeOffline=5
# 最大同时在线用户数量，超过禁止其他用户登录，0表示不限制，管理员不受限制；默认值为0；
mp.component.onlineUser.maxOnlineUserNumbers=0
#是否一个账号只能同时登录一台电脑
mp.component.kickUser=false
#允许一个账号同时登陆几台电脑，mp.component.kickUser=true本设置生效，大于0正整数为有效设置；默认为1；
mp.component.onlineUser.oneUserMaxOnlineNumbers=2
# IP地址解析开关
mp.component.risk.geoIP2Db=true
# GeoIP2数据库本地地址：根据IP获取数据库详细地址数据库的本地地址
mp.component.risk.geoIP2DbPath=/home/<USER>/dockerfiles/GeoLite2-City.mmdb
# ip地址数，超过该数值则视为有异地登录的风险，小于该数值则分析ip是否在多个城市，是则存在风险
mp.component.risk.numOfIps=3
# api调用次数，当一个用户的api调用次数大于该值，并且满足mp.component.risk.frequency条件时，才视为有api风险
mp.component.risk.numOfApi=50
# 每分钟调用次数，大于该频率则视为用户调用API有风险（必须同时满足mp.component.risk.numOfApi）
mp.component.risk.frequency=30
# 是否用户风险分析作业
mp.component.risk.jobEnable=true
# 跑作业的时间
mp.component.risk.jobCron=0 30 10 * * ?
# 风险通知邮件标题
mp.component.risk.title=MPJAVA_oracle_nacos系统用户帐号风险通知
# 风险通知管理员邮箱
mp.component.risk.emails=<EMAIL>
# 风险通知管理员手机,必须真实手机
mp.component.risk.mobile=***********
# 风险通知管理员邮件内容
mp.component.risk.adminContent={0}用户风险分析如下：{1}
# 风险通知个人邮件内容
mp.component.risk.accountContent={0}您的帐号[{1}]存在风险，请及时修改密码！存在风险：{2}
#-----------------系统检查问题通知邮件--------------------
mp.component.checkCron=0 0/10 * * * ?
mp.component.checkNoticeEnabled=true
mp.component.checkNoticeEmails=<EMAIL>
#license到期前多少天开始提醒,默认60
mp.component.license.advanceDays=60
#接收license到期提醒邮箱帐号,多个使用逗号分隔
mp.component.license.emails=<EMAIL>
#接收license到期提醒手机号,多个使用逗号分隔,必须真实手机
mp.component.license.mobiles=***********
#license到期提醒邮件标题
mp.component.license.title=MPJAVA系统License到期提醒
#license到期提醒内容模板
mp.component.license.content={0}您的系统License{1}，请及时更新License!
#-----------------API权限拦截--------------------
#是否启用API数据采集,仅用于项目开发、测试阶段作为数据采集辅助，系统正式上线必须关闭此功能
mp.component.apipurview.dataCollection=false
#是否启用
mp.component.apipurview.enable=false
#需要排除api权限检查的url地址(多个地址用,分隔)
mp.component.apipurview.exclude=/mp/framework/resourceslist.do,/mp/org/user/getUserInfo.do,/mp/wfdesign/manager/navigation.do,mp/wfdesign/wfPermissionCtrl/getProcessCreateUserList.do,/mp/framework/usergzscan.do,/mp/framework/wxunbind.do,/mp/org/role/search.do,/mp/org/relation/list.do,/mp/org/user/select.do,/mp/notice/unreadnum.do,/mp/file/*,/mp/framework/CustomMenuBatch.do,/mp/login/*,/mp/api/*,/mp/wfengine/*,/mp/test/*,/mp/file/uploadRichText.do,/mp/log/GetNavigationLog.do,/mp/framework/face/*,/mp/file/delfile.do,/mp/framework/modifyPassword.do,/mp/framework/sysetsave.do,/mp/framework/sysetload.do,/mp/notice/detail.do,/mp/notice/list.do,/mp/framework/getMyMenuTree.do,/mp/org/userPurview/getCtrlPriv.do,/mp/framework/getWebFormListPaged.do,/mp/framework/getloginpage.do,/mp/framework/getUserLayout.do,/mp/framework/GetCustomMenu.do,/mp/framework/DelCustomMenu.do,/mp/framework/CustomMenuAdd.do,/mp/log/NavigationLogAdd.do,/mp/wfdesign/setpedit/getnames.do
#-----------------API权限拦截--------------------
#-----------------租户校验配置--------------------
#是否启用租户id校验
mp.component.tenancy.tenancyIdCheck=false
#启用租户id校验后，哪些api请求可以不带tenancyId访问
mp.component.tenancy.exclude=/mp/log/*,/mp/framework/*,/mp/notice/*,/mp/wfdesign/*,/mp/login/*,/mp/org/*,/mp/wfengine/*,/mp/file/*
#-----------------默认租户配置--------------------
#-----------------sign参数加签校验--------------------
#是否启用
mp.component.signprotect.enable=false
#需要排除保护url地址(多个地址用,分隔)
mp.component.signprotect.exclude=
#签名密钥
mp.component.signprotect.secret=Iv3RlGKyxrCDfu3a
#-----------------sign参数加签校验--------------------
#-----------------XSS保护--------------------
#是否启用
mp.component.xssprotect.enable=false
#需要排除保护url地址(多个地址用,分隔)
mp.component.xssprotect.exclude=/mp/notice/add.do,/mp/notice/modify.do,/mp/wfdesign/setpedit/savegraph.do
#需要保护的表达式
mp.component.xssprotect.expression=
#-----------------XSS保护--------------------
#JTA配置
#----------------- JTA配置 --------------------
spring.jta.atomikos.properties.defaultJtaTimeout=300000
spring.jta.atomikos.properties.maxTimeout=300000
spring.jta.atomikos.properties.maxActives=-1
#----------------- JTA配置 --------------------
#----------------- 安全配置 --------------------
# 跨域设置, 值为*: 允许跨所有域, 没有值:不允许跨域, 值为指定域名:可以跨指定的域
mp.component.accessControlAllowOrigin=*
# 通知邮件，如果title或content没配置值，则不会发邮件
mp.component.disableUser.title=账号停用通知邮件
mp.component.disableUser.content=<br/>    尊敬的用户 {0}, 您好:<br/><br/>您收到这封电子邮件是因为您的账号被停用。如果有疑问，请联系系统管理员。
mp.component.lockUser.title=账号锁定通知邮件
mp.component.lockUser.content=<br/>    尊敬的用户 {0}, 您好:<br/><br/>您收到这封电子邮件是因为您的账号因登录失败次数过多被锁定。如果有疑问，请联系系统管理员。
#用户账号到期前多少天开始提醒管理员,默认10
mp.component.expireUser.advanceDays=10
mp.component.expireUser.title=账号到期通知邮件
mp.component.expireUser.content=<br/>    您好: <br/><br/>下列用户 {0} 的账号即将到期，请检查是否需要更改用户的失效日期。
mp.component.changePass.title=更改密码通知邮件
mp.component.changePass.content=<br/>    尊敬的用户 {0}, 您好:<br/><br/>您收到这封电子邮件是因为您的账号更改了密码。如果有疑问，请联系系统管理员。
# 是否启用防重放攻击
mp.component.antiReplayAttack=true
#输入密码多少次出现验证码,默认三次
mp.component.displayVerifyCode=3
#验证码类型，0：数字字母组合；1：汉字验证码，2：纯数字验证码
mp.component.vcodeType=2
#验证码长度，3~6位
mp.component.vcodeSize=4
# 对配置项加密的密码
jasypt.encryptor.password=cOIHAgfFZ41NJaNnVB+ylA==
#----------------- 安全配置 --------------------
#----------------- 一元化接入配置 --------------------
mp.component.oauth2.oauth2ClientId=mp20
mp.component.oauth2.oauth2RedirectUri=
mp.component.oauth2.oauth2GrantType=authorization_code
mp.component.oauth2.oauth2ResponseType=code
mp.component.oauth2.oauth2ClientSecret=mp20
mp.component.oauth2.oauth2Url=http://172.26.161.151:8021/
mp.component.oauth2.oauth2Authorize=/oauth/authorize
mp.component.oauth2.oauth2Token=/oauth/token
mp.component.oauth2.oauth2User=/api/user?access_token=
#----------------- 一元化接入配置 --------------------
# 表单文件存放目录
mp.component.form.fileDir=/mpjava/form/files
#根据使用不同的数据库，扫描不到的DAL包,多个以","逗号分隔;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver
write.mp.jdbc.packagescan=com.ly.mp.**.mysql
#mp的数据库(主库)事务策略 普通: normal 多库(jta) : jta  多服务:tcc
write.mp.jdbc.transactionPolicy=gtsx
#没有配置这配置项不影响升级,作用是返回的默认集合查询结果转成大写.没有配置或者默认是true:key转成大写,false:key不作转换
write.mp.jdbc.upperCaseColumn=true
# url,username,password可以进行加密，使用密文
write.mp.jdbc.name=mp_write
write.mp.jdbc.url=jdbc:mysql://10.170.250.168:3306/csc?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true
write.mp.jdbc.username=adpuser
write.mp.jdbc.password=Adp@smart2024

other.write.mp.jdbc.name[0] = tidb
other.write.mp.jdbc.url[0] =jdbc:mysql://10.170.250.168:3306/busicen_1?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
other.write.mp.jdbc.username[0] = adpuser
other.write.mp.jdbc.password[0] = Adp@smart2024
#read.jdbc.name[mp_write#1]=default_mp_read
#read.jdbc.url[mp_write#1]=jdbc:oracle:thin:@172.26.241.63:1521:TMP2XDB
#read.jdbc.username[mp_write#1]=mp24
#read.jdbc.password[mp_write#1]=mp24
mp.read.db.size=0
#
#druid datasource
#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE
druid.initialSize=3
druid.minIdle=3
druid.maxActive=20
druid.maxWait=60000
druid.timeBetweenEvictionRunsMillis=60000
druid.minEvictableIdleTimeMillis=300000
druid.validationQuery=select 1 from dual
druid.testWhileIdle=true
druid.testOnBorrow=false
druid.testOnReturn=false
druid.poolPreparedStatements=false
druid.maxPoolPreparedStatementPerConnectionSize=20
#druid.keepAlive=true
druid.phyTimeoutMillis=1200000
#wall,slf4j,stat
druid.filters=stat
#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3
#mp2.24增量配置
#mybatis
mybatis-plus.mapperLocations=classpath:/mybatis/mapping/*Mapper.xml,classpath*:/assembly/mapper/*Mapper.xml
#实体扫描，多个package用逗号或者分号分隔
mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities
mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums
#数据库相关配置
#主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
mybatis-plus.global-config.db-config.id-type=UUID
#字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
mybatis-plus.global-config.db-config.field-strategy=not_empty
#驼峰下划线转换
mybatis-plus.global-config.db-config.column-underline=true
#数据库大写下划线转换
#capital-mode: true
#逻辑删除配置
mybatis-plus.global-config.db-config.logic-delete-value=0
mybatis-plus.global-config.db-config.logic-not-delete-value=1
#mybatis-plus.global-config.db-config.db-type=sqlserver
#刷新mapper 调试神器
mybatis-plus.global-config.refresh=true
# 原生配置
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# 消息队列 bucn.redis.mq   adp.rabbit
bucn.ms.adapter=adp.rabbit

# 业务策略
csc.clue.addreview.strategy=addReviewDefault

# 过期时间
csc.cache.cluedlrbypage.time=50
csc.cache.departClueTransfer.time=60

bucn.result.stack.enable=false



