<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacExpertTypeMapper">

    <!-- 选增性新增 -->
    <insert id="insertSelective">
        INSERT INTO `t_sac_expert_type`
        <trim prefix="(" suffixOverrides="," suffix=")">
            <if test="data.typeId != null">
                `TYPE_ID`,
            </if>
            <if test="data.typeName != null">
                `TYPE_NAME`,
            </if>
            <if test="data.typeContent != null">
                `TYPE_CONTENT`,
            </if>
            <if test="data.creator != null">
                `CREATOR`,
            </if>
            <if test="data.createdName != null">
                `CREATED_NAME`,
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE`,
            </if>
            <if test="data.modifier != null">
                `MODIFIER`,
            </if>
            <if test="data.modifyName != null">
                `MODIFY_NAME`,
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffixOverrides="," suffix=")">
            <if test="data.typeId != null">
                #{data.typeId},
            </if>
            <if test="data.typeName != null">
                #{data.typeName},
            </if>
            <if test="data.typeContent != null">
                #{data.typeContent},
            </if>
            <if test="data.creator != null">
                #{data.creator},
            </if>
            <if test="data.createdName != null">
                #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                #{data.createdDate},
            </if>
            <if test="data.modifier != null">
                #{data.modifier},
            </if>
            <if test="data.modifyName != null">
                #{data.modifyName},
            </if>
            <if test="data.lastUpdatedDate != null">
                #{data.lastUpdatedDate},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM `t_sac_expert_type` WHERE `TYPE_ID` = #{typeId}
    </delete>

    <!-- 选增性修改 -->
    <update id="updateSelective">
        UPDATE
            `t_sac_expert_type`
        <set>
            <if test="data.typeName != null and data.typeName != ''">
                `TYPE_NAME` = #{data.typeName},
            </if>
            <if test="data.typeContent != null and data.typeContent != ''">
                `TYPE_CONTENT` = #{data.typeContent},
            </if>
            <if test="data.creator != null and data.creator != ''">
                `CREATOR` = #{data.creator},
            </if>
            <if test="data.createdName != null and data.createdName != ''">
                `CREATED_NAME` = #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE` = #{data.createdDate},
            </if>
            <if test="data.modifier != null and data.modifier != ''">
                `MODIFIER` = #{data.modifier},
            </if>
            <if test="data.modifyName != null and data.modifyName != ''">
                `MODIFY_NAME` = #{data.modifyName},
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE` = #{data.lastUpdatedDate},
            </if>
        </set>
        WHERE
            `TYPE_ID` = #{data.typeId}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultType="com.ly.adp.csc.entities.SacExpertType">
        SELECT
            `TYPE_ID`           AS typeId,
            `TYPE_NAME`         AS typeName,
            `TYPE_CONTENT`      AS typeContent,
            `CREATOR`           AS creator,
            `CREATED_NAME`      AS createdName,
            `CREATED_DATE`      AS createdDate,
            `MODIFIER`          AS modifier,
            `MODIFY_NAME`       AS modifyName,
            `LAST_UPDATED_DATE` AS lastUpdatedDate
        FROM
            `t_sac_expert_type`
        WHERE
            `TYPE_ID` = #{typeId}
    </select>

</mapper>