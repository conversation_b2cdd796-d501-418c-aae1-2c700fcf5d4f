<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacDemoCarMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacDemoCar">
		<id column="CAR_ID" property="carId" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="PLATE_NUMBER" property="plateNumber" />
        <result column="SMALL_CAR_TYPE_CN" property="smallCarTypeCn" />
        <result column="SMALL_CAR_TYPE_CODE" property="smallCarTypeCode" />
        <result column="CAR_STATUS_CODE" property="carStatusCode" />
        <result column="CAR_STATUS_NAME" property="carStatusName" />
        <result column="CAR_VIN" property="carVin" />
        <result column="ROAD_HAUL" property="roadHaul" />
        <result column="REGISTRATION_TIME" property="registrationTime" />
        <result column="RETIRED_TIME" property="retiredTime" />
        <result column="REMARK" property="remark" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		DLR_CODE, CAR_ID, DLR_NAME, PLATE_NUMBER, SMALL_CAR_TYPE_CN, SMALL_CAR_TYPE_CODE, 
		CAR_STATUS_CODE, CAR_STATUS_NAME, CAR_VIN, ROAD_HAUL, REGISTRATION_TIME, RETIRED_TIME,
		 REMARK, IS_ENABLE, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, 
		 MODIFY_NAME, LAST_UPDATED_DATE, UPDATE_CONTROL_ID
	</sql>
	<!-- 试驾车查询 -->
	<select id="selectDemoCar" resultType="java.util.Map">
		select
		CAR_ID,
		DLR_CODE,
		DLR_NAME,
		CAR_STATUS_CODE,
		CAR_STATUS_NAME,
		PLATE_NUMBER,
		SMALL_CAR_TYPE_CN,
		SMALL_CAR_TYPE_CODE,
		CAR_VIN, ROAD_HAUL,
		REGISTRATION_TIME,
		RETIRED_TIME,
		REMARK,
		IS_ENABLE,
		(case IS_ENABLE when '1' then '启用' else '停用' end) as isEnableName,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID
		from t_sac_demo_car
		where 1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> and DLR_NAME=#{param.dlrName}</if>
		<if test="param.carStatusCode !=null and param.carStatusCode !=''"> and CAR_STATUS_CODE=#{param.carStatusCode}</if>
		<if test="param.carStatusName !=null and param.carStatusName !=''"> and CAR_STATUS_NAME=#{param.carStatusName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> and PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.smallCarTypeCn !=null and param.smallCarTypeCn !=''"> and SMALL_CAR_TYPE_CN=#{param.smallCarTypeCn}</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> and SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.carVin !=null and param.carVin !=''"> and CAR_VIN=#{param.carVin}</if>
		<if test="param.roadHaul !=null and param.roadHaul !=''"> and ROAD_HAUL=#{param.roadHaul}</if>
		<if test="param.registrationTime !=null"> and REGISTRATION_TIME=#{param.registrationTime}</if>
		<if test="param.registrationTimeMin !=null"> <![CDATA[ and REGISTRATION_TIME>=#{param.registrationTimeMin}]]></if>
		<if test="param.registrationTimeMax !=null"> <![CDATA[ and REGISTRATION_TIME<date_add(#{param.registrationTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.retiredTime !=null"> and RETIRED_TIME=#{param.retiredTime}</if>
		<if test="param.retiredTimeMin !=null"> <![CDATA[ and RETIRED_TIME>=#{param.retiredTimeMin}]]></if>
		<if test="param.retiredTimeMax !=null"> <![CDATA[ and RETIRED_TIME<date_add(#{param.retiredTimeMax}, INTERVAL 1 day)]]></if>
		<choose>
			<when  test='param.isRetired=="1"'>
				<![CDATA[and RETIRED_TIME <= CURRENT_TIMESTAMP]]>
			</when>
			<when  test='param.isRetired=="0"'>
				<![CDATA[and RETIRED_TIME > CURRENT_TIMESTAMP]]>
			</when>
		</choose>
		<if test="param.remark !=null and param.remark !=''"> and REMARK=#{param.remark}</if>
		<if test="param.oemId !=null and param.oemId !=''"> and OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''"> and GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''"> and CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''"> and CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null"> and CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''"> and MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> and MODIFY_NAME=#{param.modifyName}</if>
		<if
			test="param.lastUpdatedDate !=null"> and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and IS_ENABLE=#{param.isEnable}</if>
		
	</select>


	<!-- 试驾车新增 -->
	<insert id="insertDemoCar">
		insert into t_sac_demo_car(
		CAR_ID,
		DLR_CODE,
		DLR_NAME,
		PLATE_NUMBER,
		SMALL_CAR_TYPE_CN,
		SMALL_CAR_TYPE_CODE,
		CAR_STATUS_CODE,
		CAR_STATUS_NAME,
		CAR_VIN,
		ROAD_HAUL,
		REGISTRATION_TIME,
		RETIRED_TIME,
		REMARK,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		COLUMN6,
		COLUMN7,
		COLUMN8,
		COLUMN9,
		COLUMN10,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		IS_ENABLE)
		values(
		#{param.carId},
		#{param.dlrCode},
		#{param.dlrName},
		#{param.plateNumber},
		#{param.smallCarTypeCn},
		#{param.smallCarTypeCode},
		#{param.smallCarTypeCn},
		#{param.smallCarTypeCode},
		#{param.carVin},
		#{param.roadHaul},
		#{param.registrationTime},
		#{param.retiredTime},
		#{param.remark},
		#{param.column1},
		#{param.column2},
		#{param.column3},
		#{param.column4},
		#{param.column5},
		#{param.column6},
		#{param.column7},
		#{param.column8},
		#{param.column9},
		#{param.column10},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		#{param.updateControlId},
		#{param.isEnable})
	</insert>

	<!-- 试驾车保存 -->
	<update id="updateDemoCar">
		update t_sac_demo_car set
		<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> DLR_NAME=#{param.dlrName},</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> PLATE_NUMBER=#{param.plateNumber},</if>
		<if test="param.smallCarTypeCn !=null and param.smallCarTypeCn !=''"> SMALL_CAR_TYPE_CN=#{param.smallCarTypeCn},</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode},</if>
		<if test="param.carStatusCode !=null and param.carStatusCode !=''"> CAR_STATUS_CODE=#{param.carStatusCode},</if>
		<if test="param.carStatusName !=null and param.carStatusName !=''"> CAR_STATUS_NAME=#{param.carStatusName},</if>
		<if test="param.carVin !=null and param.carVin !=''"> CAR_VIN=#{param.carVin},</if>
		<if test="param.roadHaul !=null and param.roadHaul !=''"> ROAD_HAUL=#{param.roadHaul},</if>
		<if
			test="param.registrationTime !=null"> REGISTRATION_TIME=#{param.registrationTime},</if>
		<if test="param.retiredTime !=null"> RETIRED_TIME=#{param.retiredTime},</if>
		<if test="param.remark !=null and param.remark !=''"> REMARK=#{param.remark},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if
			test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		where CAR_ID=#{param.carId}
	</update>
	
	<select id="checkPlateNumberRepeat" resultType="int">
		select count(1)
		from t_sac_demo_car
		where PLATE_NUMBER=#{param.plateNumber}
		<if test="param.carId !=null and param.carId !=''"> and CAR_ID!=#{param.carId}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
	</select>
	
	<select id="checkCarVinRepeat" resultType="int">
		select count(1)
		from t_sac_demo_car
		where CAR_VIN=#{param.carVin}
		<if test="param.carId !=null and param.carId !=''"> and CAR_ID!=#{param.carId}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
	</select>
	<select id="checkRepeat" resultType="int">
		select count(1)
		from t_sac_demo_car
		where CAR_VIN=#{param.carVin}
		and PLATE_NUMBER=#{param.plateNumber}
		<if test="param.carId !=null and param.carId !=''"> and CAR_ID!=#{param.carId}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
	</select>
	
	<update id="updateRoadHaul">
		update t_sac_demo_car set
		<choose>
			<when  test='param.updateFlag.toString()=="true"'>
				<![CDATA[ROAD_HAUL=ROAD_HAUL-#{param.oldTestRoadHaul}+#{param.testRoadHaul}]]>
			</when>
			<otherwise>
				<![CDATA[ROAD_HAUL=ROAD_HAUL+#{param.testRoadHaul}]]>
			</otherwise>
		</choose>
		where 1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> and PLATE_NUMBER=#{param.plateNumber}</if>
		<if
			test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> and SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.carVin !=null and param.carVin !=''"> and CAR_VIN=#{param.carVin}</if>
	</update>
</mapper>
