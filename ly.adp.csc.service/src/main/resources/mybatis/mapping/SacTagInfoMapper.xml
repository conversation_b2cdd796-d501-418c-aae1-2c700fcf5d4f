<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacTagInfoMapper">

    <!-- 选增性新增 -->
    <insert id="insertSelective">
        INSERT INTO `t_sac_tag_info`
        <trim prefix="(" suffixOverrides="," suffix=")">
            <if test="data.tagId != null">
                `TAG_ID`,
            </if>
            <if test="data.tagName != null">
                `TAG_NAME`,
            </if>
            <if test="data.tagName != null">
                `PARENT_TAG_ID`,
            </if>
            <if test="data.tagLevel != null">
                `TAG_LEVEL`,
            </if>
            <if test="data.fullPath != null">
                `FULL_PATH`,
            </if>
            <if test="data.tagStatus != null">
                `TAG_STATUS`,
            </if>
            <if test="data.sortOrder != null">
                `SORT_ORDER`,
            </if>
            <if test="data.creator != null">
                `CREATOR`,
            </if>
            <if test="data.createdName != null">
                `CREATED_NAME`,
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE`,
            </if>
            <if test="data.modifier != null">
                `MODIFIER`,
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffixOverrides="," suffix=")">
            <if test="data.tagId != null">
                #{data.tagId},
            </if>
            <if test="data.tagName != null">
                #{data.tagName},
            </if>
            <if test="data.parentTagId != null">
                #{data.parentTagId},
            </if>
            <if test="data.tagLevel != null">
                #{data.tagLevel},
            </if>
            <if test="data.fullPath != null">
                #{data.fullPath},
            </if>
            <if test="data.tagStatus != null">
                #{data.tagStatus},
            </if>
            <if test="data.sortOrder != null">
                #{data.sortOrder},
            </if>
            <if test="data.creator != null">
                #{data.creator},
            </if>
            <if test="data.createdName != null">
                #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                #{data.createdDate},
            </if>
            <if test="data.modifier != null">
                #{data.modifier},
            </if>
            <if test="data.lastUpdatedDate != null">
                #{data.lastUpdatedDate},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM `t_sac_tag_info` WHERE `TAG_ID` = #{tagId}
    </delete>

    <!-- 选增性修改 -->
    <update id="updateSelective">
        UPDATE
            `t_sac_tag_info`
        <set>
            <if test="data.tagName != null and data.tagName != ''">
                `TAG_NAME` = #{data.tagName},
            </if>
            <if test="data.parentTagId != null and data.parentTagId != ''">
                `PARENT_TAG_ID` = #{data.parentTagId},
            </if>
            <if test="data.tagLevel != null">
                `TAG_LEVEL` = #{data.tagLevel},
            </if>
            <if test="data.fullPath != null and data.fullPath != ''">
                `FULL_PATH` = #{data.fullPath},
            </if>
            <if test="data.tagStatus != null">
                `TAG_STATUS` = #{data.tagStatus},
            </if>
            <if test="data.sortOrder != null">
                `SORT_ORDER` = #{data.sortOrder},
            </if>
            <if test="data.creator != null and data.creator != ''">
                `CREATOR` = #{data.creator},
            </if>
            <if test="data.createdName != null and data.createdName != ''">
                `CREATED_NAME` = #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE` = #{data.createdDate},
            </if>
            <if test="data.modifier != null and data.modifier != ''">
                `MODIFIER` = #{data.modifier},
            </if>
            <if test="data.modifyName != null and data.modifyName != ''">
                `MODIFY_NAME` = #{data.modifyName},
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE` = #{data.lastUpdatedDate},
            </if>
        </set>
        WHERE
            `TAG_ID` = #{data.tagId}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultType="com.ly.adp.csc.entities.SacTagInfo">
        SELECT
            `TAG_ID`            AS tagId,
            `TAG_NAME`          AS tagName,
            `PARENT_TAG_ID`     AS parentTagId,
            `TAG_LEVEL`         AS tagLevel,
            `FULL_PATH`         AS fullPath,
            `TAG_STATUS`        AS tagStatus,
            `SORT_ORDER`        AS sortOrder,
            `CREATOR`           AS creator,
            `CREATED_NAME`      AS createdName,
            `CREATED_DATE`      AS createdDate,
            `MODIFIER`          AS modifier,
            `MODIFY_NAME`       AS modifyName,
            `LAST_UPDATED_DATE` AS lastUpdatedDate
        FROM
            `t_sac_tag_info`
        WHERE
            `TAG_ID` = #{tagId}
    </select>

</mapper>