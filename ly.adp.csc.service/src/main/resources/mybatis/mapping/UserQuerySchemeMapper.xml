<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.UserQuerySchemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.UserQueryScheme">
        <id column="SCHEME_ID" property="schemeId" />
        <result column="SCHEME_NAME" property="schemeName" />
        <result column="SCHEME_CONTEXT" property="schemeContext" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SCHEME_ID, SCHEME_NAME, SCHEME_CONTEXT, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

    <select id="queryScheme" parameterType="java.lang.String" resultType="java.util.Map">
        select * from t_sac_bu_user_query_scheme
        where IS_ENABLE='1'
          and CREATOR=#{creator}
          and SCHEME_TYPE=#{schemeType}
    </select>
  <insert id="insertScheme" parameterType="java.util.Map">
INSERT INTO `t_sac_bu_user_query_scheme`
(`SCHEME_ID`, SCHEME_TYPE,`SCHEME_NAME`, `SCHEME_CONTEXT`, `CREATOR`, `CREATED_NAME`, `CREATED_DATE`, `MODIFIER`, `MODIFY_NAME`, `LAST_UPDATED_DATE`, `IS_ENABLE`, `UPDATE_CONTROL_ID`)
VALUES
(UUID(),#{schemeType},#{schemeName} , #{schemeContext},
 #{creator},  #{createdName},  #{createdDate},  #{modifier},  #{modifyName},  #{lastUpdatedDate}, '1',  #{updateControlId});
  </insert>
    <update id="updateScheme" parameterType="java.util.Map">
        update t_sac_bu_user_query_scheme set
        <if test="schemeName !=null and schemeName !=''">
            SCHEME_NAME=#{schemeName},
        </if>
        <if test="schemeContext !=null and schemeContext !=''">
            SCHEME_CONTEXT=#{schemeContext},
        </if>
        `MODIFIER`= #{modifier},
        `MODIFY_NAME`= #{modifyName},
        `LAST_UPDATED_DATE`= #{lastUpdatedDate},
        `IS_ENABLE`= #{isEnable},
        `UPDATE_CONTROL_ID`=UUID()
         where
        SCHEME_ID=#{schemeId}
        and UPDATE_CONTROL_ID=#{updateControlId}
    </update>
    <delete id="delScheme" parameterType="java.util.Map">
        delete from t_sac_bu_user_query_scheme where SCHEME_ID=#{schemeId}
    </delete>
</mapper>
