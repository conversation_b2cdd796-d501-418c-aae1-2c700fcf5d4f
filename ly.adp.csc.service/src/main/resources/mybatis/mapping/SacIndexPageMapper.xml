<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacIndexPageMapper">

	<!-- 查询首页显示信息 -->
	<select id="sacIndexPageInfoQueryByDlr" parameterType="map" resultType="map">
		WITH A AS (
		SELECT
		'TJ' AS ITEM,
		sum(CASE WHEN c.STATUS_CODE = '10' OR c.STATUS_CODE = '7' THEN 0 ELSE 1 END) AS TOTAL_NUM, /*全部线索 - 不统计战败和战败申请*/
		<!--  sum(CASE WHEN c.STATUS_CODE = '7' THEN 0 ELSE 1 END) AS DESTROY_CLUE_NUM, --> <!-- 战败申请数  -->
		SUM(CASE WHEN IFNULL(c.COLUMN5, '') = '' then 0 else 1 end) HEAT_NUM
		FROM
		t_sac_clue_info_dlr c
		WHERE
		1 = 1
		AND c.DLR_CODE = #{param.dlrCode}
		<if test = "param.personID != null and param.personID != ''">
			AND c.REVIEW_PERSON_ID = #{param.personID}
		</if>
		),
		AA AS (
		SELECT
		'TJ' AS ITEM,
		/*待分配*/
		(SELECT COUNT(1) FROM
		csc.t_sac_review r
		INNER JOIN csc.t_sac_clue_info_dlr c1 ON r.REVIEW_ID = c1.REVIEW_ID
		LEFT JOIN csc.t_sac_onecust_info i ON i.CUST_ID = c1.CUST_ID
		LEFT JOIN mp.t_usc_mdm_org_employee e ON e.USER_ID = r.REVIEW_PERSON_ID  WHERE 	r.REVIEW_STATUS IN ( '0', '1','3')
		AND r.org_code = #{param.dlrCode}
		AND r.ASSIGN_STATUS = '0'
		<if test="param.filterDccFlag">
			AND r.COLUMN19 IS NULL
		</if>
		)  AS UN_FP_NUM,
		/*待审核*/
		SUM( CASE WHEN r.review_status='3' THEN 1 ELSE 0 END ) AS UN_AUDIT_NUM,
		/*待回访*/
		SUM( CASE WHEN r.review_status IN ('0', '1')
		<!-- 1-已分配 -->
		<if test="param.assignStatus != null and ''!= param.assignStatus ">
			AND r.ASSIGN_STATUS = #{param.assignStatus}
		</if>
		<if test="param.planStartTime != null and ''!= param.planStartTime">
			<![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
		</if>
		<if test="param.planEndTime != null and ''!= param.planEndTime">
			<![CDATA[ AND r.PLAN_REVIEW_TIME <= #{param.planEndTime} ]]>
		</if>
		THEN 1 ELSE 0 END ) AS FOLLOW_NUM
		FROM t_sac_review r
		WHERE 1=1
		AND r.org_code= #{param.dlrCode}
		<if test = "param.personID != null and param.personID != ''">
			AND r.REVIEW_PERSON_ID = #{param.personID}
		</if>
		),
		B AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS DRIVE_NUM
		FROM
		t_sac_test_drive_sheet s
		WHERE
		s.IS_ENABLE = '1'
		AND s.TEST_STATUS != '2'
		AND s.DLR_CODE = #{param.dlrCode}
		<if test = "param.personID != null and param.personID != ''">
			AND s.SALES_CONSULTANT_ID = #{param.personID}
		</if>
		),
		C AS (
		SELECT 'TJ' AS ITEM, COUNT(1) AS TRANSFER_NUM
		FROM t_sac_transfer_apply s
		JOIN t_sac_transfer_audit A ON s.APPLY_ID = A.APPLY_ID
		WHERE A.SH_STATUS = '1' /*审核通过*/
		AND (S.OUT_DLR_CODE = #{param.dlrCode} OR S.IN_DLR_CODE = #{param.dlrCode} )),
		D AS (
		SELECT
		'TJ' AS ITEM,
		count(1) AS ACT_NUM /*已发布*/
		FROM t_acc_bu_activity t1
		WHERE 1 = 1
		AND t1.STATUS_CODE = '2'
		AND t1.RELEASE_STATUS_CODE = '1'
		AND t1.IS_ENABLE = '1'
		<if test = "param.activityTypeCodes != null and param.activityTypeCodes != ''">
			AND t1.ACTIVITY_TYPE_CODE IN
			<foreach collection="param.activityTypeCodes.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<!-- AND t1.END_TIME <![CDATA[>=]]> DATE_FORMAT(now(), '%Y-%m-%d %H:%i:%s') -->
		<if test = "param.dlrCode != null and param.dlrCode != ''">
			AND #{param.dlrCode} REGEXP REPLACE(t1.dlr_code, ',', '|')
		</if>
		AND CASE WHEN t1.CREATE_TYPE_CODE IN ('DLR', 'DEVELOP') THEN 1 = 1 ELSE T1.RELEASE_STATUS_CODE = '1' END
		)
		SELECT
		A.TOTAL_NUM,/*全部线索*/
		A.HEAT_NUM, <!-- 热度统计 -->
		AA.UN_FP_NUM,/*待分配*/
		AA.UN_AUDIT_NUM,/*待审核*/
		AA.FOLLOW_NUM,/*待回访*/
		ifnull( B.DRIVE_NUM, 0 ) AS DRIVE_NUM,/*试驾任务数*/
		ifnull( C.TRANSFER_NUM, 0 ) AS TRANSFER_NUM, /*划转数*/
		D.ACT_NUM
		FROM A
		LEFT JOIN AA ON A.ITEM = AA.ITEM
		LEFT JOIN B ON A.ITEM = B.ITEM
		LEFT JOIN C ON A.ITEM = C.ITEM
		LEFT JOIN D ON A.ITEM = D.ITEM
	</select>

	<!-- 查询首页消息显示 -->
	<select id="queryinfoMsgbydlr" resultType="java.util.Map">
		WITH A AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS ONE_NUM
		FROM
		t_sac_test_drive_task t
		WHERE
		1 = 1
		AND t.TASK_PERSON_DLR_CODE = #{param.dlrCode}
		AND t.TASK_STATE_CODE = '0'
		<if test="param.taskPersonId !=null and param.taskPersonId !=''">and t.TASK_PERSON_ID=#{param.taskPersonId}</if>
		<if test="param.orDlrCode !=null and param.orDlrCode.size()>0 ">
			and t.TASK_PERSON_DLR_CODE in
			<foreach collection="param.orDlrCode"  item="item" open="(" close=")" separator="," >#{item}</foreach>
		</if>
		),
		B AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS TWO_NUM
		FROM
		t_sac_clue_msg_record msg
		INNER JOIN t_sac_onetask_info info ON msg.RELATION_BILL_ID = info.TASK_ID
		WHERE
		1 = 1
		AND RECEIVE_EMP_ID = #{param.receiveEmpId}
		AND msg.MESSAGE_TYPE IN ( '5' )
		AND msg.IS_READ = '0'
		AND info.TASK_STATE_CODE = '3'
		AND msg.DLR_CODE = #{param.dlrCode}
		),
		C AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS THREE_NUM
		FROM
		t_sac_clue_msg_record
		WHERE
		1 = 1
		AND RECEIVE_EMP_ID = #{param.receiveEmpId}
		AND MESSAGE_TYPE IN ( '2', '3', '6', '7', '8', '9', '10', '13', '14','15' )
		),
		D AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS FOUR_NUM
		FROM
		t_sac_clue_msg_record msg
		INNER JOIN t_sac_onetask_info info ON msg.RELATION_BILL_ID = info.TASK_ID
		WHERE
		1 = 1
		AND RECEIVE_EMP_ID = #{param.receiveEmpId}
		AND msg.MESSAGE_TYPE IN ( '4' )
		AND msg.IS_READ = '0'
		AND info.TASK_STATE_CODE = '3'
		AND msg.DLR_CODE = #{param.dlrCode}
		),
		E AS (
			SELECT
			'TJ' AS ITEM,
			COUNT(1) AS FIVE_NUM
			FROM (
			SELECT DISTINCT info.TASK_ID
			FROM t_sac_clue_msg_record msg
			INNER JOIN t_sac_onetask_info info
			ON msg.RELATION_BILL_ID = info.TASK_ID
			INNER JOIN t_sac_onetask_detail d
			ON d.TASK_ID = info.TASK_ID
			WHERE
			msg.RECEIVE_EMP_ID = #{param.receiveEmpId}
			AND msg.DLR_CODE = #{param.dlrCode}
			AND msg.MESSAGE_TYPE = '4'
			AND msg.IS_READ = '0'
			AND info.TASK_STATE_CODE = '3'
			AND info.BUSS_END_TIME &lt; NOW()
			AND (
				d.BUSS_END_TIME &lt; d.BUSS_TIME
				OR (
					d.BUSS_TIME IS NULL AND d.BUSS_END_TIME &lt; NOW()
				)
			)
			ORDER BY info.BUSS_TIME DESC
			) task
		)
		SELECT
		A.ONE_NUM,
		B.TWO_NUM,
		C.THREE_NUM,
		D.FOUR_NUM,
		E.FIVE_NUM
		FROM
		A
		LEFT JOIN B ON A.ITEM = B.ITEM
		LEFT JOIN C ON A.ITEM = C.ITEM
		LEFT JOIN D ON A.ITEM = D.ITEM
		LEFT JOIN E ON A.ITEM = E.ITEM
	</select>

	<!-- Deprecated -->
	<!-- 查询首页未审核任务数:回访审核 + 划转申请审核 + 超长出库申请审核 -->
	<select id="sacIndexPageUnAuditNumQueryByDlr" parameterType="map" resultType="int">
		SELECT  /*回访审核*/
		IFNULL((SELECT IFNULL(SUM( CASE WHEN T.review_status='3' THEN 1 ELSE 0 END ), 0) AS UN_AUDIT_NUM FROM t_sac_review T
		WHERE 1 = 1
		<!-- 总部，品牌大使orgCode为空 -->
		AND T.ORG_CODE = #{param.orgCode}
		<if test = "param.personID != null and param.personID != ''">
			AND T.CREATOR = #{param.personID}
		</if>)
		+   /*划转申请审核*/
		(SELECT COUNT(1) FROM t_sac_transfer_apply T1
		LEFT JOIN t_sac_transfer_audit T2 ON T1.APPLY_ID = T2.APPLY_ID AND T2.IS_ENABLE = 1
		WHERE T2.SH_STATUS = '0'
		AND T2.APPLY_TYPE_CODE = #{param.applyTypeCode}
		<if test = "param.dlrCode != null and param.dlrCode != ''">
			AND T1.OUT_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test = "param.personID != null and param.personID != ''">
			AND T2.CREATOR = #{param.personID}
		</if>)
		+ (SELECT COUNT(1) FROM t_sac_test_drive_long_apply T WHERE T.AUDIT_STATUS = '1' /*超长出库申请审核*/
		<if test = "param.dlrCode != null and param.dlrCode != ''">
			AND T.APPLY_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test = "param.personID != null and param.personID != ''">
			AND T.CREATOR = #{param.personID}
		</if>
		AND T.AUDIT_TYPE = #{param.auditType}  <!-- 审核类型 -->
		)
		, 0) AS TOTAL_UN_AUDIT_NUM
	</select>

	<!-- 查询首页未审核任务数:回访审核 + 划转申请审核 + 超长出库申请审核 , 战败审核-->
	<select id="sacIndexPageAuditNumQueryByDlr" parameterType="map" resultType="map">
		WITH
		<!-- 回访审核 -->
		A AS (
		SELECT
		'TJ' AS ITEM,
		/* IFNULL(SUM(CASE WHEN T.review_status='3' THEN 1 ELSE 0 END), 0) AS REVIEW_AUDIT_NUM */
		COUNT(1) AS DESTROY_CLUE_NUM
		FROM t_sac_review_audit A
		INNER JOIN t_sac_review T ON A.review_id = T.review_id
		WHERE 1 = 1
		AND A.sh_status = '0'
		<!-- 总部，品牌大使orgCode为空 -->
		AND T.ORG_CODE = #{param.orgCode}
		AND (a.sh_person_id is null or a.sh_person_id =#{param.shPersonId})
		<if test = "param.personID != null and param.personID != ''">
			AND T.CREATOR = #{param.personID}
		</if>
		),
		<!-- 回访审核 -->
		AA AS (
		SELECT
		'TJ' AS ITEM,
		COUNT( 1 ) AS DESTROY_NUM
		FROM (SELECT
		r.REVIEW_ID
		FROM
		t_sac_review_audit a
		INNER JOIN t_sac_clue_info_dlr r ON a.review_id = r.review_id
		WHERE
		a.is_enable = '1'
		and a.sh_status='1'
		and r.COLUMN20 is NULL
		and r.DLR_CODE=#{param.orgCode}
		<!--<choose>
			<when test="param.orgId !=null and param.orgId !=''">
				and r.DLR_CODE in (
				SELECT
				DLR_CODE
				FROM
				mp.t_usc_mdm_org_dlr
				WHERE
				COMPANY_ID=#{param.orgId}
				)
			</when>
			<otherwise>
				and r.DLR_CODE=#{param.orgCode}
			</otherwise>
		</choose>-->
		<if test="param.identification !=null and param.identification !=''">
		and	r.REVIEW_PERSON_ID=#{param.shPersonId}
		</if>
		) AA
		),
		/*划转申请审核*/
		B AS (
		SELECT
		'TJ' AS ITEM,
		COUNT(1) AS TRANSFER_APPLY_AUDIT_NUM
		FROM t_sac_transfer_apply T1
		LEFT JOIN t_sac_transfer_audit T2 ON T1.APPLY_ID = T2.APPLY_ID AND T2.IS_ENABLE = 1
		WHERE T2.SH_STATUS = '0' AND T2.APPLY_TYPE_CODE = #{param.applyTypeCode}
		<if test = "param.dlrCode != null and param.dlrCode != ''">
			AND T1.OUT_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test = "param.personID != null and param.personID != ''">
			AND T2.CREATOR = #{param.personID}
		</if>
		),
		C AS (
		SELECT
		'TJ' AS ITEM,
		COUNT(1)  AS LONG_APPLY_AUDIT_NUM
		FROM t_sac_test_drive_long_apply T WHERE T.AUDIT_STATUS = '1' /*超长出库申请审核*/
		<if test = "param.dlrCode != null and param.dlrCode != ''">
			AND T.APPLY_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test = "param.personID != null and param.personID != ''">
			AND T.CREATOR = #{param.personID}
		</if>
		AND T.AUDIT_TYPE = #{param.auditType}  <!-- 审核类型 -->
		)
		SELECT
		A.DESTROY_CLUE_NUM,
		AA.DESTROY_NUM,
		B.TRANSFER_APPLY_AUDIT_NUM,
		C.LONG_APPLY_AUDIT_NUM <!-- 超长出库申请审核 -->
		FROM A
		LEFT JOIN AA ON A.ITEM = AA.ITEM
		LEFT JOIN B ON A.ITEM = B.ITEM
		LEFT JOIN C ON A.ITEM = C.ITEM
	</select>

	<select id="queryHeatRatio" parameterType="map" resultType="map">
		SELECT
		round(B.HOT / A.TOTAL, 2) AS HOT_RATIO,
		round(C.WARM / A.TOTAL, 2) AS WARM_RATIO,
		round(D.COLD / A.TOTAL, 2) AS COLD_RATIO
		FROM
		(SELECT count(1) AS TOTAL FROM t_sac_clue_info_dlr WHERE
		DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		<if test="param.reviewPersonId != null and param.reviewPersonId != ''">
			AND REVIEW_PERSON_ID = #{param.reviewPersonId}
		</if>
		<if test="param.clueCreateDateStart != null and param.clueCreateDateStart != ''">
			AND CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreateDateStart}
		</if>
		<if test="param.clueCreateDateEnd != null and param.clueCreateDateEnd != ''">
			AND CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreateDateEnd}
		</if>
		) A,
		(SELECT count(1) AS HOT FROM t_sac_clue_info_dlr WHERE
		DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND COLUMN5 = 'Hot'
		<if test="param.reviewPersonId != null and param.reviewPersonId != ''">
			AND REVIEW_PERSON_ID = #{param.reviewPersonId}
		</if>
		<if test="param.clueCreateDateStart != null and param.clueCreateDateStart != ''">
			AND CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreateDateStart}
		</if>
		<if test="param.clueCreateDateEnd != null and param.clueCreateDateEnd != ''">
			AND CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreateDateEnd}
		</if>
		) B,
		(SELECT count(1) AS WARM FROM t_sac_clue_info_dlr WHERE
		DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND COLUMN5 = 'Warm'
		<if test="param.reviewPersonId != null and param.reviewPersonId != ''">
			AND REVIEW_PERSON_ID = #{param.reviewPersonId}
		</if>
		<if test="param.clueCreateDateStart != null and param.clueCreateDateStart != ''">
			AND CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreateDateStart}
		</if>
		<if test="param.clueCreateDateEnd != null and param.clueCreateDateEnd != ''">
			AND CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreateDateEnd}
		</if>
		) C,
		(SELECT count(1) AS COLD FROM t_sac_clue_info_dlr WHERE
		DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND COLUMN5 = 'Cold'
		<if test="param.reviewPersonId != null and param.reviewPersonId != ''">
			AND REVIEW_PERSON_ID = #{param.reviewPersonId}
		</if>
		<if test="param.clueCreateDateStart != null and param.clueCreateDateStart != ''">
			AND CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreateDateStart}
		</if>
		<if test="param.clueCreateDateEnd != null and param.clueCreateDateEnd != ''">
			AND CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreateDateEnd}
		</if>
		) D
	</select>

	<!-- ADP报表统计样例 - 线索报表 -->
	<select id="queryAdpReportForm" resultType="java.util.Map">
	SELECT
		A.TOTAL_CLUE_NUM,
		A.DESTROY_CLUE_NUM,
		round(A.DESTROY_CLUE_NUM / A.TOTAL_CLUE_NUM, 2) AS DESTROY_CLUE_RATIO,
		A.SLEEP_CLUE_NUM, /*休眠线索量*/
		A.VALID_CLUE_NUM, /*(非战败+休眠线索量)*/
		B.OVER_CLUE_NUM,
		A.L0_CLUE_NUM,
		A.L1_CLUE_NUM,
		A.L2_CLUE_NUM,
		A.L3_CLUE_NUM,
		A.L4_CLUE_NUM,
		A.L5_CLUE_NUM,
		A.HOT_CLUE_NUM,
		A.WARM_CLUE_NUM,
		A.COLD_CLUE_NUM,
		A.IS_SPECIAL_NUM,
		round(A.REVIEWED_CLUE_NUM / A.TOTAL_CLUE_NUM, 2) AS REVIEW_RATIO, /*24小时跟进率*/
		A.num19,
		A.num21,
		A.num23,
		A.num24
	FROM (
	SELECT
		1 AS ID,
		count(DISTINCT(T.CUST_ID)) AS TOTAL_CLUE_NUM, /*线索总数*/
		sum(CASE WHEN T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 1 ELSE 0 END) AS SLEEP_CLUE_NUM,
		sum(CASE WHEN T.STATUS_CODE = '10' THEN 1 ELSE 0 END) AS DESTROY_CLUE_NUM,
<!--		sum(CASE WHEN (T.STATUS_CODE = '10') OR T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 0 ELSE 1 END) AS VALID_CLUE_NUM,-->
		count(DISTINCT(CASE WHEN (T.STATUS_CODE = '10') OR T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN null ELSE T.cust_id END)) AS VALID_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L0' THEN 1 ELSE 0 END) AS L0_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L1' THEN 1 ELSE 0 END) AS L1_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L2' THEN 1 ELSE 0 END) AS L2_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L3' THEN 1 ELSE 0 END) AS L3_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L4' THEN 1 ELSE 0 END) AS L4_CLUE_NUM,
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L5' THEN 1 ELSE 0 END) AS L5_CLUE_NUM,
		sum(CASE WHEN T.COLUMN5 = 'Hot' THEN 1 ELSE 0 END) AS HOT_CLUE_NUM,
		sum(CASE WHEN T.COLUMN5 = 'Warm' THEN 1 ELSE 0 END) AS WARM_CLUE_NUM,
		sum(CASE WHEN T.COLUMN5 = 'Cold' THEN 1 ELSE 0 END) AS COLD_CLUE_NUM,
		sum(CASE WHEN ifnull(T.COLUMN11, '0') = '1' THEN 1 ELSE 0 END) AS IS_SPECIAL_NUM,
		ifnull(sum(CASE WHEN
			(IFNULL(T.FIRST_REVIEW_TIME, '') != '') AND T.FIRST_REVIEW_TIME <![CDATA[ <= ]]> DATE_ADD(T.CREATED_DATE, INTERVAL 1 DAY)
			THEN 1 ELSE 0 END), 0) AS REVIEWED_CLUE_NUM, /*下发后24小时内已跟进线索数 首次跟进时间不为空*/
		count(DISTINCT(case when R1.custId is not null
		then T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) num19, /*线索到店率*/
		<!-- count(DISTINCT(case when C.BUY_CUST_ID is not null
		THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) as num21, /*线索大定转化率*/ -->
		count(DISTINCT(case when C.sale_order_state not in ('8')
		THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) as num21, /*线索大定转化率*/
		sum(case when C.sale_order_state in ('230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800')   then 1 else 0 end)/sum(case when C.sale_order_state in ('220','230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800') then 1 else 0 end) as num23,/*大定锁单转化率*/
		sum(case when C.sale_order_state in ('730','800') then 1 else 0 end)/sum(case when C.sale_order_state in ('230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800') then 1 else 0 end)   as num24 /*锁单交付转化率*/

		FROM t_sac_clue_info_dlr T
		left join (select DISTINCT(CUST_ID) custId
		from t_sac_onecust_resume
		where SENCE_CODE in ('2','16','19')
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			and CREATED_DATE >=#{param.createdTimeStart}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND DLR_CODE_OWNER IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		) R1 on R1.custId=T.CUST_ID
		<!-- left join (select DISTINCT(BUY_CUST_ID) BUY_CUST_ID,sale_order_state from orc.t_orc_ve_bu_sale_order_to_c
		where 1=1 and sale_order_state not in ('8','15','221')
		) C on C.BUY_CUST_ID=T.CUST_ID -->
		left join orc.t_orc_ve_bu_sale_order_to_c C on C.BUY_CUST_ID=T.CUST_ID
	WHERE 1 = 1
	<if test="param.dlrCode != null and param.dlrCode != ''">
		AND T.DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
		AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
	</if>
	<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
		AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
	</if>
	<if test="param.filterDccFlag">
		AND T.COLUMN19 IS NULL
	</if>
	) A
	LEFT JOIN
		(SELECT 1 AS ID, count(1) AS OVER_CLUE_NUM FROM t_sac_review R
		WHERE R.OVER_REVIEW_TIME <![CDATA[ < ]]> now()
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND R.ORG_CODE IN  /*SEND_DLR_CODE未使用*/
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<!-- 创建时间 -->
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND DATE_FORMAT(R.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
		</if>
		<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
			AND DATE_FORMAT(R.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
		</if>
		) B ON A.ID = B.ID
	</select>

	<!-- 用户分组界面线索统计 queryUserGroupClueReport -->
	<select id="queryUserGroupClueReport" parameterType="map" resultType="map">
		SELECT
		sum(CASE WHEN T.COLUMN5 = 'Hot' THEN 1 ELSE 0 END) AS HOT_CLUE_NUM,
		sum(CASE WHEN T.COLUMN5 = 'Warm' THEN 1 ELSE 0 END) AS WARM_CLUE_NUM,
		sum(CASE WHEN T.COLUMN5 = 'Cold' THEN 1 ELSE 0 END) AS COLD_CLUE_NUM,
		sum(CASE WHEN ifnull(T.COLUMN11, '0') = '1' THEN 1 ELSE 0 END) AS IS_SPECIAL_NUM <!-- 是否特别关注 -->
		FROM t_sac_clue_info_dlr T WHERE 1 = 1
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
		</if>
		<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
		</if>
		<if test="param.reviewPersonId != null and param.reviewPersonId != ''">
			AND T.REVIEW_PERSON_ID = #{param.reviewPersonId}
		</if>
		<if test="param.filterDccFlag">
			AND T.COLUMN19 IS NULL
		</if>
	</select>

	<!-- 试驾报表 试驾指标 -->
	<select id="queryTestDriveReport" parameterType="map" resultType="map">
		SELECT
		C.TEST_DRIVE_NUM AS FINISHED_TEST_DRIVE_NUM, /*已完成的试驾数*/
		C.TEST_DRIVER_CUST_NUM, /*试驾人数*/
		A.TEST_DRIVER_CAR_NUM, /*试驾车数量*/
		C.TEST_DRIVE_VALID_NUM / D.VALID_CLUE_NUM AS FINISHED_TEST_DRIVE_RATIO, /*试驾率*/
		0 AS TEST_DRIVE_XP_RATIO,/*试驾选配率*/
		C.TRANSFER_NUM / C.TEST_DRIVER_CUST_NUM AS TRANSFER_NUM, /*试驾转化率=试驾过产生的有效订单数(大定)/试驾人数*/
		C.TEST_DRIVE_NUM / B.TOTAL_DAY_NUM AS CAR_USE_RATIO,/*试驾车使用率*/
		C.num20
		FROM
		(SELECT
		0 AS ID,
		COUNT(1) AS TEST_DRIVER_CAR_NUM /*试驾车数量*/
		FROM t_usc_bu_testCar_prepare
		WHERE RESPONSE_ORDER_STATUS = '1'
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND APPLY_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		) A
		LEFT JOIN (
		SELECT
		0 AS ID,
		round(TIMESTAMPDIFF(HOUR, min(t.START_TIME), max(t.END_TIME)) / 24) * 12 AS TOTAL_DAY_NUM -- 总天数
		FROM t_sac_test_drive_sheet t WHERE t.IS_ENABLE = '1' AND t.TEST_STATUS = '2'
		) B ON A.ID = B.ID
		LEFT JOIN (
		SELECT
		0 AS ID,
		sum(PER_TEST_DRIVE_NUM) AS TEST_DRIVE_NUM,/*试驾数*/
		count(DISTINCT(case when CUSTOMER_ID is not null and end_time is not null
		THEN SERVER_ORDER ELSE null END)) AS TEST_DRIVE_VALID_NUM,
		/*试驾并且是有效线索 - 试驾过的有效线索数量*/
		/*ifnull(sum(CASE WHEN C1.CUST_ID IS NOT NULL AND C1.STATUS_CODE = '10' OR C1.DLR_CODE = 'HOST_SLEEP' OR C1.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 0 ELSE 1 END), 0) AS TEST_DRIVE_VALID_NUM,*/
		count(C1.CUSTOMER_PHONE) AS TEST_DRIVER_CUST_NUM, /*试驾人数*/
		/* CURRENT_ORDER_TYPE 当前订单类型，2-大定*/
		count( DISTINCT ( CASE WHEN C1.BUY_CUST_PHONE IS NOT NULL AND C1.CURRENT_ORDER_TYPE = '2' AND C1.end_time <![CDATA[ < ]]> C1.SALE_ORDER_DATE THEN C1.SERVER_ORDER ELSE NULL END )) AS TRANSFER_NUM,/* 大定转化数 */
		count(DISTINCT(case when C1.CUSTOMER_ID is not null and end_time is not null THEN C1.SERVER_ORDER ELSE null END))/count(DISTINCT(C1.SERVER_ORDER)) num20 /*线索试驾率（试乘试驾率）*/
		FROM (
		SELECT
		t2.CUST_ID , t2.STATUS_CODE, t2.DLR_CODE, DLR_SHORT_NAME,D.end_time,D.CUSTOMER_ID,t2.SERVER_ORDER,
		t.CUSTOMER_PHONE, O.BUY_CUST_PHONE, O.CURRENT_ORDER_TYPE,O.SALE_ORDER_DATE,
		count(DISTINCT t.TEST_DRIVE_SHEET_ID) AS PER_TEST_DRIVE_NUM /*单人的试驾数量*/
		FROM t_sac_test_drive_sheet t /* 试驾单表 */
		LEFT JOIN t_sac_appointment_sheet t1 ON t.APPOINTMENT_ID = t1.APPOINTMENT_ID AND t.DLR_CLUE_ORDER_NO = t1.DLR_CLUE_ORDER_NO
		LEFT JOIN t_sac_clue_info_dlr t2 ON t2.CUST_ID = t.CUSTOMER_ID
		left join
		(select
		t1.CUSTOMER_ID,end_time,T1.CREATED_DATE
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
		group by t1.CUSTOMER_ID) D on D.CUSTOMER_ID=t2.CUST_ID
		LEFT JOIN t_orc_ve_bu_sale_order_to_c O ON O.BUY_CUST_ID = t2.CUST_ID
		WHERE t.IS_ENABLE = '1' AND t.TEST_STATUS = '2' /*已完成*/
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND t.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<!-- 试驾结束时间 -->
		<if test="param.testDriveEndTimeStart != null and param.testDriveEndTimeStart != ''">
			AND DATE_FORMAT(t2.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.testDriveEndTimeStart}
		</if>
		<if test="param.testDriveEndTimeEnd != null and param.testDriveEndTimeEnd != ''">
			AND DATE_FORMAT(t2.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.testDriveEndTimeEnd}
		</if>
		GROUP BY t.CUSTOMER_PHONE
		) C1
		) C ON A.ID = C.ID
		LEFT JOIN (
		SELECT
		0 AS ID,
		sum(CASE WHEN
		T.DLR_CODE IN ('HOST', 'HOST_SLEEP')
		/*(T.STATUS_CODE = '10') OR T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP'*/
		THEN 0 ELSE 1 END) AS VALID_CLUE_NUM /*有效线索数, 7-战败申请，10-战败*/
		FROM t_sac_clue_info_dlr T
		WHERE 1 = 1
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		/*线索创建时间*/
<!--		<if test="param.clueCreatedTimeStart != null and param.clueCreatedTimeStart != ''">-->
<!--			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.clueCreatedTimeStart}-->
<!--		</if>-->
<!--		<if test="param.clueCreatedTimeEnd != null and param.clueCreatedTimeEnd != ''">-->
<!--			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.clueCreatedTimeEnd}-->
<!--		</if>-->
		<if test="param.testDriveEndTimeStart != null and param.testDriveEndTimeStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.testDriveEndTimeStart}
		</if>
		<if test="param.testDriveEndTimeEnd != null and param.testDriveEndTimeEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.testDriveEndTimeEnd}
		</if>
		) D ON A.ID = D.ID
	</select>

	<!--活动报表-->
	<select id="queryActivityReport" resultType="map">
	SELECT
		T1.*,
		T2.MAX_NUM, /*容量*/
		ROUND(T1.CUSTOMER_NUM/T2.MAX_NUM,2) AS APPLY_RATE/*报名率（报名人数/容量）*/
	FROM(
	SELECT
		COUNT(DISTINCT a.ACTIVITY_ID) AS ACTIVITY_NUM,/*活动总数*/
		SUM(CASE WHEN C.ACTIVITY_ID IS NOT NULL THEN 1 ELSE 0 END) AS CUSTOMER_NUM,/*活动报名人次*/
		SUM(CASE WHEN C.IS_CHECK_IN='1' THEN 1 ELSE 0 END) AS CHECK_NUM,/*签到人次*/
		ROUND(SUM(CASE WHEN C.IS_CHECK_IN='1' THEN 1 ELSE 0 END)/SUM(CASE WHEN C.ACTIVITY_ID IS NOT NULL THEN 1 ELSE 0 END),2) AS CHECK_RATE,/*签到率(签到人数/报名人数)*/
		ROUND(SUM(CASE WHEN C.IS_CHECK_IN='1' THEN 1 ELSE 0 END)/COUNT(DISTINCT a.ACTIVITY_ID),2) AS AVG_CUST_NUM/*场均人数(签到人数/活动总数)*/
	FROM t_acc_bu_activity a
		LEFT JOIN t_acc_bu_activity_customer c ON c.ACTIVITY_ID=a.ACTIVITY_ID AND c.IS_ENABLE='1'
		WHERE 1=1
		AND a.RELEASE_STATUS_CODE='1' /*已发布*/
		AND a.CREATE_TYPE_CODE!='DEVELOP' /* 非本地开发类活动*/
		<if test="param.actTypeCodeList != null and param.actTypeCodeList != ''">
			AND a.ACTIVITY_TYPE_CODE IN
			<foreach collection="param.actTypeCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
			AND DATE_FORMAT(a.BEGIN_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ >= ]]> #{param.actBeginTimeStart}
		</if>
		<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
			AND DATE_FORMAT(a.BEGIN_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator=" OR " close=")">
				find_in_set(#{item}, a.DLR_CODE) > 0
			</foreach>
		</if>
	) T1
	LEFT JOIN(
		SELECT
			SUM(IFNULL(A.COLUMN6,0)+IFNULL(A.COLUMN7,0)) AS MAX_NUM /*容量*/
		FROM t_acc_bu_activity a
			WHERE 1=1
			AND a.RELEASE_STATUS_CODE='1' /*已发布*/
			AND a.CREATE_TYPE_CODE != 'DEVELOP' /*非本地开发类活动 */
			<if test="param.actTypeCodeList != null and param.actTypeCodeList != ''">
				AND a.ACTIVITY_TYPE_CODE IN
				<foreach collection="param.actTypeCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
				AND DATE_FORMAT(a.BEGIN_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ >= ]]> #{param.actBeginTimeStart}
			</if>
			<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
				AND DATE_FORMAT(a.BEGIN_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
			</if>
			<if test="param.dlrCode != null and param.dlrCode != ''">
				AND
				<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator=" OR " close=")">
					find_in_set(#{item}, a.DLR_CODE) > 0
				</foreach>
			</if>
	) T2 ON 1=1
	</select>

	<!-- 报表展板-线索和订单数量 -->
	<select id="queryReportBoardClueAndOrder" parameterType="map" resultType="map">
	SELECT * FROM
		(
		with clue as (
			SELECT
			row_number ( ) over ( PARTITION BY CUST_ID ORDER BY c.CREATED_DATE DESC ) n,
			c.CUST_ID
			FROM
			t_sac_clue_info_dlr c
			WHERE 1=1
			<if test="param.dlrCode != null and param.dlrCode != ''">
				AND c.DLR_CODE IN
				<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.clueCreatedDateStart != null and param.clueCreatedDateStart != ''">
				AND c.CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreatedDateStart}
			</if>
			<if test="param.clueCreatedDateEnd != null and param.clueCreatedDateEnd != ''">
				AND c.CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreatedDateEnd}
			</if>
		)
		SELECT
			/*当前订单类型 用户选择大定或者小订 1. 小订 2. 大定*/
			ifnull(sum(CASE WHEN o.CURRENT_ORDER_TYPE = '1' and o.SALE_ORDER_STATE not in ('1','15') and o.SAMLL_ORDER_PAY_DATE is not null THEN 1 ELSE 0 END), 0) AS SMALL_ORDER_NUM, /*小订数*/
			/* 大定数 220=已下定（大定已付款）、230=已确认（大定48小时犹豫期后的订单确认状态）*/
			ifnull(sum(CASE WHEN o.DEPOSIT_PAY_DATE is not null and o.SALE_ORDER_STATE not in ('1','221') THEN 1 ELSE 0 END ), 0) AS BIG_ORDER_NUM,
			ifnull(sum(CASE WHEN o.SALE_ORDER_STATE IN ('7') THEN 1 ELSE 0 END), 0) AS USER_NUM /*用户数*/
		FROM t_orc_ve_bu_sale_order_to_c o
		INNER JOIN clue c on o.BUY_CUST_ID = c.CUST_ID and c.n =1
			WHERE 1 = 1
			<if test="param.orderCreatedDateStart != null and param.orderCreatedDateStart != ''">
				AND o.CREATED_DATE <![CDATA[ >= ]]> #{param.orderCreatedDateStart}
			</if>
			<if test="param.orderCreatedDateEnd != null and param.orderCreatedDateEnd != ''">
				AND o.CREATED_DATE <![CDATA[ <= ]]> #{param.orderCreatedDateEnd}
			</if>
		) A
		LEFT JOIN (
			SELECT count(1) AS TOTAL_CLUE_NUM /*总线索数*/
			FROM t_sac_clue_info_dlr T
			WHERE 1 = 1 and T.STATUS_CODE not in ('10')
			<if test="param.dlrCode != null and param.dlrCode != ''">
				AND T.DLR_CODE IN
				<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.clueCreatedDateStart != null and param.clueCreatedDateStart != ''">
				AND T.CREATED_DATE <![CDATA[ >= ]]> #{param.clueCreatedDateStart}
			</if>
			<if test="param.clueCreatedDateEnd != null and param.clueCreatedDateEnd != ''">
				AND T.CREATED_DATE <![CDATA[ <= ]]> #{param.clueCreatedDateEnd}
			</if>
		) E ON 1 = 1
	</select>

	<!-- 报表展板门店数量 -->
	<select id="queryReportBoardDlr" parameterType="map" resultType="map">
	SELECT
		sum(CASE WHEN T.DLR_TYPE = 'BC' THEN 1 ELSE 0 END) AS DLR_BC_NUM,
		sum(CASE WHEN T.DLR_TYPE = 'POS' THEN 1 ELSE 0 END) AS DLR_POS_NUM,
		sum(CASE WHEN T.DLR_TYPE = 'DC' THEN 1 ELSE 0 END) AS DLR_DC_NUM,
		sum(CASE WHEN T.DLR_TYPE = 'POL' THEN 1 ELSE 0 END) AS DLR_POL_NUM
	FROM t_usc_mdm_org_dlr T
	WHERE T.ONLINE_FLAG = '1' /*已上线*/
	<if test="param.dlrCode != null and param.dlrCode != ''">
		AND T.DLR_CODE IN
		<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	</select>

	<!-- 报表展板当日活动，外呼，当前投诉数量 -->
	<select id="queryReportBoardAct" parameterType="map" resultType="map">
	SELECT * FROM
	(
		SELECT
			COUNT(DISTINCT a.ACTIVITY_ID) AS ACTIVITY_NUM /*活动总数*/
		FROM
			t_acc_bu_activity a
			LEFT JOIN t_acc_bu_activity_customer c ON c.ACTIVITY_ID = a.ACTIVITY_ID AND c.IS_ENABLE = '1'
				WHERE a.CREATE_TYPE_CODE != 'DEVELOP' /*非本地开发类活动*/
				AND a.RELEASE_STATUS_CODE = '1' /*已发布*/
				AND to_days(a.BEGIN_TIME) = to_days(now())
				<if test="param.dlrCode != null and param.dlrCode != ''">
					AND a.DLR_CODE IN
					<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.actCreatedDateStart != null and param.actCreatedDateStart != ''">
					AND a.CREATED_DATE <![CDATA[ >= ]]> #{param.actCreatedDateStart}
				</if>
				<if test="param.actCreatedDateEnd != null and param.actCreatedDateEnd != ''">
					AND a.CREATED_DATE <![CDATA[ <= ]]> #{param.actCreatedDateEnd}
				</if>
	) A
	LEFT JOIN (
		SELECT count(1) AS COMPLAINT_NUM /*当日投诉数*/
			FROM t_sac_complaints_info T
			WHERE to_days(T.CREATED_DATE) = to_days(now())
			<if test="param.dlrCode != null and param.dlrCode != ''">
				AND T.DLR_CODE IN
				<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.complaintCreatedDateStart != null and param.complaintCreatedDateStart != ''">
				AND T.CREATED_DATE <![CDATA[ >= ]]> #{param.complaintCreatedDateStart}
			</if>
			<if test="param.complaintCreatedDateEnd != null and param.complaintCreatedDateEnd != ''">
				AND T.CREATED_DATE <![CDATA[ <= ]]> #{param.complaintCreatedDateEnd}
			</if>
	) B ON 1 = 1
	LEFT JOIN (
		SELECT count(1) AS OUT_CALL_NUM /*外呼数，客户履历-去电 值列表ADP_CLUE_001*/
			FROM t_sac_onecust_resume
		WHERE SENCE_CODE IN ('1', '14', '17')
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND DLR_CODE_OWNER IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.resumeCreatedDateStart != null and param.resumeCreatedDateStart != ''">
			AND CREATED_DATE <![CDATA[ >= ]]> #{param.resumeCreatedDateStart}
		</if>
		<if test="param.resumeCreatedDateEnd != null and param.resumeCreatedDateEnd != ''">
			AND CREATED_DATE <![CDATA[ <= ]]> #{param.resumeCreatedDateEnd}
		</if>
	) C ON 1 = 1
	</select>

	<!-- 报表展板-展车试驾车数量 -->
	<select id="queryReportBoardCar" parameterType="map" resultType="map">
	SELECT * FROM
	(
		SELECT count(1) AS TEST_CAR_NUM  /*试驾车数量*/
			FROM t_usc_bu_testCar_prepare p
		WHERE p.RESPONSE_ORDER_STATUS IN ('1')  <!-- 服役中 -->
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND p.APPLY_DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<!-- 上架日期 -->
		<if test="param.testCarOnlineStart != null and param.testCarOnlineStart != ''">
			AND p.TEST_CAR_ONLINE <![CDATA[ >= ]]> #{param.testCarOnlineStart}
		</if>
		<if test="param.testCarOnlineEnd != null and param.testCarOnlineEnd != ''">
			AND p.TEST_CAR_ONLINE <![CDATA[ <= ]]> #{param.testCarOnlineEnd}
		</if>
	) A
	LEFT JOIN (
		SELECT
			sum(SHOW_CAR_NUM) AS SHOW_CAR_NUM /*总展车数量*/
		FROM
		(
			SELECT count(1) AS SHOW_CAR_NUM
			FROM t_usc_bu_showcar_manage m
				LEFT JOIN t_usc_bu_showcar_apply a ON m.SHOW_CARAPPLY_ID = a.APPLY_ID
				LEFT JOIN t_usc_bu_showcar_prepare ap ON m.SHOW_CARAPPLY_ID = ap.SHOW_CARAPPLY_ID
				LEFT JOIN t_usc_bu_showcar_retire re ON m.SHOW_CAR_ID = re.SHOW_CAR_ID
				LEFT JOIN t_prc_mds_lookup_value v6 ON v6.lookup_type_code = 'DB1038' AND v6.lookup_value_code = CONCAT(m.SHOW_CAR_STATUS, a.APPLY_STATU, ifnull(AP.PREPARE_STATUS, ''), ifnull(RE.RETIRE_CAR_STATUS, ''))
			WHERE m.IS_ENABLE = '1' AND (a.APPLY_TYPE != '2' OR (a.APPLY_TYPE = '2' AND a.APPLY_STATU = '4'))
				AND FIND_IN_SET( v6.lookup_value_code, '244,2440,3441,3442,3443' /*服役中*/)
			  and a.IS_ENABLE = '1'
			<if test="param.dlrCode != null and param.dlrCode != ''">
				AND m.APPLY_DLR_CODE IN
				<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.orgId != null and param.orgId != ''"> <!-- 总部 -->
				AND m.APPLY_DLR_CODE IN (
				SELECT d.DLR_CODE
				FROM
				t_usc_mdm_org_relation p
				inner join t_usc_mdm_org_relation_real r on p.ORG_TREE_ID = r.ORG_TREE_ID
				inner join t_usc_mdm_org_dlr d on r.LINK_ID = d.DLR_ID
				where
				p.PARENT_ORG_TREE_ID = #{param.orgId} or p.ORG_TREE_ID = #{param.orgId}
				)
			</if>
			<if test="param.testCarOnlineStart != null and param.testCarOnlineStart != ''">
				AND m.TEST_CAR_ONLINE <![CDATA[ >= ]]> #{param.testCarOnlineStart}
			</if>
			<if test="param.testCarOnlineEnd != null and param.testCarOnlineEnd != ''">
				AND m.TEST_CAR_ONLINE <![CDATA[ <= ]]> #{param.testCarOnlineEnd}
			</if>
		) t1
	) B ON 1 = 1
	</select>

	<select id="querytestCarReport" resultType="java.util.Map" parameterType="java.util.Map">
SELECT
	T.AREA_NAME,
	T.DLR_CODE,
	T.DLR_SHORT_NAME,
	T.SMALL_CAR_TYPE_CN,
	T.SMALL_CAR_TYPE_CODE,
	DRI.TEST_NUM,
	DRI.TEST_ROAD_HAUL,
	TSK.APO_NUM,
	DRI.DRIVE_HOUR,
	IF(DRI.TEST_NUM = 0, 0, DRI.DRIVE_NUM / DRI.TEST_NUM) AS DIRVE_RATE,
	IF(DRI.TEST_NUM = 0, 0, DRI.RIDE_NUM / DRI.TEST_NUM) AS RIDE_RATE,
	IF(DRI.TEST_NUM = 0, 0, DRI.DEEP_NUM / DRI.TEST_NUM) AS DEEP_RATE,
	IF(LDRI.LONG_DRIVE_HOUR = 0, 0, LDRI.LONG_DRIVE_NUM / LDRI.LONG_DRIVE_HOUR) AS LONG_DRIVE_RATE

FROM
	(
SELECT DISTINCT
	AF.AREA_NAME,
	D.DLR_CODE,
	D.DLR_SHORT_NAME,
	SC.SMALL_CAR_TYPE_CN,
	SC.SMALL_CAR_TYPE_CODE
FROM
	T_USC_BU_TESTCAR_PREPARE C
	INNER JOIN T_USC_MDM_ORG_DLR D ON C.APPLY_DLR_CODE = D.DLR_CODE
	INNER JOIN T_PRC_MDM_SMALL_CAR_TYPE SC ON C.APPLY_CAR_TYPE_CODE = SC.SMALL_CAR_TYPE_CODE
	LEFT JOIN T_USC_AREA_RELATION RE ON D.PROVINCE_ID = RE.REL_OBJ_ID
	LEFT JOIN T_USC_AREA_INFO AF ON RE.AREA_ID = AF.AREA_ID
WHERE
	C.RESPONSE_ORDER_STATUS NOT IN ( '0', '2' )
	<if test="param.dlrCode !=null and param.dlrCode !=''">
    and C.APPLY_DLR_CODE=#{param.dlrCode}
	</if>

		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and C.APPLY_DLR_CODE in
			<foreach collection="param.dlrCodeList.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>

		</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">
			and SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}
		</if>
		<if test="param.areaId !=null and param.areaId !=''">
			and AF.AREA_ID=#{param.areaId}
		</if>
	) T
	LEFT JOIN (
SELECT
	DLR_CODE,
	SMALL_CAR_TYPE_NAME,
	COUNT( 1 ) TEST_NUM,
	SUM( IF ( TEST_TYPE = '0', 1, 0 ) ) RIDE_NUM,
	SUM( IF ( TEST_TYPE = '1', 1, 0 ) ) DRIVE_NUM,
	SUM( IF ( TEST_TYPE = '2', 1, 0 ) ) DEEP_NUM,
	SUM( TEST_ROAD_HAUL ) TEST_ROAD_HAUL,
	SUM( TIMESTAMPDIFF( MINUTE, START_TIME, END_TIME ) )/60 DRIVE_HOUR
FROM
	T_SAC_TEST_DRIVE_SHEET
		WHERE 1=1
		<if test="param.staTime !=null and param.staTime !=''">
			and DATE_FORMAT(END_TIME,'%Y-%m-%d') <![CDATA[ >= ]]> #{param.staTime}
			and DATE_FORMAT(END_TIME,'%Y-%m-%d') <![CDATA[ <= ]]> #{param.endTime}
		</if>
GROUP BY
	DLR_CODE,
	SMALL_CAR_TYPE_NAME
	) DRI ON T.DLR_CODE = DRI.DLR_CODE
	AND T.SMALL_CAR_TYPE_CN = DRI.SMALL_CAR_TYPE_NAME
	LEFT JOIN ( SELECT DLR_CODE, SMALL_CAR_TYPE_CODE, COUNT( 1 ) APO_NUM FROM T_SAC_APPOINTMENT_SHEET
		WHERE 1=1
		<if test="param.staTime !=null and param.staTime !=''">
			and DATE_FORMAT(APPOINTMENT_END_TIME,'%Y-%m-%d') <![CDATA[ >= ]]> #{param.staTime}
			and DATE_FORMAT(APPOINTMENT_END_TIME,'%Y-%m-%d') <![CDATA[ <= ]]> #{param.endTime}
		</if>
	GROUP BY DLR_CODE, SMALL_CAR_TYPE_CODE ) TSK ON T.DLR_CODE = TSK.DLR_CODE
	AND T.SMALL_CAR_TYPE_CODE = TSK.SMALL_CAR_TYPE_CODE
	LEFT JOIN (
SELECT
	APPLY_DLR_CODE,
	CAR_TYPE_CODE,
	COUNT( 1 ) LONG_DRIVE_NUM,
	SUM( TIMESTAMPDIFF( HOUR, APPLY_TIME_BEGIN, APPLY_TIME_END ) ) LONG_DRIVE_HOUR
FROM
	T_SAC_TEST_DRIVE_LONG_APPLY
WHERE
	AUDIT_STATUS = '2'
		<if test="param.staTime !=null and param.staTime !=''">
			and DATE_FORMAT(APPLY_TIME_END,'%Y-%m-%d') <![CDATA[ >= ]]> #{param.staTime}
			and DATE_FORMAT(APPLY_TIME_END,'%Y-%m-%d') <![CDATA[ <= ]]> #{param.endTime}
		</if>
GROUP BY
	APPLY_DLR_CODE,
	CAR_TYPE_CODE
	) LDRI ON T.DLR_CODE = LDRI.APPLY_DLR_CODE
	AND T.SMALL_CAR_TYPE_CN = LDRI.CAR_TYPE_CODE
	</select>

	<select id="queryUser" parameterType="java.util.Map" resultType="java.util.Map">
		with drive_sheet as (
		select  count(1) as testCar,sum(case when T.COLUMN2 is null then 0 else 1 end) as invite
		  FROM t_sac_test_drive_sheet T
		  where 1=1
	 	  and T.TEST_STATUS='2'
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND DATE_FORMAT(T.END_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
		</if>
		<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
			AND DATE_FORMAT(T.END_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		),cust_num as(
		select  count(1) as custNum from (
		select re.CUST_ID as CUST_ID
		FROM t_sac_onecust_resume re
		where
		re.SENCE_CODE in (16,2,19,3,4)
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND re.DLR_CODE_OWNER IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND quarter(re.CREATED_DATE)= quarter(#{param.createdTimeStart}) and  DATE_FORMAT(re.CREATED_DATE, '%Y')=SUBSTR(#{param.createdTimeStart},1,4)
		</if>group by re.CUST_ID
		) t
		), onecust_resume as (
		select re.CUST_ID
		FROM t_sac_onecust_resume re
		where
		re.SENCE_CODE=3
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND re.DLR_CODE_OWNER IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND DATE_FORMAT(re.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
		</if>
		<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
			AND DATE_FORMAT(re.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
		</if>
		group by re.CUST_ID
		),to_c as (
		select t.BUY_CUST_ID  from t_orc_ve_bu_sale_order_to_c t
		left join t_usc_mdm_org_dlr dlr on T.SALE_DLR_ID and dlr.DLR_ID
		where 1=1
		<if test="param.createdTimeStart != null and param.createdTimeStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.createdTimeStart}
		</if>
		<if test="param.createdTimeEnd != null and param.createdTimeEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.createdTimeEnd}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND dlr.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		group by t.BUY_CUST_ID
		)

	select '-' as  num1,'-' as num2,cn.custNum,round(st.invite/st.testCar*100,2) as testCar,round(rs.activityNum/c.orderNum*100,2) as activity  from (select count(1) as orderNum  from to_c) c
		left join cust_num cn on 1=1
		left join (select count(1) as activityNum  from onecust_resume) rs on 1=1
		left join drive_sheet st on 1=1
	</select>


	<select id="querySaleConstant" parameterType="map" resultType="map">
		select
			u1.EMP_NAME,
			t2.DLR_CODE dlrCode,
			t2.DLR_SHORT_NAME dlrShortName,
			t10.AGENT_COMPANY_NAME agentCompanyName,
			t11.AGENT_NAME agentName,
			(case when n2.num1 is null then 0 else n2.num1 end) num1, <!-- （期间新增线索）战败量 -->
			(case when n2.num2 is null then 0 else n2.num2 end) num2, <!-- （期间新增线索）有效剩余量 -->
			CONCAT(ROUND((case when n2.num3 is null then 0 else n2.num3 end)*100,2),'%')  num3, <!-- （期间新增线索）邀约到店率 -->
			concat(ROUND((case when n2.num4 is null then 0 else n2.num4 end)*100,2),'%') num4, <!-- （期间新增线索）试驾率-->
			concat(ROUND((case when n2.num5 is null then 0 else n2.num5 end)*100,2),'%') num5, <!-- （期间新增线索）下定率-->
			concat(ROUND((case when n2.num6 is null then 0 else n2.num6 end)*100,2),'%') num6, <!-- （期间新增线索）退定率-->
			(case when n1.num7 is null then 0 else n1.num7 end) num7, <!-- （期间累计产生）跟进数-->
			(case when n3.num8 is null then 0 else n3.num8 end) num8, <!-- （期间累计产生）试乘试驾完成数-->
			(case when n4.num9 is null then 0 else n4.num9 end) num9, <!-- （期间累计产生）试乘试驾任务数-->
			(case when n4.num10 is null then 0 else n4.num10 end) num10, <!-- （期间累计产生）试驾任务完成数-->
			(case when n5.num11 is null then 0 else n5.num11 end) num11, <!-- （期间累计产生）任务数-->
			(case when n6.num12 is null then 0 else n6.num12 end) num12,  <!-- （期间累计产生）任务完成数-->
			(case when n11.num13 is null then 0 else n11.num13 end) num13, -- 新增试乘试驾排程数
			(case when n11.num14 is null then 0 else n11.num14 end) num14, -- 本日试乘试驾排程取消数
			(case when n10.SELF_CREATE_CLUE_NUM is null then 0 else n10.SELF_CREATE_CLUE_NUM end) SELF_CREATE_CLUE_NUM,
			(case when n10.ASSIGN_CLUE_NUM is null then 0 else n10.ASSIGN_CLUE_NUM end) ASSIGN_CLUE_NUM,
			(case when n10.SELF_CREATE_CLUE_OTHER_NUM is null then 0 else n10.SELF_CREATE_CLUE_OTHER_NUM end) SELF_CREATE_CLUE_OTHER_NUM

		from
			(select USER_ID userId,EMP_NAME,DLR_CODE,USER_STATUS from t_usc_mdm_org_employee where (station_id in ('smart_bm_0018','smart_bm_0007','smart_bm_0061','smart_bm_0064')
			                                                                                            or COLUMN2 like '%smart_bm_0018%' or COLUMN2 like '%smart_bm_0007%' or COLUMN2 like '%smart_bm_0061%' or COLUMN2 like '%smart_bm_0064%')
				and USER_STATUS='1'
			<if test="param.dlrCode !=null and param.dlrCode !=''">
				and DLR_CODE=#{param.dlrCode}
			</if>
			<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
				AND DLR_CODE IN
				<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.userId !=null and param.userId!=''">
				and USER_ID=#{param.userId}
			</if>
			) u1
				left join
			(
				select
					d.REVIEW_PERSON_ID userId,
					count(DISTINCT(case when d.STATUS_CODE in ('10') then d.SERVER_ORDER ELSE null END)) num1, <!-- （期间新增线索）战败量 -->
					count(DISTINCT(case when d.STATUS_CODE in ('1','2','14') then d.SERVER_ORDER ELSE null END)) num2, <!-- （期间新增线索）有效剩余量-->
					count(DISTINCT(case when r.custId is not null then d.SERVER_ORDER ELSE null END))/count(DISTINCT(d.SERVER_ORDER)) num3, <!-- （期间新增线索）邀约到店率-->
					count(DISTINCT(case when t.end_time is not null then d.SERVER_ORDER ELSE null END))/count(DISTINCT(d.SERVER_ORDER)) num4, <!-- （期间新增线索）试驾率-->
					count(DISTINCT(case when tc.sale_order_state in ('2','220','230','300','310','320','500','600','630','640','650','651','660',
																	  '661','664','665','675','680','690','700','710') then d.SERVER_ORDER ELSE null END))/count(DISTINCT(d.SERVER_ORDER)) num5, <!-- （期间新增线索）下定率-->
					count(DISTINCT(case when tc.sale_order_state in ('8','15','221') then d.SERVER_ORDER ELSE null END))/count(DISTINCT(d.SERVER_ORDER)) num6 <!-- （期间新增线索）退定率-->
				from
					t_sac_clue_info_dlr d
						left join (select DISTINCT(CUST_ID) custId
								   from t_sac_onecust_resume
								   where SENCE_CODE in ('2','16','19')
						<if test="param.dlrCode !=null and param.dlrCode !=''">
							and DLR_CODE_OWNER=#{param.dlrCode}
						</if>
						<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
							AND DLR_CODE_OWNER IN
							<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
								#{item}
							</foreach>
						</if>
						<if test="param.userId !=null and param.userId!=''">
							and RESUME_PERSON_CODE=#{param.userId}
						</if>
					)r on d.CUST_ID=r.custId
						left join
					(select
						 max(end_time) end_time,t.CUSTOMER_ID
					 from
						 t_sac_test_drive_sheet t
							 left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
					 where 1=1

					<if test="param.dlrCode !=null and param.dlrCode !=''">
						and t.DLR_CODE=#{param.dlrCode}
					</if>
					<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
						AND t.DLR_CODE IN
						<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
					<if test="param.userId !=null and param.userId!=''">
						and t.SALES_CONSULTANT_ID=#{param.userId}
					</if>
					 group by t.CUSTOMER_ID
					) t on t.CUSTOMER_ID=d.CUST_ID
						left join orc.t_orc_ve_bu_sale_order_to_c tc on tc.BUY_CUST_ID=d.CUST_ID
				where
					1=1
					<if test="param.dlrCode !=null and param.dlrCode !=''">
						and d.DLR_CODE=#{param.dlrCode}
					</if>
					<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
						AND d.DLR_CODE IN
						<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
					<if test="param.userId !=null and param.userId!=''">
						and d.REVIEW_PERSON_ID=#{param.userId}
					</if>
					<if test="param.createdDateStart != null and param.createdDateStart != ''">
						AND date (d.CREATED_DATE) &gt;= date (#{param.createdDateStart})
					</if>
					<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
						AND date(d.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
					</if>
				group by d.REVIEW_PERSON_ID) n2 on n2.userId = u1.userId


				left join
			( select count(1) num7,
					 RESUME_PERSON_CODE userId
			  from t_sac_onecust_resume
			  where
				 1=1
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and DLR_CODE_OWNER=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND DLR_CODE_OWNER IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and RESUME_PERSON_CODE=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (CREATED_DATE) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(CREATED_DATE) &lt;= date(#{param.createdDateEnd})
				</if>
			  group by RESUME_PERSON_CODE) n1 on u1.userId=n1.userId

				left join
			(
				select
					count(1) num8,
					SALES_CONSULTANT_ID userId
				from
					t_sac_test_drive_sheet t
						left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
				where end_time is not null

				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and t.DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND t.DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and t.SALES_CONSULTANT_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (END_TIME) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(END_TIME) &lt;= date(#{param.createdDateEnd})
				</if>
				group by SALES_CONSULTANT_ID
			) n3 on u1.userId=n3.userId
				left join
			(select count(1) num9,
					sum(case when TASK_STATE_CODE='1' then 1 else 0 end ) num10,
					TASK_PERSON_ID userId
			 from t_sac_test_drive_task
			 where
				 1=1
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and TASK_PERSON_DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND TASK_PERSON_DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and TASK_PERSON_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (CREATED_DATE) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(CREATED_DATE) &lt;= date(#{param.createdDateEnd})
				</if>
			 group by TASK_PERSON_ID
			) n4 on u1.userId=n4.userId

				left join
			(select count(1) num11,
				d.TASK_PERSON_ID userId
			 from t_sac_onetask_detail d
				join t_sac_onetask_info i on i.TASK_ID=d.TASK_ID
			 where
				 1=1
				and i.TASK_STATE_CODE <![CDATA[ <> ]]>   '4'
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and d.TASK_PERSON_DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND d.TASK_PERSON_DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and d.TASK_PERSON_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (d.BUSS_START_TIME) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(d.BUSS_START_TIME) &lt;= date(#{param.createdDateEnd})
				</if>
			 group by d.TASK_PERSON_ID
			) n5 on u1.userId=n5.userId

				left join
			(select count(1) num12,
				d.TASK_PERSON_ID userId
			 from t_sac_onetask_detail d
				join t_sac_onetask_info i on i.TASK_ID=d.TASK_ID
			 where
				 1=1
				and i.TASK_STATE_CODE <![CDATA[ <> ]]>   '4'
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and d.TASK_PERSON_DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND d.TASK_PERSON_DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and d.TASK_PERSON_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (d.BUSS_TIME) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(d.BUSS_TIME) &lt;= date(#{param.createdDateEnd})
				</if>
			 group by d.TASK_PERSON_ID
			) n6 on u1.userId=n6.userId

				left join
			(select
				 REVIEW_PERSON_ID userId,
				 sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM,
				 sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM ,
		count(1)-sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END)-
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END)
		AS SELF_CREATE_CLUE_OTHER_NUM
			 FROM t_sac_clue_info_dlr T
					  LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049' and L.ATTRIBUTE1='agent'
			 WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')


				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and T.DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND T.DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and T.REVIEW_PERSON_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (T.CREATED_DATE) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(T.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
				</if>
			 	group by REVIEW_PERSON_ID) n10 on u1.userId=n10.userId

				left join
				(select
				sum(case when t1.IS_ENABLE='1' then 1 else 0 end) num13, -- 当天创建试驾预约数（排程）
				sum(case when t1.IS_ENABLE='0' then 1 else 0 end) num14, -- 当天创建试驾取消数（排程）
				SALES_CONSULTANT_ID userId
				from
				t_sac_test_drive_sheet t
				left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO  )
				where
				1=1
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and t.DLR_CODE=#{param.dlrCode}
				</if>
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND t.DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.userId !=null and param.userId!=''">
					and t.SALES_CONSULTANT_ID=#{param.userId}
				</if>
				<if test="param.createdDateStart != null and param.createdDateStart != ''">
					AND date (t1.CREATED_DATE) &gt;= date (#{param.createdDateStart})
				</if>
				<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
					AND date(t1.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
				</if>
				group by SALES_CONSULTANT_ID
				) n11 on  u1.userId=n11.userId

				LEFT JOIN mp.t_usc_mdm_org_dlr t2 ON u1.DLR_CODE = t2.DLR_CODE
				LEFT JOIN mp.t_usc_mdm_agent_company t10 ON t2.COMPANY_ID =t10.AGENT_COMPANY_ID
				LEFT JOIN mp.t_usc_mdm_agent_info t11 ON t10.AGENT_ID = t11.AGENT_ID

				where u1.USER_STATUS='1'


	</select>

	<select id="querySaleConstantOld" parameterType="map" resultType="map">
		with onecust_resume as (
		select   T.RESUME_PERSON_CODE as CREATED_DATE ,'0' as SELF_CREATE_CLUE_NUM,'0' as ASSIGN_CLUE_NUM,
		'0' as SELF_CREATE_CLUE_OTHER_NUM,
		count(T.DLR_CODE_OWNER) as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM,
		'0' AS dlr_owner_num
		FROM t_sac_onecust_resume T
		where
		T.SENCE_CODE in (16,2,19)
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE_OWNER,#{param.dlrCodeList})
		</if>

		<if test="param.userId !=null and param.userId!=''">
			and T.RESUME_PERSON_CODE=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND date (T.CREATED_DATE) &gt;= date (#{param.createdDateStart})
		</if>
		<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND date(T.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
		</if>
		group by T.RESUME_PERSON_CODE
		),order_to_c as  (
		select e.USER_ID as CREATED_DATE,'0' as SELF_CREATE_CLUE_NUM,'0' as ASSIGN_CLUE_NUM,
		'0' as SELF_CREATE_CLUE_OTHER_NUM,
		'0' as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		count(1) as ORDER_NUM ,
		sum(case when o.SALE_ORDER_STATE in (8,15,221) then 1 else 0 end) as orderCount,
		sum(case when o.SALE_ORDER_STATE in (select LOOKUP_VALUE_CODE from t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='VE8090') then 1 else 0 end) as DEPOSIT_NUM,   /*交车数，当前只到大定*/
		'0' AS dlr_owner_num
		FROM t_orc_ve_bu_sale_order_to_c O
		left join t_usc_mdm_org_dlr dlr on dlr.DLR_ID=o.SALE_DLR_ID
		left join t_usc_mdm_org_employee e on O.DLR_EMP_ID=e.EMP_ID
		WHERE 1 = 1
		<if test="param.userId !=null and param.userId!=''">
			AND e.USER_ID=#{param.userId}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(dlr.DLR_CODE,#{param.dlrCodeList})
		</if>
		<if test="param !=null and param.dlrCode!=null and param.dlrCode !=''">
			AND  dlr.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND date (O.CREATED_DATE) &gt;= date (#{param.createdDateStart})
		</if>
		<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND date(O.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
		</if>
		group by O.DLR_EMP_ID
		),drive_sheet as (
		SELECT T.SALES_CONSULTANT_ID as  CREATED_DATE/*试驾数*/,
		'0' as SELF_CREATE_CLUE_NUM,
		'0' as ASSIGN_CLUE_NUM,
		'0' as SELF_CREATE_CLUE_OTHER_NUM,
		'0' as TO_DLR_NUM,
		count(1) AS TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM, /*交车数，当前只到大定*/
		'0' AS dlr_owner_num
		FROM t_sac_test_drive_sheet T
		WHERE 1=1
		and T.TEST_STATUS='2'
		<if test="param.userId !=null and param.userId!=''">
			and 	T.SALES_CONSULTANT_ID = #{param.userId}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE,#{param.dlrCodeList})
		</if>
		<if test="param !=null and param.dlrCode!=null and param.dlrCode !=''">
			AND  T.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND date (T.END_TIME) &gt;= date (#{param.createdDateStart})
		</if>
		<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND date(T.END_TIME) &lt;= date(#{param.createdDateEnd})
		</if>
		group by T.SALES_CONSULTANT_ID
		),
		dlr_owner_count AS (
		SELECT
		t.RESUME_PERSON_CODE as  CREATED_DATE,
		'0' as SELF_CREATE_CLUE_NUM,
		'0' as ASSIGN_CLUE_NUM,
		'0' as SELF_CREATE_CLUE_OTHER_NUM,
		'0' as TO_DLR_NUM,
		'0' AS TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM,
		COUNT( * ) AS dlr_owner_num
		FROM
		t_sac_onecust_resume t
		WHERE
		t.SENCE_NAME IN ( '订单跟进-去电', '去电' )
		<if test="param.userId !=null and param.userId!=''">
			and 	T.RESUME_PERSON_CODE = #{param.userId}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE_OWNER,#{param.dlrCodeList})
		</if>
		<if test="param !=null and param.dlrCode!=null and param.dlrCode !=''">
			AND  T.DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND date (T.CREATED_DATE) &gt;= date (#{param.createdDateStart})
		</if>
		<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND date(T.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
		</if>
		GROUP BY T.RESUME_PERSON_CODE
		)
		,clue_info_dlr as (
		select
		T.REVIEW_PERSON_ID as CREATED_DATE,
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL OR T.INFO_CHAN_M_CODE = 'dlr.build' THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL OR T.INFO_CHAN_M_CODE = 'dlr.build' THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM ,/*新增线索数-下发*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL AND  T.INFO_CHAN_M_CODE in('2','3','4','5') THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_OTHER_NUM, /*新增线索数-其他*/

		'0' as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM,
		'0' AS dlr_owner_num
		FROM t_sac_clue_info_dlr T
		LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049'
		WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE,#{param.dlrCodeList})
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and T.REVIEW_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND date (T.CREATED_DATE) &gt;= date (#{param.createdDateStart})
		</if>
		<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND date(T.CREATED_DATE) &lt;= date(#{param.createdDateEnd})
		</if>
		group by T.REVIEW_PERSON_ID
		)
		SELECT
		sum(t.SELF_CREATE_CLUE_NUM) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(t.ASSIGN_CLUE_NUM) AS ASSIGN_CLUE_NUM /*新增线索数-下发*/
		,t.CREATED_DATE
		,ur.EMP_NAME,
		sum(t.DEPOSIT_NUM) as DEPOSIT_NUM, /*交车数，当前只到大定*/
		sum(t.ORDER_NUM) as ORDER_NUM,
		sum(t.orderCount) as orderCount,
		sum(t.TEST_DRIVE_NUM) as TEST_DRIVE_NUM,
		sum(t.TO_DLR_NUM) as TO_DLR_NUM,
		sum(t.dlr_owner_num )as dlr_owner_num,
		t2.DLR_CODE dlrCode,
		t2.DLR_SHORT_NAME dlrShortName,
		t10.AGENT_COMPANY_NAME agentCompanyName,
		t11.AGENT_NAME agentName
		FROM
		(select * from clue_info_dlr
		union all
		select * from  onecust_resume
		union all select * from  order_to_c
		union all  select * from  drive_sheet
		union all select * FROM dlr_owner_count
		) t
		inner join t_usc_mdm_org_employee ur on t.CREATED_DATE=ur.USER_ID
		and (ur.COLUMN2 in (select LOOKUP_VALUE_NAME from t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='VE1111')
		or ur.STATION_ID in (select LOOKUP_VALUE_NAME from t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='VE1111'))
	    and ur.IS_ENABLE='1'
	   	LEFT JOIN mp.t_usc_mdm_org_dlr t2 ON ur.DLR_CODE = t2.DLR_CODE
		LEFT JOIN mp.t_usc_mdm_agent_company t10 ON t2.COMPANY_ID =t10.AGENT_COMPANY_ID
		LEFT JOIN mp.t_usc_mdm_agent_info t11 ON t10.AGENT_ID = t11.AGENT_ID
		where T.CREATED_DATE is not null
		group by T.CREATED_DATE
		order by ur.EMP_NAME desc

	</select>


	<!-- 销售顾问日报明细 -->
	<select id="querySaleConstantReport" parameterType="map" resultType="map">
		select
		icd.EMP_NAME,icd.AGENT_NAME,icd.DLR_SHORT_NAME,icd.AGENT_COMPANY_NAME,
		d1.day,
		(case when n1.num1 is null then 0 else n1.num1 end) num1, -- 本日跟进数
		(case when n2.num2 is null then 0 else n2.num2 end) num2, -- 当天试驾预约数
		(case when n2.num3 is null then 0 else n2.num3 end) num3, -- 当天试驾取消数
		(case when n3.num4 is null then 0 else n3.num4 end) num4, -- 当天试乘试驾完成数
		(case when n4.num5 is null then 0 else n4.num5 end) num5, -- 本日新增试乘试驾任务
		(case when n4.num6 is null then 0 else n4.num6 end) num6, -- 本日试乘试驾任务完成数
		(case when n5.num7 is null then 0 else n5.num7 end) num7, -- 本日新增任务数
		(case when n6.num8 is null then 0 else n6.num8 end) num8, -- 本日任务完成数
		(case when n7.num9 is null then 0 else n7.num9 end) num9, -- （本日跟进中）首次跟进用户量
		(case when n7.num10 is null then 0 else n7.num10 end) num10, -- （本日跟进中）再次跟进用户量
		(case when n7.num11 is null then 0 else n7.num11 end) num11, -- （本日跟进中）已下定用户量
		(case when n7.num12 is null then 0 else n7.num12 end) num12, -- （本日跟进中）交车用户数
		(case when n7.num13 is null then 0 else n7.num13 end) num13, -- （本日跟进中）退订用户数
		(case when n8.num14 is null then 0 else n8.num14 end) num14, -- 下定量
		(case when n9.num15 is null then 0 else n9.num15 end) num15, -- 退定量
		(case when n11.num16 is null then 0 else n11.num16 end) num16, -- 新增试乘试驾排程数
		(case when n11.num17 is null then 0 else n11.num17 end) num17, -- 本日试乘试驾排程取消数
		(case when n10.SELF_CREATE_CLUE_NUM is null then 0 else n10.SELF_CREATE_CLUE_NUM end) SELF_CREATE_CLUE_NUM,
		(case when n10.ASSIGN_CLUE_NUM is null then 0 else n10.ASSIGN_CLUE_NUM end) ASSIGN_CLUE_NUM,
		(case when n10.SELF_CREATE_CLUE_OTHER_NUM is null then 0 else n10.SELF_CREATE_CLUE_OTHER_NUM end) SELF_CREATE_CLUE_OTHER_NUM
		from

		(select date_format(date_add(concat(#{param.createdDateStart},'-01'), interval (cast(help_topic_id as signed integer) + 0) day), '%Y-%m-%d') DAY ,'MP' as mp
		from mysql.help_topic where help_topic_id <![CDATA[ < ]]>  day(last_day(concat(#{param.createdDateStart},'-01'))) order by help_topic_id) d1

		left join (
		select
		<if test="param.userId !=null and param.userId!=''">
			(select EMP_NAME from t_usc_mdm_org_employee where USER_ID=#{param.userId}) as EMP_NAME,
		</if>
		<if test="param.userId == null or param.userId == ''">
			'' as EMP_NAME,
		</if>
		i.AGENT_NAME,d.DLR_SHORT_NAME,c.AGENT_COMPANY_NAME,'MP' as mp
		from
		t_usc_mdm_agent_info i
		left join t_usc_mdm_agent_company c on c.AGENT_ID=i.AGENT_ID
		left join t_usc_mdm_org_dlr d on d.COMPANY_ID=c.AGENT_COMPANY_ID
		where DLR_CODE=#{param.dlrCode}
		) icd on icd.mp=d1.mp
		left join
		(
		select count(1) num1, -- 本日跟进数
		date(CREATED_DATE) date
		from t_sac_onecust_resume
		where
		1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and RESUME_PERSON_CODE=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(CREATED_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>

		group by date(CREATED_DATE)
		) n1 on d1.day=n1.date
		left join
		(select
		sum(case when t1.IS_ENABLE='1' then 1 else 0 end) num2, -- 当天试驾预约数
		sum(case when t1.IS_ENABLE='0' then 1 else 0 end) num3, -- 当天试驾取消数
		date(APPOINTMENT_TEST_DATE) date
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO  )
		where
		1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and t.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and t.SALES_CONSULTANT_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(APPOINTMENT_TEST_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(APPOINTMENT_TEST_DATE)
		) n2 on d1.day=n2.date
		left join
		(
		select
		count(1) num4, -- 当天试乘试驾完成数
		date(END_TIME) date
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
		where end_time is not null
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and t.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and t.SALES_CONSULTANT_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(END_TIME,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(END_TIME)
		) n3 on d1.day=n3.date

		left join
		(select count(1) num5,  -- 本日新增试乘试驾任务
		sum(case when TASK_STATE_CODE='1' then 1 else 0 end ) num6,  -- 本日试乘试驾任务完成数
		date(CREATED_DATE) date
		from t_sac_test_drive_task
		where 1=1

		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and TASK_PERSON_DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and TASK_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(CREATED_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>

		group by date(CREATED_DATE)
		) n4 on d1.day=n4.date

		left join
		(select count(1) num7, -- 本日新增任务数
		date(d.BUSS_START_TIME) date
		from t_sac_onetask_detail d
		join t_sac_onetask_info i on i.TASK_ID=d.TASK_ID
		where 1=1
		and i.TASK_STATE_CODE <![CDATA[ <> ]]>   '4'
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and d.TASK_PERSON_DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and d.TASK_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(d.BUSS_START_TIME,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(d.BUSS_START_TIME)
		) n5 on d1.day=n5.date

		left join
		(select count(1) num8, -- 本日任务完成数
		date(d.BUSS_TIME) date
		from t_sac_onetask_detail d
		join t_sac_onetask_info i on i.TASK_ID=d.TASK_ID
		where 1=1
		and i.TASK_STATE_CODE <![CDATA[ <> ]]>   '4'
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and d.TASK_PERSON_DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and d.TASK_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(d.BUSS_TIME,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(BUSS_TIME)
		) n6 on d1.day=n6.date

		left join
		(select
		count(DISTINCT(case when date(d.FIRST_REVIEW_TIME)=p.dayDate then d.SERVER_ORDER ELSE null END)) as num9, -- （本日跟进中）首次跟进用户量
		count(DISTINCT(case when date(d.FIRST_REVIEW_TIME) <![CDATA[ <> ]]> p.dayDate then d.SERVER_ORDER ELSE null END)) as num10, -- （本日跟进中）再次跟进用户量
		count(DISTINCT(case when tc.sale_order_state in ('2','220','230','300','310','320','500','600','630','640','650','651','660',
		'661','664','665','675','680','690','700','710') then d.SERVER_ORDER ELSE null END)) as num11, -- （本日跟进中）已下定用户量
		count(DISTINCT(case when tc.sale_order_state in ('730','800') then d.SERVER_ORDER ELSE null END)) as num12, -- （本日跟进中）交车用户数
		count(DISTINCT(case when tc.sale_order_state in ('8','15','221') then d.SERVER_ORDER ELSE null END)) as num13, -- （本日跟进中）退订用户数
		p.dayDate date
		from t_sac_clue_info_dlr d inner join (
		SELECT count(1),cust_id,date(CREATED_DATE) as dayDate from t_sac_onecust_resume
		where 1=1

		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and RESUME_PERSON_CODE=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(CREATED_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by CUST_ID,date(CREATED_DATE) order by CREATED_DATE desc) p on d.CUST_ID=p.CUST_ID
		left join orc.t_orc_ve_bu_sale_order_to_c tc on tc.BUY_CUST_ID=p.CUST_ID
		group by p.dayDate
		) n7 on d1.day=n7.date

		left join
		(select COUNT(1) num14, -- 下定量
		date(SALE_ORDER_DATE) date
		from orc.t_orc_ve_bu_sale_order_to_c
		where 1=1

		<if test="param.dlrId !=null and param.dlrId !=''">
			and SALE_DLR_ID=#{param.dlrId}
		</if>
		<if test="param.empId !=null and param.empId!=''">
			and DLR_EMP_ID=#{param.empId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(SALE_ORDER_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(SALE_ORDER_DATE)
		) n8 on d1.day=n8.date

		left join
		(select
		count(DISTINCT SALE_ORDER_CODE) as num15, -- 	退定量
		date(CREATED_DATE) date
		from
		orc.t_orc_ve_bu_sale_order_log
		where 1=1
		and sale_order_state in ('8','15','221')
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.empId !=null and param.empId!=''">
			and SALES_ID=#{param.empId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(CREATED_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(CREATED_DATE)
		) n9 on d1.day=n9.date

		left join
		(select
		date(T.CREATED_DATE) as date,
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM ,/*新增线索数-下发*/
		count(1)-sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END)-
		    sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END)
		    AS SELF_CREATE_CLUE_OTHER_NUM /*新增线索数-其他*/
		FROM t_sac_clue_info_dlr T
		LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049' and L.ATTRIBUTE1='agent'
		WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and T.REVIEW_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%Y-%m') = #{param.createdDateStart}
		</if>
		group by date(T.CREATED_DATE)) n10 on d1.day=n10.date

		left join
		(select
		sum(case when t1.IS_ENABLE='1' then 1 else 0 end) num16, -- 当天创建试驾预约数（排程）
		sum(case when t1.IS_ENABLE='0' then 1 else 0 end) num17, -- 当天创建试驾取消数（排程）
		date(t1.CREATED_DATE) date
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO  )
		where
		1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and t.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and t.SALES_CONSULTANT_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			and DATE_FORMAT(t1.CREATED_DATE,'%Y-%m')=#{param.createdDateStart}
		</if>
		group by date(t1.CREATED_DATE)
		) n11 on d1.day=n11.date

		where d1.day <![CDATA[ <= ]]>  now()
		order by d1.day desc
	</select>

	<!-- 销售顾问日报 -->
	<select id="queryClueStatisticalReport"  resultType="java.util.Map">
		WITH A AS (
		-- 期初线索
		select * from t_sac_clue_info_dlr T1 where
		1=1
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND T1.DLR_CODE = #{param.dlrCode}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND T1.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''"> and T1.CREATED_DATE  <![CDATA[ < ]]> #{param.createdDateStart}</if>
		),
		B AS (
		-- 期间线索
		select * from t_sac_clue_info_dlr T2 where
		1=1
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND T2.DLR_CODE = #{param.dlrCode}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND T2.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and T2.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and T2.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		),
		C AS (
		select
		T.DLR_CODE,
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			'' as INFO_CHAN_M_CODE,'' as INFO_CHAN_M_NAME,'' as INFO_CHAN_D_CODE,'' as INFO_CHAN_D_NAME,
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			T.INFO_CHAN_M_CODE,T.INFO_CHAN_M_NAME,'' as INFO_CHAN_D_CODE,'' as INFO_CHAN_D_NAME,
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			T.INFO_CHAN_M_CODE,T.INFO_CHAN_M_NAME,T.INFO_CHAN_D_CODE, T.INFO_CHAN_D_NAME,
		</if>
		(case when T.INFO_CHAN_M_CODE like 'agent_%' then '代理商' when T.INFO_CHAN_M_CODE like 'ho_%' then '总部' else '' end) as firstChannel,
		count(DISTINCT(CASE WHEN (IFNULL(T.FIRST_REVIEW_TIME, '') != '') AND T.FIRST_REVIEW_TIME  <![CDATA[ <= ]]>  DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY) THEN T.SERVER_ORDER ELSE null END)) AS num9, /*新增线索24小时完成首次跟进*/
		count(DISTINCT(CASE WHEN (IFNULL(T.FIRST_REVIEW_TIME, '') != '') AND T.FIRST_REVIEW_TIME  <![CDATA[ <= ]]>  DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY) THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) as num10,/*新建线索24小时跟进率*/
		count(DISTINCT(case when D.CUSTOMER_ID is not null and end_time is not null
		<if test="param.queryFlag =='1'.toString ">
			and end_time >=#{param.createdDateStart} and end_time <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END)) num11,/*新增线索试驾数*/
		count(DISTINCT(case when T.STATUS_CODE = '10'
		<if test="param.queryFlag =='1'.toString ">
			and T.LAST_REVIEW_TIME >=#{param.createdDateStart} and T.LAST_REVIEW_TIME <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END)) as num12,/*新增线索战败数*/
		count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END)) as num13, /*新增线索成单线索量*/
		count(T.SERVER_ORDER)-count(DISTINCT(case when T.STATUS_CODE = '10'
		<if test="param.queryFlag =='1'.toString ">
			and T.LAST_REVIEW_TIME >=#{param.createdDateStart} and T.LAST_REVIEW_TIME <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END))-count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END)) as num14,/*新增线索待转化剩余*/
		count(DISTINCT(CASE WHEN D.CUSTOMER_ID is not null
		<if test="param.queryFlag =='1'.toString ">
			and D.CREATED_DATE >=#{param.createdDateStart} and D.CREATED_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT T.SERVER_ORDER) as num15,/*试驾排程率*/
		sum(case when C.sale_order_state not in ('8','15','221') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN 1 ELSE 0 END) as num16, /*新增线索订单数*/
		sum(case when C.sale_order_state in ('8','15','221') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN 1 ELSE 0 END) as num17, /*新增线索退单数*/
		sum(case when C.sale_order_state in ('730','800') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN 1 ELSE 0 END) as num18, /*新增线索交付单数*/
		count(DISTINCT(case when R1.custId is not null
		then T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) num19, /*线索到店率*/

		count(DISTINCT(case when D.CUSTOMER_ID is not null and end_time is not null
		<if test="param.queryFlag =='1'.toString ">
			and end_time >=#{param.createdDateStart} and end_time <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) num20, /*线索试驾率（试乘试驾率）*/

		count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.CURRENT_ORDER_TYPE='2'
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END))/count(DISTINCT(T.SERVER_ORDER)) as num21, /*线索大定转化率*/


		count(DISTINCT(case when D.CUSTOMER_ID is not null and end_time is not null and end_time <![CDATA[ < ]]> C.DEPOSIT_PAY_DATE and C.sale_order_state in ('220','230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800')
		<if test="param.queryFlag =='1'.toString ">
			and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]> #{param.createdDateEnd}
			and end_time >=#{param.createdDateStart} and end_time <![CDATA[ <= ]]> #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END))/
		count(DISTINCT(case when D.CUSTOMER_ID is not null and end_time is not null
		<if test="param.queryFlag =='1'.toString ">
		and end_time >=#{param.createdDateStart} and end_time <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		THEN T.SERVER_ORDER ELSE null END)) as  num22,/*试驾大定转化率*/

		-- 大定支付成功订单中锁单数量/大定支付成功订单量
		<if test="param.queryFlag =='1'.toString ">
			'0' as num23,
		</if>
		<if test="param.queryFlag =='2'.toString ">
			sum(case when C.sale_order_state in ('230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800')   then 1 else 0 end)/sum(case when C.sale_order_state in ('15','221','220','230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800') then 1 else 0 end) as num23,/*大定锁单转化率*/
		</if>
		<if test="param.queryFlag =='1'.toString ">
			'0' as num24,
		</if>
		<if test="param.queryFlag =='2'.toString ">
			-- 交付订单量/大定锁单数量
			sum(case when C.sale_order_state in ('730','800') then 1 else 0 end)/sum(case when C.sale_order_state in ('230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800') then 1 else 0 end)   as num24, /*锁单交付转化率*/
		</if>
		<if test="param.queryFlag =='1'.toString ">
			'0' as num25,
		</if>
		<if test="param.queryFlag =='2'.toString ">
			sum(case when C.sale_order_state  in ('230','300','310','320','500','600','630','640','650','651','660','661','664','665','675','680','690','700','710','730','740','745','800')
			THEN 1 ELSE 0 END) as num25,/*新增线索锁单数（不含退订）*/
		</if>
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L0' THEN T.SERVER_ORDER ELSE null END)) AS L0_CLUE_NUM, /*L0线索数*/
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L1' THEN T.SERVER_ORDER ELSE null END)) AS L1_CLUE_NUM, /*L1线索数*/
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L2' THEN T.SERVER_ORDER ELSE null END)) AS L2_CLUE_NUM, /*L2线索数*/
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L3' THEN T.SERVER_ORDER ELSE null END)) AS L3_CLUE_NUM, /*L3线索数*/
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L4' THEN T.SERVER_ORDER ELSE null END)) AS L4_CLUE_NUM, /*L4线索数*/
		count(DISTINCT(CASE WHEN T.INTEN_LEVEL_CODE = 'L5' THEN T.SERVER_ORDER ELSE null END)) AS L5_CLUE_NUM, /*L5线索数*/
		count(DISTINCT(CASE WHEN T.COLUMN5 = 'Hot' THEN T.SERVER_ORDER ELSE null END)) AS HOT_CLUE_NUM, /*Hot线索数*/
		count(DISTINCT(CASE WHEN T.COLUMN5 = 'Warm' THEN T.SERVER_ORDER ELSE null END)) AS WARM_CLUE_NUM, /*Warm线索数*/
		count(DISTINCT(CASE WHEN T.COLUMN5 = 'Cold' THEN T.SERVER_ORDER ELSE null END)) AS COLD_CLUE_NUM /*Cold线索数*/
		from t_sac_clue_info_dlr T left join
		(select
		t1.CUSTOMER_ID,end_time,T1.CREATED_DATE
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
		group by t1.CUSTOMER_ID) D on D.CUSTOMER_ID=T.CUST_ID
		left join orc.t_orc_ve_bu_sale_order_to_c C on C.BUY_CUST_ID=T.CUST_ID
		left join (select DISTINCT(CUST_ID) custId
		from t_sac_onecust_resume
		where SENCE_CODE in ('2','16','19')
		<if test="param.queryFlag =='1'.toString ">
			and CREATED_DATE >=#{param.createdDateStart} and CREATED_DATE <![CDATA[ <= ]]>  #{param.createdDateEnd}
		</if>
		<if test="param.queryFlag =='2'.toString ">
			and CREATED_DATE >=#{param.createdDateStart}
		</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND  DLR_CODE_OWNER IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		) R1 on R1.custId=T.CUST_ID
		WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND T.DLR_CODE = #{param.dlrCode}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''   ">and T.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''   "><![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			group by firstChannel,T.DLR_CODE
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			group by T.INFO_CHAN_M_NAME,T.DLR_CODE
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			group by T.INFO_CHAN_D_NAME,T.DLR_CODE
		</if>
		),
		D AS (
		select
		A.DLR_CODE,
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			'' as INFO_CHAN_M_CODE,'' as INFO_CHAN_M_NAME,'' as INFO_CHAN_D_CODE,'' as INFO_CHAN_D_NAME,
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			 A.INFO_CHAN_M_CODE, A.INFO_CHAN_M_NAME,'' as INFO_CHAN_D_CODE,'' as INFO_CHAN_D_NAME,
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			A.INFO_CHAN_M_CODE, A.INFO_CHAN_M_NAME,A.INFO_CHAN_D_CODE,A.INFO_CHAN_D_NAME,
		</if>
		(case when A.INFO_CHAN_M_CODE like 'agent_%' then '代理商' when A.INFO_CHAN_M_CODE like 'ho_%' then '总部' else '' end) as firstChannel,
		count(DISTINCT(CASE WHEN A.STATUS_CODE = '10' and A.LAST_REVIEW_TIME <![CDATA[ < ]]> #{param.createdDateStart} THEN A.SERVER_ORDER ELSE null END)) AS num1, /*期初战败线索总量*/
		count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.SALE_ORDER_DATE <![CDATA[ < ]]> #{param.createdDateStart} THEN A.SERVER_ORDER ELSE null END)) as num2, /*期初成单线索总量*/
		count(DISTINCT A.SERVER_ORDER)-count(DISTINCT(CASE WHEN A.STATUS_CODE = '10' and A.LAST_REVIEW_TIME <![CDATA[ < ]]> #{param.createdDateStart} THEN A.SERVER_ORDER ELSE null END))-count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.SALE_ORDER_DATE <![CDATA[ < ]]> #{param.createdDateStart} THEN A.SERVER_ORDER ELSE null END)) as
		num3,/*期初待转化线索剩余*/
		count(DISTINCT(case when D.CUSTOMER_ID is not null and D.end_time >=#{param.createdDateStart} and D.end_time <![CDATA[ <= ]]> #{param.createdDateEnd} THEN A.SERVER_ORDER ELSE null END)) as num4, /*期初待转化线索期间试驾数*/
		count(DISTINCT(CASE WHEN A.STATUS_CODE = '10' and A.LAST_REVIEW_TIME >=#{param.createdDateStart} and A.LAST_REVIEW_TIME <![CDATA[ <= ]]> #{param.createdDateEnd} THEN A.SERVER_ORDER ELSE null END )) as num5, /*期初待转化线索期间战败数*/
		count(DISTINCT(case when C.sale_order_state not in ('8','15','221') and C.SALE_ORDER_DATE >=#{param.createdDateStart} and C.SALE_ORDER_DATE <![CDATA[ <= ]]> #{param.createdDateEnd} THEN A.SERVER_ORDER ELSE null END)) as num6, /*期初待转化线索期间成单线索量*/
		sum(case when C.sale_order_state not in ('8','15','221') and C.SALE_ORDER_DATE >=#{param.createdDateStart} and  C.SALE_ORDER_DATE <![CDATA[ <= ]]> #{param.createdDateEnd} THEN 1 ELSE 0 END) as num7 /*期初线索期间成单线索总数*/
		from
		A left join B on A.CUST_ID=B.CUST_ID
		left join orc.t_orc_ve_bu_sale_order_to_c C on C.BUY_CUST_ID=A.CUST_ID
		left join (select
		t1.CUSTOMER_ID,end_time
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1' )
		where end_time is not null
		group by t1.CUSTOMER_ID) D on D.CUSTOMER_ID=A.CUST_ID
		WHERE A.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			group by firstChannel,A.DLR_CODE
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			group by A.INFO_CHAN_M_NAME,A.DLR_CODE
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			group by A.INFO_CHAN_D_NAME,A.DLR_CODE
		</if>
		)
		SELECT
		t3.AREA_NAME,
		h.PROVINCE_NAME,
		g.CITY_NAME ,
		s3.AGENT_NAME ,
		s2.AGENT_COMPANY_NAME ,
		d1.DLR_SHORT_NAME,
		C.DLR_CODE,
		C.INFO_CHAN_M_NAME,
		C.INFO_CHAN_D_NAME,
		C.INFO_CHAN_M_CODE,
		C.firstChannel,
		C.INFO_CHAN_D_CODE,
		ifnull( C.num1, 0 ) num1,
		ifnull( C.num2, 0 ) num2,
		ifnull( C.num3, 0 ) num3,
		ifnull( C.num4, 0 ) num4,
		ifnull( C.num5, 0 ) num5,
		ifnull( C.num6, 0 ) num6,
		ifnull( C.num7, 0 ) num7,
		ifnull( E.num8, 0 ) num8,
		ifnull(C.num9,0) num9,
		ifnull(C.num10,0) num10,
		ifnull(C.num11,0) num11,
		ifnull(C.num12,0) num12,
		ifnull(C.num13,0) num13,
		ifnull(C.num14,0) num14,
		ifnull(C.num15,0) num15,
		ifnull(C.num16,0) num16,
		ifnull(C.num17,0) num17,
		ifnull(C.num18,0) num18,
		ifnull(C.num19,0) num19,
		ifnull(C.num20,0) num20,
		ifnull(C.num21,0) num21,
		ifnull(C.num22,0) num22,
		ifnull(C.num23,0) num23,
		ifnull(C.num24,0) num24,
		ifnull(C.num25,0) num25,
		ifnull(C.HOT_CLUE_NUM,0) HOT_CLUE_NUM,
		ifnull(C.WARM_CLUE_NUM,0) WARM_CLUE_NUM,
		ifnull(C.COLD_CLUE_NUM,0) COLD_CLUE_NUM,
		ifnull(C.L0_CLUE_NUM,0) L0_CLUE_NUM,
		ifnull(C.L1_CLUE_NUM,0) L1_CLUE_NUM,
		ifnull(C.L2_CLUE_NUM,0) L2_CLUE_NUM,
		ifnull(C.L3_CLUE_NUM,0) L3_CLUE_NUM,
		ifnull(C.L4_CLUE_NUM,0) L4_CLUE_NUM,
		ifnull(C.L5_CLUE_NUM,0) L5_CLUE_NUM
		FROM
		(select
		C.DLR_CODE,
		C.INFO_CHAN_M_NAME,
		C.INFO_CHAN_D_NAME,
		C.INFO_CHAN_M_CODE,
		C.firstChannel,
		C.INFO_CHAN_D_CODE,
		ifnull( D.num1, 0 ) num1,
		ifnull( D.num2, 0 ) num2,
		ifnull( D.num3, 0 ) num3,
		ifnull( D.num4, 0 ) num4,
		ifnull( D.num5, 0 ) num5,
		ifnull( D.num6, 0 ) num6,
		ifnull( D.num7, 0 ) num7,
		ifnull( C.num9, 0 ) num9,
		ifnull( C.num10, 0 ) num10,
		ifnull( C.num11, 0 ) num11,
		ifnull( C.num12, 0 ) num12,
		ifnull( C.num13, 0 ) num13,
		ifnull( C.num14, 0 ) num14,
		ifnull( C.num15, 0 ) num15,
		ifnull( C.num16, 0 ) num16,
		ifnull( C.num17, 0 ) num17,
		ifnull( C.num18, 0 ) num18,
		ifnull(C.num19,0) num19,
		ifnull(C.num20,0) num20,
		ifnull(C.num21,0) num21,
		ifnull(C.num22,0) num22,
		ifnull(C.num23,0) num23,
		ifnull(C.num24,0) num24,
		ifnull(C.num25,0) num25,
		ifnull( C.L0_CLUE_NUM, 0 ) L0_CLUE_NUM,
		ifnull( C.L1_CLUE_NUM, 0 ) L1_CLUE_NUM,
		ifnull( C.L2_CLUE_NUM, 0 ) L2_CLUE_NUM,
		ifnull( C.L3_CLUE_NUM, 0 ) L3_CLUE_NUM,
		ifnull( C.L4_CLUE_NUM, 0 ) L4_CLUE_NUM,
		ifnull( C.L5_CLUE_NUM, 0 ) L5_CLUE_NUM,
		ifnull( C.HOT_CLUE_NUM, 0 ) HOT_CLUE_NUM,
		ifnull( C.WARM_CLUE_NUM, 0 ) WARM_CLUE_NUM,
		ifnull( C.COLD_CLUE_NUM, 0 ) COLD_CLUE_NUM
		FROM C
		LEFT JOIN D ON C.DLR_CODE = D.DLR_CODE
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			AND C.firstChannel = D.firstChannel
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			AND C.INFO_CHAN_M_NAME = D.INFO_CHAN_M_NAME
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			AND C.INFO_CHAN_D_NAME = D.INFO_CHAN_D_NAME
		</if>
		UNION
		select
		D.DLR_CODE,
		D.INFO_CHAN_M_NAME,
		D.INFO_CHAN_D_NAME,
		D.INFO_CHAN_M_CODE,
		D.firstChannel,
		D.INFO_CHAN_D_CODE,
		ifnull( D.num1, 0 ) num1,
		ifnull( D.num2, 0 ) num2,
		ifnull( D.num3, 0 ) num3,
		ifnull( D.num4, 0 ) num4,
		ifnull( D.num5, 0 ) num5,
		ifnull( D.num6, 0 ) num6,
		ifnull( D.num7, 0 ) num7,
		ifnull( C.num9, 0 ) num9,
		ifnull( C.num10, 0 ) num10,
		ifnull( C.num11, 0 ) num11,
		ifnull( C.num12, 0 ) num12,
		ifnull( C.num13, 0 ) num13,
		ifnull( C.num14, 0 ) num14,
		ifnull( C.num15, 0 ) num15,
		ifnull( C.num16, 0 ) num16,
		ifnull( C.num17, 0 ) num17,
		ifnull( C.num18, 0 ) num18,
		ifnull(C.num19,0) num19,
		ifnull(C.num20,0) num20,
		ifnull(C.num21,0) num21,
		ifnull(C.num22,0) num22,
		ifnull(C.num23,0) num23,
		ifnull(C.num24,0) num24,
		ifnull(C.num25,0) num25,
		ifnull( C.L0_CLUE_NUM, 0 ) L0_CLUE_NUM,
		ifnull( C.L1_CLUE_NUM, 0 ) L1_CLUE_NUM,
		ifnull( C.L2_CLUE_NUM, 0 ) L2_CLUE_NUM,
		ifnull( C.L3_CLUE_NUM, 0 ) L3_CLUE_NUM,
		ifnull( C.L4_CLUE_NUM, 0 ) L4_CLUE_NUM,
		ifnull( C.L5_CLUE_NUM, 0 ) L5_CLUE_NUM,
		ifnull( C.HOT_CLUE_NUM, 0 ) HOT_CLUE_NUM,
		ifnull( C.WARM_CLUE_NUM, 0 ) WARM_CLUE_NUM,
		ifnull( C.COLD_CLUE_NUM, 0 ) COLD_CLUE_NUM
		FROM D
		LEFT JOIN C ON C.DLR_CODE = D.DLR_CODE
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			AND C.firstChannel = D.firstChannel
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			AND C.INFO_CHAN_M_NAME = D.INFO_CHAN_M_NAME
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			AND C.INFO_CHAN_D_NAME = D.INFO_CHAN_D_NAME
		</if>
		) C
		left join (
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			select count(1) as num8,X.DLR_CODE,X.firstChannel from (select /*期间新增线索数*/ DLR_CODE,(case when T2.INFO_CHAN_M_CODE like 'agent_%' then '代理商' when T2.INFO_CHAN_M_CODE like 'ho_%' then '总部' else '' end) as firstChannel from t_sac_clue_info_dlr T2 where
			T2.CREATED_DATE >= #{param.createdDateStart}
			and T2.CREATED_DATE <![CDATA[ <= ]]> #{param.createdDateEnd}
			and T2.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP'))X
			group by X.firstChannel,X.DLR_CODE) E on E.DLR_CODE=C.DLR_CODE AND E.firstChannel=C.firstChannel
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			select count(1) num8, /*期间新增线索数*/ DLR_CODE,INFO_CHAN_M_NAME from t_sac_clue_info_dlr T2 where
			T2.CREATED_DATE >= #{param.createdDateStart}
			and T2.CREATED_DATE <![CDATA[ <= ]]> #{param.createdDateEnd}
			and T2.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
			group by T2.INFO_CHAN_M_NAME,T2.DLR_CODE) E on E.DLR_CODE=C.DLR_CODE AND E.INFO_CHAN_M_NAME=C.INFO_CHAN_M_NAME
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			select count(1) num8, /*期间新增线索数*/ DLR_CODE,INFO_CHAN_D_NAME from t_sac_clue_info_dlr T2 where
			T2.CREATED_DATE >= #{param.createdDateStart}
			and T2.CREATED_DATE <![CDATA[ <= ]]> #{param.createdDateEnd}
			and T2.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
			group by T2.INFO_CHAN_D_NAME,T2.DLR_CODE) E on E.DLR_CODE=C.DLR_CODE AND E.INFO_CHAN_D_NAME=C.INFO_CHAN_D_NAME
		</if>
		LEFT JOIN t_usc_mdm_org_dlr d1 ON C.DLR_CODE = d1.DLR_CODE
		LEFT JOIN t_usc_mdm_agent_company s2 ON d1.COMPANY_ID = s2.AGENT_COMPANY_ID
		LEFT JOIN t_usc_mdm_agent_info s3 ON s2.AGENT_ID = s3.AGENT_ID
		LEFT JOIN t_usc_mdm_org_city g ON d1.CITY_ID = g.CITY_ID
		LEFT JOIN t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d1.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		WHERE 1 = 1
		and C.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		<if test="param.firstchannelCode != null and param.firstchannelCode != '' ">
			AND C.firstChannel =#{param.firstchannelCode}
		</if>
		<if test="param.infoChanMCode != null and param.infoChanMCode != '' ">
			AND C.INFO_CHAN_M_CODE in
			<foreach item="item" collection="param.infoChanMCode.split(',')" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
		<if test="param.infoChanDCode != null and param.infoChanDCode != '' ">
			AND C.INFO_CHAN_d_CODE in
			<foreach item="item" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>

		</if>
		<if test="param.provinceCode != null and param.provinceCode != '' ">
			AND h.PROVINCE_CODE =#{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != '' ">
			AND g.CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.areaCode != null and param.areaCode != '' ">
			AND t3.AREA_CODE = #{param.areaCode}
		</if>
		<if test="param.agentCompanyId != null and param.agentCompanyId != '' ">
			AND s2.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
		<if test="param.agentId != null and param.agentId != '' ">
			AND s3.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.orgType != null and param.orgType == '0'.toString ">
			AND t3.AREA_CODE in (SELECT
			ua.area_code
			FROM
			t_usc_bigarea_user bu
			LEFT JOIN t_usc_area_info ua ON ua.AREA_ID = bu.BIG_AREA_ID
			AND ua.AREA_TYPE = '1'
			WHERE
			bu.IS_ENABLE = 1
			AND ua.IS_ENABLE = 1
			<if test="param !=null and param.userId !=null and param.userId !=''"> and bu.USER_ID=#{param.userId} </if>
			)
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '1'.toString ">
			order by C.DLR_CODE,C.firstChannel
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '2'.toString ">
			order by C.DLR_CODE,C.INFO_CHAN_M_NAME
		</if>
		<if test="param.clueChannelCode != null and param.clueChannelCode == '3'.toString ">
			order by C.DLR_CODE,C.INFO_CHAN_D_NAME
		</if>
	</select>

	<select id="querySaleConstantReportOld" parameterType="map" resultType="map">
		with onecust_resume as (
		select   date(T.CREATED_DATE) as CREATED_DATE ,'0' as SELF_CREATE_CLUE_NUM,'0' as ASSIGN_CLUE_NUM,
		'0' AS SELF_CREATE_CLUE_OTHER_NUM,
		count(T.DLR_CODE_OWNER) as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM,
		'0' AS dlr_owner_num
		FROM t_sac_onecust_resume T
		where
		 T.SENCE_CODE in (16,2,19)
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE_OWNER,#{param.dlrCodeList})
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and T.RESUME_PERSON_CODE=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%Y-%m') = #{param.createdDateStart}
		</if>
		<!--<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%y%m') <![CDATA[ <= ]]>DATE_FORMAT( #{param.createdDateEnd},'%y%m')
		</if>-->
		group by date(T.CREATED_DATE)
		),order_to_c as  (
		select date(O.CREATED_DATE) as CREATED_DATE,'0' as SELF_CREATE_CLUE_NUM,'0' as ASSIGN_CLUE_NUM,
		'0' AS SELF_CREATE_CLUE_OTHER_NUM,
		'0' as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		count(1) as ORDER_NUM ,
		sum(case when o.SALE_ORDER_STATE in (8,15,221) then 1 else 0 end) as orderCount,
		sum(case when o.SALE_ORDER_STATE in (select LOOKUP_VALUE_CODE from t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='VE8090') then 1 else 0 end) as DEPOSIT_NUM, /*交车数，当前只到大定*/
		'0' AS dlr_owner_num
		FROM t_orc_ve_bu_sale_order_to_c O
		left join t_usc_mdm_org_dlr dlr on dlr.DLR_ID=o.SALE_DLR_ID
				WHERE 1 = 1
		<if test="param.userId !=null and param.userId!=''">
			AND o.DLR_EMP_ID=#{param.userId}
		</if>

		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(dlr.DLR_CODE,#{param.dlrCodeList})
		</if>

		<if test="param !=null and param.dlrCode!=null and param.dlrCode !=''">
			AND  dlr.DLR_CODE=#{param.dlrCode}
		</if>
		<!--	O.DLR_EMP_ID = #{param.userId}-->
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(O.CREATED_DATE,'%Y-%m') = #{param.createdDateStart}
		</if>
		<!--<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND DATE_FORMAT(O.CREATED_DATE,'%y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{param.createdDateEnd},'%y%m')
		</if>-->
		group by date(O.CREATED_DATE)
		),
		<!-- INFO_DLR as (
		SELECT row_number() over (partition by CUST_ID order by CREATED_DATE DESC ) n,r.* FROM t_sac_clue_info_dlr r
		where 1=1
		<if test="param.dlrCode != null and '' != param.dlrCode">
			and r.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param !=null and param.dlrEmpId!=null and (param.isdlr == null or ''== param.isdlr)">
			AND r.REVIEW_PERSON_ID  in
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.dlrEmpId" >
				#{item.userId}
			</foreach>
		</if>
		), -->drive_sheet as (
		SELECT date(T.CREATED_DATE) as  CREATED_DATE/*试驾数*/,
		'0' as SELF_CREATE_CLUE_NUM,
		'0' as ASSIGN_CLUE_NUM,
		'0' AS SELF_CREATE_CLUE_OTHER_NUM,
		'0' as TO_DLR_NUM,
		count(1) AS TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM, /*交车数，当前只到大定*/
		'0' AS dlr_owner_num
		FROM t_sac_test_drive_sheet T
		WHERE 1=1
		and T.TEST_STATUS='2'
		<if test="param.userId !=null and param.userId!=''">
			and 	T.SALES_CONSULTANT_ID = #{param.userId}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE,#{param.dlrCodeList})
		</if>

		<if test="param !=null and param.dlrCode!=null and param.dlrCode !=''">
			AND  T.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(T.END_TIME,'%Y-%m') = #{param.createdDateStart}
		</if>

		<!--<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{param.createdDateEnd}
		</if>-->
		group by date(T.CREATED_DATE)
		),
		dlr_owner_count AS (
		SELECT
		date(t.CREATED_DATE) AS CREATED_DATE,
		'0' AS SELF_CREATE_CLUE_NUM,
		'0' AS ASSIGN_CLUE_NUM,
		'0' AS SELF_CREATE_CLUE_OTHER_NUM,
		'0' AS TO_DLR_NUM,
		'0' AS TEST_DRIVE_NUM,
		'0' AS ORDER_NUM,
		'0' AS orderCount,
		'0' AS DEPOSIT_NUM,
		COUNT( * ) AS dlr_owner_num
		FROM
		t_sac_onecust_resume t
		WHERE
		t.SENCE_NAME IN ( '订单跟进-去电', '去电' )
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE_OWNER=#{param.dlrCode}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE_OWNER,#{param.dlrCodeList})
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and T.RESUME_PERSON_CODE=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%Y-%m') = #{param.createdDateStart}
		</if>
		GROUP BY
		date( T.CREATED_DATE )
		)
		,clue_info_dlr as (
		select
		date(T.CREATED_DATE) as CREATED_DATE,
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM ,/*新增线索数-下发*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL AND  T.INFO_CHAN_M_CODE in('2','3','4','5') THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_OTHER_NUM, /*新增线索数-其他*/
		'0' as TO_DLR_NUM,
		'0' as TEST_DRIVE_NUM,
		'0' as ORDER_NUM,
		'0' as orderCount,
		'0' as DEPOSIT_NUM,
		'0' AS dlr_owner_num
		FROM t_sac_clue_info_dlr T
		LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049' and L.ATTRIBUTE1='agent'
		WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and T.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">
			and FIND_IN_SET(T.DLR_CODE,#{param.dlrCodeList})
		</if>
		<if test="param.userId !=null and param.userId!=''">
			and T.REVIEW_PERSON_ID=#{param.userId}
		</if>
		<if test="param.createdDateStart != null and param.createdDateStart != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%Y-%m') = #{param.createdDateStart}
		</if>
		<!--<if test="param.createdDateEnd != null and param.createdDateEnd != ''">
			AND DATE_FORMAT(T.CREATED_DATE,'%y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{param.createdDateEnd},'%y%m')
		</if>-->
		group by date(T.CREATED_DATE)
		)
		SELECT
		sum(t.SELF_CREATE_CLUE_NUM) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(t.ASSIGN_CLUE_NUM) AS ASSIGN_CLUE_NUM /*新增线索数-下发*/
		,t.CREATED_DATE ,
		sum(t.DEPOSIT_NUM) as DEPOSIT_NUM, /*交车数，当前只到大定*/
		sum(t.ORDER_NUM) as ORDER_NUM,
		sum(t.orderCount) as orderCount,
		sum(t.TEST_DRIVE_NUM) as TEST_DRIVE_NUM,
		sum(t.TO_DLR_NUM) as TO_DLR_NUM,
		sum(t.dlr_owner_num )as dlr_owner_num
		<!--A.SELF_CREATE_CLUE_NUM,/*新增线索量-自建*/
		A.ASSIGN_CLUE_NUM, /*新增线索量-下发*/
		B.TO_DLR_NUM, /*邀约到店数(有到店跟进记录的线索)*/
		C.ORDER_NUM, /*新增订单数(订单总量)*/
		c.orderCount, /*新增退订数*/
		 '0' as DEPOSIT_NUM, /*交车数，当前只到大定*/
		D.TEST_DRIVE_NUM /*试驾数*/
		,(case
		when a.CREATED_DATE is not null   then a.CREATED_DATE
		when b.CREATED_DATE is not null and  a.CREATED_DATE is null then b.CREATED_DATE
		when c.CREATED_DATE is not  null and b.CREATED_DATE is  null and  a.CREATED_DATE is null then c.CREATED_DATE
		else d.CREATED_DATE end) as CREATED_DATE-->
		FROM
		(select * from clue_info_dlr
		union all
		select * from  onecust_resume
		union all select * from  order_to_c
		union all  select * from  drive_sheet
		UNION ALL SELECT * FROM dlr_owner_count
		) t
		group by T.CREATED_DATE
		order by T.CREATED_DATE desc
		<!--clue_info_dlr A
		union all onecust_resume B ON b.CREATED_DATE=a.CREATED_DATE
		union all  order_to_c
		c  ON c.CREATED_DATE=a.CREATED_DATE

		union all  drive_sheet

		 D ON d.CREATED_DATE=a.CREATED_DATE-->
		<!--full outer join order_to_c
		E  ON E.CREATED_DATE=a.CREATED_DATE-->
	</select>

	<!-- 1.线索指标统计报表，使用角色：总部/大区/`大使/门店 -->
	<select id="queryClueStatisticalReportOld"  resultType="java.util.Map">
	WITH A AS (
		SELECT
			d.CITY_ID, d.DLR_ID, T.DLR_CODE , d.COMPANY_ID, T.DLR_SHORT_NAME,
			sum(CASE WHEN T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 1 ELSE 0 END) AS SLEEP_CLUE_NUM, /*休眠线索数量*/
			count(*) AS CLUE_NUM, /*每个门店线索总数*/
			sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL OR T.INFO_CHAN_M_CODE = 'dlr.build' THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
			sum(CASE WHEN (T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='门店自建') or T.INFO_CHAN_M_CODE = 'dlr.build' THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM1, /*新增线索数-门店自建*/
			sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='市场活动'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM2, /*新增线索数-市场活动*/
			sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='垂直网站'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM3, /*新增线索数-垂直网站*/
			sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='社交媒体'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM4, /*新增线索数-社交媒体*/
			sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='其他渠道'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM5, /*新增线索数-其他渠道*/
			sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL OR T.INFO_CHAN_M_CODE = 'dlr.build' THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM, /*新增线索数-下发*/
			sum(CASE WHEN T.STATUS_CODE = '10' THEN 1 ELSE 0 END) AS DESTROY_CLUE_NUM, /*战败数量*/
			sum(CASE WHEN T.STATUS_CODE = '10' OR T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 0 ELSE 1 END) AS VALID_CLUE_NUM, /*有效线索数量*/
			/*下发后24小时内未跟进线索数*/
			sum(CASE WHEN
			(IFNULL(T.FIRST_REVIEW_TIME, '') = '')
			OR
			(T.FIRST_REVIEW_TIME IS NOT NULL AND T.FIRST_REVIEW_TIME > DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY ))
			THEN 1 ELSE 0 END) AS UNREVIEW_CLUE_NUM,
			sum(CASE WHEN
			(IFNULL(T.FIRST_REVIEW_TIME, '') != '') AND T.FIRST_REVIEW_TIME <![CDATA[ <= ]]> DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY)
			THEN 1 ELSE 0 END) AS REVIEWED_CLUE_NUM, /*下发后24小时内已跟进线索数 首次跟进时间不为空*/
			sum(CASE WHEN T.STATUS_CODE = '1' THEN 1 ELSE 0 END) AS UNASSIGN_CLUE_NUM, /*待分配线索数*/
			sum(CASE WHEN T.STATUS_CODE NOT IN ('1') THEN 1 ELSE 0 END) AS ASSIGNED_CLUE_NUM, /*已分配线索数*/
			sum(CASE WHEN ifnull(R.OVER_REVIEW_TIME, now()) <![CDATA[ < ]]> now() THEN 1 ELSE 0 END) AS OVER_UNREVIEW_CLUE_NUM, /*逾期未跟进线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L0' THEN 1 ELSE 0 END) AS L0_CLUE_NUM, /*L0线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L1' THEN 1 ELSE 0 END) AS L1_CLUE_NUM, /*L1线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L2' THEN 1 ELSE 0 END) AS L2_CLUE_NUM, /*L2线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L3' THEN 1 ELSE 0 END) AS L3_CLUE_NUM, /*L3线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L4' THEN 1 ELSE 0 END) AS L4_CLUE_NUM, /*L4线索数*/
			sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L5' THEN 1 ELSE 0 END) AS L5_CLUE_NUM, /*L5线索数*/
			sum(CASE WHEN T.COLUMN5 = 'Hot' THEN 1 ELSE 0 END) AS HOT_CLUE_NUM, /*Hot线索数*/
			sum(CASE WHEN T.COLUMN5 = 'Warm' THEN 1 ELSE 0 END) AS WARM_CLUE_NUM, /*Warm线索数*/
			sum(CASE WHEN T.COLUMN5 = 'Cold' THEN 1 ELSE 0 END) AS COLD_CLUE_NUM /*Cold线索数*/
		FROM t_sac_clue_info_dlr T
			LEFT JOIN t_usc_mdm_org_dlr d ON T.DLR_CODE = d.DLR_CODE
			LEFT JOIN t_sac_review R ON T.SERVER_ORDER = R.BILL_CODE
			LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049'
		WHERE T.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND T.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and T.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		GROUP BY T.DLR_CODE
	),
	B AS (
		SELECT
			DLR_CODE,
			COUNT(SALE_ORDER_CODE) AS ORDER_NUM,
			sum(CASE WHEN SALE_ORDER_STATE IN ( '15', '8', '221' ) THEN 1 ELSE 0 END ) AS RETURN_NUM /*新增退订数*/
		FROM (
			SELECT
					T.DLR_CODE,
					T.PHONE,
					O.SALE_ORDER_STATE,
					O.SALE_ORDER_CODE
			FROM t_sac_clue_info_dlr T
					LEFT JOIN t_orc_ve_bu_sale_order_to_c O ON T.CUST_ID = O.BUY_CUST_ID
			WHERE O.SALE_ORDER_CODE IS NOT NULL
				AND T.DLR_CODE NOT IN ( 'HOST', 'HOST_SLEEP' ) /*非城市，非休眠线索*/
				<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
					AND T.DLR_CODE IN
					<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="param.dlrCode != null and param.dlrCode != '' ">
					AND T.DLR_CODE = #{param.dlrCode}
				</if>
				<if test="param.createdDateStart !=null and param.createdDateStart !=''">and T.CREATED_DATE>=#{param.createdDateStart}</if>
				<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]></if>
			GROUP BY O.SALE_ORDER_CODE
		) CO
		GROUP BY CO.DLR_CODE
	),
	C AS (
		SELECT
			DLR_CODE, COUNT(*) AS DLR_SALE_CONSTANT_NUM /*销售顾问数量*/
		FROM t_usc_mdm_org_employee e
		WHERE
			e.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
			AND e.USER_STATUS = '1' /*在职*/
			AND EXISTS (
				SELECT
					LOOKUP_VALUE_NAME
				FROM t_prc_mds_lookup_value
				WHERE LOOKUP_TYPE_CODE = 'ADP_CLUE_046'
					AND (LOOKUP_VALUE_NAME = e.STATION_ID OR LOOKUP_VALUE_NAME = e.COLUMN2)
			)
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND e.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND e.DLR_CODE = #{param.dlrCode}
		</if>
		GROUP BY e.DLR_CODE
	)
	SELECT
		t3.AREA_NAME,
		h.PROVINCE_NAME,
		g.CITY_NAME ,
		s3.AGENT_NAME ,
		s2.AGENT_COMPANY_NAME ,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.CLUE_NUM AS CLUE_TOTAL_NUM, /*线索总量*/
		A.SELF_CREATE_CLUE_NUM,
		A.SELF_CREATE_CLUE_NUM1,
		A.SELF_CREATE_CLUE_NUM2,
		A.SELF_CREATE_CLUE_NUM3,
		A.SELF_CREATE_CLUE_NUM4,
		A.SELF_CREATE_CLUE_NUM5,
		A.ASSIGN_CLUE_NUM,
		A.DESTROY_CLUE_NUM,
		round(A.DESTROY_CLUE_NUM / A.CLUE_NUM, 2) AS DESTROY_CLUE_RATIO, /*战败率*/
		A.SLEEP_CLUE_NUM,
		A.VALID_CLUE_NUM,
		A.REVIEWED_CLUE_NUM,
		A.UNREVIEW_CLUE_NUM,
		round(A.REVIEWED_CLUE_NUM / A.CLUE_NUM, 2) AS REVIEW_RATIO, /*24小时跟进率*/
		A.UNASSIGN_CLUE_NUM,
		A.ASSIGNED_CLUE_NUM,
		A.OVER_UNREVIEW_CLUE_NUM,
		A.L0_CLUE_NUM,
		A.L1_CLUE_NUM,
		A.L2_CLUE_NUM,
		A.L3_CLUE_NUM,
		A.L4_CLUE_NUM,
		A.L5_CLUE_NUM,
		A.HOT_CLUE_NUM,
		A.WARM_CLUE_NUM,
		A.COLD_CLUE_NUM,
		ifnull(B.ORDER_NUM, 0) AS ORDER_NUM,
		ifnull(B.RETURN_NUM, 0) AS RETURN_NUM,
		round(ifnull(A.CLUE_NUM / IFNULL(C.DLR_SALE_CONSTANT_NUM, 0), 0), 2) AS DLR_PERSON_AVG_CLUE_NUM /*店内人均线索数量*/
	FROM A
		LEFT JOIN B ON A.DLR_CODE = B.DLR_CODE
		LEFT JOIN C ON A.DLR_CODE = C.DLR_CODE
		LEFT JOIN t_usc_mdm_agent_company s2 ON A.COMPANY_ID = s2.AGENT_COMPANY_ID
		LEFT JOIN t_usc_mdm_agent_info s3 ON s2.AGENT_ID = s3.AGENT_ID
		LEFT JOIN t_usc_mdm_org_city g ON A.CITY_ID = g.CITY_ID
		LEFT JOIN t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = A.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
	WHERE 1 = 1
	<if test="param.provinceCode != null and param.provinceCode != '' ">
		AND h.PROVINCE_CODE =#{param.provinceCode}
	</if>
	<if test="param.cityCode != null and param.cityCode != '' ">
		AND g.CITY_CODE = #{param.cityCode}
	</if>
	<if test="param.areaCode != null and param.areaCode != '' ">
		AND t3.AREA_CODE = #{param.areaCode}
	</if>
	<if test="param.agentCompanyId != null and param.agentCompanyId != '' ">
		AND s2.AGENT_COMPANY_ID = #{param.agentCompanyId}
	</if>
	<if test="param.agentId != null and param.agentId != '' ">
		AND s3.AGENT_ID = #{param.agentId}
	</if>
	</select>

	<!--市场类活动报表-->
	<select id="queryMarketActivityReport" parameterType="map" resultType="map">
	WITH A AS(
		SELECT
			ACC.ACTIVITY_ID,
			/*开发类活动*/
			ifnull(sum(CASE WHEN ACC.IS_CHECK_IN = '1' THEN 1 ELSE 0 END), 0) AS CHECK_IN_NUM, /*活动签到数量*/
			ifnull(sum(CASE WHEN ACC.ACTIVITY_ID = CLUE.COLUMN8 THEN 1 ELSE 0 END), 0) AS ACT_CLUE_NUM, /*活动报名线索数量*/
			count(1) AS ACT_SIGN_NUM /*活动报名数量*/
		FROM t_acc_bu_activity_customer ACC
			LEFT JOIN t_sac_clue_info_dlr CLUE ON ACC.CUSTOMER_PHONE = CLUE.PHONE
		GROUP BY ACC.ACTIVITY_ID
	),
	B AS (
		SELECT
			ACC.ACTIVITY_ID,
			count(DISTINCT ACC.CUSTOMER_PHONE) AS ACT_ORDER_NUM /*活动订单人数*/
		FROM t_acc_bu_activity_customer ACC
			JOIN t_orc_ve_bu_sale_order_to_c ORC ON ACC.CUSTOMER_PHONE = ORC.BUY_CUST_PHONE
		GROUP BY ACC.ACTIVITY_ID
	)
	SELECT
		ACT.BEGIN_TIME,
		t3.AREA_NAME,
		d.DLR_SHORT_NAME,
		h.PROVINCE_NAME,
		g.CITY_NAME,
		a.AGENT_NAME,
	    c.AGENT_COMPANY_NAME,
		ACT.DLR_CODE,
	    ACT.ACTIVITY_NAME,
	    ACT.ACTIVITY_ID,
		ACT.ACTIVITY_TYPE_NAME,
		ROUND(ACT.BUDGET,2) BUDGET,/*活动预算*/
		CAST(ifnull(ACT.COLUMN18, '0') AS DECIMAL) AS ACTUAL_COST_NUM, /*实际花费数量*/
	    CAST(ifnull(ACT.COLUMN19, '0') AS DECIMAL) AS PROPAGATION_CHANNEL_NUM, /*传播渠道数量*/
	    CAST(ifnull(ACT.COLUMN20, '0') AS DECIMAL) AS PROPAGATION_NUM, /*传播数量*/
	    ifnull(A.CHECK_IN_NUM, 0) AS CHECK_IN_NUM,
	    ifnull(A.ACT_CLUE_NUM, 0) AS CLUE_CREATE_NUM,
	    ifnull(A.ACT_SIGN_NUM, 0) AS ACT_SIGN_NUM,
	    ifnull(B.ACT_ORDER_NUM, 0) AS ORDER_NUM
	FROM
		t_acc_bu_activity ACT
		LEFT JOIN A ON ACT.ACTIVITY_ID = A.ACTIVITY_ID
		LEFT JOIN B ON ACT.ACTIVITY_ID = B.ACTIVITY_ID
		/*可能导致重复，ACT.DLR_CODE可能存多个门店编码*/
		LEFT JOIN t_usc_mdm_org_dlr d ON find_in_set(d.DLR_CODE, ACT.DLR_CODE) <![CDATA[ > ]]> 0
		LEFT JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
		LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
		LEFT JOIN t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
		LEFT JOIN t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
	WHERE ACT.ACTIVITY_TYPE_CODE = '1' /*市场类活动*/ AND ACT.RELEASE_STATUS_CODE = '1' /*已发布*/
	/* 活动开始时间 */
	<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
		AND ACT.BEGIN_TIME <![CDATA[ >= ]]> #{param.actBeginTimeStart}
	</if>
	<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
		AND ACT.BEGIN_TIME <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
	</if>
	<!--活动表门店区域编码存区域ID-->
	<if test="param.areaId != null and param.areaId != ''">
		AND ACT.DLR_REGION_CODE = #{param.areaId}
	</if>
	<if test="param.provinceCode != null and param.provinceCode != ''">
		AND h.PROVINCE_CODE = #{param.provinceCode}
	</if>
	<if test="param.cityCode != null and param.cityCode != ''">
		AND ACT.DLR_CITY_CODE = #{param.cityCode}
	</if>
	<if test="param.dlrCode != null and param.dlrCode != ''">
		AND ACT.DLR_CODE = #{param.dlrCode}
	</if>
	<if test="param.activityName != null and param.activityName != ''">
		AND INSTR(ACT.ACTIVITY_NAME, #{param.activityName}) <![CDATA[ > ]]> 0
	</if>
	<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
		AND c.AGENT_COMPANY_ID = #{param.agentCompanyId}
	</if>
	<if test="param.activityTypeCode != null and param.activityTypeCode != ''">
		AND ACT.ACTIVITY_TYPE_CODE = #{param.activityTypeCode}
	</if>
	<if test="param.agentCode != null and param.agentCode != ''">
		AND a.AGENT_CODE = #{param.agentCode}
	</if>
	<if test="param.agentId != null and param.agentId != ''">
		AND a.AGENT_ID = #{param.agentId}
	</if>
	<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
		AND d.DLR_CODE IN
		<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	</select>

	<!--代理商线下活动报表-->
	<select id="queryAgentOfflineActivityReport" resultType="java.util.Map">
		WITH A AS(
			SELECT
				ACC.ACTIVITY_ID,
				/*开发类活动*/
				ifnull(sum(CASE WHEN ACC.IS_CHECK_IN = '1' THEN 1 ELSE 0 END), 0) AS CHECK_IN_NUM, /*活动签到数量*/
				ifnull(sum(CASE WHEN ACC.IS_ADP_NEW_CLUE = 1 THEN 1 ELSE 0 END), 0) AS ACT_CLUE_NUM /*活动报名线索数量*/
			FROM csc.t_acc_bu_activity_customer ACC
			GROUP BY ACC.ACTIVITY_ID
		),
			 B AS (
				 SELECT
					 ACC.ACTIVITY_ID,
					 count(DISTINCT ACC.CUSTOMER_PHONE) AS ACT_ORDER_NUM /*活动订单人数*/
				 FROM csc.t_acc_bu_activity_customer ACC
						  JOIN orc.t_orc_ve_bu_sale_order_to_c ORC ON ACC.CUSTOMER_PHONE = ORC.BUY_CUST_PHONE
				 GROUP BY ACC.ACTIVITY_ID
			 )
		SELECT
			t3.AREA_NAME,/*大区*/
			h.PROVINCE_NAME,/*省份*/
			g.CITY_NAME,/*城市*/
			a.AGENT_NAME,/*代理商*/
			c.AGENT_COMPANY_NAME,/*城市公司*/
			ACT.DLR_CODE,
			d.DLR_SHORT_NAME,/*门店*/
			ACT.BEGIN_TIME,/*活动开始时间*/
			ACT.END_TIME,/*活动结束时间*/
			ACT.ACTIVITY_NAME,/*活动名称*/
			ACT.ACTIVITY_TYPE_NAME,/*活动大类*/
			ACT.ACTIVITY_SUBTYPE_NAME,/*活动小类*/

			CAST(ifnull(ACT.COLUMN29, '0') AS DECIMAL) AS ESTIMATE_PEOPLES,/*预计邀约*/
			ifnull(A.CHECK_IN_NUM, 0) AS CHECK_IN_NUM,/*实际签到人数*/
			CONCAT(TRUNCATE(ifnull(ifnull(A.CHECK_IN_NUM, 0)/CAST(ifnull(ACT.COLUMN29, '0') AS DECIMAL),0)*100,0),'%') AS CHECK_RATE,/*签约到场率*/

			CAST(ifnull(ACT.COLUMN24, '0') AS DECIMAL) AS NEW_CLUES,/*预计新增线索量*/
			ifnull(A.ACT_CLUE_NUM, 0) AS CLUE_CREATE_NUM,/*实际新增线索量*/
			CONCAT(TRUNCATE(ifnull(ifnull(A.ACT_CLUE_NUM, 0)/CAST(ifnull(ACT.COLUMN24, '0') AS DECIMAL),0)*100,0),'%') AS CLUE_RATE,/*线索完成率*/

			CAST(ifnull(ACT.COLUMN25, '0') AS DECIMAL) AS NEW_ORDERS,/*预计新增订单量*/
			ifnull(B.ACT_ORDER_NUM, 0) AS ORDER_NUM,/*实际产生订单量*/
			CONCAT(TRUNCATE(ifnull(ifnull(B.ACT_ORDER_NUM, 0)/CAST(ifnull(ACT.COLUMN25, '0') AS DECIMAL),0)*100,0),'%') AS ORDER_RATE,/*订单完成率*/

			ifnull(ROUND(ACT.BUDGET,2), 0) AS BUDGET,/*活动预算*/
			ifnull(ROUND(ACT.ACTUAL_COST,2),0) AS ACTUAL_COST/*实际活动费用*/,
		ACT.STATUS_CODE,
		ACT.STATUS_NAME,
		CASE
			WHEN ACT.HX_STATUS_CODE = 'bh' THEN '财务未审核'
			WHEN ACT.HX_STATUS_CODE = 'audited' THEN '财务已审核'
			WHEN ACT.HX_STATUS_CODE = 'AreaSh' THEN '财务未审核'
			WHEN ACT.HX_STATUS_CODE = 'MoneySh' THEN '财务未审核'
			WHEN ACT.HX_STATUS_CODE = 'AreaMgSh' THEN '财务未审核'
		else '财务未审核' end AS hxStatusName,
		case when ACT.ACTIVITY_SUPPORT ='1' then '是' else '否' end AS activitySupport,
		case when ACT.EXHIBITION_EQUIPMENT ='1' then '是' else '否' end AS exhibitionEquipment,
		ACT.EXHIBITION_EQUIPMENT_NAME,
		ACT.EXHIBITION_EQUIPMENT_AMOUNT
		FROM
			csc.t_acc_bu_activity ACT
				LEFT JOIN A ON ACT.ACTIVITY_ID = A.ACTIVITY_ID
				LEFT JOIN B ON ACT.ACTIVITY_ID = B.ACTIVITY_ID
				/*可能导致重复，ACT.DLR_CODE可能存多个门店编码*/
				LEFT JOIN mp.t_usc_mdm_org_dlr d ON find_in_set(d.DLR_CODE, ACT.DLR_CODE) <![CDATA[ > ]]> 0
				LEFT JOIN mp.t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
				LEFT JOIN mp.t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
				LEFT JOIN mp.t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
				LEFT JOIN mp.t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
				LEFT JOIN mp.t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
				LEFT JOIN mp.t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
				LEFT JOIN mp.t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		WHERE ACT.ACTIVITY_TYPE_CODE = '4' /*线下活动*/
		AND ACT.RELEASE_STATUS_CODE = '1' /*已发布*/
		/* 活动开始时间 */
		<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
			AND ACT.BEGIN_TIME <![CDATA[ >= ]]> #{param.actBeginTimeStart}
		</if>
		<if test='param.hxStatus == "1"'>
			AND (ACT.HX_STATUS_CODE in ('AreaSh','MoneySh','AreaMgSh','bh') or ACT.HX_STATUS_CODE is null )
		</if>
		<if test='param.hxStatus == "2"'>
			AND ACT.HX_STATUS_CODE ='audited'
		</if>

		<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
			AND ACT.BEGIN_TIME <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
		</if>
		<!--活动表门店区域编码存区域ID-->
		<if test="param.areaId != null and param.areaId != ''">
			AND ACT.DLR_REGION_CODE = #{param.areaId}
		</if>
		<if test="param.provinceCode != null and param.provinceCode != ''">
			AND h.PROVINCE_CODE = #{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != ''">
			AND ACT.DLR_CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND ACT.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.activityName != null and param.activityName != ''">
			AND INSTR(ACT.ACTIVITY_NAME, #{param.activityName}) <![CDATA[ > ]]> 0
		</if>
		<if test="param.activityId != null and param.activityId != ''">
			AND ACT.ACTIVITY_ID = #{param.activityId}
		</if>
		<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
			AND c.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
		<if test="param.activityTypeCode != null and param.activityTypeCode != ''">
			AND ACT.ACTIVITY_TYPE_CODE = #{param.activityTypeCode}
		</if>
		<if test="param.agentCode != null and param.agentCode != ''">
			AND a.AGENT_CODE = #{param.agentCode}
		</if>
		<if test="param.agentId != null and param.agentId != ''">
			AND a.AGENT_ID = #{param.agentId}
		</if>
		<if test = "param.dlrCodeLists != null ">
			AND d.DLR_CODE IN
			<foreach collection="param.dlrCodeLists" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<!--代理商线上投放报表-->
	<select id="queryAgentOnlineActivityReport" resultType="java.util.Map">
		WITH A AS(
			SELECT
				ACC.ACTIVITY_ID,
				/*开发类活动*/
				ifnull(sum(CASE WHEN ACC.IS_CHECK_IN = '1' THEN 1 ELSE 0 END), 0) AS CHECK_IN_NUM, /*活动签到数量*/
				ifnull(sum(CASE WHEN ACC.ACTIVITY_ID = CLUE.COLUMN8 THEN 1 ELSE 0 END), 0) AS ACT_CLUE_NUM /*活动报名线索数量*/
			FROM csc.t_acc_bu_activity_customer ACC
					 LEFT JOIN csc.t_sac_clue_info_dlr CLUE ON ACC.CUSTOMER_PHONE = CLUE.PHONE
			GROUP BY ACC.ACTIVITY_ID
		),
			 B AS (
				 SELECT
					 ACC.ACTIVITY_ID,
					 count(DISTINCT ACC.CUSTOMER_PHONE) AS ACT_ORDER_NUM /*活动订单人数*/
				 FROM csc.t_acc_bu_activity_customer ACC
						  JOIN orc.t_orc_ve_bu_sale_order_to_c ORC ON ACC.CUSTOMER_PHONE = ORC.BUY_CUST_PHONE
				 GROUP BY ACC.ACTIVITY_ID
			 )
		SELECT
			t3.AREA_NAME,/*大区*/
			h.PROVINCE_NAME,/*省份*/
			g.CITY_NAME,/*城市*/
			a.AGENT_NAME,/*代理商*/
			c.AGENT_COMPANY_NAME,/*城市公司*/
			ACT.DLR_CODE,
			d.DLR_SHORT_NAME,/*门店*/
			ACT.BEGIN_TIME,/*活动开始时间*/
			ACT.END_TIME,/*活动结束时间*/
			ACT.ACTIVITY_NAME,/*活动名称*/
			ACT.ACTIVITY_TYPE_NAME,/*活动大类*/
			ACT.ACTIVITY_SUBTYPE_NAME,/*活动小类*/

			CAST(ifnull(ACT.COLUMN22, '0') AS DECIMAL) AS EXPOSURE,/*预计曝光量*/
			CAST(ifnull(ACT.COLUMN26, '0') AS DECIMAL) AS ACTUAL_EXPOSURE,/*实际曝光量*/
			CONCAT(TRUNCATE(ifnull(CAST(ifnull(ACT.COLUMN26, '0') AS DECIMAL)/CAST(ifnull(ACT.COLUMN22, '0') AS DECIMAL),0)*100,0),'%') AS EXPOSURE_RATE,/*曝光量完成率*/

			CAST(ifnull(ACT.COLUMN23, '0') AS DECIMAL) AS HITS,/*预计点击量*/
			CAST(ifnull(ACT.COLUMN28, '0') AS DECIMAL) AS ACTUAL_HITS,/*实际点击量*/
			CONCAT(TRUNCATE(ifnull(CAST(ifnull(ACT.COLUMN28, '0') AS DECIMAL)/CAST(ifnull(ACT.COLUMN23, '0') AS DECIMAL),0)*100,0),'%') AS HITS_RATE,/*点击量完成率*/

			CAST(ifnull(ACT.COLUMN24, '0') AS DECIMAL) AS NEW_CLUES,/*预计新增线索量*/
			ifnull(A.ACT_CLUE_NUM, 0) AS CLUE_CREATE_NUM,/*实际新增线索量*/
			CONCAT(TRUNCATE(ifnull(ifnull(A.ACT_CLUE_NUM, 0)/CAST(ifnull(ACT.COLUMN24, '0') AS DECIMAL),0)*100,0),'%') AS CLUE_RATE,/*线索完成率*/

			CAST(ifnull(ACT.COLUMN25, '0') AS DECIMAL) AS NEW_ORDERS,/*预计新增订单量*/
			ifnull(B.ACT_ORDER_NUM, 0) AS ORDER_NUM,/*实际产生订单量*/
			CONCAT(TRUNCATE(ifnull(ifnull(B.ACT_ORDER_NUM, 0)/CAST(ifnull(ACT.COLUMN25, '0') AS DECIMAL),0)*100,0),'%') AS ORDER_RATE,/*订单完成率*/

			ifnull(ROUND(ACT.BUDGET,2), 0) AS BUDGET,/*活动预算*/
			ifnull(ROUND(ACT.ACTUAL_COST,2),0) AS ACTUAL_COST/*实际活动费用*/,
		ACT.STATUS_CODE,
		ACT.STATUS_NAME,
		CASE
		WHEN ACT.HX_STATUS_CODE = 'bh' THEN '财务未审核'
		WHEN ACT.HX_STATUS_CODE = 'audited' THEN '财务已审核'
		WHEN ACT.HX_STATUS_CODE = 'AreaSh' THEN '财务未审核'
		WHEN ACT.HX_STATUS_CODE = 'MoneySh' THEN '财务未审核'
		WHEN ACT.HX_STATUS_CODE = 'AreaMgSh' THEN '财务未审核'
		else '财务未审核' end AS hxStatusName,
		case when ACT.ACTIVITY_SUPPORT ='1' then '是' else '否' end AS activitySupport
		FROM
			csc.t_acc_bu_activity ACT
				LEFT JOIN A ON ACT.ACTIVITY_ID = A.ACTIVITY_ID
				LEFT JOIN B ON ACT.ACTIVITY_ID = B.ACTIVITY_ID
				/*可能导致重复，ACT.DLR_CODE可能存多个门店编码*/
				LEFT JOIN mp.t_usc_mdm_org_dlr d ON find_in_set(d.DLR_CODE, ACT.DLR_CODE) <![CDATA[ > ]]> 0
				LEFT JOIN mp.t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
				LEFT JOIN mp.t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
				LEFT JOIN mp.t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
				LEFT JOIN mp.t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
				LEFT JOIN mp.t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
				LEFT JOIN mp.t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
				LEFT JOIN mp.t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		WHERE ACT.ACTIVITY_TYPE_CODE = '3' /*线上活动*/
		AND ACT.RELEASE_STATUS_CODE = '1' /*已发布*/
		/* 活动开始时间 */
		<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
			AND ACT.BEGIN_TIME <![CDATA[ >= ]]> #{param.actBeginTimeStart}
		</if>
		<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
			AND ACT.BEGIN_TIME <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
		</if>
		<if test='param.hxStatus == "1"'>
			AND (ACT.HX_STATUS_CODE in ('AreaSh','MoneySh','AreaMgSh','bh') or ACT.HX_STATUS_CODE is null  )
		</if>
		<if test='param.hxStatus == "2"'>
			AND ACT.HX_STATUS_CODE ='audited'
		</if>

		<!--活动表门店区域编码存区域ID-->
		<if test="param.areaId != null and param.areaId != ''">
			AND ACT.DLR_REGION_CODE = #{param.areaId}
		</if>
		<if test="param.provinceCode != null and param.provinceCode != ''">
			AND h.PROVINCE_CODE = #{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != ''">
			AND ACT.DLR_CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND ACT.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.activityName != null and param.activityName != ''">
			AND INSTR(ACT.ACTIVITY_NAME, #{param.activityName}) <![CDATA[ > ]]> 0
		</if>
		<if test="param.activityId != null and param.activityId != ''">
			AND ACT.ACTIVITY_ID = #{param.activityId}
		</if>
		<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
			AND c.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
		<if test="param.activityTypeCode != null and param.activityTypeCode != ''">
			AND ACT.ACTIVITY_TYPE_CODE = #{param.activityTypeCode}
		</if>
		<if test="param.agentCode != null and param.agentCode != ''">
			AND a.AGENT_CODE = #{param.agentCode}
		</if>
		<if test="param.agentId != null and param.agentId != ''">
			AND a.AGENT_ID = #{param.agentId}
		</if>
		<if test = "param.dlrCodeLists != null">
			AND d.DLR_CODE IN
			<foreach collection="param.dlrCodeLists" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<!-- 线索指标统计报表-城市休眠线索 -->
	<select id="queryClueDormancyReport" resultType="java.util.Map">
		WITH A AS (
		SELECT
		T.PROVINCE_CODE,
		T.PROVINCE_NAME,
		T.CITY_CODE,
		T.CITY_NAME,
		T.DLR_CODE,
		IF(T.DLR_CODE = 'HOST', '城市线索', '休眠线索') AS DLR_SHORT_NAME,
		COUNT(1) AS SLEEP_CLUE_NUM, /*休眠线索数量*/
		count(DISTINCT T.PHONE) AS CLUE_NUM, /*每个门店线索总数*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM, /*新增线索数-自建*/
		sum(CASE WHEN (T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='门店自建') or T.INFO_CHAN_M_CODE = 'dlr.build' THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM1, /*新增线索数-门店自建*/
		sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='市场活动'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM2, /*新增线索数-市场活动*/
		sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='垂直网站'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM3, /*新增线索数-垂直网站*/
		sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='社交媒体'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM4, /*新增线索数-社交媒体*/
		sum(CASE WHEN T.INFO_CHAN_M_NAME IS NOT NULL and T.INFO_CHAN_M_NAME='其他渠道'  THEN 1 ELSE 0 END) AS SELF_CREATE_CLUE_NUM5, /*新增线索数-其他渠道*/
		sum(CASE WHEN L.LOOKUP_VALUE_NAME IS NOT NULL THEN 0 ELSE 1 END) AS ASSIGN_CLUE_NUM, /*新增线索数-下发*/
		sum(CASE WHEN T.STATUS_CODE = '10' THEN 1 ELSE 0 END) AS DESTROY_CLUE_NUM, /*战败数量*/
		sum(CASE WHEN T.STATUS_CODE = '10' OR T.DLR_CODE = 'HOST_SLEEP' OR T.DLR_SHORT_NAME = 'HOST_SLEEP' THEN 0 ELSE 1 END) AS VALID_CLUE_NUM, /*有效线索数量*/
		/*下发后24小时内未跟进线索数*/
		sum(CASE WHEN
		(IFNULL(T.FIRST_REVIEW_TIME, '') = '')
		OR
		(T.FIRST_REVIEW_TIME IS NOT NULL AND T.FIRST_REVIEW_TIME <![CDATA[>]]> DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY ))
		THEN 1 ELSE 0 END) AS UNREVIEW_CLUE_NUM,
		sum(CASE WHEN
		(IFNULL(T.FIRST_REVIEW_TIME, '') != '') AND T.FIRST_REVIEW_TIME <![CDATA[<=]]> DATE_ADD(T.CREATED_DATE , INTERVAL 1 DAY)
		THEN 1 ELSE 0 END) AS REVIEWED_CLUE_NUM, /*下发后24小时内已跟进线索数 首次跟进时间不为空*/
		sum(CASE WHEN T.STATUS_CODE = '1' THEN 1 ELSE 0 END) AS UNASSIGN_CLUE_NUM, /*待分配线索数*/
		sum(CASE WHEN T.STATUS_CODE NOT IN ('1') THEN 1 ELSE 0 END) AS ASSIGNED_CLUE_NUM, /*已分配线索数*/
		sum(CASE WHEN ifnull(R.OVER_REVIEW_TIME, now()) <![CDATA[<]]> now() THEN 1 ELSE 0 END) AS OVER_UNREVIEW_CLUE_NUM, /*逾期未跟进线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L0' THEN 1 ELSE 0 END) AS L0_CLUE_NUM, /*L0线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L1' THEN 1 ELSE 0 END) AS L1_CLUE_NUM, /*L1线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L2' THEN 1 ELSE 0 END) AS L2_CLUE_NUM, /*L2线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L3' THEN 1 ELSE 0 END) AS L3_CLUE_NUM, /*L3线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L4' THEN 1 ELSE 0 END) AS L4_CLUE_NUM, /*L4线索数*/
		sum(CASE WHEN T.INTEN_LEVEL_CODE = 'L5' THEN 1 ELSE 0 END) AS L5_CLUE_NUM, /*L5线索数*/
		sum(CASE WHEN T.COLUMN5 = 'Hot' THEN 1 ELSE 0 END) AS HOT_CLUE_NUM, /*Hot线索数*/
		sum(CASE WHEN T.COLUMN5 = 'Warm' THEN 1 ELSE 0 END) AS WARM_CLUE_NUM, /*Warm线索数*/
		sum(CASE WHEN T.COLUMN5 = 'Cold' THEN 1 ELSE 0 END) AS COLD_CLUE_NUM /*Cold线索数*/
		FROM t_sac_clue_info_dlr T
		LEFT JOIN t_sac_review R ON T.SERVER_ORDER = R.BILL_CODE
		LEFT JOIN t_prc_mds_lookup_value L ON T.INFO_CHAN_M_NAME = L.LOOKUP_VALUE_NAME AND L.LOOKUP_TYPE_CODE = 'ADP_CLUE_049' and L.ATTRIBUTE1='agent'
		WHERE T.DLR_CODE IN ('HOST', 'HOST_SLEEP') /*非城市，非休眠线索*/
		<if test="param.startTime != null and param.startTime != '' ">
			AND T.CREATED_DATE <![CDATA[>=]]> #{param.startTime}
		</if>
		<if test="param.endTime != null and param.endTime != '' ">
			AND T.CREATED_DATE <![CDATA[<=]]> #{param.endTime}
		</if>
		<if test="param.provinceCode != null and param.provinceCode != '' ">
			AND T.PROVINCE_CODE =#{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != '' ">
			AND T.CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.cityCodeIn != null and param.cityCodeIn != '' ">
			AND T.CITY_CODE in
			<foreach item="item" collection=" param.cityCodeIn.split(',')" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrShortName !=null and ''!=param.dlrShortName">
			AND IF(T.DLR_CODE = 'HOST', '城市线索', '休眠线索') =#{param.dlrShortName}
		</if>
		GROUP BY T.PROVINCE_CODE,T.PROVINCE_NAME,T.CITY_CODE,T.CITY_NAME, T.DLR_CODE
		)
		SELECT
		t3.AREA_CODE,
		t3.AREA_NAME,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.CLUE_NUM AS CLUE_TOTAL_NUM, /*线索总量*/
		A.SELF_CREATE_CLUE_NUM,
		A.SELF_CREATE_CLUE_NUM1,
		A.SELF_CREATE_CLUE_NUM2,
		A.SELF_CREATE_CLUE_NUM3,
		A.SELF_CREATE_CLUE_NUM4,
		A.SELF_CREATE_CLUE_NUM5,
		A.ASSIGN_CLUE_NUM,
		A.DESTROY_CLUE_NUM,
		round(A.DESTROY_CLUE_NUM / A.CLUE_NUM, 2) AS DESTROY_CLUE_RATIO, /*战败率*/
		A.SLEEP_CLUE_NUM,
		A.VALID_CLUE_NUM,
		A.REVIEWED_CLUE_NUM,
		A.UNREVIEW_CLUE_NUM,
		round(A.REVIEWED_CLUE_NUM / A.CLUE_NUM, 2) AS REVIEW_RATIO, /*24小时跟进率*/
		A.UNASSIGN_CLUE_NUM,
		A.ASSIGNED_CLUE_NUM,
		A.OVER_UNREVIEW_CLUE_NUM,
		A.L0_CLUE_NUM,
		A.L1_CLUE_NUM,
		A.L2_CLUE_NUM,
		A.L3_CLUE_NUM,
		A.L4_CLUE_NUM,
		A.L5_CLUE_NUM,
		A.HOT_CLUE_NUM,
		A.WARM_CLUE_NUM,
		A.COLD_CLUE_NUM
		FROM A
		LEFT JOIN t_usc_area_relation t2 ON A.PROVINCE_CODE = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		WHERE 1 = 1
		<if test="param.areaCode != null and param.areaCode != '' ">
			AND t3.AREA_CODE = #{param.areaCode}
		</if>
	</select>

	<!--活动报表-->
	<select id="queryAllActivityReport" resultType="java.util.Map">
		select  * from (
		SELECT
		t3.AREA_NAME,/*大区*/
		d.DLR_SHORT_NAME,/*门店名称*/
		h.PROVINCE_NAME,
		g.CITY_NAME,/*城市*/
		a.AGENT_NAME,/*代理商*/
		c.AGENT_COMPANY_NAME,/*城市公司*/
		ACT.DLR_CODE,/*门店编码*/
		ACT.CREATED_NAME,/*发起人*/
		ACT.ACTIVITY_NAME,/*活动名称*/
		ACT.ACTIVITY_TYPE_CODE,/*活动类型编码*/
		ACT.ACTIVITY_TYPE_NAME,/*活动类型名称*/
		ACT.ACTIVITY_SUBTYPE_CODE,
		ACT.ACTIVITY_SUBTYPE_NAME,
		ACT.PUBLISH_TIME,/*活动发布时间*/
		ACT.BEGIN_TIME,/*活动开始时间*/
		ACT.END_TIME,/*活动结束时间*/
		T.CUSTOMER_NAME,/*用户名*/
		T.CUSTOMER_PHONE phone,
		INSERT(T.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE,
		T.COLUMN4 userIdentity,/*用户身份*/
		GROUP_CONCAT(v.LOOKUP_VALUE_NAME) userIdentityCn,
		i.smart_id  customerId,/*smartid*/
		T.COLUMN3 enorllChannel,/*报名渠道*/
		(case when ACT.CREATE_TYPE_CODE='DEVELOP' then UNIX_TIMESTAMP(T.CREATED_DATE) else T.apply_time end ) as APPLY_TIME,/*报名时间*/
		FROM_UNIXTIME((case when ACT.CREATE_TYPE_CODE='DEVELOP' then UNIX_TIMESTAMP(T.CREATED_DATE) else LEFT(T.apply_time,LENGTH(T.apply_time)-3) end),'%Y-%m-%d %H:%i:%s') as APPLY_TIME_TWO,/*报名时间*/
		T.COLUMN1 ticketType,/*报名票种*/
		T.COLUMN2 ticketNum,/*报名票数*/
		(case when ACT.CREATE_TYPE_CODE='DEVELOP' then '1' else T.IS_CHECK_IN end ) as IS_CHECK_IN,/*是否签到*/
		(case (case when ACT.CREATE_TYPE_CODE='DEVELOP' then '1' else T.IS_CHECK_IN end ) when '1' then '已签到' else '未签到' end)  IS_CHECK_IN_CN,
		(case when ACT.CREATE_TYPE_CODE='DEVELOP' then UNIX_TIMESTAMP(T.CREATED_DATE) else T.SIGN_IN_TIME end ) as SIGN_IN_TIME,/*签到时间*/
		FROM_UNIXTIME((case when ACT.CREATE_TYPE_CODE='DEVELOP' then UNIX_TIMESTAMP(T.CREATED_DATE) else LEFT(T.SIGN_IN_TIME,LENGTH(T.SIGN_IN_TIME)-3) end ),'%Y-%m-%d %H:%i:%s') SIGN_IN_TIME_TWO,/*签到时间*/
		(case when scr.CUSTOMER_ID is null then '否' else '是' end ) as isCompleteCarApply,/*是否完成试驾*/
		(case when ISNULL(i.COLUMN1) then '否' else '是' end) as newCustFlagCn/*是否为新增用户*/
		/*活动评价*/
		/*,IFNULL(K.dlSignNum,0) AS dlSignNum
		,IFNULL(K.yhSignNum,0) AS yhSignNum*/
		,ACT.CREATE_TYPE_CODE
		,T.sign_num
		,(case when ACT.CREATE_TYPE_CODE='DEVELOP' then T.sign_num end) as dlSignNum
		,(case when ACT.CREATE_TYPE_CODE  <![CDATA[ <> ]]> 'DEVELOP' then T.sign_num end) as yhSignNum,
		'' as isPlaceOrder
		FROM
		csc.t_acc_bu_activity_customer T
		left join csc.t_sac_onecust_info i on T.CUSTOMER_PHONE=i.PHONE
		left join (SELECT CUSTOMER_ID FROM csc.t_sac_test_drive_sheet where TEST_TYPE in ('1','2') and TEST_STATUS='2' GROUP BY CUSTOMER_ID) scr on scr.CUSTOMER_ID=i.CUST_ID
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		/*可能导致重复，ACT.DLR_CODE可能存多个门店编码*/
		LEFT JOIN mp.t_usc_mdm_org_dlr d ON find_in_set(d.DLR_CODE, ACT.DLR_CODE) <![CDATA[ > ]]> 0
		LEFT JOIN mp.t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
		LEFT JOIN mp.t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
		LEFT JOIN mp.t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
		LEFT JOIN mp.t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN mp.t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN mp.t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN mp.t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		/*left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from t_acc_bu_activity_customer T
		    LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		    K ON K.CUSTOMER_PHONE =T.CUSTOMER_PHONE*/
		left join mp.t_prc_mds_lookup_value v on LOOKUP_TYPE_CODE = 'ACTIVITY_APPLY_IDENTITY_LIMIT' and FIND_IN_SET(v.LOOKUP_VALUE_CODE,T.COLUMN4)>0
		WHERE T.IS_ENABLE='1' AND ACT.RELEASE_STATUS_CODE = '1' /*已发布*/
		/* 活动开始时间 */
		<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
			AND ACT.BEGIN_TIME <![CDATA[ >= ]]> #{param.actBeginTimeStart}
		</if>
		<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
			AND ACT.BEGIN_TIME <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
		</if>
		<if test="param.actEndTimeStart != null and param.actEndTimeStart != ''">
			AND ACT.END_TIME <![CDATA[ >= ]]> #{param.actEndTimeEnd}
		</if>
		<if test="param.actEndTimeEnd != null and param.actEndTimeEnd != ''">
			AND ACT.END_TIME <![CDATA[ <= ]]> #{param.actEndTimeEnd}
		</if>
		<if test="param.inCreateTypeCodeList !=null">
			and ACT.CREATE_TYPE_CODE in
			<foreach collection="param.inCreateTypeCodeList"  item="item" open="(" close=")" separator="," >
				#{item}
			</foreach>
		</if>
		<if test="param.activityName !=null and param.activityName !=''">
		  	and INSTR(ACT.ACTIVITY_NAME,#{param.activityName})>0
		</if>
		<!--活动表门店区域编码存区域ID-->
		<if test="param.areaId != null and param.areaId != ''">
			AND ACT.DLR_REGION_CODE = #{param.areaId}
		</if>
		<if test="param.provinceCode != null and param.provinceCode != ''">
			AND h.PROVINCE_CODE = #{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != ''">
			AND ACT.DLR_CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND ACT.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
			AND c.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
		<if test="param.agentCode != null and param.agentCode != ''">
			AND a.AGENT_CODE = #{param.agentCode}
		</if>
		<if test="param.agentId != null and param.agentId != ''">
			AND a.AGENT_ID = #{param.agentId}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND d.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND T.sign_num = #{param.yhSignNum}
			AND ACT.CREATE_TYPE_CODE != 'DEVELOP'
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND T.sign_num = #{param.dlSignNum}
			AND ACT.CREATE_TYPE_CODE = 'DEVELOP'
		</if>

		<if test="param.activitySubtypeCode !=null and param.activitySubtypeCode !=''">
			and FIND_IN_SET(#{param.activitySubtypeCode},act.ACTIVITY_SUBTYPE_CODE)
		</if>

		GROUP BY T.ACTIVITY_CUSTOMER_ID
		) T
		where 1 = 1
<!--		<if test="param.isPlaceOrder != null and param.isPlaceOrder != ''">-->
<!--			and isPlaceOrder = #{param.isPlaceOrder}-->
<!--		</if>-->
	</select>
	<select id="fetchCount" resultType="java.util.Map">
		select count(1) from (
		SELECT
		t3.AREA_NAME
		FROM
		csc.t_acc_bu_activity_customer T
		left join csc.t_sac_onecust_info i on T.CUSTOMER_PHONE=i.PHONE
		left join (SELECT CUSTOMER_ID FROM csc.t_sac_test_drive_sheet where TEST_TYPE in ('1','2') and TEST_STATUS='2' GROUP BY CUSTOMER_ID) scr on scr.CUSTOMER_ID=i.CUST_ID
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		/*可能导致重复，ACT.DLR_CODE可能存多个门店编码*/
		LEFT JOIN mp.t_usc_mdm_org_dlr d ON find_in_set(d.DLR_CODE, ACT.DLR_CODE) <![CDATA[ > ]]> 0
		LEFT JOIN mp.t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
		LEFT JOIN mp.t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
		LEFT JOIN mp.t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
		LEFT JOIN mp.t_usc_mdm_org_province h ON g.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN mp.t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN mp.t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN mp.t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		/*left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from t_acc_bu_activity_customer T
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		K ON K.CUSTOMER_PHONE =T.CUSTOMER_PHONE*/
		left join mp.t_prc_mds_lookup_value v on LOOKUP_TYPE_CODE = 'ACTIVITY_APPLY_IDENTITY_LIMIT' and FIND_IN_SET(v.LOOKUP_VALUE_CODE,T.COLUMN4)>0
		WHERE T.IS_ENABLE='1' AND ACT.RELEASE_STATUS_CODE = '1' /*已发布*/
		/* 活动开始时间 */
		<if test="param.actBeginTimeStart != null and param.actBeginTimeStart != ''">
			AND ACT.BEGIN_TIME <![CDATA[ >= ]]> #{param.actBeginTimeStart}
		</if>
		<if test="param.actBeginTimeEnd != null and param.actBeginTimeEnd != ''">
			AND ACT.BEGIN_TIME <![CDATA[ <= ]]> #{param.actBeginTimeEnd}
		</if>
		<if test="param.actEndTimeStart != null and param.actEndTimeStart != ''">
			AND ACT.END_TIME <![CDATA[ >= ]]> #{param.actEndTimeEnd}
		</if>
		<if test="param.actEndTimeEnd != null and param.actEndTimeEnd != ''">
			AND ACT.END_TIME <![CDATA[ <= ]]> #{param.actEndTimeEnd}
		</if>
		<if test="param.inCreateTypeCodeList !=null">
			and ACT.CREATE_TYPE_CODE in
			<foreach collection="param.inCreateTypeCodeList"  item="item" open="(" close=")" separator="," >
				#{item}
			</foreach>
		</if>
		<if test="param.activityName !=null and param.activityName !=''">
			and INSTR(ACT.ACTIVITY_NAME,#{param.activityName})>0
		</if>
		<!--活动表门店区域编码存区域ID-->
		<if test="param.areaId != null and param.areaId != ''">
			AND ACT.DLR_REGION_CODE = #{param.areaId}
		</if>
		<if test="param.provinceCode != null and param.provinceCode != ''">
			AND h.PROVINCE_CODE = #{param.provinceCode}
		</if>
		<if test="param.cityCode != null and param.cityCode != ''">
			AND ACT.DLR_CITY_CODE = #{param.cityCode}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND ACT.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
			AND c.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
		<if test="param.agentCode != null and param.agentCode != ''">
			AND a.AGENT_CODE = #{param.agentCode}
		</if>
		<if test="param.agentId != null and param.agentId != ''">
			AND a.AGENT_ID = #{param.agentId}
		</if>
		<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
			AND d.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND T.sign_num = #{param.yhSignNum}
			AND ACT.CREATE_TYPE_CODE != 'DEVELOP'
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND T.sign_num = #{param.dlSignNum}
			AND ACT.CREATE_TYPE_CODE = 'DEVELOP'
		</if>

		<if test="param.activitySubtypeCode !=null and param.activitySubtypeCode !=''">
			and FIND_IN_SET(#{param.activitySubtypeCode},act.ACTIVITY_SUBTYPE_CODE)
		</if>

		GROUP BY T.ACTIVITY_CUSTOMER_ID
		) T
	</select>
	<select id="findUrl" resultType="java.lang.String">
		SELECT LOOKUP_VALUE_NAME FROM mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='ADP_H5_LINK' and LOOKUP_VALUE_CODE='1'
	</select>

	<select id="findSum" resultType="int">
		SELECT LOOKUP_VALUE_CODE FROM mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='VE1031'
    </select>
	<select id="findStation" resultType="java.lang.String">
		select
			STATION_ID
		from  mp.t_usc_mdm_org_employee where EMP_ID=#{empId}
	</select>

	<select id="findAgentCompany" resultType="java.lang.String">
		SELECT
			DLR_CODE
		FROM
			mp.t_usc_mdm_org_dlr
		WHERE
			COMPANY_ID=#{orgId}
	</select>
    <select id="queryCustomerTestDriveInfo" resultType="com.ly.adp.csc.entities.vo.CustomerTestDriveVO">
		select
			 soi.SMART_ID smartID
			 ,stds.CUSTOMER_NAME customerName
			 ,stds.TEST_STATUS testStatus
			 ,stds.CAR_VIN vin
			 ,stds.START_TIME starTime
			 ,stds.END_TIME endTime
			 ,stds.RECEIVER_TIME receiverTime
			 ,stds.TEST_TYPE testType
			 ,stds.PLATE_NUMBER plateNumber
		     ,stds.SMALL_CAR_TYPE_NAME smallCarTypeName
		     ,stds.DLR_NAME dlrName
		     ,stds.SALES_CONSULTANT_NAME salesConsultantName
		FROM
			t_sac_test_drive_sheet stds
			LEFT JOIN t_sac_onecust_info soi ON stds.CUSTOMER_ID = soi.CUST_ID
		where
			soi.SMART_ID = #{smartID}
			order by stds.END_TIME desc
	</select>

	<select id="queryDccDefeatCount" resultType="java.lang.Integer">
		select count(1)
		from t_sac_clue_info_dlr
		where dlr_code in (
			SELECT
				DLR_CODE
			FROM
				mp.t_usc_mdm_org_dlr
			WHERE
				COMPANY_ID=#{param.orgId}
		) and column19 = '1' and status_code = '10'
	</select>

	<select id="getReactivationStat" resultType="com.ly.adp.csc.entities.vo.ClueReactivationStatVO">
		WITH dlr_codes AS (
			SELECT DLR_CODE
			FROM mp.t_usc_mdm_org_dlr
			WHERE COMPANY_ID = #{companyID}
		)
		SELECT
			SUM(reactivationCount) AS reactivationCount,
			SUM(dccReactivationCount) AS dccReactivationCount
		FROM (
				 SELECT COUNT(1) AS reactivationCount, 0 AS dccReactivationCount
				 FROM t_sac_review_audit a
						  INNER JOIN t_sac_clue_info_dlr r ON a.review_id = r.review_id
				 WHERE r.DLR_CODE IN (SELECT DLR_CODE FROM dlr_codes)
				   AND a.is_enable = '1'
				   AND a.sh_status = '1'
				   AND r.COLUMN20 IS NULL
				 UNION ALL
				 SELECT 0 AS reactivationCount, COUNT(1) AS dccReactivationCount
				 FROM t_sac_clue_info_dlr r
				 WHERE r.DLR_CODE IN (SELECT DLR_CODE FROM dlr_codes)
				   AND r.column19 = '1'
				   AND r.status_code = '10'
			 ) tmp
	</select>
</mapper>
