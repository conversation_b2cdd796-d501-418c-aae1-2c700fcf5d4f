<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrMapper">


	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacClueInfoDlr">
		<id column="ID" property="id" />
		<result column="SERVER_ORDER" property="serverOrder" />
		<result column="PV_SERVER_ORDER" property="pvServerOrder" />
		<result column="CUST_ID" property="custId" />
		<result column="CUST_NAME" property="custName" />
		<result column="PHONE" property="phone" />
		<result column="PHONE_BACKUP" property="phoneBackup" />
		<result column="INTEN_LEVEL_CODE" property="intenLevelCode" />
		<result column="INTEN_LEVEL_NAME" property="intenLevelName" />
		<result column="INTEN_BRAND_CODE" property="intenBrandCode" />
		<result column="INTEN_BRAND_NAME" property="intenBrandName" />
		<result column="INTEN_SERIES_CODE" property="intenSeriesCode" />
		<result column="INTEN_SERIES_NAME" property="intenSeriesName" />
		<result column="INTEN_CAR_TYPE_CODE" property="intenCarTypeCode" />
		<result column="INTEN_CAR_TYPE_NAME" property="intenCarTypeName" />
		<result column="INTEN_OPTION_PACKAGE_CODE" property="intenOptionPackageCode" />
		<result column="INTEN_OPTION_PACKAGE_NAME" property="intenOptionPackageName" />
		<result column="INNER_COLOR_CODE" property="innerColorCode" />
		<result column="INNER_COLOR_NAME" property="innerColorName" />
		<result column="OUT_COLOR_CODE" property="outColorCode" />
		<result column="OUT_COLOR_NAME" property="outColorName" />
		<result column="DLR_CODE" property="dlrCode" />
		<result column="DLR_SHORT_NAME" property="dlrShortName" />
		<result column="SOURCE_SYSTEMT_CODE" property="sourceSystemtCode" />
		<result column="SOURCE_SYSTEMT_NAME" property="sourceSystemtName" />
		<result column="RECEIVE_TIME" property="receiveTime" />
		<result column="SOURCE_SERVER_ORDER" property="sourceServerOrder" />
		<result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
		<result column="INFO_CHAN_M_NAME" property="infoChanMName" />
		<result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
		<result column="INFO_CHAN_D_NAME" property="infoChanDName" />
		<result column="INFO_CHAN_DD_CODE" property="infoChanDdCode" />
		<result column="INFO_CHAN_DD_NAME" property="infoChanDdName" />
		<result column="CHANNEL_CODE" property="channelCode" />
		<result column="CHANNEL_NAME" property="channelName" />
		<result column="GENDER_CODE" property="genderCode" />
		<result column="GENDER_NAME" property="genderName" />
		<result column="STATUS_CODE" property="statusCode" />
		<result column="STATUS_NAME" property="statusName" />
		<result column="DEAL_NODE_CODE" property="dealNodeCode" />
		<result column="DEAL_NODE_NAME" property="dealNodeName" />
		<result column="REVIEW_ID" property="reviewId" />
		<result column="FIRST_REVIEW_TIME" property="firstReviewTime" />
		<result column="LAST_REVIEW_TIME" property="lastReviewTime" />
		<result column="COLUMN1" property="column1" />
		<result column="COLUMN2" property="column2" />
		<result column="COLUMN3" property="column3" />
		<result column="COLUMN4" property="column4" />
		<result column="COLUMN5" property="column5" />
		<result column="COLUMN6" property="column6" />
		<result column="COLUMN7" property="column7" />
		<result column="COLUMN8" property="column8" />
		<result column="COLUMN9" property="column9" />
		<result column="COLUMN10" property="column10" />
		<result column="COLUMN11" property="column11" />
		<result column="COLUMN12" property="column12" />
		<result column="COLUMN13" property="column13" />
		<result column="COLUMN14" property="column14" />
		<result column="COLUMN15" property="column15" />
		<result column="COLUMN16" property="column16" />
		<result column="COLUMN17" property="column17" />
		<result column="COLUMN18" property="column18" />
		<result column="COLUMN19" property="column19" />
		<result column="COLUMN20" property="column20" />
		<result column="BIG_COLUMN1" property="bigColumn1" />
		<result column="BIG_COLUMN2" property="bigColumn2" />
		<result column="BIG_COLUMN3" property="bigColumn3" />
		<result column="BIG_COLUMN4" property="bigColumn4" />
		<result column="BIG_COLUMN5" property="bigColumn5" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
		<result column="ASSIGN_TIME" property="assignTime" />
		<result column="REVIEW_PERSON_NAME" property="reviewPersonName" />
		<result column="REVIEW_PERSON_ID" property="reviewPersonId" />
		<result column="PROVINCE_CODE" property="provinceCode" />
        <result column="PROVINCE_NAME" property="provinceName" />
        <result column="CITY_CODE" property="cityCode" />
        <result column="CITY_NAME" property="cityName" />
        <result column="COUNTY_CODE" property="countyCode" />
        <result column="COUNTY_NAME" property="countyName" />
		<result column="CITY_FIRM_CODE" property="cityFirmCode" />
		<result column="CITY_FIRM_NAME" property="cityFirmName" />
        <result column="MANAGE_LABEL_CODE" property="manageLabelCode" />
        <result column="MANAGE_LABEL_NAME" property="manageLabelName" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.ID , t1.SERVER_ORDER , t1.PV_SERVER_ORDER, t1.CUST_ID, t1.CUST_NAME, t1.PHONE, t1.PHONE_BACKUP
		, t1.INTEN_LEVEL_CODE, t1.INTEN_LEVEL_NAME
		, t1.INTEN_BRAND_CODE, t1.INTEN_BRAND_NAME
		, t1.INTEN_SERIES_CODE, t1.INTEN_SERIES_NAME
		, t1.INTEN_CAR_TYPE_CODE, t1.INTEN_CAR_TYPE_NAME
		, t1.INTEN_OPTION_PACKAGE_CODE, t1.INTEN_OPTION_PACKAGE_NAME
		, t1.INNER_COLOR_CODE, t1.INNER_COLOR_NAME
		, t1.OUT_COLOR_CODE, t1.OUT_COLOR_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.SOURCE_SYSTEMT_CODE, t1.SOURCE_SYSTEMT_NAME
		, t1.RECEIVE_TIME
		, t1.SOURCE_SERVER_ORDER
		, t1.INFO_CHAN_M_CODE, t1.INFO_CHAN_M_NAME
		, t1.INFO_CHAN_D_CODE, t1.INFO_CHAN_D_NAME
		, t1.INFO_CHAN_DD_CODE, t1.INFO_CHAN_DD_NAME
		, t1.CHANNEL_CODE, t1.CHANNEL_NAME
		, t1.GENDER_CODE, t1.GENDER_NAME, t1.STATUS_CODE, t1.STATUS_NAME
		, t1.DEAL_NODE_CODE, t1.DEAL_NODE_NAME
		, t1.REVIEW_ID, t1.FIRST_REVIEW_TIME, t1.LAST_REVIEW_TIME
		, t1.EXTENDS_JSON, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
		, t1.ASSIGN_TIME, t1.REVIEW_PERSON_NAME, t1.REVIEW_PERSON_ID
		, t1.PROVINCE_CODE
		, t1.PROVINCE_NAME
		, t1.CITY_CODE
		, t1.CITY_NAME
		, t1.COUNTY_CODE
		, t1.COUNTY_NAME
		, t1.MANAGE_LABEL_CODE
		, t1.MANAGE_LABEL_NAME
		, t1.CITY_FIRM_CODE
		, t1.CITY_FIRM_NAME,t1.OPEN_STATUS,t1.COLUMN10 AS SMART_ID,t1.COLUMN20
	</sql>

	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.id !=null and param.id !=''">and t1.ID=#{param.id}</if>
		<if test="param.smartId !=null and param.smartId !=''">and t1.COLUMN10=#{param.smartId}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t1.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and t1.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE=#{param.infoChanDCode}</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME like concat('%',#{param.channelName},'%')</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>


		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and t1.PV_SERVER_ORDER like concat('%',#{param.pvServerOrder},'%')</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
	    <if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
	    <if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and t1.REVIEW_PERSON_NAME like concat('%', #{param.reviewPersonName}, '%')</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and t1.REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and t1.ASSIGN_TIME>=#{param.assignTimeStart}</if>
	    <if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''"><![CDATA[and t1.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
	    <if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and t1.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}</if>
	    <if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''"><![CDATA[and t1.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and t1.RECEIVE_TIME>=#{param.receiveTimeStart}</if>
	    <if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''"><![CDATA[and t1.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
	    <if test="param.statusCodeMap !=null and param.statusCodeMap !=''"><![CDATA[and t1.STATUS_CODE in (${param.statusCodeMap})]]></if>
	    <if test="param.statusCodeList !=null">
	    	and t1.STATUS_CODE in <foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
	    </if>

	   	<if test="param.provinceCode !=null and param.provinceCode !=''"> and t1.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''"> and t1.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''"> and t1.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''"> and t1.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''"> and t1.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''"> and t1.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.cityFirmCode !=null and param.cityFirmCode !=''">and t1.CITY_FIRM_CODE=#{param.cityFirmCode}</if>
		<if test="param.cityFirmName !=null and param.cityFirmName !=''">and t1.CITY_FIRM_NAME=#{param.cityFirmName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and t1.MANAGE_LABEL_CODE=#{param.manageLabelCode}</if>
    	<if test="param.manageLabelName !=null and param.manageLabelName !=''">and t1.MANAGE_LABEL_NAME=#{param.manageLabelName}</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''"> and t1.PROVINCE_CODE IN <foreach collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''"> and t1.CITY_CODE IN <foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''"> and t1.COUNTY_CODE IN <foreach collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''"> and t1.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.cityFirmCodeIn !=null and param.cityFirmCodeIn !=''">
		 and t1.CITY_FIRM_CODE IN
		 	<foreach collection="param.cityFirmCodeIn.split(',')" item="item" separator="," open="(" close=")">
		 		 #{item}
		  	</foreach>
		    and t1.OPEN_STATUS='1' </if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''"> and t1.COLUMN2=#{param.planBuyDate}</if>
	</sql>

	<sql id="clueFields">
		ID,SERVER_ORDER,PV_SERVER_ORDER,CUST_ID,CUST_NAME,PHONE,PHONE_BACKUP,INTEN_LEVEL_CODE,INTEN_LEVEL_NAME,INTEN_BRAND_CODE,INTEN_BRAND_NAME
		,INTEN_SERIES_CODE,INTEN_SERIES_NAME,INTEN_CAR_TYPE_CODE,INTEN_CAR_TYPE_NAME,INTEN_OPTION_PACKAGE_CODE,INTEN_OPTION_PACKAGE_NAME
		,INNER_COLOR_CODE,INNER_COLOR_NAME,OUT_COLOR_CODE,OUT_COLOR_NAME,DLR_CODE,DLR_SHORT_NAME,SOURCE_SYSTEMT_CODE,SOURCE_SYSTEMT_NAME
		,RECEIVE_TIME,SOURCE_SERVER_ORDER,INFO_CHAN_M_CODE,INFO_CHAN_M_NAME,INFO_CHAN_D_CODE,INFO_CHAN_D_NAME,INFO_CHAN_DD_CODE,INFO_CHAN_DD_NAME
		,CHANNEL_CODE,CHANNEL_NAME,GENDER_CODE,GENDER_NAME,STATUS_CODE,STATUS_NAME,DEAL_NODE_CODE,DEAL_NODE_NAME,REVIEW_ID,FIRST_REVIEW_TIME
		,LAST_REVIEW_TIME,EXTENDS_JSON,COLUMN18,COLUMN19,COLUMN20,OEM_ID,GROUP_ID,CREATOR,CREATED_NAME,CREATED_DATE,MODIFIER,MODIFY_NAME,LAST_UPDATED_DATE,IS_ENABLE
		,UPDATE_CONTROL_ID,ASSIGN_TIME,REVIEW_PERSON_NAME,REVIEW_PERSON_ID,PROVINCE_CODE,PROVINCE_NAME,CITY_CODE,CITY_NAME,COUNTY_CODE,COUNTY_NAME
		,CUS_SOURCE
	</sql>

	<!-- clue 新增 -->
	<insert id="sacClueInfoDlrInsert">
		insert into adp_leads.t_sac_clue_info_dlr(
		<include refid="clueFields"></include>
		) values (
		#{param.id}
		, #{param.serverOrder}
		, #{param.pvServerOrder}
		, #{param.custId}
		, #{param.custName}
		, #{param.phone}
		, #{param.phoneBackup}
		, #{param.intenLevelCode}
		, #{param.intenLevelName}
		, #{param.intenBrandCode}
		, #{param.intenBrandName}
		, #{param.intenSeriesCode}
		, #{param.intenSeriesName}
		, #{param.intenCarTypeCode}
		, #{param.intenCarTypeName}
		, #{param.intenOptionPackageCode}
		, #{param.intenOptionPackageName}
		, #{param.innerColorCode}
		, #{param.innerColorName}
		, #{param.outColorCode}
		, #{param.outColorName}
		, #{param.dlrCode}
		, #{param.dlrShortName}
		, #{param.sourceSystemtCode}
		, #{param.sourceSystemtName}
		, #{param.receiveTime}
		, #{param.sourceServerOrder}
		, #{param.infoChanMCode}
		, #{param.infoChanMName}
		, #{param.infoChanDCode}
		, #{param.infoChanDName}
		, #{param.infoChanDdCode}
		, #{param.infoChanDdName}
		, #{param.channelCode}
		, #{param.channelName}
		, #{param.genderCode}
		, #{param.genderName}
		, #{param.statusCode}
		, #{param.statusName}
		, #{param.dealNodeCode}
		, #{param.dealNodeName}
		, #{param.reviewId}
		, #{param.firstReviewTime}
		, #{param.lastReviewTime}
		, #{param.extendsJson}
		, #{param.column18}
		, #{param.dccFlag}
		, #{param.column20}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		, #{param.assignTime}
		, #{param.reviewPersonName}
		, #{param.reviewPersonId}
		, #{param.provinceCode}
		, #{param.provinceName}
		, #{param.cityCode}
		, #{param.cityName}
		, #{param.countyCode}
		, #{param.countyName}
		, #{param.c_cus_source}
		)
	</insert>

	<!-- clue 更新 -->
	<!-- 修改 -->
	<update id="sacClueInfoDlrUpdate">
		update ${param.dbName}.t_sac_clue_info_dlr
		<set>
		LAST_UPDATED_DATE=sysdate(),
		UPDATE_CONTROL_ID=uuid(),
			<if test="param.provinceCode !=null"> PROVINCE_CODE=#{param.provinceCode},</if>
			<if test="param.provinceName !=null"> PROVINCE_NAME=#{param.provinceName},</if>
			<if test="param.cityCode !=null"> CITY_CODE=#{param.cityCode},</if>
			<if test="param.cityName !=null"> CITY_NAME=#{param.cityName},</if>
			<if test="param.countyCode !=null"> COUNTY_CODE=#{param.countyCode},</if>
			<if test="param.countyName !=null"> COUNTY_NAME=#{param.countyName},</if>

			<if test="param.id !=null and param.id !=''"> ID=#{param.id},</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''"> SERVER_ORDER=#{param.serverOrder},</if>
			<if test="param.openStatus !=null and param.openStatus !=''"> OPEN_STATUS=#{param.openStatus},</if>
			<if test="param.pvServerOrder !=null and param.pvServerOrder !=''"> PV_SERVER_ORDER=#{param.pvServerOrder},</if>
			<if test="param.custId !=null and param.custId !=''"> CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''"> CUST_NAME=#{param.custName},</if>
			<if test="param.phone !=null and param.phone !=''"> PHONE=#{param.phone},</if>
			<if test="param.phoneBackup !=null and param.phoneBackup !=''"> PHONE_BACKUP=#{param.phoneBackup},</if>
			<if test="param.intenLevelCode !=null and param.intenLevelCode !=''"> INTEN_LEVEL_CODE=#{param.intenLevelCode},</if>
			<if test="param.intenLevelName !=null and param.intenLevelName !=''"> INTEN_LEVEL_NAME=#{param.intenLevelName},</if>
			<if test="param.intenBrandCode !=null and param.intenBrandCode !=''"> INTEN_BRAND_CODE=#{param.intenBrandCode},</if>
			<if test="param.intenBrandName !=null and param.intenBrandName !=''"> INTEN_BRAND_NAME=#{param.intenBrandName},</if>
			<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''"> INTEN_SERIES_CODE=#{param.intenSeriesCode},</if>
			<if test="param.intenSeriesName !=null and param.intenSeriesName !=''"> INTEN_SERIES_NAME=#{param.intenSeriesName},</if>
			<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''"> INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode},</if>
			<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''"> INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName},</if>
			<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''"> INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode},</if>
			<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''"> INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName},</if>
			<if test="param.innerColorCode !=null and param.innerColorCode !=''"> INNER_COLOR_CODE=#{param.innerColorCode},</if>
			<if test="param.innerColorName !=null and param.innerColorName !=''"> INNER_COLOR_NAME=#{param.innerColorName},</if>
			<if test="param.outColorCode !=null and param.outColorCode !=''"> OUT_COLOR_CODE=#{param.outColorCode},</if>
			<if test="param.outColorName !=null and param.outColorName !=''"> OUT_COLOR_NAME=#{param.outColorName},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrShortName !=null and param.dlrShortName !=''"> DLR_SHORT_NAME=#{param.dlrShortName},</if>
			<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''"> SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode},</if>
			<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''"> SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName},</if>
			<if test="param.receiveTime !=null">
			RECEIVE_TIME=case when #{param.receiveTime}='' then null else #{param.receiveTime} end,
			</if>
			<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''"> SOURCE_SERVER_ORDER=#{param.sourceServerOrder},</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''"> INFO_CHAN_M_CODE=case when INFO_CHAN_M_CODE is null then #{param.infoChanMCode} else INFO_CHAN_M_CODE end,</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''"> INFO_CHAN_M_NAME=case when INFO_CHAN_M_NAME is null then #{param.infoChanMName} else INFO_CHAN_M_NAME end,</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''"> INFO_CHAN_D_CODE=case when INFO_CHAN_D_CODE is null then #{param.infoChanDCode} else INFO_CHAN_D_CODE end,</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''"> INFO_CHAN_D_NAME=case when INFO_CHAN_D_NAME is null then #{param.infoChanDName} else INFO_CHAN_D_NAME end,</if>
			<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''"> INFO_CHAN_DD_CODE=#{param.infoChanDdCode},</if>
			<if test="param.infoChanDdName !=null and param.infoChanDdName !=''"> INFO_CHAN_DD_NAME=#{param.infoChanDdName},</if>
			<if test="param.channelCode !=null and param.channelCode !=''"> CHANNEL_CODE=case when CHANNEL_CODE is null then #{param.channelCode} else CHANNEL_CODE end,</if>
			<if test="param.channelName !=null and param.channelName !=''"> CHANNEL_NAME=case when CHANNEL_NAME is null then #{param.channelName} else CHANNEL_NAME end,</if>
			<if test="param.genderCode !=null and param.genderCode !=''"> GENDER_CODE=#{param.genderCode},</if>
			<if test="param.genderName !=null and param.genderName !=''"> GENDER_NAME=#{param.genderName},</if>
			<if test="param.statusCode !=null and param.statusCode !=''"> STATUS_CODE=#{param.statusCode},</if>
			<if test="param.statusName !=null and param.statusName !=''"> STATUS_NAME=#{param.statusName},</if>
			<if test="param.dealNodeCode !=null and param.dealNodeCode !=''"> DEAL_NODE_CODE=#{param.dealNodeCode},</if>
			<if test="param.dealNodeName !=null and param.dealNodeName !=''"> DEAL_NODE_NAME=#{param.dealNodeName},</if>
			<if test="param.reviewId !=null and param.reviewId !=''"> REVIEW_ID=#{param.reviewId},</if>
			<if test="param.firstReviewTime !=null">
			FIRST_REVIEW_TIME=case when #{param.firstReviewTime}='' then null else #{param.firstReviewTime} end,
			</if>
			<if test="param.lastReviewTime !=null">
			LAST_REVIEW_TIME=case when #{param.lastReviewTime}='' then null else #{param.lastReviewTime} end,
			</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
			<if test="param.assignTime !=null">
			ASSIGN_TIME=case when #{param.assignTime}='' then null else #{param.assignTime} end,
			</if>
			<if test="param.reviewPersonName !=null"> REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
			<if test="param.reviewPersonId !=null"> REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
		</set>
		where 1=1
		and ID=#{param.id}
		<if test="param.updateControlId!=null and param.updateControlId !=''">
		 	and UPDATE_CONTROL_ID = #{param.updateControlId}
		</if>
	</update>

	<!-- clue 更新 -->
	<!-- 批量修改 -->
	<update id="sacClueInfoDlrUpdateBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            update ${param.dbName}.t_sac_clue_info_dlr
            <set>
                LAST_UPDATED_DATE=sysdate(),
                UPDATE_CONTROL_ID=uuid(),
                <if test="param.provinceCode !=null">PROVINCE_CODE=#{param.provinceCode},</if>
                <if test="param.provinceName !=null">PROVINCE_NAME=#{param.provinceName},</if>
                <if test="param.cityCode !=null">CITY_CODE=#{param.cityCode},</if>
                <if test="param.cityName !=null">CITY_NAME=#{param.cityName},</if>
                <if test="param.countyCode !=null">COUNTY_CODE=#{param.countyCode},</if>
                <if test="param.countyName !=null">COUNTY_NAME=#{param.countyName},</if>

                <if test="param.id !=null and param.id !=''">ID=#{param.id},</if>
                <if test="param.serverOrder !=null and param.serverOrder !=''">SERVER_ORDER=#{param.serverOrder},</if>
                <if test="param.openStatus !=null and param.openStatus !=''">OPEN_STATUS=#{param.openStatus},</if>
                <if test="param.pvServerOrder !=null and param.pvServerOrder !=''">
                    PV_SERVER_ORDER=#{param.pvServerOrder},
                </if>
                <if test="param.custId !=null and param.custId !=''">CUST_ID=#{param.custId},</if>
                <if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
                <if test="param.phone !=null and param.phone !=''">PHONE=#{param.phone},</if>
                <if test="param.phoneBackup !=null and param.phoneBackup !=''">PHONE_BACKUP=#{param.phoneBackup},</if>
                <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
                    INTEN_LEVEL_CODE=#{param.intenLevelCode},
                </if>
                <if test="param.intenLevelName !=null and param.intenLevelName !=''">
                    INTEN_LEVEL_NAME=#{param.intenLevelName},
                </if>
                <if test="param.intenBrandCode !=null and param.intenBrandCode !=''">
                    INTEN_BRAND_CODE=#{param.intenBrandCode},
                </if>
                <if test="param.intenBrandName !=null and param.intenBrandName !=''">
                    INTEN_BRAND_NAME=#{param.intenBrandName},
                </if>
                <if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">
                    INTEN_SERIES_CODE=#{param.intenSeriesCode},
                </if>
                <if test="param.intenSeriesName !=null and param.intenSeriesName !=''">
                    INTEN_SERIES_NAME=#{param.intenSeriesName},
                </if>
                <if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">
                    INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode},
                </if>
                <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
                    INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName},
                </if>
                <if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">
                    INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode},
                </if>
                <if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">
                    INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName},
                </if>
                <if test="param.innerColorCode !=null and param.innerColorCode !=''">
                    INNER_COLOR_CODE=#{param.innerColorCode},
                </if>
                <if test="param.innerColorName !=null and param.innerColorName !=''">
                    INNER_COLOR_NAME=#{param.innerColorName},
                </if>
                <if test="param.outColorCode !=null and param.outColorCode !=''">OUT_COLOR_CODE=#{param.outColorCode},
                </if>
                <if test="param.outColorName !=null and param.outColorName !=''">OUT_COLOR_NAME=#{param.outColorName},
                </if>
                <if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
                <if test="param.dlrShortName !=null and param.dlrShortName !=''">DLR_SHORT_NAME=#{param.dlrShortName},
                </if>
                <if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">
                    SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode},
                </if>
                <if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">
                    SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName},
                </if>
                <if test="param.receiveTime !=null">
                    RECEIVE_TIME=case when #{param.receiveTime}='' then null else #{param.receiveTime} end,
                </if>
                <if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">
                    SOURCE_SERVER_ORDER=#{param.sourceServerOrder},
                </if>
                <if test="param.infoChanMCode !=null and param.infoChanMCode !=''">INFO_CHAN_M_CODE=case when
                    INFO_CHAN_M_CODE is null then #{param.infoChanMCode} else INFO_CHAN_M_CODE end,
                </if>
                <if test="param.infoChanMName !=null and param.infoChanMName !=''">INFO_CHAN_M_NAME=case when
                    INFO_CHAN_M_NAME is null then #{param.infoChanMName} else INFO_CHAN_M_NAME end,
                </if>
                <if test="param.infoChanDCode !=null and param.infoChanDCode !=''">INFO_CHAN_D_CODE=case when
                    INFO_CHAN_D_CODE is null then #{param.infoChanDCode} else INFO_CHAN_D_CODE end,
                </if>
                <if test="param.infoChanDName !=null and param.infoChanDName !=''">INFO_CHAN_D_NAME=case when
                    INFO_CHAN_D_NAME is null then #{param.infoChanDName} else INFO_CHAN_D_NAME end,
                </if>
                <if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">
                    INFO_CHAN_DD_CODE=#{param.infoChanDdCode},
                </if>
                <if test="param.infoChanDdName !=null and param.infoChanDdName !=''">
                    INFO_CHAN_DD_NAME=#{param.infoChanDdName},
                </if>
                <if test="param.channelCode !=null and param.channelCode !=''">CHANNEL_CODE=case when CHANNEL_CODE is
                    null then #{param.channelCode} else CHANNEL_CODE end,
                </if>
                <if test="param.channelName !=null and param.channelName !=''">CHANNEL_NAME=case when CHANNEL_NAME is
                    null then #{param.channelName} else CHANNEL_NAME end,
                </if>
                <if test="param.genderCode !=null and param.genderCode !=''">GENDER_CODE=#{param.genderCode},</if>
                <if test="param.genderName !=null and param.genderName !=''">GENDER_NAME=#{param.genderName},</if>
                <if test="param.statusCode !=null and param.statusCode !=''">STATUS_CODE=#{param.statusCode},</if>
                <if test="param.statusName !=null and param.statusName !=''">STATUS_NAME=#{param.statusName},</if>
                <if test="param.dealNodeCode !=null and param.dealNodeCode !=''">DEAL_NODE_CODE=#{param.dealNodeCode},
                </if>
                <if test="param.dealNodeName !=null and param.dealNodeName !=''">DEAL_NODE_NAME=#{param.dealNodeName},
                </if>
                <if test="param.reviewId !=null and param.reviewId !=''">REVIEW_ID=#{param.reviewId},</if>
                <if test="param.firstReviewTime !=null">
                    FIRST_REVIEW_TIME=case when #{param.firstReviewTime}='' then null else #{param.firstReviewTime} end,
                </if>
                <if test="param.lastReviewTime !=null">
                    LAST_REVIEW_TIME=case when #{param.lastReviewTime}='' then null else #{param.lastReviewTime} end,
                </if>
                <if test="param.extendsJson !=null and param.extendsJson !=''">EXTENDS_JSON=#{param.extendsJson},</if>
                <if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
                <if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
                <if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
                <if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
                <if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
                <if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
                <if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
                <if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
                <if test="param.assignTime !=null">
                    ASSIGN_TIME=case when #{param.assignTime}='' then null else #{param.assignTime} end,
                </if>
                <if test="param.reviewPersonName !=null">REVIEW_PERSON_NAME=#{param.reviewPersonName},</if>
                <if test="param.reviewPersonId !=null">REVIEW_PERSON_ID=#{param.reviewPersonId},</if>
            </set>
            where 1=1
			<choose>
				<when test="param.serverOrder!=null and param.serverOrder !=''">
					and SERVER_ORDER = #{param.serverOrder}
				</when>
				<otherwise>
					and ID=#{param.id}
				</otherwise>
			</choose>
            <if test="param.updateControlId!=null and param.updateControlId !=''">
                and UPDATE_CONTROL_ID = #{param.updateControlId}
            </if>
        </foreach>
    </update>


	<select id="selectMapById" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.id = #{param.id}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE = #{param.phone}</if>
		<if test="param.statusCode !=null and param.statusCode !=''"> and t1.STATUS_CODE = #{param.statusCode}</if>
	</select>
	<select id="selectHisById" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr_his t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.id = #{param.id}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE = #{param.phone}</if>
		<if test="param.statusCode !=null and param.statusCode !=''"> and t1.STATUS_CODE = #{param.statusCode}</if>
	</select>

<!--	根据 selectByPage 的单个查询 -->
	<select id="selectOneAndWrap" resultType="java.util.Map">
		select <include refid="Base_Column_List"/>, t1.COLUMN1 as planBuyDateName, t1.COLUMN2 as planBuyDate, t1.COLUMN3 as testDriveDateName, t1.COLUMN4 as testDriveDate
		, t1.COLUMN5 as businessHeatName, t1.COLUMN6 as businessHeatCode, t1.COLUMN7 as carPurchaseBudget, t1.COLUMN8 as activityId, t1.COLUMN9 as clueScore
		, t1.COLUMN10 as smartId, t1.COLUMN11 as isSpecial, t1.COLUMN19 as dccFlag
		from t_sac_clue_info_dlr t1
		where 1=1
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE = #{param.phone}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND t1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		limit 1
	</select>

	<!--	根据 selectByPage 的单个查询 - 新库 -->
	<select id="selectOneAndWrapNew" resultType="java.util.Map">
		select <include refid="Base_Column_List"/>, t1.COLUMN1 as planBuyDateName, t1.COLUMN2 as planBuyDate, t1.COLUMN3 as testDriveDateName, t1.COLUMN4 as testDriveDate
		, t1.COLUMN5 as businessHeatName, t1.COLUMN6 as businessHeatCode, t1.COLUMN7 as carPurchaseBudget, t1.COLUMN8 as activityId, t1.COLUMN9 as clueScore
		, t1.COLUMN10 as smartId, t1.COLUMN11 as isSpecial
		from adp_leads.t_sac_clue_info_dlr t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.ID = #{param.id}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE = #{param.phone}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND t1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		limit 1
	</select>

<!--	生成对应的select count by andy.liu -->
	<select id="fetchCount" resultType="Integer">
		select count(1) from (
		select t1.PHONE
		from t_sac_clue_info_dlr t1
		LEFT JOIN t_sac_onecust_info i on i.CUST_ID=t1.CUST_ID
		left join t_sac_review t2 on t2.REVIEW_ID=t1.REVIEW_ID
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=t1.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON t1.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
			LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE  else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where 1=1
		<if test="param.errorReasonCode !=null and param.errorReasonCode !=''" >
			AND t2.ERROR_REASON_CODE =#{param.errorReasonCode}
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.saleOrderState.split(',')" >
				#{item}
			</foreach></if>
		<include refid="where_condition"></include>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND T1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		<if test="param.openStatus !=null and param.openStatus !=''" >
			AND T1.OPEN_STATUS =#{param.openStatus}
		</if>
		<if test="param.BeginTime != null and ''!= param.BeginTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
		</if>
		<if test="param.EndTime != null and ''!= param.EndTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
		</if>
		<if test="param.createdStartTime!=null and ''!=param.createdStartTime">
			<![CDATA[ AND t1.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND t1.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
			AND t1.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
		</if>
		<if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
			AND t1.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
		</if>
		<if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
			<![CDATA[	AND instr(t1.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
		</if>
		<if test="param.newPhone !=null and param.newPhone !=''">
			and t1.PHONE = #{param.newPhone}
		</if>

		<if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">AND t2.review_person_name IN<foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and t1.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		GROUP BY t1.PHONE ) tc
	</select>

	<select id="selectByPage" resultType="Map">
		select
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,/*是否完成试驾*/
		t2.ERROR_REASON_CODE,
		i.attr83,
		t2.ERROR_REASON_NAME,
		ifnull(t2.COLUMN15,'定期跟进') followReason,
		<include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr t1
		LEFT JOIN t_sac_onecust_info i on i.CUST_ID=t1.CUST_ID
		left join t_sac_review t2 on t2.REVIEW_ID=t1.REVIEW_ID
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=t1.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON t1.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
			LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE  else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where 1 = 1
		<if test="param.errorReasonCode !=null and param.errorReasonCode !=''" >
			AND t2.ERROR_REASON_CODE =#{param.errorReasonCode}
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.saleOrderState.split(',')" >
				#{item}
			</foreach></if>
		<include refid="where_condition"></include>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND T1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		<if test="param.openStatus !=null and param.openStatus !=''" >
			AND T1.OPEN_STATUS =#{param.openStatus}
		</if>
		<if test="param.BeginTime != null and ''!= param.BeginTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
		</if>
		<if test="param.EndTime != null and ''!= param.EndTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
		</if>
		<if test="param.createdStartTime!=null and ''!=param.createdStartTime">
			<![CDATA[ AND t1.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND t1.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
			AND t1.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
		</if>
		<if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
			AND t1.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
		</if>
		<if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
			<![CDATA[	AND instr(t1.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
		</if>
		<if test="param.newPhone !=null and param.newPhone !=''">
			and t1.PHONE = #{param.newPhone}
		</if>

		<if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">AND t2.review_person_name IN<foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and t1.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.filterDccFlag">
			AND t1.COLUMN19 IS NULL
		</if>
		/*非城市，非休眠线索*/
		GROUP BY t1.phone
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="selectMap" resultType="Map">
		select
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,/*是否完成试驾*/
		t2.ERROR_REASON_CODE,
		i.attr83,
		t2.ERROR_REASON_NAME,
		ifnull(t2.COLUMN15,'定期跟进') followReason,
		<include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr t1
		LEFT JOIN t_sac_onecust_info i on i.CUST_ID=t1.CUST_ID
		left join t_sac_review t2 on t2.REVIEW_ID=t1.REVIEW_ID
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=t1.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON t1.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
			LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE  else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where 1=1
		<if test="param.errorReasonCode !=null and param.errorReasonCode !=''" >
			AND t2.ERROR_REASON_CODE =#{param.errorReasonCode}
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.saleOrderState.split(',')" >
				#{item}
			</foreach></if>
		<include refid="where_condition"></include>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND T1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		<if test="param.openStatus !=null and param.openStatus !=''" >
			AND T1.OPEN_STATUS =#{param.openStatus}
		</if>
		<if test="param.BeginTime != null and ''!= param.BeginTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
		</if>
		<if test="param.EndTime != null and ''!= param.EndTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
		</if>
		<if test="param.createdStartTime!=null and ''!=param.createdStartTime">
			<![CDATA[ AND t1.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND t1.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
			AND t1.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
		</if>
		<if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
			AND t1.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
		</if>
		<if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
			<![CDATA[	AND instr(t1.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
		</if>
		<if test="param.newPhone !=null and param.newPhone !=''">
			and t1.PHONE = #{param.newPhone}
		</if>

		<if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">AND t2.review_person_name IN<foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and t1.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		/*非城市，非休眠线索*/
		GROUP BY t1.phone
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="selectByPageSpecifyFields" resultType="Map">
		select
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,/*是否完成试驾*/
		t2.ERROR_REASON_CODE,
		i.attr83,
		t2.ERROR_REASON_NAME,
		ifnull(t2.COLUMN15,'定期跟进') followReason,
		<include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr t1
		LEFT JOIN t_sac_onecust_info i on i.CUST_ID=t1.CUST_ID
		left join t_sac_review t2 on t2.REVIEW_ID=t1.REVIEW_ID
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=t1.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON t1.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
			LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE  else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where 1=1
		<if test="param.errorReasonCode !=null and param.errorReasonCode !=''" >
			AND t2.ERROR_REASON_CODE =#{param.errorReasonCode}
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.saleOrderState.split(',')" >
				#{item}
			</foreach></if>
		<include refid="where_condition"></include>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND T1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		<if test="param.openStatus !=null and param.openStatus !=''" >
			AND T1.OPEN_STATUS =#{param.openStatus}
		</if>
		<if test="param.BeginTime != null and ''!= param.BeginTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
		</if>
		<if test="param.EndTime != null and ''!= param.EndTime">
			AND DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
		</if>
		<if test="param.createdStartTime!=null and ''!=param.createdStartTime">
			<![CDATA[ AND t1.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND t1.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
			AND t1.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
		</if>
		<if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
			AND t1.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
		</if>
		<if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
			<![CDATA[	AND instr(t1.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
		</if>
		<if test="param.newPhone !=null and param.newPhone !=''">
			and t1.PHONE = #{param.newPhone}
		</if>

		<if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">AND t2.review_person_name IN<foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">#{item}</foreach></if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and t1.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		/*非城市，非休眠线索*/
		GROUP BY t1.phone
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="selectHisByPage" resultType="Map">
		select
		t2.ERROR_REASON_CODE,
		i.attr83,
		t2.ERROR_REASON_NAME,
		ifnull(t2.COLUMN15,'定期跟进') followReason,
		<include refid="Base_Column_List"></include>
		from t_sac_clue_info_dlr_his t1
		LEFT JOIN t_sac_onecust_info i on i.CUST_ID=t1.CUST_ID
		left join t_sac_review t2 on t2.REVIEW_ID=t1.REVIEW_ID
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON t1.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
			LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE  else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where 1=1
		<if test="param.errorReasonCode !=null and param.errorReasonCode !=''" >
			AND t2.ERROR_REASON_CODE =#{param.errorReasonCode}
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[ AND instr(i.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''" >
			AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index"  open="(" separator="," close=")" collection="param.saleOrderState.split(',')" >
				#{item}
			</foreach>
		</if>
		<include refid="where_condition"></include>
		<if test="param.openStatus !=null and param.openStatus !=''" >
			AND T1.OPEN_STATUS =#{param.openStatus}
		</if>
		<if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
		  AND t2.review_person_name IN
		      <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">
		      	#{item}
			  </foreach>
		</if>
		<if test="param.filterDccFlag">
			AND t1.COLUMN19 IS NULL
		</if>
	    order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="checkRepeat" resultType="Map">
    	SELECT
		ID
		FROM t_sac_clue_info_dlr a
		WHERE 1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
		<if test='param.phone !=null and param.phone !="" and param.checkPhone=="1"'>and PHONE = #{param.phone}</if>
		<if test='param.checkTimeHorizon !=null and param.checkTimeHorizon !="" and param.checkTime=="1" and param.checkPhone=="1"'>
      		and DATE_SUB(NOW(), INTERVAL #{param.checkTimeHorizon} DAY) <![CDATA[<=DATE(CREATED_DATE)]]>
    	</if>
		<if test="param.customSqlString !=null and param.customSqlString !=''">
			${param.customSqlString}
		</if>
		<if test="param.jsonFieldMappingList !=null">
			<foreach item="item1" index="index" collection="param.jsonFieldMappingList" >
				and ${item1}
			</foreach>
		</if>
		<if test="param.noFieldMappingList !=null">
			<foreach item="item" index="index" collection="param.noFieldMappingList" >
			   <if test="item.columnName =='id'">and ID ${item.condition}</if>
        	   <if test="item.columnName =='serverOrder'">and SERVER_ORDER ${item.condition}</if>
			   <if test="item.columnName =='pvServerOrder'">and PV_SERVER_ORDER ${item.condition}</if>
			   <if test="item.columnName =='custId'">and CUST_ID ${item.condition}</if>
			   <if test="item.columnName =='custName'">and CUST_NAME ${item.condition}</if>
			   <if test="item.columnName =='phone'">and PHONE ${item.condition}</if>
			   <if test="item.columnName =='phoneBackup'">and PHONE_BACKUP ${item.condition}</if>
			   <if test="item.columnName =='intenLevelCode'">and INTEN_LEVEL_CODE ${item.condition}</if>
			   <if test="item.columnName =='intenLevelName'">and INTEN_LEVEL_NAME ${item.condition}</if>
			   <if test="item.columnName =='intenBrandCode'">and INTEN_BRAND_CODE ${item.condition}</if>
			   <if test="item.columnName =='intenBrandName'">and INTEN_BRAND_NAME ${item.condition}</if>
			   <if test="item.columnName =='intenSeriesCode'">and INTEN_SERIES_CODE ${item.condition}</if>
			   <if test="item.columnName =='intenSeriesName'">and INTEN_SERIES_NAME ${item.condition}</if>
			   <if test="item.columnName =='intenCarTypeCode'">and INTEN_CAR_TYPE_CODE ${item.condition}</if>
			   <if test="item.columnName =='intenCarTypeName'">and INTEN_CAR_TYPE_NAME ${item.condition}</if>
               <if test="item.columnName =='intenOptionPackageCode'">and INTEN_OPTION_PACKAGE_CODE ${item.condition}</if>
			   <if test="item.columnName =='intenOptionPackageName'">and INTEN_OPTION_PACKAGE_NAME ${item.condition}</if>
			   <if test="item.columnName =='innerColorCode'">and INNER_COLOR_CODE ${item.condition} </if>
			   <if test="item.columnName =='innerColorName'">and INNER_COLOR_NAME ${item.condition}</if>
			   <if test="item.columnName =='outColorCode'">and OUT_COLOR_CODE ${item.condition}</if>
			   <if test="item.columnName =='outColorName'">and OUT_COLOR_NAME ${item.condition}</if>
			   <if test="item.columnName =='dlrCode'">and DLR_CODE ${item.condition}</if>
			   <if test="item.columnName =='dlrShortName'">and DLR_SHORT_NAME ${item.condition}</if>
			   <if test="item.columnName =='sourceSystemtCode'">and SOURCE_SYSTEMT_CODE ${item.condition}</if>
			   <if test="item.columnName =='sourceSystemtName'">and SOURCE_SYSTEMT_NAME ${item.condition}</if>
			   <if test="item.columnName =='receiveTime'">and RECEIVE_TIME ${ item.condition}</if>
			   <if test="item.columnName =='sourceServerOrder'">and SOURCE_SERVER_ORDER ${ item.condition}</if>
			   <if test="item.columnName =='infoChanMCode'">and INFO_CHAN_M_CODE ${item.condition}</if>
			   <if test="item.columnName =='infoChanMName'">and INFO_CHAN_M_NAME ${item.condition}</if>
			   <if test="item.columnName =='infoChanDCode'">and INFO_CHAN_D_CODE ${item.condition} </if>
			   <if test="item.columnName =='infoChanDName'">and INFO_CHAN_D_NAME ${item.condition}</if>
			   <if test="item.columnName =='infoChanDdCode'">and INFO_CHAN_DD_CODE ${ item.condition}</if>
			   <if test="item.columnName =='infoChanDdName'">and INFO_CHAN_DD_NAME ${ item.condition} </if>
			   <if test="item.columnName =='channelCode'">and CHANNEL_CODE ${item.condition}</if>
			   <if test="item.columnName =='channelName'">and CHANNEL_NAME ${item.condition}</if>
			   <if test="item.columnName =='genderCode'">and GENDER_CODE ${item.condition}</if>
			   <if test="item.columnName =='genderName'">and GENDER_NAME ${item.condition}</if>
			   <if test="item.columnName =='statusCode'">and STATUS_CODE ${item.condition}</if>
			   <if test="item.columnName =='statusName'">and STATUS_NAME ${item.condition}</if>
			   <if test="item.columnName =='dealNodeCode'">and DEAL_NODE_CODE ${item.condition}</if>
			   <if test="item.columnName =='dealNodeName'">and DEAL_NODE_NAME ${item.condition}</if>
			   <if test="item.columnName =='reviewId'">and REVIEW_ID ${item.condition}</if>
			   <if test="item.columnName =='firstReviewTime'">and FIRST_REVIEW_TIME ${item.condition}</if>
			   <if test="item.columnName =='lastReviewTime'">and LAST_REVIEW_TIME ${item.condition}</if>
			   <if test="item.columnName =='extendsJson'">and EXTENDS_JSON ${item.condition}</if>
			   <if test="item.columnName =='updateControlId'">and UPDATE_CONTROL_ID ${item.condition}</if>
    		</foreach>
		</if>
	    <if test="param.id !=null and param.id !=''">and a.id!=#{param.id}</if>
    </select>

	<select id="checkRepeatNew" resultType="Map">
		SELECT
		ID
		FROM adp_leads.t_sac_clue_info_dlr a
		WHERE 1=1
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
		<if test='param.phone !=null and param.phone !="" and param.checkPhone=="1"'>and PHONE = #{param.phone}</if>
		<if test='param.checkTimeHorizon !=null and param.checkTimeHorizon !="" and param.checkTime=="1" and param.checkPhone=="1"'>
			and DATE_SUB(NOW(), INTERVAL #{param.checkTimeHorizon} DAY) <![CDATA[<=DATE(CREATED_DATE)]]>
		</if>
		<if test="param.customSqlString !=null and param.customSqlString !=''">
			${param.customSqlString}
		</if>
		<if test="param.jsonFieldMappingList !=null">
			<foreach item="item1" index="index" collection="param.jsonFieldMappingList" >
				and ${item1}
			</foreach>
		</if>
		<if test="param.noFieldMappingList !=null">
			<foreach item="item" index="index" collection="param.noFieldMappingList" >
				<if test="item.columnName =='id'">and ID ${item.condition}</if>
				<if test="item.columnName =='serverOrder'">and SERVER_ORDER ${item.condition}</if>
				<if test="item.columnName =='pvServerOrder'">and PV_SERVER_ORDER ${item.condition}</if>
				<if test="item.columnName =='custId'">and CUST_ID ${item.condition}</if>
				<if test="item.columnName =='custName'">and CUST_NAME ${item.condition}</if>
				<if test="item.columnName =='phone'">and PHONE ${item.condition}</if>
				<if test="item.columnName =='phoneBackup'">and PHONE_BACKUP ${item.condition}</if>
				<if test="item.columnName =='intenLevelCode'">and INTEN_LEVEL_CODE ${item.condition}</if>
				<if test="item.columnName =='intenLevelName'">and INTEN_LEVEL_NAME ${item.condition}</if>
				<if test="item.columnName =='intenBrandCode'">and INTEN_BRAND_CODE ${item.condition}</if>
				<if test="item.columnName =='intenBrandName'">and INTEN_BRAND_NAME ${item.condition}</if>
				<if test="item.columnName =='intenSeriesCode'">and INTEN_SERIES_CODE ${item.condition}</if>
				<if test="item.columnName =='intenSeriesName'">and INTEN_SERIES_NAME ${item.condition}</if>
				<if test="item.columnName =='intenCarTypeCode'">and INTEN_CAR_TYPE_CODE ${item.condition}</if>
				<if test="item.columnName =='intenCarTypeName'">and INTEN_CAR_TYPE_NAME ${item.condition}</if>
				<if test="item.columnName =='intenOptionPackageCode'">and INTEN_OPTION_PACKAGE_CODE ${item.condition}</if>
				<if test="item.columnName =='intenOptionPackageName'">and INTEN_OPTION_PACKAGE_NAME ${item.condition}</if>
				<if test="item.columnName =='innerColorCode'">and INNER_COLOR_CODE ${item.condition} </if>
				<if test="item.columnName =='innerColorName'">and INNER_COLOR_NAME ${item.condition}</if>
				<if test="item.columnName =='outColorCode'">and OUT_COLOR_CODE ${item.condition}</if>
				<if test="item.columnName =='outColorName'">and OUT_COLOR_NAME ${item.condition}</if>
				<if test="item.columnName =='dlrCode'">and DLR_CODE ${item.condition}</if>
				<if test="item.columnName =='dlrShortName'">and DLR_SHORT_NAME ${item.condition}</if>
				<if test="item.columnName =='sourceSystemtCode'">and SOURCE_SYSTEMT_CODE ${item.condition}</if>
				<if test="item.columnName =='sourceSystemtName'">and SOURCE_SYSTEMT_NAME ${item.condition}</if>
				<if test="item.columnName =='receiveTime'">and RECEIVE_TIME ${ item.condition}</if>
				<if test="item.columnName =='sourceServerOrder'">and SOURCE_SERVER_ORDER ${ item.condition}</if>
				<if test="item.columnName =='infoChanMCode'">and INFO_CHAN_M_CODE ${item.condition}</if>
				<if test="item.columnName =='infoChanMName'">and INFO_CHAN_M_NAME ${item.condition}</if>
				<if test="item.columnName =='infoChanDCode'">and INFO_CHAN_D_CODE ${item.condition} </if>
				<if test="item.columnName =='infoChanDName'">and INFO_CHAN_D_NAME ${item.condition}</if>
				<if test="item.columnName =='infoChanDdCode'">and INFO_CHAN_DD_CODE ${ item.condition}</if>
				<if test="item.columnName =='infoChanDdName'">and INFO_CHAN_DD_NAME ${ item.condition} </if>
				<if test="item.columnName =='channelCode'">and CHANNEL_CODE ${item.condition}</if>
				<if test="item.columnName =='channelName'">and CHANNEL_NAME ${item.condition}</if>
				<if test="item.columnName =='genderCode'">and GENDER_CODE ${item.condition}</if>
				<if test="item.columnName =='genderName'">and GENDER_NAME ${item.condition}</if>
				<if test="item.columnName =='statusCode'">and STATUS_CODE ${item.condition}</if>
				<if test="item.columnName =='statusName'">and STATUS_NAME ${item.condition}</if>
				<if test="item.columnName =='dealNodeCode'">and DEAL_NODE_CODE ${item.condition}</if>
				<if test="item.columnName =='dealNodeName'">and DEAL_NODE_NAME ${item.condition}</if>
				<if test="item.columnName =='reviewId'">and REVIEW_ID ${item.condition}</if>
				<if test="item.columnName =='firstReviewTime'">and FIRST_REVIEW_TIME ${item.condition}</if>
				<if test="item.columnName =='lastReviewTime'">and LAST_REVIEW_TIME ${item.condition}</if>
				<if test="item.columnName =='extendsJson'">and EXTENDS_JSON ${item.condition}</if>
				<if test="item.columnName =='updateControlId'">and UPDATE_CONTROL_ID ${item.condition}</if>
			</foreach>
		</if>
		<if test="param.id !=null and param.id !=''">and a.id!=#{param.id}</if>
	</select>

	<select id="selectComp" resultType="java.util.Map">
		SELECT COMPLAINTS_ID,COMPLAINTS_NO,SMART_ID,COMPLAINT_CONTENT,CUST_NAME,PHONE
		FROM t_sac_complaints_info
		WHERE COMPLAINTS_ID =#{param.complaintsId}
	</select>

	<select id="getCustId" resultType="java.lang.String">
		SELECT CUST_ID
		FROM t_sac_onecust_info
		WHERE phone =#{param.phone}
	</select>

    <select id="getDlrShortName" resultType="java.util.Map">
		select DLR_ID,DLR_CODE,DLR_SHORT_NAME
		from mp.t_usc_mdm_org_dlr WHERE DLR_CODE=#{dlrCode}
	</select>
    <select id="provinceByDlrCode" resultType="java.util.Map">
		SELECT
			p.province_code,
			p.province_name,
			ci.city_code,
			ci.city_name,
			left(dlr.COUNTY_ID,if(LOCATE(',',dlr.COUNTY_ID)=0,length(dlr.COUNTY_ID),LOCATE(',',dlr.COUNTY_ID)-1)) COUNTY_ID,
			left(co.COUNTY_NAME,if(LOCATE(',',co.COUNTY_NAME)=0,length(co.COUNTY_NAME),LOCATE(',',co.COUNTY_NAME)-1)) COUNTY_NAME
		FROM
			mp.t_usc_mdm_org_dlr dlr
			LEFT JOIN mp.t_usc_mdm_org_province p ON p.province_id = dlr.province_id
			LEFT JOIN mp.t_usc_mdm_org_city ci ON ci.CITY_ID = dlr.CITY_ID
			LEFT JOIN mp.t_usc_mdm_org_community co ON co.COUNTY_ID=dlr.COUNTY_ID
		WHERE dlr.DLR_CODE=#{param.dlrCode}
		GROUP BY dlr.DLR_ID;
	</select>

    <!-- 客户履历新增 -->
	<insert id="createOnecustResumeInfo">
		insert into t_sac_onecust_resume(
			RESUME_ID,/*履历ID*/
			CUST_ID,/*CUST_ID*/
			SMART_ID,/*SMARTID*/
			CLUE_LEVEL_CODE,/*意向级别(L0-L5)*/
			DLR_CODE_OWNER,/*数据所属门店编码*/
			DLR_NAME_OWNER,/*数据所属门店名称*/
			RESUME_PERSON_CODE,/*跟进人员编码*/
			RESUME_PERSON_NAME,/*跟进人员名称*/
			SENCE_CODE,/*场景编码 值列表:ADP_CLUE_001*/
			SENCE_NAME,/*场景名称 值列表:ADP_CLUE_001*/
			RESUME_DESC,/*客户履历内容*/
			REMARK,/*客户履历备注*/
			BUSS_TIME,/*作业时间*/
			BUSS_START_TIME,/*作业开始时间*/
			BUSS_END_TIME,/*作业结束时间*/
			RELATION_BILL_ID,/*关联单据ID*/
			OEM_ID,/*厂商标识ID*/
			GROUP_ID,/*集团标识ID*/
			OEM_CODE,/*厂商标识*/
			GROUP_CODE,/*集团标识*/
			CREATOR,/*创建人ID*/
			CREATED_NAME,/*创建人*/
			CREATED_DATE,/*创建日期*/
			MODIFIER,/*修改人ID*/
			MODIFY_NAME,/*修改人*/
			LAST_UPDATED_DATE,/*最后更新日期*/
			IS_ENABLE,/*是否可用*/
			SDP_USER_ID,/*SDP用户ID*/
			SDP_ORG_ID,/*SDP组织ID*/
			UPDATE_CONTROL_ID/*并发控制ID*/
		)value
			(
			 #{param.resumeId},
			 #{param.custId},
			 #{param.smartId},
			 #{param.clueLevelCode},
			 #{param.dlrCodeOwner},
			 #{param.dlrNameOwner},
			 #{param.resumePersonCode},
			 #{param.resumePersonName},
			 #{param.senceCode},
			 #{param.senceName},
			 #{param.resumeDesc},
			 #{param.remark},
			 #{param.bussTime},
			 #{param.bussStartTime},
			 #{param.bussEndTime},
			 #{param.relationBillId},
			 #{param.oemId},
			 #{param.groupId},
			 #{param.oemCode},
			 #{param.groupCode},
			 #{param.creator},
			 #{param.createdName},
			 #{param.createdDate},
			 #{param.modifier},
			 #{param.modifyName},
			 #{param.lastUpdatedDate},
			 #{param.isEnable},
			 #{param.sdpUserId},
			 #{param.sdpOrgId},
			 #{param.updateControlId}
				)
	</insert>

	<select id="checkData" resultType="java.util.Map">
		select
		 t1.ACTIVITY_NAME
		from t_acc_bu_activity t1
		where
		t1.ACTIVITY_ID =#{activityId}
		and t1.CREATE_TYPE_CODE = 'DEVELOP'
		and t1.END_TIME &gt; #{dates}
		  AND t1.STATUS_CODE = '2'
		  AND t1.RELEASE_STATUS_CODE = '1'
		  AND t1.IS_ENABLE = '1'
		GROUP BY t1.ACTIVITY_ID
		order by t1.LAST_UPDATED_DATE desc
    </select>

	<select id="findDlrProvince" resultType="java.util.Map">
		SELECT
			p.province_code,
			p.province_name,
			ci.city_code,
			ci.city_name,
			left(dlr.COUNTY_ID,if(LOCATE(',',dlr.COUNTY_ID)=0,length(dlr.COUNTY_ID),LOCATE(',',dlr.COUNTY_ID)-1)) COUNTY_ID,
			left(co.COUNTY_NAME,if(LOCATE(',',co.COUNTY_NAME)=0,length(co.COUNTY_NAME),LOCATE(',',co.COUNTY_NAME)-1)) COUNTY_NAME,
			dlr.dlr_Code,
			dlr.DLR_SHORT_NAME
		FROM
			mp.t_usc_mdm_org_dlr dlr
			LEFT JOIN mp.t_usc_mdm_org_province p ON p.province_id = dlr.province_id
			LEFT JOIN mp.t_usc_mdm_org_city ci ON ci.CITY_ID = dlr.CITY_ID
			LEFT JOIN mp.t_usc_mdm_org_community co ON co.COUNTY_ID=dlr.COUNTY_ID
		WHERE
			dlr.DLR_CODE=#{dlrCode}
		GROUP BY dlr.DLR_ID
	</select>

	<select id="findDlrProvinceAndCity" resultType="java.util.Map">
		SELECT
		p.province_code,
		p.province_name,
		ci.city_code,
		ci.city_name,
		left(dlr.COUNTY_ID,if(LOCATE(',',dlr.COUNTY_ID)=0,length(dlr.COUNTY_ID),LOCATE(',',dlr.COUNTY_ID)-1)) COUNTY_ID,
		left(co.COUNTY_NAME,if(LOCATE(',',co.COUNTY_NAME)=0,length(co.COUNTY_NAME),LOCATE(',',co.COUNTY_NAME)-1)) COUNTY_NAME,
		dlr.dlr_Code,
		dlr.DLR_SHORT_NAME,
		emp.emp_name,
		emp.emp_id
		FROM
		mp.t_usc_mdm_org_dlr dlr
		LEFT JOIN mp.t_usc_mdm_org_province p ON p.province_id = dlr.province_id
		LEFT JOIN mp.t_usc_mdm_org_city ci ON ci.CITY_ID = dlr.CITY_ID
		LEFT JOIN mp.t_usc_mdm_org_community co ON co.COUNTY_ID=dlr.COUNTY_ID
		LEFT JOIN mp.t_usc_mdm_org_employee emp ON emp.dlr_CODE=dlr.dlr_CODE
		WHERE
		emp.STATION_ID in ('smart_bm_0005','smart_bm_0016')
		and	dlr.DLR_CODE in
			<foreach collection="list" close=")" open="(" item="item" separator=",">
				#{item.dlrCode}
			</foreach>

		GROUP BY dlr.DLR_ID
	</select>

	<select id="findSecondaryChannels" resultType="java.util.Map">
		select LOOKUP_VALUE_CODE, LOOKUP_VALUE_NAME,ATTRIBUTE1 from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='ADP_CLUE_072'
	</select>

	<!-- clue 新增 -->
	<insert id="insertClueInfoDlr">
		insert into adp_leads.t_sac_clue_info_dlr(
										 ID
									   , SERVER_ORDER
									   , PV_SERVER_ORDER
									   , CUST_ID
									   , CUST_NAME
									   , PHONE
									   , PHONE_BACKUP
									   , INTEN_LEVEL_CODE
									   , INTEN_LEVEL_NAME
									   , INTEN_BRAND_CODE
									   , INTEN_BRAND_NAME
									   , INTEN_SERIES_CODE
									   , INTEN_SERIES_NAME
									   , INTEN_CAR_TYPE_CODE
									   , INTEN_CAR_TYPE_NAME
									   , INTEN_OPTION_PACKAGE_CODE
									   , INTEN_OPTION_PACKAGE_NAME
									   , INNER_COLOR_CODE
									   , INNER_COLOR_NAME
									   , OUT_COLOR_CODE
									   , OUT_COLOR_NAME
									   , DLR_CODE
									   , DLR_SHORT_NAME
									   , SOURCE_SYSTEMT_CODE
									   , SOURCE_SYSTEMT_NAME
									   , RECEIVE_TIME
									   , SOURCE_SERVER_ORDER
									   , INFO_CHAN_M_CODE
									   , INFO_CHAN_M_NAME
									   , INFO_CHAN_D_CODE
									   , INFO_CHAN_D_NAME
									   , INFO_CHAN_DD_CODE
									   , INFO_CHAN_DD_NAME
									   , CHANNEL_CODE
									   , CHANNEL_NAME
									   , GENDER_CODE
									   , GENDER_NAME
									   , STATUS_CODE
									   , STATUS_NAME
									   , DEAL_NODE_CODE
									   , DEAL_NODE_NAME
									   , REVIEW_ID
									   , FIRST_REVIEW_TIME
									   , LAST_REVIEW_TIME
									   , EXTENDS_JSON
									   , COLUMN19
									   , COLUMN20
									   , OEM_ID
									   , GROUP_ID
									   , CREATOR
									   , CREATED_NAME
									   , CREATED_DATE
									   , MODIFIER
									   , MODIFY_NAME
									   , LAST_UPDATED_DATE
									   , IS_ENABLE
									   , UPDATE_CONTROL_ID
									   , ASSIGN_TIME
									   , REVIEW_PERSON_NAME
									   , REVIEW_PERSON_ID
									   ,PROVINCE_CODE
									   ,PROVINCE_NAME
									   ,CITY_CODE
									   ,CITY_NAME
									   ,COUNTY_CODE
									   ,COUNTY_NAME
										,COLUMN5
										,COLUMN6

		) values
			<foreach collection="list" separator="," item="item">
				(
				uuid(),
				#{item.serverOrder},
				#{item.pvServerOrder},
				#{item.custId},
				 #{item.custName},
				 #{item.phone},
				 #{item.phoneBackup},
				 #{item.intenLevelCode},
				 #{item.intenLevelName},
				 #{item.intenBrandCode},
				#{item.intenBrandName},
				 #{item.intenSeriesCode},
				 #{item.intenSeriesName},
				 #{item.intenCarTypeCode},
				 #{item.intenCarTypeName},
				 #{item.intenOptionPackageCode},
				#{item.intenOptionPackageName},
				 #{item.innerColorCode},
				 #{item.innerColorName},
				#{item.outColorCode},
				 #{item.outColorName},
				 #{item.dlrCode},
				 #{item.dlrShortName},
				#{item.sourceSystemtCode},
				 #{item.sourceSystemtName},
				#{item.receiveTime},
				 #{item.sourceServerOrder},
				 #{item.infoChanMCode},
				 #{item.infoChanMName},
				 #{item.infoChanDCode},
				 #{item.infoChanDName},
				 #{item.infoChanDdCode},
				 #{item.infoChanDdName},
				#{item.infoChanMCode},
				#{item.infoChanMName},
				 #{item.genderCode},
				#{item.genderName},
				 #{item.statusCode},
				 #{item.statusName},
				 #{item.dealNodeCode},
				 #{item.dealNodeName},
				 #{item.reviewId},
				 #{item.firstReviewTime},
				 #{item.lastReviewTime},
				 #{item.extendsJson},
				#{item.dccFlag},
				#{item.column20},
				 #{item.oemId},
				 #{item.groupId},
				 #{empId},
				 #{empName},
				 now(),
				 #{empId},
				 #{empName},
				 now(),
				 #{item.isEnable},
				 UUID(),
				#{item.assignTime},
				 #{item.reviewPersonName},
				 #{item.reviewPersonId},
				#{item.provinceCode},
				 #{item.provinceName},
				#{item.cityCode},
				 #{item.cityName},
				#{item.countyCode},
				 #{item.countyName},
				 #{item.businessHeatName},
				 #{item.businessHeatName}
				)
			</foreach>

	</insert>

	<select id="findSecondaryChannelName" resultType="java.util.Map">
		select LOOKUP_VALUE_CODE, LOOKUP_VALUE_NAME, ATTRIBUTE1 from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='ADP_CLUE_049' and IS_ENABLE='1'
	</select>

	<insert id="insertClueInfoDlrLog">
		insert into t_sac_clue_info_dlr_log(
			ID,
			PHONE,
			SYSTEM_PARAM,
			SYSTEM_SOURCE,
			SYSTEM_RECORD,
			OEM_ID,
			GROUP_ID,
			OEM_CODE,
			GROUP_CODE,
			IS_ENABLE,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			SDP_USER_ID,
			SDP_ORG_ID,
			UPDATE_CONTROL_ID)
		value
			<foreach collection="list" item="item" separator=",">
				(
				uuid(),
				#{item.phone},
				#{item.systemParam},
				#{item.systemSource},
				#{item.systemRecord},
				'1',
				'HOST',
				'1',
				'HOST',
				'1',
				#{empId},
				#{empName},
				now(),
				#{empId},
				#{empName},
				now(),
				'1',
				'1',
				uuid()
				)
			</foreach>

	</insert>

	<insert id="insertinsertReview">

		insert into t_sac_review(
								 REVIEW_ID
								,ORG_CODE
								,ORG_NAME
								,BILL_TYPE
								,BILL_TYPE_NAME
								,BUSINESS_TYPE
								,BUSINESS_TYPE_NAME
								,INFO_CHAN_M_CODE
								,INFO_CHAN_M_NAME
								,INFO_CHAN_D_CODE
								,INFO_CHAN_D_NAME
								,INFO_CHAN_DD_CODE
								,INFO_CHAN_DD_NAME
								,CHANNEL_CODE
								,CHANNEL_NAME
								,BILL_CODE
								,PLAN_REVIEW_TIME
								,PLAN_COME_TIME
								,FACT_COME_TIME
								,IS_COME
								,REVIEW_TIME
								,LAST_REVIEW_TIME
								,OVER_REVIEW_TIME
								,ASSIGN_STATUS
								,ASSIGN_STATUS_NAME
								,ASSIGN_TIME
								,ASSIGN_PERSON_ID
								,ASSIGN_PERSON_NAME
								,REVIEW_PERSON_ID
								,REVIEW_PERSON_NAME
								,REVIEW_DESC
								,REVIEW_STATUS
								,REVIEW_STATUS_NAME
								,CUST_ID
								,CUST_NAME
								,PHONE
								,GENDER
								,GENDER_NAME
								,TOUCH_STATUS
								,TOUCH_STATUS_NAME
								,ERROR_REASON_CODE
								,ERROR_REASON_NAME
								,NODE_CODE
								,NODE_NAME
								,SEND_DLR_CODE
								,SEND_DLR_SHORT_NAME
								,SEND_TIME
								,INTEN_LEVEL_CODE
								,INTEN_LEVEL_NAME
								,INTEN_BRAND_CODE
								,INTEN_BRAND_NAME
								,INTEN_SERIES_CODE
								,INTEN_SERIES_NAME
								,INTEN_CAR_TYPE_CODE
								,INTEN_CAR_TYPE_NAME
								,COLUMN1
								,COLUMN2
								,COLUMN3
								,COLUMN4
								,COLUMN5
								,COLUMN6
								,COLUMN7
								,COLUMN8
								,COLUMN9
								,COLUMN10
								,COLUMN11
								,COLUMN12
								,COLUMN13
								,COLUMN14
								,COLUMN15
								,COLUMN16
								,COLUMN17
								,COLUMN18
								,COLUMN19
								,COLUMN20
								,BIG_COLUMN1
								,BIG_COLUMN2
								,BIG_COLUMN3
								,BIG_COLUMN4
								,BIG_COLUMN5
								,EXTENDS_JSON
								,OEM_ID
								,GROUP_ID
								,CREATOR
								,CREATED_NAME
								,CREATED_DATE
								,MODIFIER
								,MODIFY_NAME
								,LAST_UPDATED_DATE
								,IS_ENABLE
								,UPDATE_CONTROL_ID
								,PROVINCE_CODE
								,PROVINCE_NAME
								,CITY_CODE
								,CITY_NAME
								,COUNTY_CODE
								,COUNTY_NAME
		)
		values
			<foreach collection="list" separator="," item="item">
				(
				#{item.reviewId},
				#{item.dlrCode},
				#{item.dlrShortName},
				#{item.billType},
				#{item.billTypeName},
				#{item.businessType},
				#{item.businessTypeName},
				#{item.infoChanMCode},
				#{item.infoChanMName},
				#{item.infoChanDCode},
				#{item.infoChanDName},
				#{item.infoChanDdCode},
				#{item.infoChanDdName},
				#{item.infoChanMCode},
				#{item.infoChanMName},
				#{item.serverOrder},
				#{item.planReviewTime},
				#{item.planComeTime},
				#{item.factComeTime},
				#{item.isCome},
				#{item.reviewTime},
				#{item.lastReviewTime},
				#{item.planReviewTime},
				#{item.assignStatus},
				#{item.assignStatusName},
				#{item.assignTime},
				#{item.assignPersonId},
				#{item.assignPersonName},
				#{item.reviewPersonId},
				#{item.reviewPersonName},
				#{item.reviewDesc},
				#{item.reviewStatus},
				#{item.reviewStatusName},
				#{item.custId},
				#{item.custName},
				#{item.phone},
				#{item.genderCode},
				#{item.genderName},
				#{item.touchStatus},
				#{item.touchStatusName},
				#{item.errorReasonCode},
				#{item.errorReasonName},
				#{item.nodeCode},
				#{item.nodeName},
				#{item.sendDlrCode},
				#{item.sendDlrShortName},
				#{item.sendTime},
				#{item.intenLevelCode},
				#{item.intenLevelName},
				#{item.intenBrandCode},
				#{item.intenBrandName},
				#{item.intenSeriesCode},
				#{item.intenSeriesName},
				#{item.intenCarTypeCode},
				#{item.intenCarTypeName},
				#{item.column1},
				#{item.column2},
				#{item.column3},
				#{item.column4},
				#{item.column5},
				#{item.column6},
				#{item.businessHeatName},
				#{item.businessHeatName},
				#{item.column9},
				#{item.column10},
				#{item.column11},
				#{item.column12},
				#{item.column13},
				#{item.column14},
				#{item.column15},
				#{item.column16},
				#{item.column17},
				#{item.column18},
				#{item.dccFlag},
				#{item.column20},
				#{item.bigColumn1},
				#{item.bigColumn2},
				#{item.bigColumn3},
				#{item.bigColumn4},
				#{item.bigColumn5},
				#{item.extendsJson},
				'1',
				'HOST',
				#{empId},
				#{empName},
				now(),
				#{empId},
				#{empName},
				now(),
				'1',
				uuid(),
				#{item.provinceCode},
				#{item.provinceName},
				#{item.cityCode},
				#{item.cityName},
				#{item.countyCode},
				#{item.countyName}
				)
			</foreach>
	</insert>

	<insert id="insertcustResumeInfo">
		insert into t_sac_onecust_resume(
			RESUME_ID,/*履历ID*/
			CUST_ID,/*CUST_ID*/
			SMART_ID,/*SMARTID*/
			CLUE_LEVEL_CODE,/*意向级别(L0-L5)*/
			DLR_CODE_OWNER,/*数据所属门店编码*/
			DLR_NAME_OWNER,/*数据所属门店名称*/
			RESUME_PERSON_CODE,/*跟进人员编码*/
			RESUME_PERSON_NAME,/*跟进人员名称*/
			SENCE_CODE,/*场景编码 值列表:ADP_CLUE_001*/
			SENCE_NAME,/*场景名称 值列表:ADP_CLUE_001*/
			RESUME_DESC,/*客户履历内容*/
			REMARK,/*客户履历备注*/
			BUSS_TIME,/*作业时间*/
			BUSS_START_TIME,/*作业开始时间*/
			BUSS_END_TIME,/*作业结束时间*/
			RELATION_BILL_ID,/*关联单据ID*/
			OEM_ID,/*厂商标识ID*/
			GROUP_ID,/*集团标识ID*/
			OEM_CODE,/*厂商标识*/
			GROUP_CODE,/*集团标识*/
			CREATOR,/*创建人ID*/
			CREATED_NAME,/*创建人*/
			CREATED_DATE,/*创建日期*/
			MODIFIER,/*修改人ID*/
			MODIFY_NAME,/*修改人*/
			LAST_UPDATED_DATE,/*最后更新日期*/
			IS_ENABLE,/*是否可用*/
			SDP_USER_ID,/*SDP用户ID*/
			SDP_ORG_ID,/*SDP组织ID*/
			UPDATE_CONTROL_ID,/*并发控制ID*/
		COLUMN5,
		COLUMN6
		)values
			<foreach collection="list" item="item" separator=",">
				(
				#{item.resumeId},
				#{item.custId},
				#{item.smartId},
				#{item.clueLevelCode},
				#{item.dlrCodeOwner},
				#{item.dlrNameOwner},
				#{item.resumePersonCode},
				#{item.resumePersonName},
				#{item.senceCode},
				#{item.senceName},
				#{item.resumeDesc},
				#{item.remark},
				#{item.bussTime},
				#{item.bussStartTime},
				#{item.bussEndTime},
				#{item.relationBillId},
				'1',
				'HOST',
				'1',
				'HOST',
				#{empId},
				#{empName},
				now(),
				#{empId},
				#{empName},
				now(),
				#{item.isEnable},
				#{item.sdpUserId},
				#{item.sdpOrgId},
				uuid(),
				#{item.businessHeatName},
				#{item.businessHeatName}
				)
			</foreach>

	</insert>

	<select id="checkClueInfoDlr" resultType="java.lang.Boolean">
		select exists (
					select  1
			from
				t_sac_clue_info_dlr
			where
			PHONE=#{phone}
					)
	</select>

	<insert id="insertOnecustInfo">
	INSERT INTO `t_sac_onecust_info` (
	`CUST_ID`,
	`SMART_ID`,
	`CUST_NAME`,
	`GENDER_CODE`,
	`GENDER_NAME`,
	`NICK_NAME`,
	`PHONE`,
	`PHONE_STANDBY`,
	`EMAIL`,
	`WECHAT`,
	`ID_CARD`,
	`DRIVER_CARD`,
	`DRIVING_LICENSE`,
	`PASSPORT`,
	`source5`,
	`source6`,
	`attr83`,
	`COMPLAINTS_LABEL`,
	`FREE_LABEL`,
	`USER_HOBBIES_CODE`,
	`USER_HOBBIES_NAME`,
	`CHARACTERISTICS_CODE`,
	`CHARACTERISTICS_NAME`,
	`ONE_CUST_SOURCE`,
	`EXTEND_JSON`,
	`COLUMN1`,
	`COLUMN2`,
	`COLUMN3`,
	`COLUMN4`,
	`COLUMN5`,
	`COLUMN6`,
	`COLUMN7`,
	`COLUMN8`,
	`COLUMN9`,
	`COLUMN10`,
	`OEM_ID`,
	`GROUP_ID`,
	`OEM_CODE`,
	`GROUP_CODE`,
	`CREATOR`,
	`CREATED_NAME`,
	`CREATED_DATE`,
	`MODIFIER`,
	`MODIFY_NAME`,
	`LAST_UPDATED_DATE`,
	`IS_ENABLE`,
	`SDP_USER_ID`,
	`SDP_ORG_ID`,
	`UPDATE_CONTROL_ID`
	)
	VALUES
	<foreach collection="list" item="item" separator=",">
		(
		#{item.custId},
		#{item.smartId},
		#{item.custName},
		#{item.genderCode},
		#{item.genderName},
		#{item.nickName},
		#{item.phone},
		#{item.phoneStandby},
		#{item.email},
		#{item.wechat},
		#{item.idCard},
		#{item.driverCard},
		#{item.drivingLicense},
		#{item.passport},
		#{item.source5},
		#{item.source6},
		#{item.attr83},
		#{item.complaintsLabel},
		#{item.freeLabel},
		#{item.userHobbiesCode},
		#{item.userHobbiesName},
		#{item.characteristicsCode},
		#{item.characteristicsName},
		#{item.oneCustSource},
		#{item.extendsJson},
		#{item.column1},
		#{item.column2},
		#{item.column3},
		#{item.column4},
		#{item.column5},
		#{item.column6},
		#{item.column7},
		#{item.column8},
		#{item.column9},
		#{item.column10},
		'1',
		'HOST',
		'1',
		'HOST',
		#{empId},
		#{empName},
		now(),
		#{empId},
		#{empName},
		now(),
		'1',
		'1',
		'1',
		uuid()
		)
	</foreach>
	</insert>

	<insert id="insertcdpLeads">
		INSERT INTO interfacecenter.t_ifs_base_cdp_leads(
		logs_id,
		bk,
		name,
		mobile,
		c_gender,
		source,
		c_register_channel,
		c_lastupdate_system,
		c_store,
		c_store_code,
		c_store_name,
		c_heat_name,
		c_heat_code,
		remark,
		c_first_channel,
		c_second_channel,
		c_third_channel,
		insert_date,
		send_flag,
		send_date
		)values
			<foreach collection="list" separator="," item="item">
				(
				uuid(),
				#{item.phone},
				#{item.custName},
				#{item.phone},
				#{item.genderName},
				'门店',
				#{item.infoChanMName},
				'ADP',
				#{item.dlrShortName},
				#{item.dlrCode},
				#{item.dlrShortName},
				#{item.businessHeatName},
				#{item.businessHeatName},
				'新建线索',
				'agent',
				#{item.infoChanMCode},
				#{item.infoChanDCode},
				now(),
				'0',
				now()
				)
			</foreach>

	</insert>

	<select id="findReviewPerson" resultType="java.util.Map">
		select
			a.USER_ID reviewPersonId,
			a.EMP_NAME reviewPersonName,
			b.DLR_ID,
			b.DLR_SHORT_NAME,
			b.DLR_CODE
			from mp.t_usc_mdm_org_employee a
			left join mp.t_usc_mdm_org_dlr b on a.DLR_CODE=b.DLR_CODE
		where a.EMP_CODE=#{sellerCode}
	</select>

	<select id="findCityClueSwitch" resultType="java.lang.String">
		select
			SWITCH_VALUE
			from csc.t_sac_city_clue_switch
		where DLR_CODE=#{dlrCode}
	</select>

	<insert id="insertcdpLeadsEvent">

		INSERT INTO interfacecenter.t_ifs_base_cdp_leads_event(
		logs_id,
		bk,
		event,
		date,
		c_store_code,
		c_store,
		c_first_channel,
		c_second_channel,
		c_third_channel,
		c_create_time,
		insert_date,
		send_flag,
		send_date
		)values
		<foreach collection="list" separator="," item="item">
			(
			uuid(),
			#{item.phone},
			'c_created_by_ADP',
			#{item.c_create_time},
			#{item.dlrCode},
			#{item.dlrShortName},
			'agent',
			#{item.infoChanMCode},
			#{item.infoChanDCode},
			#{item.c_create_time},
			now(),
			'0',
			now()
			)
		</foreach>
	</insert>


	<insert id="insertEvent">
		insert into interfacecenter.t_ifs_base_cdp_leads_event (
			logs_id,
			bk,
			event,
			date,
			c_staff_name,
			c_process_date,
			c_store_name,
			insert_date,
			send_flag,
			c_car_type,
			send_date
		) values (
					 #{logs_id},
					 #{bk},
					 #{event},
					 #{date},
					 #{c_staff_name},
					 #{c_process_date},
					 #{c_store_name},
					 #{insertDate},
					 '0',
					 #{c_car_type},
						now()
				 )
	</insert>

	<select id="findOverdueTime" resultType="com.ly.mp.csc.clue.entities.dto.FirstOverdueConfigDTO">
		select
			LOOKUP_VALUE_CODE as overdueTime,
			ATTRIBUTE2 as nextDayOverdueTime
		from mp.t_prc_mds_lookup_value
		where LOOKUP_TYPE_CODE='VE1130'
	</select>

	<select id="findClueSwitch" resultType="java.lang.String">
		select
			DLR_CODE
		from
			csc.t_sac_city_clue_switch
		where SWITCH_VALUE='1'
    </select>

	<select id="findObsData" resultType="com.ly.mp.csc.clue.entities.LookupValue">
		select
			LOOKUP_VALUE_CODE lookupValueCode,
			LOOKUP_VALUE_NAME lookupValueName
		from mp.t_prc_mds_lookup_value
		where LOOKUP_TYPE_CODE = 'ADP_CLUE_055'
	</select>

	<select id="getSmByDlrCode" resultType="com.ly.mp.csc.clue.entities.dto.StoreManagerInfoDto">
		SELECT
			dlr_code,emp_name,emp_code,user_id,mobile
		FROM mp.t_usc_mdm_org_employee
		WHERE station_id IN ('smart_bm_0005', 'smart_bm_0016')
		  AND user_status IN ('1', '在职')
		  AND dlr_code = #{dlrCode}
    </select>
	<select id="selectNewClueDlrById" resultType="com.ly.mp.csc.clue.entities.SacClueInfoDlr">
		select <include refid="clueFields"></include> from adp_leads.t_sac_clue_info_dlr where ID = #{id}
	</select>

	<insert id="insertNewClue">
		insert into adp_leads.t_sac_clue_info_dlr(
		<include refid="clueFields"></include>
		, cdc
		) select <include refid="clueFields"></include>
		, '6' as cdc
		from csc.t_sac_clue_info_dlr
		where 1=1
		and ID=#{param.id}
	</insert>

	<insert id="insertNewClueBatch">
		insert into adp_leads.t_sac_clue_info_dlr(
		<include refid="clueFields"></include>
		, cdc
		) select <include refid="clueFields"></include>
		, '6' as cdc
		from csc.t_sac_clue_info_dlr
		where ID in
		<foreach item="item" index="index" open="(" separator="," close=")" collection="param.idList">
			#{item}
		</foreach>
	</insert>

	<delete id="deleteNewClue">
		DELETE FROM adp_leads.t_sac_clue_info_dlr
		WHERE 1=1
		AND ID = #{param.id}
	</delete>

	<delete id="deleteNewClueBatch">
		DELETE FROM adp_leads.t_sac_clue_info_dlr
		WHERE ID IN
		<foreach item="item" index="index" open="(" separator="," close=")" collection="param.idList">
			#{item}
		</foreach>
	</delete>

	<select id="selectNewClue" resultType="java.util.Map">
		select <include refid="Base_Column_List"/>, t1.COLUMN1 as planBuyDateName, t1.COLUMN2 as planBuyDate, t1.COLUMN3 as testDriveDateName, t1.COLUMN4 as testDriveDate
		, t1.COLUMN5 as businessHeatName, t1.COLUMN6 as businessHeatCode, t1.COLUMN7 as carPurchaseBudget, t1.COLUMN8 as activityId, t1.COLUMN9 as clueScore
		, t1.COLUMN10 as smartId, t1.COLUMN11 as isSpecial
		from adp_leads.t_sac_clue_info_dlr t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.ID = #{param.id}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE = #{param.phone}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.ishost !=null and param.ishost !=''" >
			AND t1.DLR_CODE NOT IN ('HOST', 'HOST_SLEEP')
		</if>
		limit 1
	</select>
    <select id="getReviewIdByNewTable" resultType="java.util.Map">
		select REVIEW_ID as reviewId ,UPDATE_CONTROL_ID as updateControlId   from adp_leads.t_sac_clue_info_dlr where PHONE = #{phone} and STATUS_CODE = '1'
	</select>

	<update id="dccBatchUpdateClue">
		<foreach collection="list" item="param" separator=";">
			update csc.t_sac_clue_info_dlr
			<set>
				LAST_UPDATED_DATE=now(),
				UPDATE_CONTROL_ID=uuid(),
				<if test="param.reviewId !=null and param.reviewId !=''">
					REVIEW_ID=#{param.reviewId},
				</if>
				<if test="param.statusCode !=null and param.statusCode !=''">
					STATUS_CODE=#{param.statusCode},
				</if>
				<if test="param.statusName !=null and param.statusName !=''">
					STATUS_NAME=#{param.statusName},
				</if>
				<if test="param.reviewPersonName !=null">
					REVIEW_PERSON_NAME=#{param.reviewPersonName},
				</if>
				<if test="param.reviewPersonId !=null">
					REVIEW_PERSON_ID=#{param.reviewPersonId},
				</if>
				<if test="param.modifier !=null and param.modifier !=''">
					MODIFIER=#{param.modifier},
				</if>
				<if test="param.modifyName !=null and param.modifyName !=''">
					MODIFY_NAME=#{param.modifyName},
				</if>
				<if test="param.dlrCode != null and param.dlrCode != ''">
					DLR_CODE = #{param.dlrCode},
				</if>
				<if test="param.dlrShortName != null and param.dlrShortName != ''">
					DLR_SHORT_NAME = #{param.dlrShortName},
				</if>
				<if test="clearDccFlag">
					COLUMN19=NULL,
				</if>
			</set>
			where ID = #{param.id}
		</foreach>
	</update>

	<update id="transferEmployeeDimissionClues">
		<foreach collection="list" item="param" separator=";">
			update ${dbName}.t_sac_clue_info_dlr
			<set>
				LAST_UPDATED_DATE=now(),
				UPDATE_CONTROL_ID=uuid(),
				MODIFIER='dimissionCluesJob',
				MODIFY_NAME='dimissionCluesJob',
				MANAGE_LABEL_CODE='4',
				MANAGE_LABEL_NAME='线索移交-离职员工线索自动划转到店长',
				<if test="param.smName !=null">
					REVIEW_PERSON_NAME=#{param.smName},
				</if>
				<if test="param.smId !=null">
					REVIEW_PERSON_ID=#{param.smId},
				</if>
			</set>
			where ID = #{param.clueId}
		</foreach>
    </update>

	<select id="getDlrAddrInfo" resultType="com.ly.mp.csc.clue.entities.DlrAddrInfo">
		SELECT
			dlr.dlr_code,
			dlr.dlr_short_name dlrName,
			p.province_code,
			p.province_name,
			ci.city_code,
			ci.city_name,
			co.county_code,
			co.county_name
		FROM
			mp.t_usc_mdm_org_dlr dlr
				LEFT JOIN mp.t_usc_mdm_org_province p ON p.province_id = dlr.province_id
				LEFT JOIN mp.t_usc_mdm_org_city ci ON ci.CITY_ID = dlr.CITY_ID
				LEFT JOIN mp.t_usc_mdm_org_community co ON co.COUNTY_ID=dlr.COUNTY_ID
		WHERE dlr.DLR_CODE=#{dlrCode}
		limit 1
	</select>
</mapper>
