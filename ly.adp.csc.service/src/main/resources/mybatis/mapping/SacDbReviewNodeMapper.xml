<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacDbReviewNode">
        <id column="NODE_ID" property="nodeId" />
        <result column="NODE_CODE" property="nodeCode" />
        <result column="NODE_NAME" property="nodeName" />
        <result column="STATUS_CODE" property="statusCode" />
        <result column="STATUS_NAME" property="statusName" />
        <result column="OPER_TYPE" property="operType" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        NODE_ID, NODE_CODE, NODE_NAME, STATUS_CODE, STATUS_NAME, OPER_TYPE, ORDER_NO, OEM_ID, GROUP_ID, IS_ENABLE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, UPDATE_CONTROL_ID
    </sql>

 	<insert id="insertSacDbReviewNode">
        insert into t_sac_db_review_node(
         NODE_ID
        ,NODE_CODE
        ,NODE_NAME
        ,STATUS_CODE
        ,STATUS_NAME
        ,OPER_TYPE
        ,ORDER_NO
        ,OEM_ID
        ,GROUP_ID
        ,IS_ENABLE
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{nodeId}
        ,#{nodeCode}
        ,#{nodeName}
        ,#{statusCode}
        ,#{statusName}
        ,#{operType}
        ,#{orderNo}
        ,#{oemId}
        ,#{groupId}
        ,#{isEnable}
        ,#{creator}
        ,#{createdName}
        ,#{createdDate}
        ,#{modifier}
        ,#{modifyName}
        ,#{lastUpdatedDate}
        ,#{updateControlId}
		)
    </insert>
    
    <update id="updateSacDbReviewNode">
    	update t_sac_db_review_node  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.nodeCode!=null'> ,NODE_CODE = #{param.nodeCode}</if>
	    <if test = 'param.nodeName!=null'> ,NODE_NAME = #{param.nodeName}</if>
	    <if test = 'param.statusCode!=null'> ,STATUS_CODE = #{param.statusCode}</if>
	    <if test = 'param.statusName!=null'> ,STATUS_NAME = #{param.statusName}</if>
	    <if test = 'param.operType!=null'> ,OPER_TYPE = #{param.operType}</if>
	    <if test = 'param.orderNo!=null'> ,ORDER_NO = #{param.orderNo}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and NODE_ID = #{param.nodeId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
