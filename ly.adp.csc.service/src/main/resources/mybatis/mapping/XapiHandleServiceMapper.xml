<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.XapiHandleServiceMapper">

     <!-- 归档定时任务_查询数据 -->
     <select id="queryData" resultType="Map">
	${selSql}
     </select>

     <!-- 归档定时任务_写入历史表 -->
     <insert id="insertData">
	${insertSql}
     </insert>

     <!-- 归档定时任务_删除原数据 -->
     <update id="delData">
	${delSql}
     </update>
</mapper>
