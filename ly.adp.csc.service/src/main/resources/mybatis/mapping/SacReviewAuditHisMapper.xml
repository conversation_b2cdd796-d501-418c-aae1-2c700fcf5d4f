<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewAuditHisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewAuditHis">
        <id column="AUDIT_ID" property="auditId" />
        <result column="REVIEW_ID" property="reviewId" />
        <result column="BILL_CODE" property="billCode" />
        <result column="APPLY_TYPE_CODE" property="applyTypeCode" />
        <result column="APPLY_TYPE_NAME" property="applyTypeName" />
        <result column="APPLY_DESC" property="applyDesc" />
        <result column="SH_PERSON_ID" property="shPersonId" />
        <result column="SH_PERSON_NAME" property="shPersonName" />
        <result column="SH_DESC" property="shDesc" />
        <result column="SH_TIME" property="shTime" />
        <result column="SH_STATUS" property="shStatus" />
        <result column="SH_STATUS_NAME" property="shStatusName" />
        <result column="EXTENDS_JSON" property="extendsJson" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        AUDIT_ID, REVIEW_ID, BILL_CODE, APPLY_TYPE_CODE, APPLY_TYPE_NAME, APPLY_DESC, SH_PERSON_ID, SH_PERSON_NAME, SH_DESC, SH_TIME, SH_STATUS, SH_STATUS_NAME, EXTENDS_JSON, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

 	<insert id="insertSacReviewAuditHis">
        insert into t_sac_review_audit_his(
         AUDIT_ID
        ,REVIEW_ID
        ,BILL_CODE
        ,APPLY_TYPE_CODE
        ,APPLY_TYPE_NAME
        ,APPLY_DESC
        ,SH_PERSON_ID
        ,SH_PERSON_NAME
        ,SH_DESC
        ,SH_TIME
        ,SH_STATUS
        ,SH_STATUS_NAME
        ,EXTENDS_JSON
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.auditId}
        ,#{param.reviewId}
        ,#{param.billCode}
        ,#{param.applyTypeCode}
        ,#{param.applyTypeName}
        ,#{param.applyDesc}
        ,#{param.shPersonId}
        ,#{param.shPersonName}
        ,#{param.shDesc}
        ,#{param.shTime}
        ,#{param.shStatus}
        ,#{param.shStatusName}
        ,#{param.extendsJson}
        ,#{param.oemId}
        ,#{param.groupId}
        ,#{param.creator}
        ,#{param.createdName}
        ,#{param.createdDate}
        ,#{param.modifier}
        ,#{param.modifyName}
        ,#{param.lastUpdatedDate}
        ,#{param.isEnable}
        ,#{param.updateControlId}
		)
    </insert>
    
    <update id="updateSacReviewAuditHis">
    	update t_sac_review_audit_his  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.reviewId!=null'> ,REVIEW_ID = #{param.reviewId}</if>
	    <if test = 'param.billCode!=null'> ,BILL_CODE = #{param.billCode}</if>
	    <if test = 'param.applyTypeCode!=null'> ,APPLY_TYPE_CODE = #{param.applyTypeCode}</if>
	    <if test = 'param.applyTypeName!=null'> ,APPLY_TYPE_NAME = #{param.applyTypeName}</if>
	    <if test = 'param.applyDesc!=null'> ,APPLY_DESC = #{param.applyDesc}</if>
	    <if test = 'param.shPersonId!=null'> ,SH_PERSON_ID = #{param.shPersonId}</if>
	    <if test = 'param.shPersonName!=null'> ,SH_PERSON_NAME = #{param.shPersonName}</if>
	    <if test = 'param.shDesc!=null'> ,SH_DESC = #{param.shDesc}</if>
	    <if test = 'param.shTime!=null'> ,SH_TIME = #{param.shTime}</if>
	    <if test = 'param.shStatus!=null'> ,SH_STATUS = #{param.shStatus}</if>
	    <if test = 'param.shStatusName!=null'> ,SH_STATUS_NAME = #{param.shStatusName}</if>
	    <if test = 'param.extendsJson!=null'> ,EXTENDS_JSON = #{param.extendsJson}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and AUDIT_ID = #{param.auditId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
