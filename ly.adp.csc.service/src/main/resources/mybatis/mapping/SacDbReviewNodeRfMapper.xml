<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbReviewNodeRfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacDbReviewNodeRf">
        <id column="RF_ID" property="rfId" />
        <result column="BILL_TYPE" property="billType" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="NODE_CODE" property="nodeCode" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RF_ID, BILL_TYPE, BUSINESS_TYPE, NODE_CODE, OEM_ID, GROUP_ID, IS_ENABLE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, UPDATE_CONTROL_ID
    </sql>
    
    <select id="queryReviewNodeRfList" resultType="java.util.Map">
      select r.RF_ID,
      		 r.BILL_TYPE,
      		 r.BUSINESS_TYPE,
      		 r.NODE_CODE,
      		 n.NODE_NAME,
      		 n.STATUS_CODE,
      		 n.STATUS_NAME
      	from t_sac_db_review_node_rf r
      	inner join t_sac_db_review_node n on r.node_code=n.node_code
      	WHERE 1=1 
      	AND R.IS_ENABLE='1'
          <if test="param.billType != null and '' != param.billType">
              AND r.bill_type = #{param.billType}
          </if>
          <if test="param.businessType != null and '' != param.businessType">
              AND (IFNULL(r.BUSINESS_TYPE,'') ='' OR r.BUSINESS_TYPE = #{param.businessType})
          </if>
          <if test="param.nodeCode != null and '' != param.nodeCode">
              AND r.NODE_CODE = #{param.nodeCode}
          </if>
          <if test="param.overTimeSwitch != null and '' != param.overTimeSwitch">
              AND n.column1 in('-1',#{param.overTimeSwitch})
          </if>
  	</select>

    <select id="queryReviewNodeRfListLimitCount" resultType="java.util.Map">
      select r.RF_ID,
      		 r.BILL_TYPE,
      		 r.BUSINESS_TYPE,
      		 r.NODE_CODE,
      		 n.NODE_NAME,
      		 n.STATUS_CODE,
      		 n.STATUS_NAME
      	from t_sac_db_review_node_rf r
      	inner join t_sac_db_review_node n on r.node_code=n.node_code
      	WHERE 1=1
      	AND R.IS_ENABLE='1'
          <if test="param.billType != null and '' != param.billType">
              AND r.bill_type = #{param.billType}
          </if>
          <if test="param.businessType != null and '' != param.businessType">
              AND (IFNULL(r.BUSINESS_TYPE,'') ='' OR r.BUSINESS_TYPE = #{param.businessType})
          </if>
          <if test="param.nodeCode != null and '' != param.nodeCode">
              AND r.NODE_CODE = #{param.nodeCode}
          </if>
          <if test="param.overTimeSwitch != null and '' != param.overTimeSwitch">
              AND n.column1 in('-1',#{param.overTimeSwitch})
          </if>
        Limit #{param.limitCount}
  	</select>

 	<insert id="insertSacDbReviewNodeRf">
        insert into t_sac_db_review_node_rf(
         RF_ID
        ,BILL_TYPE
        ,BUSINESS_TYPE
        ,NODE_CODE
        ,OEM_ID
        ,GROUP_ID
        ,IS_ENABLE
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{rfId}
        ,#{billType}
        ,#{businessType}
        ,#{nodeCode}
        ,#{oemId}
        ,#{groupId}
        ,#{isEnable}
        ,#{creator}
        ,#{createdName}
        ,#{createdDate}
        ,#{modifier}
        ,#{modifyName}
        ,#{lastUpdatedDate}
        ,#{updateControlId}
		)
    </insert>
    
    <update id="updateSacDbReviewNodeRf">
    	update t_sac_db_review_node_rf  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.nodeCode!=null'> ,NODE_CODE = #{param.nodeCode}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and RF_ID = #{param.rfId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
