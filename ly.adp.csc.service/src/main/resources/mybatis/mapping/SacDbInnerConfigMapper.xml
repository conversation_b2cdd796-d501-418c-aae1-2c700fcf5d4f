<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbInnerConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacDbInnerConfig">
        <id column="CONFIG_ID" property="configId" />
        <result column="CONFIG_CODE" property="configCode" />
        <result column="CONFIG_NAME" property="configName" />
        <result column="BILL_TYPE" property="billType" />
        <result column="BILL_TYPE_NAME" property="billTypeName" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="BUSINESS_TYPE_NAME" property="businessTypeName" />
        <result column="CONFIG_VALUE_CODE" property="configValueCode" />
        <result column="CONFIG_VALUE_NAME" property="configValueName" />
        <result column="REMARK" property="remark" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CONFIG_ID, CONFIG_CODE, CONFIG_NAME, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, CONFIG_VALUE_CODE, CONFIG_VALUE_NAME, REMARK, ORDER_NO, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
    
    <select id="queryConfigList" resultType="java.util.Map">
        SELECT 
        c.CONFIG_ID,
		c.CONFIG_CODE,
		c.CONFIG_NAME,
		c.BILL_TYPE,
		c.BILL_TYPE_NAME,
		c.BUSINESS_TYPE,
		c.BUSINESS_TYPE_NAME,
		c.CONFIG_VALUE_CODE,
		c.CONFIG_VALUE_NAME,
		c.REMARK,
		c.COLUMN1,
		c.COLUMN2,
		c.COLUMN3,
		c.COLUMN4,
		c.COLUMN5
		FROM t_sac_db_inner_config c
		WHERE 1=1
		AND c.is_enable='1'
		<if test="param.configCode != null and ''!= param.configCode ">
		AND c.CONFIG_CODE = #{param.configCode}
		</if>
		<if test="param.billType != null and ''!= param.billType ">
		AND c.BILL_TYPE = #{param.billType}
		</if>
		<if test="param.businessType != null and ''!= param.businessType ">
		AND c.BUSINESS_TYPE = #{param.businessType}
		</if>
		<if test="param.configValueCode != null and ''!= param.configValueCode ">
		AND c.CONFIG_VALUE_CODE = #{param.configValueCode}
		</if>
		<if test="param.column1 != null and ''!= param.column1 ">
		AND c.column1 = #{param.column1}
		</if>
		<if test="param.column2 != null and ''!= param.column2 ">
		AND c.column2 = #{param.column2}
		</if>
		<if test="param.column3 != null and ''!= param.column3 ">
		AND c.column3 = #{param.column3}
		</if>
		<if test="param.column4 != null and ''!= param.column4 ">
		AND c.column4 = #{param.column4}
		</if>
		<if test="param.column5 != null and ''!= param.column5 ">
		AND c.column5 = #{param.column5}
		</if>
		ORDER BY c.CONFIG_CODE,C.ORDER_NO
  	</select>

 	<insert id="insertSacDbInnerConfig">
        insert into t_sac_db_inner_config(
         CONFIG_ID
        ,CONFIG_CODE
        ,CONFIG_NAME
        ,BILL_TYPE
        ,BILL_TYPE_NAME
        ,BUSINESS_TYPE
        ,BUSINESS_TYPE_NAME
        ,CONFIG_VALUE_CODE
        ,CONFIG_VALUE_NAME
        ,REMARK
        ,ORDER_NO
        ,COLUMN1
        ,COLUMN2
        ,COLUMN3
        ,COLUMN4
        ,COLUMN5
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.configId}
        ,#{param.configCode}
        ,#{param.configName}
        ,#{param.billType}
        ,#{param.billTypeName}
        ,#{param.businessType}
        ,#{param.businessTypeName}
        ,#{param.configValueCode}
        ,#{param.configValueName}
        ,#{param.remark}
        ,#{param.orderNo}
        ,#{param.column1}
        ,#{param.column2}
        ,#{param.column3}
        ,#{param.column4}
        ,#{param.column5}
        ,#{param.oemId}
        ,#{param.groupId}
        ,#{param.creator}
        ,#{param.createdName}
        ,#{param.createdDate}
        ,#{param.modifier}
        ,#{param.modifyName}
        ,#{param.lastUpdatedDate}
        ,#{param.isEnable}
        ,#{param.updateControlId}
		)
    </insert>
    
    <update id="updateSacDbInnerConfig">
    	update t_sac_db_inner_config  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.configCode!=null'> ,CONFIG_CODE = #{param.configCode}</if>
	    <if test = 'param.configName!=null'> ,CONFIG_NAME = #{param.configName}</if>
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.billTypeName!=null'> ,BILL_TYPE_NAME = #{param.billTypeName}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{param.businessTypeName}</if>
	    <if test = 'param.configValueCode!=null'> ,CONFIG_VALUE_CODE = #{param.configValueCode}</if>
	    <if test = 'param.configValueName!=null'> ,CONFIG_VALUE_NAME = #{param.configValueName}</if>
	    <if test = 'param.remark!=null'> ,REMARK = #{param.remark}</if>
	    <if test = 'param.orderNo!=null'> ,ORDER_NO = #{param.orderNo}</if>
	    <if test = 'param.column1!=null'> ,COLUMN1 = #{param.column1}</if>
	    <if test = 'param.column2!=null'> ,COLUMN2 = #{param.column2}</if>
	    <if test = 'param.column3!=null'> ,COLUMN3 = #{param.column3}</if>
	    <if test = 'param.column4!=null'> ,COLUMN4 = #{param.column4}</if>
	    <if test = 'param.column5!=null'> ,COLUMN5 = #{param.column5}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and CONFIG_ID = #{param.configId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
