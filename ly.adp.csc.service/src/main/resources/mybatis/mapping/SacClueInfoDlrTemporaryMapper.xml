<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrTemporaryMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacClueInfoDlrTemporary">
		<id column="TEMPORARY_ID" property="temporaryId" />
		<result column="BATCH_UUID" property="batchUuid" />
		<result column="DEAL_STATUS" property="dealStatus" />
		<result column="DEAL_STATUS_CODE" property="dealStatusCode" />
        <result column="ERR_MESSAGE" property="errMessage" />
		<result column="CLUE_ID" property="clueId" />
		<result column="SERVER_ORDER" property="serverOrder" />
		<result column="PV_SERVER_ORDER" property="pvServerOrder" />
		<result column="CUST_ID" property="custId" />
		<result column="CUST_NAME" property="custName" />
		<result column="PHONE" property="phone" />
		<result column="PHONE_BACKUP" property="phoneBackup" />
		<result column="INTEN_LEVEL_CODE" property="intenLevelCode" />
		<result column="INTEN_LEVEL_NAME" property="intenLevelName" />
		<result column="INTEN_BRAND_CODE" property="intenBrandCode" />
		<result column="INTEN_BRAND_NAME" property="intenBrandName" />
		<result column="INTEN_SERIES_CODE" property="intenSeriesCode" />
		<result column="INTEN_SERIES_NAME" property="intenSeriesName" />
		<result column="INTEN_CAR_TYPE_CODE" property="intenCarTypeCode" />
		<result column="INTEN_CAR_TYPE_NAME" property="intenCarTypeName" />
		<result column="INTEN_OPTION_PACKAGE_CODE" property="intenOptionPackageCode" />
		<result column="INTEN_OPTION_PACKAGE_NAME" property="intenOptionPackageName" />
		<result column="INNER_COLOR_CODE" property="innerColorCode" />
		<result column="INNER_COLOR_NAME" property="innerColorName" />
		<result column="OUT_COLOR_CODE" property="outColorCode" />
		<result column="OUT_COLOR_NAME" property="outColorName" />
		<result column="DLR_CODE" property="dlrCode" />
		<result column="DLR_SHORT_NAME" property="dlrShortName" />
		<result column="SOURCE_SYSTEMT_CODE" property="sourceSystemtCode" />
		<result column="SOURCE_SYSTEMT_NAME" property="sourceSystemtName" />
		<result column="RECEIVE_TIME" property="receiveTime" />
		<result column="SOURCE_SERVER_ORDER" property="sourceServerOrder" />
		<result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
		<result column="INFO_CHAN_M_NAME" property="infoChanMName" />
		<result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
		<result column="INFO_CHAN_D_NAME" property="infoChanDName" />
		<result column="INFO_CHAN_DD_CODE" property="infoChanDdCode" />
		<result column="INFO_CHAN_DD_NAME" property="infoChanDdName" />
		<result column="CHANNEL_CODE" property="channelCode" />
		<result column="CHANNEL_NAME" property="channelName" />
		<result column="GENDER_CODE" property="genderCode" />
		<result column="GENDER_NAME" property="genderName" />
		<result column="STATUS_CODE" property="statusCode" />
		<result column="STATUS_NAME" property="statusName" />
		<result column="DEAL_NODE_CODE" property="dealNodeCode" />
		<result column="DEAL_NODE_NAME" property="dealNodeName" />
		<result column="REVIEW_ID" property="reviewId" />
		<result column="FIRST_REVIEW_TIME" property="firstReviewTime" />
		<result column="LAST_REVIEW_TIME" property="lastReviewTime" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.TEMPORARY_ID, t1.BATCH_UUID, t1.DEAL_STATUS, t1.DEAL_STATUS_CODE, t1.ERR_MESSAGE, t1.CLUE_ID
		, t1.SERVER_ORDER, t1.PV_SERVER_ORDER, t1.CUST_ID, t1.CUST_NAME, t1.PHONE, t1.PHONE_BACKUP
		, t1.INTEN_LEVEL_CODE, t1.INTEN_LEVEL_NAME
		, t1.INTEN_BRAND_CODE, t1.INTEN_BRAND_NAME
		, t1.INTEN_SERIES_CODE, t1.INTEN_SERIES_NAME
		, t1.INTEN_CAR_TYPE_CODE, t1.INTEN_CAR_TYPE_NAME
		, t1.INTEN_OPTION_PACKAGE_CODE, t1.INTEN_OPTION_PACKAGE_NAME
		, t1.INNER_COLOR_CODE, t1.INNER_COLOR_NAME
		, t1.OUT_COLOR_CODE, t1.OUT_COLOR_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.SOURCE_SYSTEMT_CODE, t1.SOURCE_SYSTEMT_NAME
		, t1.RECEIVE_TIME, t1.SOURCE_SERVER_ORDER
		, t1.INFO_CHAN_M_CODE, t1.INFO_CHAN_M_NAME
		, t1.INFO_CHAN_D_CODE, t1.INFO_CHAN_D_NAME
		, t1.INFO_CHAN_DD_CODE, t1.INFO_CHAN_DD_NAME
		, t1.CHANNEL_CODE, t1.CHANNEL_NAME
		, t1.GENDER_CODE, t1.GENDER_NAME, t1.STATUS_CODE, t1.STATUS_NAME
		, t1.DEAL_NODE_CODE, t1.DEAL_NODE_NAME
		, t1.REVIEW_ID, t1.FIRST_REVIEW_TIME, t1.LAST_REVIEW_TIME
		, t1.EXTENDS_JSON, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
	</sql>
	
	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.temporaryId !=null and param.temporaryId !=''">and t1.TEMPORARY_ID=#{param.temporaryId}</if>
		<if test="param.batchUuid !=null and param.batchUuid !=''">and t1.BATCH_UUID=#{param.batchUuid}</if>
		<if test="param.dealStatus !=null and param.dealStatus !=''">and t1.DEAL_STATUS=#{param.dealStatus}</if>
		<if test="param.dealStatusCode !=null and param.dealStatusCode !=''">and t1.DEAL_STATUS_CODE=#{param.dealStatusCode}</if>
		<if test="param.clueId !=null and param.clueId !=''">and t1.CLUE_ID=#{param.clueId}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and t1.SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and t1.PV_SERVER_ORDER=#{param.pvServerOrder}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''">and t1.CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and t1.PHONE=#{param.phone}</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP=#{param.phoneBackup}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t1.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and t1.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE=#{param.infoChanDCode}</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME=#{param.channelName}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		<if test="param.lastReviewTime !=null and param.lastReviewTime !=''">and t1.LAST_REVIEW_TIME=#{param.lastReviewTime}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</sql>
	
	<!-- 新增 -->
	<insert id="sacClueInfoDlrTemporaryInsert">
		insert into t_sac_clue_info_dlr_temporary(
		TEMPORARY_ID
		, BATCH_UUID
		, DEAL_STATUS
		, DEAL_STATUS_CODE
		, ERR_MESSAGE
		, CLUE_ID
		, SERVER_ORDER
		, PV_SERVER_ORDER
		, CUST_ID
		, CUST_NAME
		, PHONE
		, PHONE_BACKUP
		, INTEN_LEVEL_CODE
		, INTEN_LEVEL_NAME
		, INTEN_BRAND_CODE
		, INTEN_BRAND_NAME
		, INTEN_SERIES_CODE
		, INTEN_SERIES_NAME
		, INTEN_CAR_TYPE_CODE
		, INTEN_CAR_TYPE_NAME
		, INTEN_OPTION_PACKAGE_CODE
		, INTEN_OPTION_PACKAGE_NAME
		, INNER_COLOR_CODE
		, INNER_COLOR_NAME
		, OUT_COLOR_CODE
		, OUT_COLOR_NAME
		, DLR_CODE
		, DLR_SHORT_NAME
		, SOURCE_SYSTEMT_CODE
		, SOURCE_SYSTEMT_NAME
		, RECEIVE_TIME
		, SOURCE_SERVER_ORDER
		, INFO_CHAN_M_CODE
		, INFO_CHAN_M_NAME
		, INFO_CHAN_D_CODE
		, INFO_CHAN_D_NAME
		, INFO_CHAN_DD_CODE
		, INFO_CHAN_DD_NAME
		, CHANNEL_CODE
		, CHANNEL_NAME
		, GENDER_CODE
		, GENDER_NAME
		, STATUS_CODE
		, STATUS_NAME
		, DEAL_NODE_CODE
		, DEAL_NODE_NAME
		, REVIEW_ID
		, FIRST_REVIEW_TIME
		, LAST_REVIEW_TIME
		, EXTENDS_JSON
		, OEM_ID
		, GROUP_ID
		, CREATOR
		, CREATED_NAME
		, CREATED_DATE
		, MODIFIER
		, MODIFY_NAME
		, LAST_UPDATED_DATE
		, IS_ENABLE
		, UPDATE_CONTROL_ID
		) values (
		#{param.temporaryId}
		, #{param.batchUuid}
		, #{param.dealStatus}
		, #{param.dealStatusCode}
		, #{param.errMessage}
		, #{param.clueId}
		, #{param.serverOrder}
		, #{param.pvServerOrder}
		, #{param.custId}
		, #{param.custName}
		, #{param.phone}
		, #{param.phoneBackup}
		, #{param.intenLevelCode}
		, #{param.intenLevelName}
		, #{param.intenBrandCode}
		, #{param.intenBrandName}
		, #{param.intenSeriesCode}
		, #{param.intenSeriesName}
		, #{param.intenCarTypeCode}
		, #{param.intenCarTypeName}
		, #{param.intenOptionPackageCode}
		, #{param.intenOptionPackageName}
		, #{param.innerColorCode}
		, #{param.innerColorName}
		, #{param.outColorCode}
		, #{param.outColorName}
		, #{param.dlrCode}
		, #{param.dlrShortName}
		, #{param.sourceSystemtCode}
		, #{param.sourceSystemtName}
		, #{param.receiveTime}
		, #{param.sourceServerOrder}
		, #{param.infoChanMCode}
		, #{param.infoChanMName}
		, #{param.infoChanDCode}
		, #{param.infoChanDName}
		, #{param.infoChanDdCode}
		, #{param.infoChanDdName}
		, #{param.channelCode}
		, #{param.channelName}
		, #{param.genderCode}
		, #{param.genderName}
		, #{param.statusCode}
		, #{param.statusName}
		, #{param.dealNodeCode}
		, #{param.dealNodeName}
		, #{param.reviewId}
		, #{param.firstReviewTime}
		, #{param.lastReviewTime}
		, #{param.extendsJson}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		)
	</insert>

	<!-- 修改 -->
	<update id="sacClueInfoDlrTemporaryUpdate">
		update t_sac_clue_info_dlr_temporary
		<set>
		LAST_UPDATED_DATE=sysdate(),
		UPDATE_CONTROL_ID=uuid(),
			<if test="param.temporaryId !=null and param.temporaryId !=''"> TEMPORARY_ID=#{param.temporaryId},</if>
			<if test="param.batchUuid !=null and param.batchUuid !=''"> BATCH_UUID=#{param.batchUuid},</if>
			<if test="param.dealStatus !=null and param.dealStatus !=''"> DEAL_STATUS=#{param.dealStatus},</if>
			<if test="param.dealStatusCode !=null and param.dealStatusCode !=''"> DEAL_STATUS_CODE=#{param.dealStatusCode},</if>
			<if test="param.errMessage !=null and param.errMessage !=''"> ERR_MESSAGE=#{param.errMessage},</if>
			<if test="param.clueId !=null and param.clueId !=''"> CLUE_ID=#{param.clueId},</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''"> SERVER_ORDER=#{param.serverOrder},</if>
			<if test="param.pvServerOrder !=null and param.pvServerOrder !=''"> PV_SERVER_ORDER=#{param.pvServerOrder},</if>
			<if test="param.custId !=null and param.custId !=''"> CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''"> CUST_NAME=#{param.custName},</if>
			<if test="param.phone !=null and param.phone !=''"> PHONE=#{param.phone},</if>
			<if test="param.phoneBackup !=null and param.phoneBackup !=''"> PHONE_BACKUP=#{param.phoneBackup},</if>
			<if test="param.intenLevelCode !=null and param.intenLevelCode !=''"> INTEN_LEVEL_CODE=#{param.intenLevelCode},</if>
			<if test="param.intenLevelName !=null and param.intenLevelName !=''"> INTEN_LEVEL_NAME=#{param.intenLevelName},</if>
			<if test="param.intenBrandCode !=null and param.intenBrandCode !=''"> INTEN_BRAND_CODE=#{param.intenBrandCode},</if>
			<if test="param.intenBrandName !=null and param.intenBrandName !=''"> INTEN_BRAND_NAME=#{param.intenBrandName},</if>
			<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''"> INTEN_SERIES_CODE=#{param.intenSeriesCode},</if>
			<if test="param.intenSeriesName !=null and param.intenSeriesName !=''"> INTEN_SERIES_NAME=#{param.intenSeriesName},</if>
			<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''"> INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode},</if>
			<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''"> INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName},</if>
			<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''"> INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode},</if>
			<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''"> INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName},</if>
			<if test="param.innerColorCode !=null and param.innerColorCode !=''"> INNER_COLOR_CODE=#{param.innerColorCode},</if>
			<if test="param.innerColorName !=null and param.innerColorName !=''"> INNER_COLOR_NAME=#{param.innerColorName},</if>
			<if test="param.outColorCode !=null and param.outColorCode !=''"> OUT_COLOR_CODE=#{param.outColorCode},</if>
			<if test="param.outColorName !=null and param.outColorName !=''"> OUT_COLOR_NAME=#{param.outColorName},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrShortName !=null and param.dlrShortName !=''"> DLR_SHORT_NAME=#{param.dlrShortName},</if>
			<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''"> SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode},</if>
			<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''"> SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName},</if>
			<if test="param.receiveTime !=null and param.receiveTime !=''"> RECEIVE_TIME=#{param.receiveTime},</if>
			<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''"> SOURCE_SERVER_ORDER=#{param.sourceServerOrder},</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''"> INFO_CHAN_M_CODE=#{param.infoChanMCode},</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''"> INFO_CHAN_M_NAME=#{param.infoChanMName},</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''"> INFO_CHAN_D_CODE=#{param.infoChanDCode},</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''"> INFO_CHAN_D_NAME=#{param.infoChanDName},</if>
			<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''"> INFO_CHAN_DD_CODE=#{param.infoChanDdCode},</if>
			<if test="param.infoChanDdName !=null and param.infoChanDdName !=''"> INFO_CHAN_DD_NAME=#{param.infoChanDdName},</if>
			<if test="param.channelCode !=null and param.channelCode !=''"> CHANNEL_CODE=#{param.channelCode},</if>
			<if test="param.channelName !=null and param.channelName !=''"> CHANNEL_NAME=#{param.channelName},</if>
			<if test="param.genderCode !=null and param.genderCode !=''"> GENDER_CODE=#{param.genderCode},</if>
			<if test="param.genderName !=null and param.genderName !=''"> GENDER_NAME=#{param.genderName},</if>
			<if test="param.statusCode !=null and param.statusCode !=''"> STATUS_CODE=#{param.statusCode},</if>
			<if test="param.statusName !=null and param.statusName !=''"> STATUS_NAME=#{param.statusName},</if>
			<if test="param.dealNodeCode !=null and param.dealNodeCode !=''"> DEAL_NODE_CODE=#{param.dealNodeCode},</if>
			<if test="param.dealNodeName !=null and param.dealNodeName !=''"> DEAL_NODE_NAME=#{param.dealNodeName},</if>
			<if test="param.reviewId !=null and param.reviewId !=''"> REVIEW_ID=#{param.reviewId},</if>
			<if test="param.firstReviewTime !=null and param.firstReviewTime !=''"> FIRST_REVIEW_TIME=#{param.firstReviewTime},</if>
			<if test="param.lastReviewTime !=null and param.lastReviewTime !=''"> LAST_REVIEW_TIME=#{param.lastReviewTime},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		</set>
		where 1=1
		and TEMPORARY_ID=#{param.temporaryId}
		<if test='param.updateControlId!=null'>
		 	and UPDATE_CONTROL_ID = #{param.updateControlId}
		</if>
	</update>
	<select id="selectByPage" resultType="Map">
		select 
		t1.TEMPORARY_ID, t1.BATCH_UUID, t1.DEAL_STATUS, t1.DEAL_STATUS_CODE, t1.ERR_MESSAGE, t1.CLUE_ID
		, t1.SERVER_ORDER, t1.PV_SERVER_ORDER, t1.CUST_ID, t1.CUST_NAME, t1.PHONE, t1.PHONE_BACKUP
		, t1.INTEN_LEVEL_CODE, t1.INTEN_LEVEL_NAME
		, t1.INTEN_BRAND_CODE, t1.INTEN_BRAND_NAME
		, t1.INTEN_SERIES_CODE, t1.INTEN_SERIES_NAME
		, t1.INTEN_CAR_TYPE_CODE, t1.INTEN_CAR_TYPE_NAME
		, t1.INTEN_OPTION_PACKAGE_CODE, t1.INTEN_OPTION_PACKAGE_NAME
		, t1.INNER_COLOR_CODE, t1.INNER_COLOR_NAME
		, t1.OUT_COLOR_CODE, t1.OUT_COLOR_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.SOURCE_SYSTEMT_CODE, t1.SOURCE_SYSTEMT_NAME
		, t1.RECEIVE_TIME, t1.SOURCE_SERVER_ORDER
		, t1.INFO_CHAN_M_CODE, t1.INFO_CHAN_M_NAME
		, t1.INFO_CHAN_D_CODE, t1.INFO_CHAN_D_NAME
		, t1.INFO_CHAN_DD_CODE, t1.INFO_CHAN_DD_NAME
		, t1.CHANNEL_CODE, t1.CHANNEL_NAME
		, t1.GENDER_CODE, t1.GENDER_NAME, t1.STATUS_CODE, t1.STATUS_NAME
		, t1.DEAL_NODE_CODE, t1.DEAL_NODE_NAME
		, t1.REVIEW_ID, t1.FIRST_REVIEW_TIME, t1.LAST_REVIEW_TIME
		, t1.EXTENDS_JSON, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
		from t_sac_clue_info_dlr_temporary t1
		<where>
			<if test="param.dealStatus !=null and param.dealStatus !=''"> and t1.DEAL_STATUS=#{param.dealStatus}</if>
			<if test="param.dealStatusCode !=null and param.dealStatusCode !=''"> and t1.DEAL_STATUS_CODE=#{param.dealStatusCode}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''"> and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> and t1.IS_ENABLE=#{param.isEnable}</if>
			
			<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER like concat('%',#{param.serverOrder},'%')</if>
			<if test="param.batchUuid !=null and param.batchUuid !=''"> and t1.BATCH_UUID like concat('%',#{param.batchUuid},'%')</if>
			<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		    <if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
		    <if test="param.errMessage!=null and param.errMessage !=''"> and t1.ERR_MESSAGE like concat('%',#{param.errMessage},'%')</if>
		</where>
	    order by t1.CREATED_DATE desc
	</select>
</mapper>