<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacAppointmentSheetMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacAppointmentSheet">
		  <id column="APPOINTMENT_ID" property="appointmentId" />
        <result column="IS_TEST_DRIVE" property="isTestDrive" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="DLR_CLUE_ORDER_NO" property="dlrClueOrderNo" />
        <result column="CUSTOMER_NAME" property="customerName" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="CUSTOMER_PHONE" property="customerPhone" />
        <result column="CUSTOMER_SEX" property="customerSex" />
        <result column="APPOINTMENT_ORDER_NO" property="appointmentOrderNo" />
        <result column="APPOINTMENT_TIME" property="appointmentTime" />
        <result column="SMALL_CAR_TYPE_CODE" property="smallCarTypeCode" />
        <result column="SMALL_CAR_TYPE_NAME" property="smallCarTypeName" />
        <result column="PLATE_NUMBER" property="plateNumber" />
        <result column="CAR_VIN" property="carVin" />
        <result column="TEST_TYPE" property="testType" />
        <result column="APPOINTMENT_TEST_DATE" property="appointmentTestDate" />
        <result column="APPOINTMENT_TEST_TIME" property="appointmentTestTime" />
        <result column="APPOINTMENT_START_TIME" property="appointmentStartTime" />
        <result column="APPOINTMENT_END_TIME" property="appointmentEndTime" />
        <result column="NEW_DLR_APPOINTMENT_START_TIME" property="newDlrAppointmentStartTime" />
        <result column="NEW_DLR_APPOINTMENT_END_TIME" property="newDlrAppointmentEndTime" />
        <result column="APPOINTMENT_CHANNEL" property="appointmentChannel" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		APPOINTMENT_ID, IS_TEST_DRIVE, DLR_CODE, DLR_NAME, DLR_CLUE_ORDER_NO, CUSTOMER_ID,
		CUSTOMER_NAME, CUSTOMER_PHONE, CUSTOMER_SEX, APPOINTMENT_ORDER_NO, CAR_VIN,
		APPOINTMENT_TIME, SMALL_CAR_TYPE_CODE, SMALL_CAR_TYPE_NAME, PLATE_NUMBER, 
		TEST_TYPE, APPOINTMENT_TEST_DATE, APPOINTMENT_TEST_TIME, APPOINTMENT_START_TIME, 
		APPOINTMENT_END_TIME, APPOINTMENT_CHANNEL, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, 
		NEW_DLR_APPOINTMENT_START_TIME,NEW_DLR_APPOINTMENT_END_TIME
		CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, UPDATE_CONTROL_ID, IS_ENABLE
	</sql>

	<!--试乘试驾预约单查询 -->
	<select id="selectSacAppointmentSheet"
		resultType="java.util.Map">
		select t.APPOINTMENT_ID, t.DLR_CODE, t.DLR_NAME,
		t.CUSTOMER_NAME,
		t.CUSTOMER_PHONE,
		INSERT(t.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM,
		t.CUSTOMER_SEX,t.CUSTOMER_ID,
		t.APPOINTMENT_ORDER_NO,
		t.APPOINTMENT_TIME,
		t.SMALL_CAR_TYPE_CODE,t.SMALL_CAR_TYPE_NAME, t.PLATE_NUMBER,t.CAR_VIN,
		t.TEST_TYPE,t.DLR_CLUE_ORDER_NO,
		(case when t.TEST_TYPE='0' then '试乘'
		when t.TEST_TYPE='1' then '试驾' 
		when t.TEST_TYPE='2' then '深度试驾' end) as testTypeName,
		t.IS_TEST_DRIVE,
		(case when t.IS_TEST_DRIVE='0' then '未试乘试驾' when t.IS_TEST_DRIVE='1'
		then '已试乘试驾试驾' end) as isTestDriveName,
		t.APPOINTMENT_TEST_DATE,
		t.APPOINTMENT_TEST_time,
		t.APPOINTMENT_START_TIME,
		t.APPOINTMENT_END_TIME,
		t.APPOINTMENT_CHANNEL, t.OEM_ID, t.GROUP_ID,
		t.CREATOR, t.CREATED_NAME,
		t.CREATED_DATE, t.MODIFIER, t.MODIFY_NAME,
		t.LAST_UPDATED_DATE, t.UPDATE_CONTROL_ID,
		t.IS_ENABLE,
		IF(T1.TEST_DRIVE_SHEET_ID is null,'0','1') as isAddTestSheet
		from
		t_sac_appointment_sheet t
		left join t_sac_test_drive_sheet t1 on t1.APPOINTMENT_ID=t.APPOINTMENT_ID
		where 1=1
		<if test="param.appointmentId !=null and param.appointmentId !=''"> and t.APPOINTMENT_ID =#{param.appointmentId}</if>
		<if test="param.condition !=null and param.condition !=''"> and (t.CUSTOMER_NAME like concat('%',#{param.condition},'%') or t.CUSTOMER_PHONE=#{param.condition})</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> and t.DLR_NAME=#{param.dlrName}</if>
		<if test="param.customerName !=null and param.customerName !=''"> and t.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''"> and t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerSex !=null and param.customerSex !=''"> and t.CUSTOMER_SEX=#{param.customerSex}</if>
		<if test="param.customerId !=null and param.customerId !=''"> and t.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.appointmentOrderNo !=null and param.appointmentOrderNo !=''"> and t.APPOINTMENT_ORDER_NO=#{param.appointmentOrderNo}</if>
		<if test="param.appointmentTime !=null"> and t.APPOINTMENT_TIME=#{param.appointmentTime}</if>
		<if test="param.appointmentTestDateMin !=null"> <![CDATA[ and t.APPOINTMENT_TEST_DATE>=#{param.appointmentTestDateMin}]]></if>
		<if test="param.appointmentTestDateMax !=null"> <![CDATA[ and t.APPOINTMENT_TEST_DATE<date_add(#{param.appointmentTestDateMax}, INTERVAL 1 day)]]></if>
		<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
		<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> and t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''"> and t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> and t.PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.carVin !=null and param.carVin !=''">and t.CAR_VIN=#{param.carVin}</if>
		<if test="param.testType !=null and param.testType !=''"> and t.TEST_TYPE=#{param.testType}</if>
		<if test="param.appointmentTestDate !=null and param.appointmentTestDate !=''">and t.APPOINTMENT_TEST_DATE=#{param.appointmentTestDate}</if>
		<if test="param.appointmentTestTime !=null">and t.APPOINTMENT_TEST_TIME=#{param.appointmentTestTime}</if>
		<if test="param.appointmentStartTime !=null">and t.APPOINTMENT_START_TIME=#{param.appointmentStartTime}</if>
		<if test="param.appointmentEndTime !=null">and t.APPOINTMENT_END_TIME=#{param.appointmentEndTime}</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''"> and t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.isTestDrive !=null and param.isTestDrive !=''"> and t.IS_TEST_DRIVE=#{param.isTestDrive}</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''"> and t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
		order by t.APPOINTMENT_START_TIME
	</select>

	<!-- 试乘试驾预约单新增 -->
	<insert id="insertSacAppointmentSheet">
		insert into t_sac_appointment_sheet(
		APPOINTMENT_ID,
		IS_TEST_DRIVE,
		DLR_CODE,
		DLR_NAME,
		DLR_CLUE_ORDER_NO,
		CUSTOMER_NAME,
		CUSTOMER_PHONE,
		CUSTOMER_SEX,
		APPOINTMENT_ORDER_NO,
		APPOINTMENT_TIME,
		SMALL_CAR_TYPE_CODE,
		SMALL_CAR_TYPE_NAME,
		PLATE_NUMBER,
		CAR_VIN,
		TEST_TYPE,
		CUSTOMER_ID,
		APPOINTMENT_TEST_DATE,
		APPOINTMENT_TEST_time,
		APPOINTMENT_START_TIME,
		APPOINTMENT_END_TIME,
		APPOINTMENT_CHANNEL,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		IS_ENABLE,
		NEW_DLR_APPOINTMENT_START_TIME,
        NEW_DLR_APPOINTMENT_END_TIME,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		COLUMN6,
		COLUMN7,
		COLUMN8,
		COLUMN9,
		COLUMN10)
		values(
		#{param.appointmentId},
		#{param.isTestDrive},
		#{param.dlrCode},
		#{param.dlrName},
		#{param.dlrClueOrderNo},
		#{param.customerName},
		#{param.customerPhone},
		#{param.customerSex},
		#{param.appointmentOrderNo},
		#{param.appointmentTime},
		#{param.smallCarTypeCode},
		#{param.smallCarTypeName},
		#{param.plateNumber},
		#{param.carVin},
		#{param.testType},
		#{param.customerId},
		#{param.appointmentTestDate},
		#{param.appointmentTestTime},
		#{param.appointmentStartTime},
		#{param.appointmentEndTime},
		#{param.appointmentChannel},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		#{param.updateControlId},
		#{param.isEnable},
		#{param.newDlrAppointmentStartTime},
        #{param.newDlrAppointmentEndTime},
		#{param.column1},
		#{param.column2},
		#{param.column3},
		#{param.column4},
		#{param.column5},
		#{param.column6},
		#{param.column7},
		#{param.column8},
		#{param.column9},
		#{param.column10})
	</insert>

	<!-- 试乘试驾预约单更新 -->
	<update id="updateSacAppointmentSheet">
		update t_sac_appointment_sheet set
		<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> DLR_NAME=#{param.dlrName},</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''"> DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo},</if>
		<if test="param.customerName !=null and param.customerName !=''"> CUSTOMER_NAME=#{param.customerName},</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''"> CUSTOMER_PHONE=#{param.customerPhone},</if>
		<if test="param.customerSex !=null and param.customerSex !=''"> CUSTOMER_SEX=#{param.customerSex},</if>
		<if test="param.customerId !=null and param.customerId !=''"> CUSTOMER_ID=#{param.customerId},</if>
		<if test="param.appointmentOrderNo !=null and param.appointmentOrderNo !=''"> APPOINTMENT_ORDER_NO=#{param.appointmentOrderNo},</if>
		<if test="param.appointmentTime !=null"> APPOINTMENT_TIME=#{param.appointmentTime},</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode},</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''"> SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName},</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> PLATE_NUMBER=#{param.plateNumber},</if>
		<if test="param.carVin !=null and param.carVin !=''"> CAR_VIN=#{param.carVin},</if>
		<if test="param.testType !=null and param.testType !=''"> TEST_TYPE=#{param.testType},</if>
		<if test="param.appointmentTestDate !=null">APPOINTMENT_TEST_DATE=#{param.appointmentTestDate},</if>
		<if test="param.appointmentTestTime !=null"> APPOINTMENT_TEST_TIME=#{param.appointmentTestTime},</if>
		<if test="param.appointmentStartTime !=null"> APPOINTMENT_START_TIME=#{param.appointmentStartTime},</if>
		<if test="param.appointmentEndTime !=null"> APPOINTMENT_END_TIME=#{param.appointmentEndTime},</if>
		<if test="param.newDlrAppointmentStartTime !=null"> NEW_DLR_APPOINTMENT_START_TIME=#{param.newDlrAppointmentStartTime},</if>
		<if test="param.newDlrAppointmentEndTime !=null"> NEW_DLR_APPOINTMENT_END_TIME=#{param.newDlrAppointmentEndTime},</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''"> APPOINTMENT_CHANNEL=#{param.appointmentChannel},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if test="param.isTestDrive !=null and param.isTestDrive !=''"> IS_TEST_DRIVE=#{param.isTestDrive},</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		where APPOINTMENT_ID=#{param.appointmentId}
	</update>

	<!-- 试乘试驾查重 -->
	<select id="checkRepeat" resultType="int">
		SELECT COUNT(1)
		FROM t_sac_appointment_sheet t
		left join t_sac_test_drive_sheet t1 on t1.APPOINTMENT_ID=t.APPOINTMENT_ID
		WHERE t.DLR_CODE =#{param.dlrCode}
		AND t.IS_ENABLE = '1'
		and t.PLATE_NUMBER=#{param.plateNumber} and (t1.TEST_STATUS in('0','1')or t1.TEST_STATUS is null)
		<if test="param.isCheckRepeak==true"> and t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''"> and t.APPOINTMENT_ID !=#{param.appointmentId}</if>
		<![CDATA[AND (t.APPOINTMENT_START_TIME<#{param.appointmentEndTime} AND t.APPOINTMENT_END_TIME>#{param.appointmentStartTime})]]>
		<!-- or (APPOINTMENT_END_TIME<=#{param.appointmentEndTime} AND APPOINTMENT_END_TIME>=#{param.appointmentStartTime})
		or(APPOINTMENT_START_TIME<=#{param.appointmentStartTime} AND APPOINTMENT_END_TIME>=#{param.appointmentEndTime}) -->
	</select>
	<!-- 超长出库查询 -->
	<select id="checkLongRepeat" resultType="int">
		select count(1)
		from t_sac_test_drive_long_apply
		where APPLY_DLR_CODE =#{param.dlrCode}
		AND IS_ENABLE = '1' and AUDIT_STATUS in('1','2')
		AND CAR_LICENCE_NO=#{param.plateNumber}
		<![CDATA[AND (APPLY_TIME_BEGIN<#{param.appointmentEndTime} AND APPLY_TIME_END>#{param.appointmentStartTime})]]>
	</select>
	<!-- 手机号查重 -->
	<select id="checkPhoneRepeat" resultType="int">
		select count(1) from
		t_sac_appointment_sheet
		where DLR_CODE=#{param.dlrCode}
		and
		CUSTOMER_PHONE=#{param.customerPhone}
		and
		CUSTOMER_NAME!=#{param.customerName}
	</select>
	<!-- 试乘试驾车容量查询 -->
	<select id="selectCarCapacity" resultType="java.util.Map">
		with recursive tab1(TJ_DATE) as (
		select <![CDATA[@num := #{param.appointmentDateMin}]]>
		union all
		select <![CDATA[@num := DATE_ADD(@num,interval 1 day) from tab1 where TJ_DATE < #{param.appointmentDateMax}]]>
		),
		dt as(
		select C.CAR_LICENCE_NO AS PLATE_NUMBER,t1.TJ_DATE,t2.CONFIG_VALUE_CODE,concat(t1.TJ_DATE,' ',t2.COLUMN2) as END_TIME,
		concat(t1.TJ_DATE,' ',t2.COLUMN1) as START_TIME,c.CAR_STATUS_CODE,c.CAR_STATUS_NAME,c.is_enable,c.TESTCAR_KILOMETERS,c.VIN AS CAR_VIN,c.SMALL_CAR_TYPE_CN,c.SMALL_CAR_TYPE_CODE
		from tab1 t1
		cross join(
			select p.CAR_LICENCE_NO,p.CAR_STATUS_CODE,p.CAR_STATUS_NAME,p.is_enable,p.VIN,p.TESTCAR_KILOMETERS,t.SMALL_CAR_TYPE_CN,t.SMALL_CAR_TYPE_CODE
			from t_usc_bu_testcar_prepare p 
			left join t_prc_mdm_small_car_type t on p.IS_ENABLE='1' and p.APPLY_CAR_TYPE_CODE=t.SMALL_CAR_TYPE_CODE
			where p.RESPONSE_ORDER_STATUS='1' and p.APPLY_DLR_CODE=#{param.dlrCode}) c
		cross join t_sac_db_inner_config t2 on t2.CONFIG_CODE='DRIVE_TIME'
		order by C.CAR_LICENCE_NO,t1.TJ_DATE,t2.CONFIG_VALUE_CODE
		),
		T5 as(
		SELECT
		T3.PLATE_NUMBER,T3.TJ_DATE,T3.CONFIG_VALUE_CODE,T3.CAR_STATUS_CODE,T3.CAR_STATUS_NAME,
		T3.CAR_STATUS_NAME AS CAPACITY_STATUS,T3.SMALL_CAR_TYPE_CN,T3.SMALL_CAR_TYPE_CODE,t3.CAR_VIN,t3.TESTCAR_KILOMETERS
		FROM dt T3
		<![CDATA[where T3.CAR_STATUS_CODE not in('0','1' )and T3.is_enable='1']]> <!--查询不在空闲中、试驾中的试驾车情况 -->
		GROUP BY T3.PLATE_NUMBER,T3.TJ_DATE,T3.CONFIG_VALUE_CODE
		),
		T6 as(
		SELECT
		dt2.PLATE_NUMBER,dt2.TJ_DATE,dt2.CONFIG_VALUE_CODE,dt2.SMALL_CAR_TYPE_CN,dt2.SMALL_CAR_TYPE_CODE,
		dt2.CAR_VIN,dt2.START_TIME,dt2.END_TIME,dt2.TESTCAR_KILOMETERS,
		GROUP_CONCAT(
		CASE
		<!-- <![CDATA[when T8.IS_TEST_DRIVE='1' and NOW()<dt2.END_TIME then null]]> -->
		when lt.CAR_LICENCE_NO is not null THEN '超长出库'
		<if test="param.isCan !=null and param.isCan ==true">
		<![CDATA[when T8.IS_CAN_YUE='1' or(T8.APPOINTMENT_ID IS NULL and NOW()<dt2.END_TIME)
		or (T8.TEST_STATUS='2' and T8.APPOINTMENT_ID IS NOT NULL and NOW()<dt2.END_TIME) THEN null]]></if>
		<if test="param.isCan !=null and param.isCan ==false">
		<![CDATA[when T8.IS_CAN_YUE='1' or(T8.APPOINTMENT_ID IS NULL and NOW()<dt2.START_TIME) 
		or(T8.TEST_STATUS='2' and T8.APPOINTMENT_ID IS NOT NULL and NOW()<dt2.START_TIME) THEN null]]></if>
		<![CDATA[when T8.TEST_STATUS='1' THEN CONCAT(T8.CUSTOMER_NAME,'(试驾中)')]]>
		<![CDATA[WHEN T8.IS_TEST_DRIVE='1' THEN CONCAT(T8.CUSTOMER_NAME,'(已试驾)')]]>
	    <![CDATA[WHEN T8.IS_TEST_DRIVE='0' AND NOW()<=T8.END_TIME THEN CONCAT(T8.CUSTOMER_NAME,'(已约)')
	    WHEN T8.IS_TEST_DRIVE='0' AND NOW()>T8.END_TIME THEN CONCAT(T8.CUSTOMER_NAME,'(未试驾)')
		ELSE '不可约(已过时段)' END) AS CAPACITY_STATUS]]>
		FROM dt dt2
		<![CDATA[left join t_sac_test_drive_long_apply lt on dt2.PLATE_NUMBER=lt.CAR_LICENCE_NO and lt.AUDIT_STATUS in ('1','2')
		AND dt2.START_TIME < lt.APPLY_TIME_END AND dt2.END_TIME > lt.APPLY_TIME_BEGIN]]>
		LEFT join (
		SELECT D1.*, S.APPOINTMENT_ID, s.IS_TEST_DRIVE, S.CUSTOMER_NAME, s.DLR_CODE, s.APPOINTMENT_START_TIME, s.APPOINTMENT_END_TIME,te.TEST_STATUS
		<!--如果可以预约当前时间段， IS_CAN_YUE:根据目前时间、预约日期和当前时段的最晚小时判断是否可约 -->
		<if test="param.isCan !=null and param.isCan ==true"> <![CDATA[,CASE WHEN NOW()<D1.END_TIME and S.APPOINTMENT_ID is null THEN '1' ELSE '0' END AS IS_CAN_YUE]]></if>
		<if test="param.isCan !=null and param.isCan ==false"> <![CDATA[,CASE WHEN NOW()<D1.START_TIME and S.APPOINTMENT_ID is null THEN '1' ELSE '0' END AS IS_CAN_YUE]]></if>
		FROM dt d1
		inner JOIN t_sac_appointment_sheet s ON(
		<![CDATA[d1.START_TIME < s.APPOINTMENT_END_TIME and d1.END_TIME>s.APPOINTMENT_START_TIME and s.PLATE_NUMBER=d1.PLATE_NUMBER and s.DLR_CODE=#{param.dlrCode} and s.is_enable='1')]]>
		left join t_sac_test_drive_sheet te on te.APPOINTMENT_ID=s.APPOINTMENT_ID
		) T8 ON dt2.TJ_DATE = t8.TJ_DATE AND dt2.START_TIME = T8.START_TIME AND dt2.END_TIME = T8.END_TIME 
		AND dt2.PLATE_NUMBER = t8.PLATE_NUMBER
		WHERE dt2.CAR_STATUS_CODE in ('0','1') and dt2.is_enable='1'
		GROUP BY dt2.PLATE_NUMBER,dt2.TJ_DATE,dt2.CONFIG_VALUE_CODE
		)
	
		select TT.PLATE_NUMBER,TT.tj_date,TT.SMALL_CAR_TYPE_CN,TT.SMALL_CAR_TYPE_CODE,TT.TESTCAR_KILOMETERS,TT.CAR_VIN${param.condition}
		from(
		SELECT
			t6.PLATE_NUMBER AS PLATE_NUMBER,
			t6.tj_date AS tj_date,
			t6.CONFIG_VALUE_CODE AS CONFIG_VALUE_CODE,
			t6.SMALL_CAR_TYPE_CN AS SMALL_CAR_TYPE_CN,
			t6.SMALL_CAR_TYPE_CODE AS SMALL_CAR_TYPE_CODE,
			t6.CAR_VIN AS CAR_VIN,
			t6.TESTCAR_KILOMETERS,		
			( CASE WHEN t6.CAPACITY_STATUS IS NOT NULL THEN t6.CAPACITY_STATUS ELSE '可约' END ) AS CAPACITY_STATUS 
		FROM t6
		UNION
		SELECT
			t5.PLATE_NUMBER AS PLATE_NUMBER,
			t5.tj_date AS tj_date,
			t5.CONFIG_VALUE_CODE AS CONFIG_VALUE_CODE,
			t5.SMALL_CAR_TYPE_CN AS SMALL_CAR_TYPE_CN,
			t5.SMALL_CAR_TYPE_CODE AS SMALL_CAR_TYPE_CODE,
			t5.CAR_VIN AS CAR_VIN,
			t5.TESTCAR_KILOMETERS,
			t5.CAPACITY_STATUS AS CAPACITY_STATUS
		FROM t5 ) TT where 1=1
		<if test="param.plateNumber !=null and param.plateNumber !=''"> and TT.PLATE_NUMBER=#{param.plateNumber}</if>
		GROUP BY TT.tj_date, TT.PLATE_NUMBER order by TT.tj_date,TT.SMALL_CAR_TYPE_CODE,TT.PLATE_NUMBER
	</select>


	<!-- 查询预约时间段有哪些 -->
	<select id="selectCarCapacityTimeRange" resultType="java.util.Map">
		SELECT * FROM t_sac_db_inner_config where is_enable='1' and
		CONFIG_CODE='DRIVE_TIME' ORDER BY order_no
	</select>
</mapper>
