<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.RemoveRepeatConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.RemoveRepeatConfig">
        <id column="CONFIG_ID" property="configId" />
        <result column="CHECK_PHONE" property="checkPhone" />
        <result column="CHECK_TIME" property="checkTime" />
        <result column="CHECK_TIME_HORIZON" property="checkTimeHorizon" />
        <result column="CUSTOM" property="custom" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="ORG_NAME" property="orgName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CONFIG_ID, CHECK_PHONE, CHECK_TIME, CHECK_TIME_HORIZON, CUSTOM, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE,ORG_CODE,ORG_NAME, UPDATE_CONTROL_ID
    </sql>
    
    <!-- 查询潜客去重规则 -->
    <select id="selectRemoveRepeatConfig" resultType="java.util.Map">
    	select 
	    	CONFIG_ID, 
	    	CHECK_PHONE, 
	    	CHECK_TIME, 
	    	CHECK_TIME_HORIZON, 
	    	CUSTOM, 
	    	OEM_ID, 
	    	GROUP_ID, 
	    	CREATOR, 
	    	CREATED_NAME, 
	    	CREATED_DATE, 
	    	MODIFIER, 
	    	MODIFY_NAME, 
	    	LAST_UPDATED_DATE, 
	    	IS_ENABLE, 
	    	ORG_CODE,
	    	ORG_NAME,
	    	UPDATE_CONTROL_ID
	    from t_sac_remove_repeat_config
	    where 1=1
	    <if test="param.configId !=null and param.configId !=''"> and CONFIG_ID=#{param.configId}</if>
		<if test="param.checkPhone !=null and param.checkPhone !=''"> and CHECK_PHONE=#{param.checkPhone}</if>
		<if test="param.checkTime !=null and param.checkTime !=''"> and CHECK_TIME=#{param.checkTime}</if>
		<if test="param.checkTimeHorizon !=null and param.checkTimeHorizon !=''"> and CHECK_TIME_HORIZON=#{param.checkTimeHorizon}</if>
		<if test="param.custom !=null and param.custom !=''"> and CUSTOM=#{param.custom}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''"> and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.orgCode !=null and param.orgCode !=''"> and ORG_CODE=#{param.orgCode}</if>
    	order by LAST_UPDATED_DATE desc
    </select>
    
    <!-- 新增潜客去重规则 -->
	<insert id="insertRemoveRepeatConfig">
	    insert into t_sac_remove_repeat_config(
        CONFIG_ID,
        CHECK_PHONE,
        CHECK_TIME,
        CHECK_TIME_HORIZON,
        CUSTOM,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        UPDATE_CONTROL_ID,
        ORG_CODE,
        ORG_NAME,
		IS_ENABLE)
		values(
               #{param.configId},
               #{param.checkPhone},
               #{param.checkTime},
               #{param.checkTimeHorizon},
               #{param.custom},
               #{param.oemId},
               #{param.groupId},
               #{param.creator},
               #{param.createdName},
               #{param.createdDate},
               #{param.modifier},
               #{param.modifyName},
               #{param.lastUpdatedDate},
               #{param.updateControlId},
               #{param.orgCode},
               #{param.orgName},
							 #{param.isEnable})
	</insert>
    <!-- 修改更新潜客去重规则 -->
	<update id="updateRemoveRepeatConfig">
		update t_sac_remove_repeat_config set
		<if test="param.configId !=null and param.configId !=''"> CONFIG_ID=#{param.configId},</if>
		<if test="param.checkPhone !=null and param.checkPhone !=''"> CHECK_PHONE=#{param.checkPhone},</if>
		<if test="param.checkTime !=null and param.checkTime !=''"> CHECK_TIME=#{param.checkTime},</if>
		<if test="param.checkTimeHorizon !=null and param.checkTimeHorizon !=''"> CHECK_TIME_HORIZON=#{param.checkTimeHorizon},</if>
		<if test="param.custom !=null"> CUSTOM=#{param.custom},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null and param.createdDate !=''"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
	    <if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
	    <if test="param.orgCode !=null and param.orgCode !=''"> ORG_CODE=#{param.orgCode},</if>
	    <if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		 where CONFIG_ID=#{param.configId}
	 </update>
	 
	 <!-- 校验潜客去重规则是否已存在 -->
	 <select id="checkRepeat" resultType="int">
      select count(1) as countNo
      from t_sac_remove_repeat_config t
      where t.ORG_CODE=#{param.orgCode}
      <if test="param.configId != null and param.configId != ''">
          AND t.CONFIG_ID != #{param.configId}
      </if>
  </select>
  
  <select id="checkRemoveRepeatConfigExists" resultType="int">
        select count(1)
        from t_sac_remove_repeat_config
        where CONFIG_ID=#{configId}
    </select>
</mapper>