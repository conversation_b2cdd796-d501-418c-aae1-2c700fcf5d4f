<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacAttachmentMapper">


	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacAttachment">
		<id column="ATTACHMENT_ID" property="attachmentId" />
		<result column="BILL_ID" property="billId" />
		<result column="BILL_TYPE" property="billType" />
		<result column="FILE_NAME" property="fileName" />
		<result column="FILE_EXTENSION" property="fileExtension" />
		<result column="FILE_PATH" property="filePath" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.ATTACHMENT_ID
		, t1.BILL_ID
		, t1.BILL_TYPE
		, t1.FILE_NAME
		, t1.FILE_EXTENSION
		, t1.FILE_PATH
		, t1.OEM_ID
		, t1.GROUP_ID
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.MODIFIER
		, t1.MODIFY_NAME
		, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE
		, t1.UPDATE_CONTROL_ID
	</sql>
	
	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.attachmentId !=null and param.attachmentId !=''">and t1.ATTACHMENT_ID=#{param.attachmentId}</if>
		<if test="param.billId !=null and param.billId !=''">and t1.BILL_ID=#{param.billId}</if>
		<if test="param.billType !=null and param.billType !=''">and t1.BILL_TYPE=#{param.billType}</if>
		<if test="param.fileExtension !=null and param.fileExtension !=''">and t1.FILE_EXTENSION=#{param.fileExtension}</if>
		<if test="param.filePath !=null and param.filePath !=''">and t1.FILE_PATH=#{param.filePath}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		
		<if test="param.fileName !=null and param.fileName !=''">and t1.FILE_NAME like concat('%',#{param.fileName},'%')</if>
		<if test="param.billIdList !=null">and t1.BILL_ID in
			<foreach collection="param.billIdList" separator="," open="(" item="item" close=")">
			#{item}
			</foreach>
		</if>	
		<if test="param.relationBillId != null and '' !=  param.relationBillId">
			AND t1.ATTACHMENT_ID IN
			<foreach collection="param.relationBillId.split(',')"
				item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</sql>
	
	<!-- 新增 -->
	<insert id="insertSacAttachment">
		insert into t_sac_attachment(
		ATTACHMENT_ID
		, BILL_ID
		, BILL_TYPE
		, FILE_NAME
		, FILE_EXTENSION
		, FILE_PATH
		, OEM_ID
		, GROUP_ID
		, CREATOR
		, CREATED_NAME
		, CREATED_DATE
		, MODIFIER
		, MODIFY_NAME
		, LAST_UPDATED_DATE
		, IS_ENABLE
		, UPDATE_CONTROL_ID
		) values (
		#{param.attachmentId}
		, #{param.billId}
		, #{param.billType}
		, #{param.fileName}
		, #{param.fileExtension}
		, #{param.filePath}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		)
	</insert>

	<!-- 修改 -->
	<update id="updateSacAttachment">
		update t_sac_attachment
		<set>
		LAST_UPDATED_DATE=sysdate(),
		UPDATE_CONTROL_ID=uuid(),
			<if test="param.attachmentId !=null and param.attachmentId !=''"> ATTACHMENT_ID=#{param.attachmentId},</if>
			<if test="param.billId !=null and param.billId !=''"> BILL_ID=#{param.billId},</if>
			<if test="param.billType !=null and param.billType !=''"> BILL_TYPE=#{param.billType},</if>
			<if test="param.fileName !=null and param.fileName !=''"> FILE_NAME=#{param.fileName},</if>
			<if test="param.fileExtension !=null and param.fileExtension !=''"> FILE_EXTENSION=#{param.fileExtension},</if>
			<if test="param.filePath !=null and param.filePath !=''"> FILE_PATH=#{param.filePath},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		</set>
		where 1=1
		and ATTACHMENT_ID=#{param.attachmentId}
		<if test = 'updateControlId!=null'>
		 	and UPDATE_CONTROL_ID = #{updateControlId}
		</if>
	</update>
	
	<select id="selectByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_attachment t1
		where 1=1
		<include refid="where_condition"></include>
	</select>
</mapper>