<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbCarColorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.DbCarColor">
        <id column="CAR_COLOR_ID" property="carColorId" />
        <result column="CAR_COLOR_CODE" property="carColorCode" />
        <result column="CAR_COLOR_NAME" property="carColorName" />
        <result column="SUPPLY_STATUS" property="supplyStatus" />
        <result column="CAR_BRAND_CODE" property="carBrandCode" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CAR_COLOR_ID, CAR_COLOR_CODE, CAR_COLOR_NAME, SUPPLY_STATUS, CAR_BRAND_CODE, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10
    </sql>
    <!-- 车身颜色查询 -->
	<select id="selectDbCarColor" resultType="java.util.Map">
	    select
        t.CAR_COLOR_ID,
        t.CAR_COLOR_CODE,
        t.CAR_COLOR_NAME,
        t.SUPPLY_STATUS,
        t.CAR_BRAND_CODE,
        t2.CAR_BRAND_CN,
        t.OEM_ID,
        t.GROUP_ID,
        t.CREATOR,
        t.CREATED_NAME,
        t.CREATED_DATE,
        t.MODIFIER,
        t.MODIFY_NAME,
        t.LAST_UPDATED_DATE,
        t.UPDATE_CONTROL_ID,
		t.IS_ENABLE
		from t_sac_db_car_color t
		left join t_sac_db_car_brand t2 on t2.CAR_BRAND_CODE=t.CAR_BRAND_CODE
		where 1=1
		<if test="param.carColorCode !=null and param.carColorCode !=''"> and t.CAR_COLOR_CODE like concat('%',#{param.carColorCode},'%')</if>
		<if test="param.carColorName !=null and param.carColorName !=''"> and t.CAR_COLOR_NAME like concat('%',#{param.carColorName},'%')</if>
		<if test="param.supplyStatus !=null and param.supplyStatus !=''"> and t.SUPPLY_STATUS=#{param.supplyStatus}</if>
		<if test="param.carBrandCode !=null and param.carBrandCode !=''"> and t.CAR_BRAND_CODE like concat('%',#{param.carBrandCode},'%')</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and t.IS_ENABLE=#{param.isEnable}</if>
	</select>
</mapper>
