<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacEvaluationInfoMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacEvaluationInfo">
		<id column="EVALUATION_INFO_ID" property="evaluationInfoId" />
		<result column="DLR_CODE" property="dlrCode" />
		<result column="DLR_NAME" property="dlrName" />
		<result column="TEST_DRIVE_SHEET_ID" property="testDriveSheetId" />
		<result column="EVALUATION_TYPE" property="evaluationType" />
		<result column="EVALUATION_ITEM" property="evaluationItem" />
		<result column="EVALUATION_SCORE" property="evaluationScore" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="COLUMN1" property="column1" />
		<result column="COLUMN2" property="column2" />
		<result column="COLUMN3" property="column3" />
		<result column="COLUMN4" property="column4" />
		<result column="COLUMN5" property="column5" />
		<result column="COLUMN6" property="column6" />
		<result column="COLUMN7" property="column7" />
		<result column="COLUMN8" property="column8" />
		<result column="COLUMN9" property="column9" />
		<result column="COLUMN10" property="column10" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		EVALUATION_INFO_ID, DLR_CODE, DLR_NAME,
		TEST_DRIVE_SHEET_ID, EVALUATION_TYPE,
		EVALUATION_ITEM,
		EVALUATION_SCORE, OEM_ID, GROUP_ID, CREATOR,
		CREATED_NAME,
		CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID, IS_ENABLE
	</sql>
	<!-- 试乘试驾评价查找 -->
	<select id="selectSacEvaluationInfo" resultType="java.util.Map">
		select
		t.EVALUATION_INFO_ID, t.DLR_CODE, t.DLR_NAME, t.TEST_DRIVE_SHEET_ID,
		t.EVALUATION_TYPE,
		t.EVALUATION_ITEM, t.EVALUATION_SCORE, t.OEM_ID, t.GROUP_ID,
		t.CREATOR,t.CREATED_NAME, t.CREATED_DATE, t.MODIFIER, t.MODIFY_NAME,
		t.LAST_UPDATED_DATE,
		t.UPDATE_CONTROL_ID, t.IS_ENABLE
		from t_sac_evaluation_info t
		where 1=1
		<if
			test="param.evaluationInfoId !=null and param.evaluationInfoId !=''">and t.EVALUATION_INFO_ID=#{param.evaluationInfoId}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and t.DLR_NAME=#{param.dlrName}</if>
		<if
			test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
		<if
			test="param.evaluationType !=null and param.evaluationType !=''">and t.EVALUATION_TYPE=#{param.evaluationType}</if>
		<if
			test="param.evaluationItem !=null and param.evaluationItem !=''">and t.EVALUATION_ITEM=#{param.evaluationItem}</if>
		<if
			test="param.evaluationScore !=null and param.evaluationScore !=''">and t.EVALUATION_SCORE=#{param.evaluationScore}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and t.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		order by t.TEST_DRIVE_SHEET_ID
	</select>
	<!-- 试乘试驾评价新增 -->
	<insert id="insertSacEvaluationInfo">
		insert into t_sac_evaluation_info(
		EVALUATION_INFO_ID,
		DLR_CODE,
		DLR_NAME,
		TEST_DRIVE_SHEET_ID,
		EVALUATION_TYPE,
		EVALUATION_ITEM,
		EVALUATION_SCORE,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		IS_ENABLE)
		values(
		#{param.evaluationInfoId},
		#{param.dlrCode},
		#{param.dlrName},
		#{param.testDriveSheetId},
		#{param.evaluationType},
		#{param.evaluationItem},
		#{param.evaluationScore},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		#{param.updateControlId},
		#{param.isEnable})
	</insert>
	<!-- 试乘试驾评价修改 -->
	<update id="updateSacEvaluationInfo">
		update t_sac_evaluation_info set
		<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> DLR_NAME=#{param.dlrName},</if>
		<if
			test="param.testDriveSheetId !=null and param.testDriveSheetId !=''"> TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId},</if>
		<if
			test="param.evaluationType !=null and param.evaluationType !=''"> EVALUATION_TYPE=#{param.evaluationType},</if>
		<if
			test="param.evaluationItem !=null and param.evaluationItem !=''"> EVALUATION_ITEM=#{param.evaluationItem},</if>
		<if
			test="param.evaluationScore !=null and param.evaluationScore !=''"> EVALUATION_SCORE=#{param.evaluationScore},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		where EVALUATION_INFO_ID=#{param.evaluationInfoId}
	</update>
	
	<select id="selectEvaluationType" resultType="java.util.Map">
		select
		DLR_CODE, DLR_NAME,
		TEST_DRIVE_SHEET_ID,
		EVALUATION_TYPE,
		OEM_ID, GROUP_ID, CREATOR,
		CREATED_NAME,
		CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID, IS_ENABLE
		from t_sac_evaluation_info where TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}
		GROUP BY EVALUATION_TYPE
	</select>
</mapper>
