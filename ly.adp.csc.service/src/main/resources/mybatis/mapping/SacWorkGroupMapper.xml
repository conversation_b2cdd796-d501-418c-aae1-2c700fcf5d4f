<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacWorkGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacWorkGroup">
        <id column="WORK_GROUP_ID" property="workGroupId" />
        <result column="WORK_GROUP_NAME" property="workGroupName" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="BILL_TYPE" property="billType" />
        <result column="BILL_TYPE_NAME" property="billTypeName" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="BUSINESS_TYPE_NAME" property="businessTypeName" />
        <result column="REMARK" property="remark" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WORK_GROUP_ID, WORK_GROUP_NAME, DLR_CODE, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, REMARK, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

 	<insert id="insertSacWorkGroup">
        insert into t_sac_work_group(
         WORK_GROUP_ID
        ,WORK_GROUP_NAME
        ,DLR_CODE
        ,BILL_TYPE
        ,BILL_TYPE_NAME
        ,BUSINESS_TYPE
        ,BUSINESS_TYPE_NAME
        ,REMARK
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.workGroupId}
        ,#{param.workGroupName}
        ,#{param.dlrCode}
        ,#{param.billType}
        ,#{param.billTypeName}
        ,#{param.businessType}
        ,#{param.businessTypeName}
        ,#{param.remark}
        ,#{param.oemId}
        ,#{param.groupId}
        ,#{param.creator}
        ,#{param.createdName}
        ,#{param.createdDate}
        ,#{param.modifier}
        ,#{param.modifyName}
        ,#{param.lastUpdatedDate}
        ,#{param.isEnable}
        ,#{param.updateControlId}
		)
    </insert>
    
    <update id="updateSacWorkGroup">
    	update t_sac_work_group  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	UPDATE_CONTROL_ID = #{param.updateControlId},
    	LAST_UPDATED_DATE = #{param.lastUpdatedDate}
	    <if test = 'param.workGroupName!=null'> ,WORK_GROUP_NAME = #{param.workGroupName}</if>
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.billTypeName!=null'> ,BILL_TYPE_NAME = #{param.billTypeName}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{param.businessTypeName}</if>
	    <if test = 'param.remark!=null'> ,REMARK = #{param.remark}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
        and WORK_GROUP_ID = #{param.workGroupId}
    	
    </update>

    <select id="checkWorkGroupRepeat" resultType="int">
        select count(1) as countNo
        from t_sac_work_group g
                where 1 = 1
                  AND g.is_enable='1'
                  AND g.dlr_code = #{param.dlrCode}
                  AND g.work_group_name = #{param.workGroupName}
        <if test="param.workGroupId != null and param.workGroupId != ''">
            AND WORK_GROUP_ID != #{param.workGroupId}
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-08-19-->
    <select id="checkWorkGroupExists" resultType="int">
        select count(1)
        from t_sac_work_group
        where WORK_GROUP_ID=#{workGroupId}
    </select>

    <select id="checkWorkGroupUserExists" resultType="int">
        select count(1) as countNo
        from t_sac_work_group_user g
        <where>
           AND g.work_group_id = #{param.workGroupId}
           AND g.work_group_user_id = #{param.workGroupUserId}
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-08-19-->
    <insert id="insertListWorkGroupUser">
        INSERT INTO t_sac_work_group_user(
            WORK_GROUP_USER_ID,
            WORK_GROUP_ID,
            USER_ID,
            USER_NAME,
            EMP_ID,
            EMP_NAME,
            IS_LEADER,
            REMARK,
            OEM_ID,
            GROUP_ID,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            UPDATE_CONTROL_ID
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
        (
            #{element.workGroupUserId},
            #{element.workGroupId},
            #{element.userId},
            #{element.userName},
            #{element.empId},
            #{element.empName},
            #{element.isLeader},
            #{element.remark},
            #{element.oemId},
            #{element.groupId},
            #{element.creator},
            #{element.createdName},
            #{element.createdDate},
            #{element.modifier},
            #{element.modifyName},
            #{element.lastUpdatedDate},
            #{element.isEnable},
            #{element.updateControlId}
        )
        </foreach>
    </insert>

    <select id="queryListWorkGroupInfo" resultType="java.util.Map">
        select WORK_GROUP_ID as workGroupId,
               WORK_GROUP_NAME as workGroupName,
               BILL_TYPE as billType,
               BILL_TYPE_NAME as billTypeName,
               BUSINESS_TYPE as businessType,
               BUSINESS_TYPE_NAME as businessTypeName,
               REMARK as remark,
               IS_ENABLE as isEnable,
               IF(IS_ENABLE = '1', '启用', '禁用') AS isEnableName,
               OEM_ID,
	           GROUP_ID,
	           CREATOR,
	           CREATED_NAME,
	           CREATED_DATE,
	           MODIFIER,
	           MODIFY_NAME,
	           LAST_UPDATED_DATE,
	           UPDATE_CONTROL_ID
        from t_sac_work_group g
        where 1=1 
        AND g.DLR_CODE = #{param.dlrCode}
        <if test="param.workGroupId != null and '' != param.workGroupId">
            AND g.WORK_GROUP_ID = #{param.workGroupId}
        </if>
        <if test="param.workGroupName != null and '' != param.workGroupName">
            AND INSTR(g.WORK_GROUP_NAME,#{param.workGroupName})
        </if>
        <if test="param.billType != null and '' != param.billType">
            AND g.BILL_TYPE = #{param.billType}
        </if>
        <if test="param.businessType != null and '' != param.businessType">
            AND g.BUSINESS_TYPE = #{param.businessType}
        </if>
        <if test="param.isEnable != null and '' != param.isEnable">
            AND g.IS_ENABLE = #{param.isEnable}
        </if>
        order by LAST_UPDATED_DATE desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-08-19-->
    <update id="updateIsLeaderByWorkGroupIdAndWorkGroupUserId">
        update t_sac_work_group_user
        set IS_LEADER=#{param.isLeader},
            MODIFIER = #{param.modifier},
            MODIFY_NAME = #{param.modifyName},
            LAST_UPDATED_DATE = sysdate()
        <where>
        <if test="param.workGroupId != null and '' != param.workGroupId">
            and WORK_GROUP_ID = #{param.workGroupId}
        </if>
        <if test="param.workGroupUserId != null and '' != param.workGroupUserId and param.isLeader =='1'.toString()">
            and WORK_GROUP_USER_ID = #{param.workGroupUserId}
        </if>
        </where>
    </update>

    <delete id="deleteWorkGroupUser">
        delete from t_sac_work_group_user
        <where>
            <if test="!@com.ly.mp.component.helper.StringHelper@IsEmptyOrNull(param.workGroupUserId)">
                and WORK_GROUP_USER_ID IN
                <foreach collection="param.workGroupUserId" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <select id="queryListWorkGroupUserInfo" resultType="java.util.Map">
        select
        USER_ID as userId,
        USER_NAME as userName,
        EMP_ID as empId,
        EMP_NAME as empName,
        IS_LEADER as isLeader,
        IF(IS_LEADER = '1', '是', '否') AS isLeaderName,
        WORK_GROUP_ID as workGroupId,
        WORK_GROUP_USER_ID as workGroupUserId
        from t_sac_work_group_user
        <where>
            <if test="param.workGroupId != null and '' != param.workGroupId">
                and WORK_GROUP_ID = #{param.workGroupId}
            </if>
            <if test="param.userName != null and '' != param.userName">
                and INSTR(USER_NAME, #{param.userName})
            </if>
            <if test="param.empName != null and '' != param.empName">
                and INSTR(EMP_NAME,#{param.empName})
            </if>
        </where>
    </select>
    
    <!-- 获取本组员工列表 -->
    <select id="selectGroupUserList" resultType="Map">
       SELECT GROUP_CONCAT(USER_id) AS USER_ID_LIST
		FROM(
		SELECT DISTINCT pp.user_id
		FROM t_sac_work_group_user pp
		WHERE pp.WORK_GROUP_ID in (
		SELECT t.WORK_GROUP_ID
		FROM t_sac_work_group t
		WHERE t.IS_ENABLE='1' 
		AND EXISTS (
		SELECT 1
		FROM t_sac_work_group_user a
		WHERE t.WORK_GROUP_ID=a.WORK_GROUP_ID 
		AND a.emp_id=#{param.empId}
		<if test="param.isLeader != null and '' != param.isLeader">
		AND A.IS_LEADER=#{param.isLeader}
		</if>
				)
		)
		)a 
    </select>
    
    <!-- 获取本组员工待回访数列表 -->
    <select id="selectGroupUserReviewNum" resultType="Map">
		SELECT
		a.user_id,a.user_name,a.emp_id,a.emp_name,
		sum(case when r.REVIEW_ID IS NOT NULL then 1 ELSE 0 END) AS num
		FROM (
		SELECT DISTINCT pp.user_id,pp.user_name,pp.emp_id,pp.emp_name
				FROM t_sac_work_group_user pp
				WHERE pp.WORK_GROUP_ID in (
				SELECT t.WORK_GROUP_ID
				FROM t_sac_work_group t
				WHERE t.IS_ENABLE='1' 
				AND EXISTS (
				SELECT 1
				FROM t_sac_work_group_user a
				WHERE t.WORK_GROUP_ID=a.WORK_GROUP_ID 
				AND A.IS_LEADER='1'
				AND a.emp_id=#{param.empId}
						)
				)
		) a
		LEFT JOIN t_sac_review r ON r.REVIEW_PERSON_ID=a.user_id and r.review_status in('0','1')
		GROUP BY a.user_id	
    </select>
    
</mapper>
