<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.ClueInfoDlrFirstOverdueMapper">

    <insert id="batchSaveFirstOverDue">
        INSERT INTO csc.t_sac_clue_info_dlr_firstoverdue (id, clue_dlr_id, dlr_code, first_assign_dlr_time,
                                                              config_first_overdue_time, first_overdue_time,
                                                              creator, created_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.clueDlrId}, #{item.dlrCode}, #{item.firstAssignDlrTime}, #{item.configFirstOverdueTime}, #{item.firstOverdueTime},
            #{item.creator}, #{item.createdTime}
            )
        </foreach>
    </insert>
</mapper>