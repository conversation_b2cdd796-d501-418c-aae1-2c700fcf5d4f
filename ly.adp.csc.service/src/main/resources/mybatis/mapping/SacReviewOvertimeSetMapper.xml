<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeSetMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewOvertimeSet">
  	<id column="SET_ID" property="setId" />
    <result column="ORG_CODE" property="orgCode" />
    <result column="ORG_NAME" property="orgName" />
    <result column="BILL_TYPE" property="billType" />
    <result column="BILL_TYPE_NAME" property="billTypeName" />
    <result column="BUSINESS_TYPE" property="businessType" />
    <result column="BUSINESS_TYPE_NAME" property="businessTypeName" />
    <result column="CHANNEL_CODE" property="channelCode" />
    <result column="CHANNEL_NAME" property="channelName" />
    <result column="FIRST_RULE_ID" property="firstRuleId" />
    <result column="SEC_RULE_ID" property="secRuleId" />
    <result column="OEM_ID" property="oemId" />
    <result column="GROUP_ID" property="groupId" />
    <result column="CREATOR" property="creator" />
    <result column="CREATED_NAME" property="createdName" />
    <result column="CREATED_DATE" property="createdDate" />
    <result column="MODIFIER" property="modifier" />
    <result column="MODIFY_NAME" property="modifyName" />
    <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
    <result column="IS_ENABLE" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    <result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
    <result column="INFO_CHAN_M_NAME" property="infoChanMName" />
    <result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
    <result column="INFO_CHAN_D_NAME" property="infoChanDName" />
    <result column="INFO_CHAN_DD_CODE" property="infoChanDDCode" />
    <result column="INFO_CHAN_DD_NAME" property="infoChanDDName" />
  </resultMap>
  <sql id="Base_Column_List">
	SET_ID, ORG_CODE, ORG_NAME, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, CHANNEL_CODE, CHANNEL_NAME, 
	FIRST_RULE_ID, SEC_RULE_ID, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, 
	LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID, INFO_CHAN_M_CODE, INFO_CHAN_M_NAME, INFO_CHAN_D_CODE, 
	INFO_CHAN_D_NAME, INFO_CHAN_DD_CODE, INFO_CHAN_DD_NAME
  </sql>

  <select id="checkReviewOvertime" resultType="int">
      select count(1) as countNo
      from t_sac_review_overtime_set s
      where 1 = 1
      AND s.is_enable = '1'
      AND s.ORG_CODE=#{param.orgCode}
      AND s.bill_type = #{param.billType}
      <if test="param.businessType != null and param.businessType != ''">
          AND s.business_type = #{param.businessType}
      </if>
      <if test="param.setId != null and param.setId != ''">
          AND s.SET_ID != #{param.setd}
      </if>
      <choose>
	      <when test="param.infoChanMCode !=null and param.infoChanMCode !=''"> AND s.INFO_CHAN_M_CODE=#{param.infoChanMCode}</when>
	      <otherwise>and (s.INFO_CHAN_M_CODE is null or s.INFO_CHAN_M_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.infoChanDCode !=null and param.infoChanDCode !=''"> AND s.INFO_CHAN_D_CODE=#{param.infoChanDCode}</when>
	      <otherwise>and (s.INFO_CHAN_D_CODE is null or s.INFO_CHAN_D_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.infoChanDDCode !=null and param.infoChanDDCode !=''"> AND s.INFO_CHAN_DD_CODE=#{param.infoChanDDCode}</when>
	      <otherwise>and (s.INFO_CHAN_DD_CODE is null or s.INFO_CHAN_DD_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.channelCode !=null and param.channelCode !=''"> AND s.CHANNEL_CODE = #{param.channelCode}</when>
	      <otherwise>and (s.CHANNEL_CODE is null or s.CHANNEL_CODE='')</otherwise>
      </choose>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-08-18-->
  <insert id="insertReviewOvertimeSet">
  insert into t_sac_review_overtime_set(
       SET_ID,
        ORG_CODE,
        ORG_NAME,
        BILL_TYPE,
        BILL_TYPE_NAME,
        BUSINESS_TYPE,
        BUSINESS_TYPE_NAME,
        CHANNEL_CODE,
        CHANNEL_NAME,
        FIRST_RULE_ID,
        SEC_RULE_ID,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        UPDATE_CONTROL_ID,
        INFO_CHAN_M_CODE,
        INFO_CHAN_M_NAME,
        INFO_CHAN_D_CODE,
        INFO_CHAN_D_NAME,
        INFO_CHAN_DD_CODE,
        INFO_CHAN_DD_NAME,
		IS_ENABLE)
		values(
               #{param.setId},
               #{param.orgCode},
               #{param.orgName},
               #{param.billType},
               #{param.billTypeName},
               #{param.businessType},
               #{param.businessTypeName},
               #{param.channelCode},
               #{param.channelName},
               #{param.firstRuleId},
               #{param.secRuleId},
               #{param.oemId},
               #{param.groupId},
               #{param.creator},
               #{param.createdName},
               #{param.createdDate},
               #{param.modifier},
               #{param.modifyName},
               #{param.lastUpdatedDate},
               #{param.updateControlId},
               #{param.infoChanMCode},
               #{param.infoChanMName},
               #{param.infoChanDCode},
               #{param.infoChanDName},
               #{param.infoChanDDCode},
               #{param.infoChanDDName},
			   #{param.isEnable}
        )
</insert>
<!--auto generated by MybatisCodeHelper on 2021-08-18-->
  <update id="updateBySetIdAndUpdateControlId">
	update t_sac_review_overtime_set set
		<if test="param.setId !=null and param.setId !=''"> SET_ID=#{param.setId},</if>
		<if test="param.orgCode !=null and param.orgCode !=''"> ORG_CODE=#{param.orgCode},</if>
		<if test="param.orgName !=null and param.orgName !=''"> ORG_NAME=#{param.orgName},</if>
		<if test="param.billType !=null and param.billType !=''"> BILL_TYPE=#{param.billType},</if>
		<if test="param.billTypeName !=null and param.billTypeName !=''"> BILL_TYPE_NAME=#{param.billTypeName},</if>
		<if test="param.businessType !=null and param.businessType !=''"> BUSINESS_TYPE=#{param.businessType},</if>
		<if test="param.businessTypeName !=null and param.businessTypeName !=''"> BUSINESS_TYPE_NAME=#{param.businessTypeName},</if>
		<if test="param.channelCode !=null"> CHANNEL_CODE=#{param.channelCode},</if>
		<if test="param.channelName !=null"> CHANNEL_NAME=#{param.channelName},</if>
		<if test="param.firstRuleId !=null and param.firstRuleId !=''"> FIRST_RULE_ID=#{param.firstRuleId},</if>
		<if test="param.secRuleId !=null and param.secRuleId !=''"> SEC_RULE_ID=#{param.secRuleId},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.infoChanMCode !=null"> INFO_CHAN_M_CODE=#{param.infoChanMCode},</if>
		<if test="param.infoChanMName !=null"> INFO_CHAN_M_NAME=#{param.infoChanMName},</if>
		<if test="param.infoChanDCode !=null"> INFO_CHAN_D_CODE=#{param.infoChanDCode},</if>
		<if test="param.infoChanDName !=null"> INFO_CHAN_D_NAME=#{param.infoChanDName},</if>
		<if test="param.infoChanDDCode !=null"> INFO_CHAN_DD_CODE=#{param.infoChanDDCode},</if>
		<if test="param.infoChanDDName !=null"> INFO_CHAN_DD_NAME=#{param.infoChanDDName},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable}</if>
		UPDATE_CONTROL_ID = #{param.updateControlId},
    	LAST_UPDATED_DATE = #{param.lastUpdatedDate}
	where SET_ID=#{param.setId}
  </update>
<!--auto generated by MybatisCodeHelper on 2021-08-18-->
  <select id="countBySetId" resultType="java.lang.Integer">
    select
        count(1)
    from t_sac_review_overtime_set
    where SET_ID=#{setId}
  </select>

  <select id="queryListReviewOvertimeInfo" resultType="java.util.Map">
      select s.SET_ID             AS setId,
             s.ORG_CODE           AS orgCode,
             s.ORG_NAME           AS orgName,
             s.BILL_TYPE          AS billType,
             s.BILL_TYPE_NAME     AS billTypeName,
             s.BUSINESS_TYPE      AS businessType,
             s.BUSINESS_TYPE_NAME AS businessTypeName,
             s.CHANNEL_CODE         AS channelCode,
             s.CHANNEL_NAME       AS channelName,
             s.FIRST_RULE_ID      AS firstRuleId,
             r1.RULE_NAME         AS firstRuleName,
             s.SEC_RULE_ID        AS secRuleId,
             r2.RULE_NAME         AS secRuleName,
             s.INFO_CHAN_M_CODE as infoChanMCode,
             s.INFO_CHAN_M_NAME as infoChanMName,
             s.INFO_CHAN_D_CODE as infoChanDCode,
             s.INFO_CHAN_D_NAME as infoChanDName,
             s.INFO_CHAN_DD_CODE as infoChanDDCode,
             s.INFO_CHAN_DD_NAME as infoChanDDName
      from t_sac_review_overtime_set s
       inner join t_sac_review_overtime_rule r1 on s.first_rule_id = r1.rule_id
       inner join t_sac_review_overtime_rule r2 on s.sec_rule_id = r2.rule_id
      <where>
          <if test="param.billType != null and '' != param.billType">
              AND s.bill_type = #{param.billType}
          </if>
          <if test="param.businessType != null and '' != param.businessType">
              AND s.business_type = #{param.businessType}
          </if>
          <if test="param.orgCode != null and '' != param.orgCode">
              AND s.org_code = #{param.orgCode}
          </if>
          <if test="param.channelCode != null and '' != param.channelCode">
              AND s.CHANNEL_CODE = #{param.channelCode}
          </if>
          <if test="param.infoChanMCode != null and '' != param.infoChanMCode">
              AND s.INFO_CHAN_M_CODE = #{param.infoChanMCode}
          </if>
          <if test="param.infoChanDCode != null and '' != param.infoChanDCode">
              AND s.INFO_CHAN_D_CODE = #{param.infoChanDCode}
          </if>
          <if test="param.infoChanDDCode != null and '' != param.infoChanDDCode">
              AND s.INFO_CHAN_DD_CODE = #{param.infoChanDDCode}
          </if>
      </where>
  </select>
  
  <select id="machOverTimeRule" resultType="java.util.Map">
    WITH recursive classTb(channel_code) AS (
		SELECT CHANNEL_CODE
		FROM t_sac_channel_info 
		WHERE CHANNEL_CODE=#{param.channelCode}
		UNION ALL
		SELECT s.CHANNEL_CODE
		FROM t_sac_channel_info s,classTb
		WHERE 1=1
		AND s.PARENT_CHANNEL_CODE=classTb.CHANNEL_CODE
	),
	classTb2 as(
		SELECT 
		s.CHANNEL_CODE,s.CHANNEL_LEVEL_CODE
		FROM classTb C 
		INNER JOIN t_sac_channel_info s ON c.CHANNEL_CODE=s.CHANNEL_CODE
		UNION ALL
		SELECT '全部' AS  CHANNEL_CODE,'-1' AS CHANNEL_LEVEL_CODE FROM DUAL
	)
	SELECT 
	M.SET_ID,
	M.FIRST_RULE_ID,
	M.SEC_RULE_ID
	FROM T_SAC_REVIEW_OVERTIME_SET M
	inner JOIN classTb2 C ON c.CHANNEL_CODE=if(ifnull(m.CHANNEL_CODE,'')='','全部',m.CHANNEL_CODE)
	WHERE m.is_enable='1' 
	<if test='param.configRange != null and "GLOBAL" != param.configRange'>
	AND M.ORG_CODE = #{param.orgCode}
	</if>
	AND M.BILL_TYPE = #{param.billType}
	AND (IFNULL(M.BUSINESS_TYPE,'') ='' OR M.BUSINESS_TYPE = #{param.businessType})
	ORDER BY C.CHANNEL_LEVEL_CODE DESC
   </select>
   
   <select id="queryBillOverTime" resultType="java.util.Map">
    select 
	case when d.METHOD_TYPE='1' then 
	concat(date(DATE_ADD(#{param.planReviewTime},INTERVAL ifnull(d.ADD_DAYS,0) DAY)),' ',d.ADD_HOURS) 
	when d.METHOD_TYPE='2' then 
	DATE_ADD(#{param.planReviewTime},INTERVAL ifnull(d.ADD_DYN_HOURS,0) HOUR) 
	END AS OVER_TIME
	from t_sac_review_overtime_rule_d d
	WHERE d.IS_ENABLE='1'
	and d.RULE_ID=#{param.ruleId}
	<![CDATA[ and #{param.planReviewTime}>= concat(date(#{param.planReviewTime}),' ',d.START_TIME) ]]> 
	<![CDATA[ and #{param.planReviewTime}<concat(date(#{param.planReviewTime}),' ',d.END_TIME) ]]>
   </select>
   
</mapper>