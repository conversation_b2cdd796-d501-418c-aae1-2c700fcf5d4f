<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacSystemConfigValueMapper">
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacSystemConfigValue">
		<id column="CONFIG_VALUE_ID" jdbcType="VARCHAR" property="configValueId" />
		<result column="CONFIG_ID" jdbcType="VARCHAR" property="configId" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
		<result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
		<result column="VALUE_CODE" jdbcType="VARCHAR" property="valueCode" />
		<result column="VALUE_NAME" jdbcType="VARCHAR" property="valueName" />
		<result column="COLUMN1" jdbcType="VARCHAR" property="column1" />
		<result column="COLUMN2" jdbcType="VARCHAR" property="column2" />
		<result column="COLUMN3" jdbcType="VARCHAR" property="column3" />
		<result column="COLUMN4" jdbcType="VARCHAR" property="column4" />
		<result column="COLUMN5" jdbcType="VARCHAR" property="column5" />
		<result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
		<result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
		<result column="CREATOR" jdbcType="VARCHAR" property="creator" />
		<result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
		<result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
		<result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
		<result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
		<result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
		<result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
	</resultMap>
	<sql id="Base_Column_List">
		<!--@mbg.generated -->
		CONFIG_VALUE_ID, CONFIG_ID, ORG_CODE, ORG_NAME, VALUE_CODE,
		VALUE_NAME, COLUMN1,
		COLUMN2, COLUMN3, COLUMN4, COLUMN5, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME,
		CREATED_DATE,
		MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
	</sql>
	<!--auto generated by MybatisCodeHelper on 2021-08-18 -->
	<insert id="insertSystemConfigValue">
		INSERT INTO t_sac_system_config_value(
		CONFIG_VALUE_ID,
		CONFIG_ID,
		ORG_CODE,
		ORG_NAME,
		VALUE_CODE,
		VALUE_NAME,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		IS_ENABLE,
		UPDATE_CONTROL_ID
		)VALUES
		(
		#{element.configValueId},
		#{element.configId},
		#{element.orgCode},
		#{element.orgName},
		#{element.valueCode},
		#{element.valueName},
		#{element.oemId},
		#{element.groupId},
		#{element.creator},
		#{element.createdName},
		#{element.createdDate},
		#{element.modifier},
		#{element.modifyName},
		#{element.lastUpdatedDate},
		#{element.isEnable},
		#{element.updateControlId}
		)
	</insert>
	<!--auto generated by MybatisCodeHelper on 2021-08-18 -->
	<update id="updateByConfigValueIdAndUpdateControlId">
		update t_sac_system_config_value set
		<if test="updated.configId != null and updated.configId != ''">
			CONFIG_ID = #{updated.configId},
		</if>
		<if test="updated.orgCode != null and updated.orgCode != ''">
			ORG_CODE = #{updated.orgCode},
		</if>
		<if test="updated.orgName != null and updated.orgName != ''">
			ORG_NAME = #{updated.orgName},
		</if>
		<if test="updated.valueCode != null and updated.valueCode != ''">
			VALUE_CODE = #{updated.valueCode},
		</if>
		<if test="updated.valueName != null and updated.valueName != ''">
			VALUE_NAME = #{updated.valueName},
		</if>
		<if test="updated.oemId != null and updated.oemId != ''">
			OEM_ID = #{updated.oemId},
		</if>
		<if test="updated.groupId != null and updated.groupId != ''">
			GROUP_ID = #{updated.groupId},
		</if>
		<if test="updated.modifier != null and updated.modifier != ''">
			MODIFIER = #{updated.modifier},
		</if>
		<if test="updated.modifyName != null and updated.modifyName != ''">
			MODIFY_NAME = #{updated.modifyName},
		</if>
		<if test="updated.isEnable != null and updated.isEnable != ''">
			IS_ENABLE = #{updated.isEnable},
		</if>
		<if test="updated.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{updated.lastUpdatedDate},</if>
		<if
			test="updated.updateControlId !=null and updated.updateControlId !=''"> UPDATE_CONTROL_ID=#{updated.updateControlId}</if>
		where 1=1 and CONFIG_VALUE_ID=#{updated.configValueId}
	</update>
	<!--auto generated by MybatisCodeHelper on 2021-08-18 -->
	<select id="countByConfigValueId" resultType="java.lang.Integer">
		select count(1)
		from t_sac_system_config_value
		where CONFIG_VALUE_ID=#{configValueId}
	</select>

	<select id="countByConfigId" resultType="java.lang.Integer">
		select count(1)
		from t_sac_system_config
		where CONFIG_ID=#{configId}
	</select>
	<!--auto generated by MybatisCodeHelper on 2021-08-18 -->
	<select id="selectByAll" resultType="java.util.Map">
		select
		v.CONFIG_VALUE_ID as configValueId,
		s.CONFIG_ID as configId,
		s.CONFIG_RANGE,
		case 
		when s.CONFIG_RANGE='GLOBAL' then '全局' 
		when s.CONFIG_RANGE='HOSTDLR' then '总部和店端' 
		when s.CONFIG_RANGE='HOST' then '总部' 
		when s.CONFIG_RANGE='DLR' then '店端' 
		end as CONFIG_RANGE_NAME,
		v.ORG_CODE as orgCode,
		v.ORG_NAME as orgName,
		s.CONFIG_CODE as configCode,
		s.CONFIG_NAME as configName,
		v.VALUE_CODE as valueCode,
		v.VALUE_NAME as valueName,
		s.CONFIG_DESC as configDesc
		from t_sac_system_config s
		LEFT join t_sac_system_config_value v on (s.config_id=v.config_id and v.ORG_CODE=#{param.orgCode})
		where 1=1
		<if test='param.isPv != null and param.isPv == "1"'>
			and s.CONFIG_RANGE in('GLOBAL','HOSTDLR','HOST')
		</if>
		<if test='param.isPv != null and param.isPv == "0"'>
			and s.CONFIG_RANGE in('HOSTDLR','DLR')
		</if>
		<if test="param.configValueId != null and param.configValueId != ''">
			and v.CONFIG_VALUE_ID = #{param.configValueId}
		</if>
		<if test="param.configCode != null and param.configCode != ''">
			and s.CONFIG_CODE = #{param.configCode}
		</if>
		<if test="param.configId != null and param.configId != ''">
			and v.CONFIG_ID = #{param.configId}
		</if>
		<if test="param.valueCode != null and param.valueCode != ''">
			and v.VALUE_CODE = #{param.valueCode}
		</if>
		<if test="param.isEnable != null and param.isEnable != ''">
			and v.IS_ENABLE = #{param.isEnable}
		</if>
	</select>
	
	<select id="selectByConfigCode" resultType="java.util.Map">
		select
		v.CONFIG_VALUE_ID as configValueId,
		s.CONFIG_ID as configId,
		s.CONFIG_RANGE,
		case 
		when s.CONFIG_RANGE='GLOBAL' then '全局' 
		when s.CONFIG_RANGE='HOSTDLR' then '总部和店端' 
		when s.CONFIG_RANGE='HOST' then '总部' 
		when s.CONFIG_RANGE='DLR' then '店端' 
		end as CONFIG_RANGE_NAME,
		v.ORG_CODE as orgCode,
		v.ORG_NAME as orgName,
		s.CONFIG_CODE as configCode,
		s.CONFIG_NAME as configName,
		v.VALUE_CODE as valueCode,
		v.VALUE_NAME as valueName,
		s.CONFIG_DESC as configDesc
		from t_sac_system_config s
		inner join t_sac_system_config_value v on s.config_id=v.config_id 
		where 1=1
		and s.is_enable='1'
		and v.is_enable='1'
		and (s.CONFIG_RANGE='GLOBAL' or v.ORG_CODE=#{param.orgCode})
		and s.CONFIG_CODE = #{param.configCode}
	</select>
	
	<select id="selectConfigInfo" resultType="java.util.Map">
	select 
	s.CONFIG_ID,
	s.CONFIG_CODE,
	s.CONFIG_NAME,
	s.CONFIG_RANGE,
	s.CONFIG_DESC
	from t_sac_system_config s 
	where 1=1 
	and s.CONFIG_CODE = #{param.configCode}
	</select>
	
	<update id="updateByConfigId">
		update t_sac_system_config
		set
		CONFIG_NAME = #{map.configName},
		CONFIG_CODE = #{map.configCode},
		CONFIG_DESC = #{map.configDesc}
		where CONFIG_ID = #{map.configId}
	</update>
</mapper>