<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacWorkGroupUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacWorkGroupUser">
        <id column="WORK_GROUP_USER_ID" property="workGroupUserId" />
        <result column="WORK_GROUP_ID" property="workGroupId" />
        <result column="USER_ID" property="userId" />
        <result column="USER_NAME" property="userName" />
        <result column="EMP_ID" property="empId" />
        <result column="EMP_NAME" property="empName" />
        <result column="IS_LEADER" property="isLeader" />
        <result column="REMARK" property="remark" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WORK_GROUP_USER_ID, WORK_GROUP_ID, USER_ID, USER_NAME, EMP_ID, EMP_NAME, IS_LEADER, REMARK, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

 	<insert id="insertSacWorkGroupUser">
        insert into t_sac_work_group_user(
         WORK_GROUP_USER_ID
        ,WORK_GROUP_ID
        ,USER_ID
        ,USER_NAME
        ,EMP_ID
        ,EMP_NAME
        ,IS_LEADER
        ,REMARK
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{workGroupUserId}
        ,#{workGroupId}
        ,#{userId}
        ,#{userName}
        ,#{empId}
        ,#{empName}
        ,#{isLeader}
        ,#{remark}
        ,#{oemId}
        ,#{groupId}
        ,#{creator}
        ,#{createdName}
        ,#{createdDate}
        ,#{modifier}
        ,#{modifyName}
        ,#{lastUpdatedDate}
        ,#{isEnable}
        ,#{updateControlId}
		)
    </insert>
    
    <update id="updateSacWorkGroupUser">
    	update t_sac_work_group_user  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.workGroupId!=null'> ,WORK_GROUP_ID = #{param.workGroupId}</if>
	    <if test = 'param.userId!=null'> ,USER_ID = #{param.userId}</if>
	    <if test = 'param.userName!=null'> ,USER_NAME = #{param.userName}</if>
	    <if test = 'param.empId!=null'> ,EMP_ID = #{param.empId}</if>
	    <if test = 'param.empName!=null'> ,EMP_NAME = #{param.empName}</if>
	    <if test = 'param.isLeader!=null'> ,IS_LEADER = #{param.isLeader}</if>
	    <if test = 'param.remark!=null'> ,REMARK = #{param.remark}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and WORK_GROUP_USER_ID = #{param.workGroupUserId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
