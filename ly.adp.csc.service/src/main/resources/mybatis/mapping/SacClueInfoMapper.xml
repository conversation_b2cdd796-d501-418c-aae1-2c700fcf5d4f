<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacClueInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacClueInfo">
        <id column="ID" property="id" />
        <result column="SERVER_ORDER" property="serverOrder" />
        <result column="CUST_ID" property="custId" />
        <result column="CUST_NAME" property="custName" />
        <result column="PHONE" property="phone" />
        <result column="PHONE_BACKUP" property="phoneBackup" />
        <result column="INTEN_LEVEL_CODE" property="intenLevelCode" />
        <result column="INTEN_LEVEL_NAME" property="intenLevelName" />
        <result column="INTEN_BRAND_CODE" property="intenBrandCode" />
        <result column="INTEN_BRAND_NAME" property="intenBrandName" />
        <result column="INTEN_SERIES_CODE" property="intenSeriesCode" />
        <result column="INTEN_SERIES_NAME" property="intenSeriesName" />
        <result column="INTEN_CAR_TYPE_CODE" property="intenCarTypeCode" />
        <result column="INTEN_CAR_TYPE_NAME" property="intenCarTypeName" />
        <result column="INTEN_OPTION_PACKAGE_CODE" property="intenOptionPackageCode" />
        <result column="INTEN_OPTION_PACKAGE_NAME" property="intenOptionPackageName" />
        <result column="INNER_COLOR_CODE" property="innerColorCode" />
        <result column="INNER_COLOR_NAME" property="innerColorName" />
        <result column="OUT_COLOR_CODE" property="outColorCode" />
        <result column="OUT_COLOR_NAME" property="outColorName" />
        <result column="SEND_DLR_CODE" property="sendDlrCode" />
        <result column="SEND_DLR_SHORT_NAME" property="sendDlrShortName" />
        <result column="SOURCE_SYSTEMT_CODE" property="sourceSystemtCode" />
        <result column="SOURCE_SYSTEMT_NAME" property="sourceSystemtName" />
        <result column="SEND_TIME" property="sendTime" />
        <result column="SOURCE_SERVER_ORDER" property="sourceServerOrder" />
        <result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
        <result column="INFO_CHAN_M_NAME" property="infoChanMName" />
        <result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
        <result column="INFO_CHAN_D_NAME" property="infoChanDName" />
        <result column="INFO_CHAN_DD_CODE" property="infoChanDdCode" />
        <result column="INFO_CHAN_DD_NAME" property="infoChanDdName" />
        <result column="GENDER_CODE" property="genderCode" />
        <result column="GENDER_NAME" property="genderName" />
        <result column="STATUS_CODE" property="statusCode" />
        <result column="STATUS_NAME" property="statusName" />
        <result column="DEAL_NODE_CODE" property="dealNodeCode" />
        <result column="DEAL_NODE_NAME" property="dealNodeName" />
        <result column="REVIEW_ID" property="reviewId" />
        <result column="FIRST_REVIEW_TIME" property="firstReviewTime" />
        <result column="LAST_REVIEW_TIME" property="lastReviewTime" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="COLUMN11" property="column11" />
        <result column="COLUMN12" property="column12" />
        <result column="COLUMN13" property="column13" />
        <result column="COLUMN14" property="column14" />
        <result column="COLUMN15" property="column15" />
        <result column="COLUMN16" property="column16" />
        <result column="COLUMN17" property="column17" />
        <result column="COLUMN18" property="column18" />
        <result column="COLUMN19" property="column19" />
        <result column="COLUMN20" property="column20" />
        <result column="BIG_COLUMN1" property="bigColumn1" />
        <result column="BIG_COLUMN2" property="bigColumn2" />
        <result column="BIG_COLUMN3" property="bigColumn3" />
        <result column="BIG_COLUMN4" property="bigColumn4" />
        <result column="BIG_COLUMN5" property="bigColumn5" />
        <result column="EXTENDS_JSON" property="extendsJson" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="ASSIGN_TIME" property="assignTime" />
        <result column="REVIEW_PERSON_NAME" property="reviewPersonName" />
	<result column="REVIEW_PERSON_ID" property="reviewPersonId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.ID, t1.SERVER_ORDER, t1.CUST_ID, t1.CUST_NAME, t1.PHONE, t1.PHONE_BACKUP
		, t1.INTEN_LEVEL_CODE, t1.INTEN_LEVEL_NAME
		, t1.INTEN_BRAND_CODE, t1.INTEN_BRAND_NAME
		, t1.INTEN_SERIES_CODE, t1.INTEN_SERIES_NAME
		, t1.INTEN_CAR_TYPE_CODE, t1.INTEN_CAR_TYPE_NAME
		, t1.INTEN_OPTION_PACKAGE_CODE, t1.INTEN_OPTION_PACKAGE_NAME
		, t1.INNER_COLOR_CODE, t1.INNER_COLOR_NAME
		, t1.OUT_COLOR_CODE, t1.OUT_COLOR_NAME
		, t1.SEND_DLR_CODE, t1.SEND_DLR_SHORT_NAME
		, t1.SOURCE_SYSTEMT_CODE, t1.SOURCE_SYSTEMT_NAME
		, t1.SEND_TIME
		, t1.SOURCE_SERVER_ORDER
		, t1.INFO_CHAN_M_CODE, t1.INFO_CHAN_M_NAME
		, t1.INFO_CHAN_D_CODE, t1.INFO_CHAN_D_NAME
		, t1.INFO_CHAN_DD_CODE, t1.INFO_CHAN_DD_NAME
		, t1.CHANNEL_CODE, t1.CHANNEL_NAME
		, t1.GENDER_CODE, t1.GENDER_NAME, t1.STATUS_CODE, t1.STATUS_NAME
		, t1.DEAL_NODE_CODE, t1.DEAL_NODE_NAME
		, t1.REVIEW_ID, t1.FIRST_REVIEW_TIME, t1.LAST_REVIEW_TIME
		, t1.EXTENDS_JSON, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE , t1.UPDATE_CONTROL_ID
		, t1.ASSIGN_TIME , t1.REVIEW_PERSON_NAME, t1.REVIEW_PERSON_ID
	</sql>
	

	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.id !=null and param.id !=''">and t1.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.sendDlrCode !=null and param.sendDlrCode !=''">and t1.SEND_DLR_CODE=#{param.sendDlrCode}</if>
		<if test="param.sendDlrShortName !=null and param.sendDlrShortName !=''">and t1.SEND_DLR_SHORT_NAME=#{param.sendDlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.sendTime !=null and param.sendTime !=''">and t1.SEND_TIME=#{param.sendTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE=#{param.infoChanDCode}</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME=#{param.channelName}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER like concat('%',#{param.serverOrder},'%')</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
	    <if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
	    <if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>
	    
		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and t1.REVIEW_PERSON_NAME like concat('%', #{param.reviewPersonName}, '%')</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and t1.REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and t1.ASSIGN_TIME>=#{param.assignTimeStart}</if>
	    <if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''"><![CDATA[and t1.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
	    <if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and t1.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}</if>
	    <if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''"><![CDATA[and t1.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>
	    <if test="param.sendTimeStart !=null and param.sendTimeStart !=''">and t1.SEND_TIME>=#{param.sendTimeStart}</if>
	    <if test="param.sendTimeEnd !=null and param.sendTimeEnd !=''"><![CDATA[and t1.SEND_TIME<=#{param.sendTimeEnd}]]></if>
	</sql>

 	<insert id="insertSacClueInfo">
        insert into t_sac_clue_info(
         ID
        ,SERVER_ORDER
        ,CUST_ID
        ,CUST_NAME
        ,PHONE
        ,PHONE_BACKUP
        ,INTEN_LEVEL_CODE
        ,INTEN_LEVEL_NAME
        ,INTEN_BRAND_CODE
        ,INTEN_BRAND_NAME
        ,INTEN_SERIES_CODE
        ,INTEN_SERIES_NAME
        ,INTEN_CAR_TYPE_CODE
        ,INTEN_CAR_TYPE_NAME
        ,INTEN_OPTION_PACKAGE_CODE
        ,INTEN_OPTION_PACKAGE_NAME
        ,INNER_COLOR_CODE
        ,INNER_COLOR_NAME
        ,OUT_COLOR_CODE
        ,OUT_COLOR_NAME
        ,SEND_DLR_CODE
        ,SEND_DLR_SHORT_NAME
        ,SOURCE_SYSTEMT_CODE
        ,SOURCE_SYSTEMT_NAME
        ,SEND_TIME
        ,SOURCE_SERVER_ORDER
        ,INFO_CHAN_M_CODE
        ,INFO_CHAN_M_NAME
        ,INFO_CHAN_D_CODE
        ,INFO_CHAN_D_NAME
        ,INFO_CHAN_DD_CODE
        ,INFO_CHAN_DD_NAME
        ,CHANNEL_CODE
        ,CHANNEL_NAME
        ,GENDER_CODE
        ,GENDER_NAME
        ,STATUS_CODE
        ,STATUS_NAME
        ,DEAL_NODE_CODE
        ,DEAL_NODE_NAME
        ,REVIEW_ID
        ,FIRST_REVIEW_TIME
        ,LAST_REVIEW_TIME
        ,EXTENDS_JSON
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,ASSIGN_TIME
        ,REVIEW_PERSON_NAME
        ,REVIEW_PERSON_ID
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.id}
        ,#{param.serverOrder}
        ,#{param.custId}
        ,#{param.custName}
        ,#{param.phone}
        ,#{param.phoneBackup}
        ,#{param.intenLevelCode}
        ,#{param.intenLevelName}
        ,#{param.intenBrandCode}
        ,#{param.intenBrandName}
        ,#{param.intenSeriesCode}
        ,#{param.intenSeriesName}
        ,#{param.intenCarTypeCode}
        ,#{param.intenCarTypeName}
        ,#{param.intenOptionPackageCode}
        ,#{param.intenOptionPackageName}
        ,#{param.innerColorCode}
        ,#{param.innerColorName}
        ,#{param.outColorCode}
        ,#{param.outColorName}
        ,#{param.sendDlrCode}
        ,#{param.sendDlrShortName}
        ,#{param.sourceSystemtCode}
        ,#{param.sourceSystemtName}
        ,#{param.sendTime}
        ,#{param.sourceServerOrder}
        ,#{param.infoChanMCode}
        ,#{param.infoChanMName}
        ,#{param.infoChanDCode}
        ,#{param.infoChanDName}
        ,#{param.infoChanDdCode}
        ,#{param.infoChanDdName}
        ,#{param.channelCode}
        ,#{param.channelName}
        ,#{param.genderCode}
        ,#{param.genderName}
        ,#{param.statusCode}
        ,#{param.statusName}
        ,#{param.dealNodeCode}
        ,#{param.dealNodeName}
        ,#{param.reviewId}
        ,#{param.firstReviewTime}
        ,#{param.lastReviewTime}
        ,#{param.extendsJson}
        ,#{param.oemId}
        ,#{param.groupId}
        ,#{param.creator}
        ,#{param.createdName}
        ,#{param.createdDate}
        ,#{param.modifier}
        ,#{param.modifyName}
        ,#{param.lastUpdatedDate}
        ,#{param.isEnable}
        ,#{param.assignTime}
		,#{param.reviewPersonName}
		,#{param.reviewPersonId}
        ,#{param.updateControlId}
		)
    </insert>
    
    <update id="updateSacClueInfo">
    	update t_sac_clue_info  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.serverOrder!=null'> ,SERVER_ORDER = #{param.serverOrder}</if>
	    <if test = 'param.custId!=null'> ,CUST_ID = #{param.custId}</if>
	    <if test = 'param.custName!=null'> ,CUST_NAME = #{param.custName}</if>
	    <if test = 'param.phone!=null'> ,PHONE = #{param.phone}</if>
	    <if test = 'param.phoneBackup!=null'> ,PHONE_BACKUP = #{param.phoneBackup}</if>
	    <if test = 'param.intenLevelCode!=null'> ,INTEN_LEVEL_CODE = #{param.intenLevelCode}</if>
	    <if test = 'param.intenLevelName!=null'> ,INTEN_LEVEL_NAME = #{param.intenLevelName}</if>
	    <if test = 'param.intenBrandCode!=null'> ,INTEN_BRAND_CODE = #{param.intenBrandCode}</if>
	    <if test = 'param.intenBrandName!=null'> ,INTEN_BRAND_NAME = #{param.intenBrandName}</if>
	    <if test = 'param.intenSeriesCode!=null'> ,INTEN_SERIES_CODE = #{param.intenSeriesCode}</if>
	    <if test = 'param.intenSeriesName!=null'> ,INTEN_SERIES_NAME = #{param.intenSeriesName}</if>
	    <if test = 'param.intenCarTypeCode!=null'> ,INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}</if>
	    <if test = 'param.intenCarTypeName!=null'> ,INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName}</if>
	    <if test = 'param.intenOptionPackageCode!=null'> ,INTEN_OPTION_PACKAGE_CODE = #{param.intenOptionPackageCode}</if>
	    <if test = 'param.intenOptionPackageName!=null'> ,INTEN_OPTION_PACKAGE_NAME = #{param.intenOptionPackageName}</if>
	    <if test = 'param.innerColorCode!=null'> ,INNER_COLOR_CODE = #{param.innerColorCode}</if>
	    <if test = 'param.innerColorName!=null'> ,INNER_COLOR_NAME = #{param.innerColorName}</if>
	    <if test = 'param.outColorCode!=null'> ,OUT_COLOR_CODE = #{param.outColorCode}</if>
	    <if test = 'param.outColorName!=null'> ,OUT_COLOR_NAME = #{param.outColorName}</if>
	    <if test = 'param.sendDlrCode!=null'> ,SEND_DLR_CODE = #{param.sendDlrCode}</if>
	    <if test = 'param.sendDlrShortName!=null'> ,SEND_DLR_SHORT_NAME = #{param.sendDlrShortName}</if>
	    <if test = 'param.sourceSystemtCode!=null'> ,SOURCE_SYSTEMT_CODE = #{param.sourceSystemtCode}</if>
	    <if test = 'param.sourceSystemtName!=null'> ,SOURCE_SYSTEMT_NAME = #{param.sourceSystemtName}</if>
	    <if test = 'param.sendTime!=null'> 
	    ,SEND_TIME = case when #{param.sendTime}='' then null else #{param.sendTime} end
	    </if>
	    <if test = 'param.sourceServerOrder!=null'> ,SOURCE_SERVER_ORDER = #{param.sourceServerOrder}</if>
	    <if test = 'param.infoChanMCode!=null'> ,INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
	    <if test = 'param.infoChanMName!=null'> ,INFO_CHAN_M_NAME = #{param.infoChanMName}</if>
	    <if test = 'param.infoChanDCode!=null'> ,INFO_CHAN_D_CODE = #{param.infoChanDCode}</if>
	    <if test = 'param.infoChanDName!=null'> ,INFO_CHAN_D_NAME = #{param.infoChanDName}</if>
	    <if test = 'param.infoChanDdCode!=null'> ,INFO_CHAN_DD_CODE = #{param.infoChanDdCode}</if>
	    <if test = 'param.infoChanDdName!=null'> ,INFO_CHAN_DD_NAME = #{param.infoChanDdName}</if>
	    <if test = 'param.channelCode !=null'> ,CHANNEL_CODE = #{param.channelCode}</if>
	    <if test = 'param.channelName !=null'> ,CHANNEL_NAME = #{param.channelName}</if>
	    <if test = 'param.genderCode!=null'> ,GENDER_CODE = #{param.genderCode}</if>
	    <if test = 'param.genderName!=null'> ,GENDER_NAME = #{param.genderName}</if>
	    <if test = 'param.statusCode!=null'> ,STATUS_CODE = #{param.statusCode}</if>
	    <if test = 'param.statusName!=null'> ,STATUS_NAME = #{param.statusName}</if>
	    <if test = 'param.dealNodeCode!=null'> ,DEAL_NODE_CODE = #{param.dealNodeCode}</if>
	    <if test = 'param.dealNodeName!=null'> ,DEAL_NODE_NAME = #{param.dealNodeName}</if>
	    <if test = 'param.reviewId!=null'> ,REVIEW_ID = #{param.reviewId}</if>
	    <if test = 'param.firstReviewTime!=null'> 
	    ,FIRST_REVIEW_TIME = case when #{param.firstReviewTime}='' then null else #{param.firstReviewTime} end
	    </if>
	    <if test = 'param.lastReviewTime!=null'> 
	    ,LAST_REVIEW_TIME = case when #{param.lastReviewTime}='' then null else #{param.lastReviewTime} end
	    </if>
	    <if test = 'param.extendsJson!=null'> ,EXTENDS_JSON = #{param.extendsJson}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
	    <if test = 'param.assignTime!=null'> ,ASSIGN_TIME = #{param.assignTime}</if>
	    <if test = 'param.reviewPersonName!=null'> ,REVIEW_PERSON_NAME = #{param.reviewPersonName}</if>
	    <if test = 'param.reviewPersonId!=null'> ,REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
    	where 1=1 
         and ID = #{param.id}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

    <update id="updateSacClueInfoBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            update t_sac_clue_info set
            MODIFIER=#{param.modifier},
            MODIFY_NAME=#{param.modifyName},
            LAST_UPDATED_DATE=sysdate(),
            UPDATE_CONTROL_ID=uuid()
            <if test='param.serverOrder!=null'>,SERVER_ORDER = #{param.serverOrder}</if>
            <if test='param.custId!=null'>,CUST_ID = #{param.custId}</if>
            <if test='param.custName!=null'>,CUST_NAME = #{param.custName}</if>
            <if test='param.phone!=null'>,PHONE = #{param.phone}</if>
            <if test='param.phoneBackup!=null'>,PHONE_BACKUP = #{param.phoneBackup}</if>
            <if test='param.intenLevelCode!=null'>,INTEN_LEVEL_CODE = #{param.intenLevelCode}</if>
            <if test='param.intenLevelName!=null'>,INTEN_LEVEL_NAME = #{param.intenLevelName}</if>
            <if test='param.intenBrandCode!=null'>,INTEN_BRAND_CODE = #{param.intenBrandCode}</if>
            <if test='param.intenBrandName!=null'>,INTEN_BRAND_NAME = #{param.intenBrandName}</if>
            <if test='param.intenSeriesCode!=null'>,INTEN_SERIES_CODE = #{param.intenSeriesCode}</if>
            <if test='param.intenSeriesName!=null'>,INTEN_SERIES_NAME = #{param.intenSeriesName}</if>
            <if test='param.intenCarTypeCode!=null'>,INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}</if>
            <if test='param.intenCarTypeName!=null'>,INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName}</if>
            <if test='param.intenOptionPackageCode!=null'>,INTEN_OPTION_PACKAGE_CODE = #{param.intenOptionPackageCode}
            </if>
            <if test='param.intenOptionPackageName!=null'>,INTEN_OPTION_PACKAGE_NAME = #{param.intenOptionPackageName}
            </if>
            <if test='param.innerColorCode!=null'>,INNER_COLOR_CODE = #{param.innerColorCode}</if>
            <if test='param.innerColorName!=null'>,INNER_COLOR_NAME = #{param.innerColorName}</if>
            <if test='param.outColorCode!=null'>,OUT_COLOR_CODE = #{param.outColorCode}</if>
            <if test='param.outColorName!=null'>,OUT_COLOR_NAME = #{param.outColorName}</if>
            <if test='param.sendDlrCode!=null'>,SEND_DLR_CODE = #{param.sendDlrCode}</if>
            <if test='param.sendDlrShortName!=null'>,SEND_DLR_SHORT_NAME = #{param.sendDlrShortName}</if>
            <if test='param.sourceSystemtCode!=null'>,SOURCE_SYSTEMT_CODE = #{param.sourceSystemtCode}</if>
            <if test='param.sourceSystemtName!=null'>,SOURCE_SYSTEMT_NAME = #{param.sourceSystemtName}</if>
            <if test='param.sendTime!=null'>
                ,SEND_TIME = case when #{param.sendTime}='' then null else #{param.sendTime} end
            </if>
            <if test='param.sourceServerOrder!=null'>,SOURCE_SERVER_ORDER = #{param.sourceServerOrder}</if>
            <if test='param.infoChanMCode!=null'>,INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
            <if test='param.infoChanMName!=null'>,INFO_CHAN_M_NAME = #{param.infoChanMName}</if>
            <if test='param.infoChanDCode!=null'>,INFO_CHAN_D_CODE = #{param.infoChanDCode}</if>
            <if test='param.infoChanDName!=null'>,INFO_CHAN_D_NAME = #{param.infoChanDName}</if>
            <if test='param.infoChanDdCode!=null'>,INFO_CHAN_DD_CODE = #{param.infoChanDdCode}</if>
            <if test='param.infoChanDdName!=null'>,INFO_CHAN_DD_NAME = #{param.infoChanDdName}</if>
            <if test='param.channelCode !=null'>,CHANNEL_CODE = #{param.channelCode}</if>
            <if test='param.channelName !=null'>,CHANNEL_NAME = #{param.channelName}</if>
            <if test='param.genderCode!=null'>,GENDER_CODE = #{param.genderCode}</if>
            <if test='param.genderName!=null'>,GENDER_NAME = #{param.genderName}</if>
            <if test='param.statusCode!=null'>,STATUS_CODE = #{param.statusCode}</if>
            <if test='param.statusName!=null'>,STATUS_NAME = #{param.statusName}</if>
            <if test='param.dealNodeCode!=null'>,DEAL_NODE_CODE = #{param.dealNodeCode}</if>
            <if test='param.dealNodeName!=null'>,DEAL_NODE_NAME = #{param.dealNodeName}</if>
            <if test='param.reviewId!=null'>,REVIEW_ID = #{param.reviewId}</if>
            <if test='param.firstReviewTime!=null'>
                ,FIRST_REVIEW_TIME = case when #{param.firstReviewTime}='' then null else #{param.firstReviewTime} end
            </if>
            <if test='param.lastReviewTime!=null'>
                ,LAST_REVIEW_TIME = case when #{param.lastReviewTime}='' then null else #{param.lastReviewTime} end
            </if>
            <if test='param.extendsJson!=null'>,EXTENDS_JSON = #{param.extendsJson}</if>
            <if test='param.oemId!=null'>,OEM_ID = #{param.oemId}</if>
            <if test='param.groupId!=null'>,GROUP_ID = #{param.groupId}</if>
            <if test='param.isEnable!=null'>,IS_ENABLE = #{param.isEnable}</if>
            <if test='param.assignTime!=null'>,ASSIGN_TIME = #{param.assignTime}</if>
            <if test='param.reviewPersonName!=null'>,REVIEW_PERSON_NAME = #{param.reviewPersonName}</if>
            <if test='param.reviewPersonId!=null'>,REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
            where 1=1
            and ID = #{param.id}
            <if test='param.updateControlId!=null'>
                and UPDATE_CONTROL_ID = #{param.updateControlId}
            </if>
        </foreach>
    </update>

	<select id="selectMapById" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.id = #{param.id}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER = #{param.serverOrder}</if>
	</select>

	<select id="selectHisById" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info_his t1
		where 1=1
		<if test="param.id !=null and param.id !=''"> and t1.id = #{param.id}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER = #{param.serverOrder}</if>
	</select>
	
	<select id="selectDistinctDataByPage" resultType="Map">
		select CUST_NAME,PHONE,PHONE_BACKUP,GENDER_NAME,GENDER_CODE,CREATED_DATE from (
		SELECT CUST_NAME, PHONE, PHONE_BACKUP, GENDER_NAME,GENDER_CODE,CREATED_DATE, 
		row_number () over (
		    PARTITION BY PHONE 
				ORDER BY CREATED_DATE DESC
		  ) rn
		FROM t_sac_clue_info) t1
		WHERE rn=1 
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
	    <if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
	</select>
	
	<select id="selectByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info t1
		where 1=1
		<include refid="where_condition"></include>
	    order by t1.CREATED_DATE desc
	</select>

	<select id="selectMap" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info t1
		where 1=1
		<include refid="where_condition"></include>
		order by t1.CREATED_DATE desc
	</select>

	<select id="selectHisByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_clue_info_his t1
		where 1=1
		<include refid="where_condition"></include>
	    order by t1.CREATED_DATE desc
	</select>

	<!-- 总部线索详情查询 -->
   	<select id="clueServerQueryDetailFromHeadquarters" resultType="Map">
   		SELECT <include refid="Base_Column_List"></include>
		WHERE 1 = 1
		<include refid="where_condition"></include>
   	</select>
   	
    <!-- 总部线索查重 -->
    <select id="checkRepeat" resultType="int">
    	SELECT 
		COUNT(1) NUM
		FROM t_sac_clue_info a 
		WHERE 1=1 
		<if test='param.phone !=null and param.phone !="" and param.checkPhone=="1"'>and PHONE = #{param.phone}</if>
		<if test='param.checkTimeHorizon !=null and param.checkTimeHorizon !="" and param.checkTime=="1" and param.checkPhone=="1"'>
      		and DATE_SUB(NOW(), INTERVAL #{param.checkTimeHorizon} DAY) <![CDATA[<=DATE(CREATED_DATE)]]>
    	</if>
		<if test="param.customSqlString !=null and param.customSqlString !=''">
			${param.customSqlString}
		</if>
		<if test="param.jsonFieldMappingList !=null">
			<foreach item="item1" index="index" collection="param.jsonFieldMappingList" >
				and ${item1}
			</foreach>
		</if>
		<if test="param.noFieldMappingList !=null">
			<foreach item="item" index="index" collection="param.noFieldMappingList" >
			   <if test="item.columnName =='id'">and ID ${item.condition}</if>
         	   <if test="item.columnName =='serverOrder'">and SERVER_ORDER ${item.condition}</if>
			   <if test="item.columnName =='custId'">and CUST_ID ${item.condition}</if>
			   <if test="item.columnName =='custName'">and CUST_NAME ${item.condition}</if>
			   <if test="item.columnName =='phone'">and PHONE ${item.condition}</if>
			   <if test="item.columnName =='phoneBackup'">and PHONE_BACKUP ${ item.condition}</if>
			   <if test="item.columnName =='intenLevelCode'">and INTEN_LEVEL_CODE ${ item.condition}</if>
			   <if test="item.columnName =='intenLevelName'">and INTEN_LEVEL_NAME ${ item.condition}</if>
			   <if test="item.columnName =='intenBrandCode'">and INTEN_BRAND_CODE ${ item.condition}</if>
			   <if test="item.columnName =='intenBrandName'">and INTEN_BRAND_NAME ${ item.condition}</if>
			   <if test="item.columnName =='intenSeriesCode'">and INTEN_SERIES_CODE ${ item.condition}</if>
			   <if test="item.columnName =='intenSeriesName'">and INTEN_SERIES_NAME ${ item.condition}</if>
			   <if test="item.columnName =='intenCarTypeCode'">and INTEN_CAR_TYPE_CODE ${ item.condition}</if>
			   <if test="item.columnName =='intenCarTypeName'">and INTEN_CAR_TYPE_NAME ${ item.condition}</if>
        	   <if test="item.columnName =='intenOptionPackageCode'">and INTEN_OPTION_PACKAGE_CODE ${ item.condition}</if>
			   <if test="item.columnName =='intenOptionPackageName'">and INTEN_OPTION_PACKAGE_NAME ${ item.condition}</if>
			   <if test="item.columnName =='innerColorCode'">and INNER_COLOR_CODE ${ item.condition} </if>
			   <if test="item.columnName =='innerColorName'">and INNER_COLOR_NAME ${ item.condition}</if>
			   <if test="item.columnName =='outColorCode'">and OUT_COLOR_CODE ${ item.condition}</if>
			   <if test="item.columnName =='outColorName'">and OUT_COLOR_NAME ${ item.condition}</if>
			   <if test="item.columnName =='sendDlrCode'">and SEND_DLR_CODE ${ item.condition}</if>
			   <if test="item.columnName =='sendDlrShortName'">and SEND_DLR_SHORT_NAME ${ item.condition}</if>
			   <if test="item.columnName =='sourceSystemtCode'">and SOURCE_SYSTEMT_CODE ${ item.condition}</if>
			   <if test="item.columnName =='sourceSystemtName'">and SOURCE_SYSTEMT_NAME ${ item.condition}</if>
			   <if test="item.columnName =='sendTime'">and SEND_TIME ${ item.condition}</if>
			   <if test="item.columnName =='sourceServerOrder'">and SOURCE_SERVER_ORDER ${ item.condition}</if>
			   <if test="item.columnName =='infoChanMCode'">and INFO_CHAN_M_CODE ${ item.condition}</if>
			   <if test="item.columnName =='infoChanMName'">and INFO_CHAN_M_NAME ${ item.condition}</if>
			   <if test="item.columnName =='infoChanDCode'">and INFO_CHAN_D_CODE ${ item.condition} </if>
			   <if test="item.columnName =='infoChanDName'">and INFO_CHAN_D_NAME ${ item.condition}</if>
			   <if test="item.columnName =='infoChanDdCode'">and INFO_CHAN_DD_CODE ${ item.condition}</if>
			   <if test="item.columnName =='infoChanDdName'">and INFO_CHAN_DD_NAME ${ item.condition} </if>
			   <if test="item.columnName =='channelCode'">and CHANNEL_CODE ${item.condition}</if>
			   <if test="item.columnName =='channelName'">and CHANNEL_NAME ${item.condition}</if>
			   <if test="item.columnName =='genderCode'">and GENDER_CODE ${item.condition}</if>
			   <if test="item.columnName =='genderName'">and GENDER_NAME ${item.condition}</if>
			   <if test="item.columnName =='statusCode'">and STATUS_CODE ${item.condition}</if>
			   <if test="item.columnName =='statusName'">and STATUS_NAME ${item.condition}</if>
			   <if test="item.columnName =='dealNodeCode'">and DEAL_NODE_CODE ${item.condition}</if>
			   <if test="item.columnName =='dealNodeName'">and DEAL_NODE_NAME ${item.condition}</if>
			   <if test="item.columnName =='reviewId'">and REVIEW_ID ${item.condition}</if>
			   <if test="item.columnName =='firstReviewTime'">and FIRST_REVIEW_TIME ${item.condition}</if>
			   <if test="item.columnName =='lastReviewTime'">and LAST_REVIEW_TIME ${item.condition}</if>
			   <if test="item.columnName =='extendsJson'">and EXTENDS_JSON ${ item.condition}</if>
			   <if test="item.columnName =='updateControlId'">and UPDATE_CONTROL_ID ${ item.condition}</if>
    		</foreach>
		</if>
	    <if test="param.id !=null and param.id !=''">and a.id!=#{param.id}</if>
    </select>
    
    
</mapper>
