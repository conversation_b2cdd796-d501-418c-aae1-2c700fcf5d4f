<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacClueCompeteRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.otherservice.entities.SacClueCompeteRecord">
        <id column="RECORD_ID" property="recordId" />
        <result column="DLR_ID" property="dlrId" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_SHORT_NAME" property="dlrShortName" />
        <result column="GET_CLUE_TIMES" property="getClueTimes" />
        <result column="GET_SWITCH" property="getSwitch" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="EXTENDS_JSON" property="extendsJson" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, DLR_ID, DLR_CODE, DLR_SHORT_NAME, GET_CLUE_TIMES, GET_SWITCH, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, EXTENDS_JSON, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

    <!-- 默认的单表增删改查sql -->
    <insert id="sacClueCompeteRecordInsertOne" parameterType="map">
	INSERT INTO t_sac_clue_compete_record (
		RECORD_ID, /*记录主键ID*/
		DLR_ID, /*经销商ID*/
		DLR_CODE, /*经销商编码*/
		DLR_SHORT_NAME, /*经销商名称*/
		GET_CLUE_TIMES, /*获取线索次数，最大为10*/
		GET_SWITCH, /*线索获取开关状态, 1-开启, 0-关闭*/
		COLUMN1, /*扩展字段1*/
		COLUMN2, /*扩展字段2*/
		COLUMN3, /*扩展字段3*/
		COLUMN4, /*扩展字段4*/
		COLUMN5, /*扩展字段5*/
		COLUMN6, /*扩展字段6*/
		COLUMN7, /*扩展字段7*/
		COLUMN8, /*扩展字段8*/
		COLUMN9, /*扩展字段9*/
		COLUMN10, /*扩展字段10*/
		EXTENDS_JSON, /*JSON扩展字段*/
		OEM_ID, /*厂商标识ID*/
		GROUP_ID, /*集团标识ID*/
		CREATOR, /*创建人ID*/
		CREATED_NAME, /*创建人*/
		CREATED_DATE, /*创建日期*/
		MODIFIER, /*修改人ID*/
		MODIFY_NAME, /*修改人*/
		LAST_UPDATED_DATE, /*最后更新日期*/
		IS_ENABLE, /*是否可用*/
		UPDATE_CONTROL_ID) 
	VALUES 
		(uuid(),
		#{param.dlrId},
		#{param.dlrCode},
		#{param.dlrShortName},
		#{param.getClueTimes},
		#{param.getSwitch},
		#{param.column1},
		#{param.column2},
		#{param.column3},
		#{param.column4},
		#{param.column5},
		#{param.column6},
		#{param.column7},
		#{param.column8},
		#{param.column9},
		#{param.column10},
		#{param.extendsJson},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		now(),
		#{param.modifier},
		#{param.modifyName},
		now(),
		'1',
		uuid())
    </insert>
    
    <update id="sacClueCompeteRecordUpdate" parameterType="map">
	UPDATE t_sac_clue_compete_record SET
		DLR_ID = #{param.dlrId},
		DLR_CODE = #{param.dlrCode},
		DLR_SHORT_NAME = #{param.dlrShortName},
		GET_CLUE_TIMES = #{param.getClueTimes},
		GET_SWITCH = #{param.getSwitch},
		COLUMN1 = #{param.column1},
		COLUMN2 = #{param.column2},
		COLUMN3 = #{param.column3},
		COLUMN4 = #{param.column4},
		COLUMN5 = #{param.column5},
		COLUMN6 = #{param.column6},
		COLUMN7 = #{param.column7},
		COLUMN8 = #{param.column8},
		COLUMN9 = #{param.column9},
		COLUMN10 = #{param.column10},
		EXTENDS_JSON = #{param.extendsJson},
		OEM_ID = #{param.oemId},
		GROUP_ID = #{param.groupId},
		MODIFIER = #{param.modifier},
		MODIFY_NAME = #{param.modifyName},
		LAST_UPDATED_DATE = now(),
		<if test="param.isEnable != null and param.isEnable != '' ">
			IS_ENABLE = #{param.isEnable},
		</if>
		UPDATE_CONTROL_ID = #{param.updateControlId}
	WHERE 1 = 1
		<if test="param.recordId != null and param.recordId != '' ">
			AND RECORD_ID = #{param.recordId}
		</if>
		<if test="param.updateControlId != null and param.updateControlId != '' ">
			AND UPDATE_CONTROL_ID = #{param.updateControlId}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != '' ">
			AND DLR_CODE = #{param.dlrCode}
		</if>
    </update>
    
    <select id="sacClueCompeteRecordQuery" parameterType="map" resultType="map" >
	SELECT
		RECORD_ID, /*记录主键ID*/
		DLR_ID, /*经销商ID*/
		DLR_CODE, /*经销商编码*/
		DLR_SHORT_NAME, /*经销商名称*/
		IFNULL(GET_CLUE_TIMES,'0') AS GET_CLUE_TIMES ,  /*获取线索次数，最大为10*/
		GET_SWITCH, /*线索获取开关状态, 1-开启, 0-关闭*/
		COLUMN1, /*扩展字段1*/
		COLUMN2, /*扩展字段2*/
		COLUMN3, /*扩展字段3*/
		COLUMN4, /*扩展字段4*/
		COLUMN5, /*扩展字段5*/
		COLUMN6, /*扩展字段6*/
		COLUMN7, /*扩展字段7*/
		COLUMN8, /*扩展字段8*/
		COLUMN9, /*扩展字段9*/
		COLUMN10, /*扩展字段10*/
		EXTENDS_JSON, /*JSON扩展字段*/
		OEM_ID, /*厂商标识ID*/
		GROUP_ID, /*集团标识ID*/
		CREATOR, /*创建人ID*/
		CREATED_NAME, /*创建人*/
		CREATED_DATE, /*创建日期*/
		MODIFIER, /*修改人ID*/
		MODIFY_NAME, /*修改人*/
		LAST_UPDATED_DATE, /*最后更新日期*/
		IS_ENABLE, /*是否可用*/
		UPDATE_CONTROL_ID /*并发控制ID*/
	FROM t_sac_clue_compete_record
	WHERE 1 = 1
	<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
		AND DLR_CODE IN
		<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="param.createdDateStart != null and param.createdDateStart != '' ">
		AND CREATED_DATE <![CDATA[ >= ]]> #{param.createdDateStart}
	</if>
	<if test="param.getSwitch != null and param.getSwitch != '' ">
		AND GET_SWITCH = #{param.getSwitch}
	</if>
	<if test="param.createdDateEnd != null and param.createdDateEnd != '' ">
		AND CREATED_DATE <![CDATA[ <= ]]> #{param.createdDateEnd}
	</if>
	<if test="param.lastUpdatedDate != null and param.lastUpdatedDate != '' ">
		AND DATE_FORMAT(LAST_UPDATED_DATE, '%Y-%m-%d') = #{param.lastUpdatedDate}
	</if>
	ORDER BY DLR_CODE
    </select>
    
    <!-- 所有门店 + 门店获取休眠线索开关状态 -->
    <select id="sacClueCompeteRecordDlrInfoQuery" parameterType="map" resultType="map" >
	SELECT
		D.*,
		R.GET_CLUE_TIMES, /*获取线索次数，最大为10*/
		ifnull(R.GET_SWITCH, '0') AS GET_SWITCH /*线索获取开关状态, 1-开启, 0-关闭*/
	FROM t_usc_mdm_org_dlr D
		LEFT JOIN t_sac_clue_compete_record R ON D.DLR_CODE = R.DLR_CODE
	WHERE 1 = 1
	<if test = "param.dlrCodeList != null and param.dlrCodeList != ''">
		AND D.DLR_CODE IN
		<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="param.getSwitch != null and param.getSwitch != '' ">
		AND ifnull(R.GET_SWITCH, '0') = #{param.getSwitch}
	</if>
	ORDER BY D.DLR_CODE
    </select>
    
    <!-- 查询管辖门店 + 门店获取线索开关状态 -->
	<select id="sacClueCompeteRecordManagedDlrQuery" parameterType="map" resultType="map">
		SELECT
			 d.DLR_ID,
			 d.DLR_CODE,
			 d.DLR_TYPE,
			 d.DLR_SHORT_NAME,
			 CR.GET_CLUE_TIMES,
			 CR.GET_SWITCH
		FROM t_usc_mdm_org_dlr d
			INNER JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
			INNER JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
			LEFT JOIN t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
  			LEFT JOIN t_usc_mdm_org_province h ON d.PROVINCE_ID = h.PROVINCE_ID
			LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
			LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
			LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
			LEFT JOIN t_usc_bigarea_user bu ON bu.BIG_AREA_ID = t3.AREA_ID /*大区、用户 关系*/
			LEFT JOIN t_usc_mdm_org_employee e ON bu.USER_ID = e.USER_ID
			LEFT JOIN t_sac_clue_compete_record CR ON d.DLR_CODE = CR.DLR_CODE
		WHERE e.USER_ID = #{param.userId}
			<if test="param.cityCode !=null and param.cityCode !=''">
				AND INSTR(g.CITY_CODE, #{param.cityCode}) <![CDATA[>]]> 0
			</if>
			<if test="param.cityId !=null and param.cityId !=''">
				AND g.CITY_ID = #{param.cityId}
			</if>
			<if test="param.cityName !=null and param.cityName !=''">
				AND INSTR(g.CITY_NAME, #{param.cityName}) <![CDATA[>]]> 0
			</if>
			<if test="param.dlrType !=null and param.dlrType !=''">
				AND d.DLR_TYPE IN 
				<foreach item="item" collection="param.dlrType.split(',')" separator="," open="(" close=")" index="">
					#{item}
			    </foreach>
			</if>
		UNION  <!-- 人(品牌大使等)所管辖门店 -->
		SELECT
			  d.DLR_ID, /*门店ID*/
			  d.DLR_CODE, /*门店编码*/
			  d.DLR_TYPE, /*门店类型*/
			  d.DLR_SHORT_NAME, /*门店名称*/
			 CR.GET_CLUE_TIMES,
			 CR.GET_SWITCH
		FROM `t_usc_mdm_user_dlr` ud
			LEFT JOIN t_usc_mdm_org_employee ue ON ud.USER_ID = ue.USER_ID 
			LEFT JOIN t_usc_mdm_org_dlr d ON ud.DLR_ID = d.DLR_ID
			LEFT JOIN t_sac_clue_compete_record CR ON d.DLR_CODE = CR.DLR_CODE
		WHERE ud.USER_ID = #{param.userId}
		<if test="param.dlrType !=null and param.dlrType !=''">
			AND d.DLR_TYPE IN 
			<foreach item="item" collection="param.dlrType.split(',')" separator="," open="(" close=")" index="">
				#{item}
		    </foreach>
		</if>
	</select>
</mapper>
