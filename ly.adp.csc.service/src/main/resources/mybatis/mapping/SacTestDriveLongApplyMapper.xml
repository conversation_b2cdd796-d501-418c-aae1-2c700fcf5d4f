<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacTestDriveLongApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacTestDriveLongApply">
        <id column="APPLY_ID" property="applyId" />
        <result column="CAR_LICENCE_NO" property="carLicenceNo" />
        <result column="VIN" property="vin" />
        <result column="CAR_TYPE_CODE" property="carTypeCode" />
        <result column="CAR_TYPE_NAME" property="carTypeName" />
        <result column="CARTYPE_CONFIG_CODE" property="cartypeConfigCode" />
        <result column="CARTYPE_CONFIG_NAME" property="cartypeConfigName" />
        <result column="CAR_COLOR_CODE" property="carColorCode" />
        <result column="CAR_COLOR_NAME" property="carColorName" />
        <result column="CAR_INCOLOR_CODE" property="carIncolorCode" />
        <result column="CAR_INCOLOR_NAME" property="carIncolorName" />
        <result column="CAN_TEST_DATE" property="canTestDate" />
        <result column="APPLY_DLR_CODE" property="applyDlrCode" />
        <result column="APPLY_REASON" property="applyReason" />
        <result column="APPLY_TIME_BEGIN" property="applyTimeBegin" />
        <result column="APPLY_TIME_END" property="applyTimeEnd" />
        <result column="AUDIT_USER_ID" property="auditUserId" />
        <result column="AUDIT_EMP_NAME" property="auditEmpName" />
        <result column="AUDIT_TYPE" property="auditType" />
        <result column="AUDIT_STATUS" property="auditStatus" />
        <result column="AUDIT_REASON" property="auditReason" />
        <result column="AUDIT_DATE" property="auditDate" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        APPLY_ID, CAR_LICENCE_NO, VIN, CAR_TYPE_CODE, CAR_TYPE_NAME, CARTYPE_CONFIG_CODE, CARTYPE_CONFIG_NAME, CAR_COLOR_CODE, CAR_COLOR_NAME, CAR_INCOLOR_CODE, CAR_INCOLOR_NAME, CAN_TEST_DATE, APPLY_DLR_CODE, APPLY_REASON, APPLY_TIME_BEGIN, APPLY_TIME_END, AUDIT_USER_ID, AUDIT_EMP_NAME, AUDIT_TYPE, AUDIT_STATUS, AUDIT_REASON, AUDIT_DATE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>

	<!-- 试乘试驾单更新 -->
	<update id="updateSacTestDriveSheet">
		update t_sac_test_drive_sheet set
			EVALUATE_FLAG=#{param.evaluateFlag}
		where TEST_DRIVE_ORDER_NO=#{param.serviceId}
	</update>

	<!-- 超长试驾申请维护 -->
	<insert id="sacTestDriveLongApplySaveOne" parameterType="map">
		INSERT INTO t_sac_test_drive_long_apply (
			APPLY_ID,
			CAR_LICENCE_NO,
			VIN,
			CAR_TYPE_CODE,
			CAR_TYPE_NAME,
			CARTYPE_CONFIG_CODE,
			CARTYPE_CONFIG_NAME,
			CAR_COLOR_CODE,
			CAR_COLOR_NAME,
			CAR_INCOLOR_CODE,
			CAR_INCOLOR_NAME,
			CAN_TEST_DATE,
			APPLY_DLR_CODE, <!-- 申请门店 -->
			APPLY_REASON,
			APPLY_TIME_BEGIN,
			APPLY_TIME_END,
			APPLY_TIME_LONG,
			AUDIT_USER_ID,
			AUDIT_EMP_NAME,
			AUDIT_TYPE, /*审核类型*/
			AUDIT_STATUS, /*审核状态编码*/
			AUDIT_STATUS_NAME, /*审核状态名称*/
			OEM_ID,
			GROUP_ID,
			OEM_CODE,
			GROUP_CODE,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			SDP_USER_ID,
			SDP_ORG_ID,
			UPDATE_CONTROL_ID)
		VALUES(
			#{param.applyId},
			#{param.carLicenceNo},
			#{param.vin},
			#{param.carTypeCode},
			#{param.carTypeName},
			#{param.cartypeConfigCode},
			#{param.cartypeConfigName},
			#{param.carColorCode},
			#{param.carColorName},
			#{param.carIncolorCode},
			#{param.carIncolorName},
			#{param.canTestDate},
			#{param.dlrCode},  /*申请门店*/
			#{param.applyReason},
			#{param.applyTimeBegin},
			#{param.applyTimeEnd},
			#{param.applyTimeLong},
			'',
			'',
			#{param.auditType},
			#{param.auditStatus},
			#{param.auditStatusName},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},/*创建人*/
			#{param.createdName},/*创建人名称*/
			now(),/*创建时间*/
			#{param.modifier},/*更新人员*/
			#{param.modifyName},/*更新人员名称*/
			now(), /*最后更新时间*/
			'1',
			'88888',
			'2',
			uuid()
		)
	</insert>
	
	<!-- 超长试驾申请历史查询/出库审核列表查询 -->
	<select id="sacTestDriveLongApplyFindAll" parameterType="map" resultType="map">
		SELECT
			T.APPLY_ID,
			T.CAR_LICENCE_NO,
			T.VIN,
			T.CAR_TYPE_CODE,
			T.CAR_TYPE_NAME,
			T.CARTYPE_CONFIG_CODE,
			T.CARTYPE_CONFIG_NAME,
			T.CAR_COLOR_CODE,
			T.CAR_COLOR_NAME,
			T.CAR_INCOLOR_CODE,
			T.CAR_INCOLOR_NAME,
			T.CAN_TEST_DATE,
			T.APPLY_DLR_CODE,
			T.APPLY_REASON,
			T.APPLY_TIME_BEGIN, /*申请时间开始*/
			T.APPLY_TIME_END, /*申请时间结束*/
			T.APPLY_TIME_LONG,/*申请时长，单位：小时*/
			T.AUDIT_USER_ID,
			T.AUDIT_EMP_NAME,
			T.AUDIT_TYPE, /*审核类型*/
			T.AUDIT_STATUS, /*审核状态编码*/
			T.AUDIT_STATUS_NAME, /*审核状态名称*/
			T.AUDIT_REASON,
			T.AUDIT_DATE,
			T.UPDATE_CONTROL_ID,
			T.CREATOR,
			T.CREATED_NAME,
			T.CREATED_DATE
		FROM
			t_sac_test_drive_long_apply T
		WHERE T.IS_ENABLE = '1'
        <if test="param.applyId != null and '' != param.applyId"> <!-- 超长试驾申请ID -->
            AND T.APPLY_ID = #{param.applyId}
        </if>
        <if test="param.applyDlrCode != null and '' != param.applyDlrCode"> <!-- 申请门店 -->
            AND T.APPLY_DLR_CODE = #{param.applyDlrCode}
        </if>
        <if test="param.creator != null and '' != param.creator"> 
            AND T.CREATOR = #{param.creator}
        </if>
        <if test="param.auditType != null and '' != param.auditType"> <!-- 审核类型 -->
            AND T.AUDIT_TYPE = #{param.auditType}
        </if>
		<if test="param.auditStatus != null and '' != param.auditStatus"> <!-- 审核状态 -->
			AND T.AUDIT_STATUS IN
			<foreach collection="param.auditStatus.split(',')" item="item" separator="," open="("  close=")">
				 #{item}
			</foreach>
		</if>
		<!-- 区域经理 -->
       	<if test="param.auditType != null and '' != param.auditType and param.auditType == '2'">
       		AND T.APPLY_DLR_CODE IN
       	    <foreach collection="param.applyDlrCodeList" item="applyDlrCode" separator="," open="("  close=")">
       		 	#{applyDlrCode}
       		</foreach>
       	</if>
       	<if test="param.applyTimeBegin != null and '' != param.applyTimeBegin"> <!-- 申请开始时间 -->
            AND DATE(T.CREATED_DATE) <![CDATA[ >= ]]> #{param.applyTimeBegin}
        </if>
        <if test="param.applyTimeEnd != null and '' != param.applyTimeEnd"> <!-- 申请结束时间 -->
            AND DATE(T.CREATED_DATE) <![CDATA[ <= ]]> #{param.applyTimeEnd}
        </if>
        ORDER BY T.LAST_UPDATED_DATE DESC
	</select>
	
	<!-- 超长试驾申请更新 -->
	<update id="sacTestDriveLongApplyUpdateById" parameterType="map" >
		UPDATE
			t_sac_test_drive_long_apply T
		SET
			T.AUDIT_STATUS = #{param.auditStatus},
			T.AUDIT_STATUS_NAME = #{param.auditStatusName},
			<!-- 审批 -->
			<if test="param.auditReason != null and param.auditReason !=''">
				T.AUDIT_REASON = #{param.auditReason}, <!-- 审批意见 -->
			</if>
			T.AUDIT_USER_ID = #{param.modifier}, <!-- 审批人编码 -->
			T.AUDIT_EMP_NAME = #{param.modifyName}, <!-- 审批人名称 -->
			T.AUDIT_DATE = now(), <!-- 审批日期 -->
			T.MODIFIER = #{param.modifier},
			T.MODIFY_NAME = #{param.modifyName},
			T.LAST_UPDATED_DATE = now(),
			T.UPDATE_CONTROL_ID = uuid()
		WHERE
			1=1
			AND T.APPLY_ID = #{param.applyId}
			AND T.UPDATE_CONTROL_ID = #{param.updateControlId}
	</update>
	
	<!-- 根据申请结束时间查询某个VIN对应的超长试驾申请单 -->
	<select id="sacTestDriveLongApplyFindByApplyTime" parameterType="map" resultType="map">
		SELECT T.* FROM t_sac_test_drive_long_apply T
		WHERE T.IS_ENABLE = '1'
			AND T.VIN = #{param.vin}
			AND (T.AUDIT_STATUS = '1' OR T.AUDIT_STATUS = '2') <!-- 审核中/审核通过 -->
			AND DATE_FORMAT(T.APPLY_TIME_END, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.applyTimeBegin}
			AND DATE_FORMAT(T.APPLY_TIME_BEGIN, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.applyTimeEnd}
		LIMIT 0, 1
	</select>
	
	<!-- 根据申请时间段查询某个VIN对应的预约单，且不存在关联的未结束的试驾单 -->
	<select id="sacTestDriveAppointmentFindByTime" parameterType="map" resultType="map">
		SELECT T.* FROM t_sac_appointment_sheet T
			JOIN t_sac_test_drive_sheet A ON T.APPOINTMENT_ID = A.APPOINTMENT_ID
		WHERE T.IS_ENABLE = '1'
			AND A.TEST_STATUS != '2'
			AND T.CAR_VIN = #{param.vin}
			AND DATE_FORMAT(T.APPOINTMENT_END_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.applyTimeBegin}
			AND DATE_FORMAT(T.APPOINTMENT_START_TIME, '%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.applyTimeEnd}
		LIMIT 0, 1
	</select>

	<select id="queryMobileByActiviteId" resultType="java.util.Map">
		SELECT CUSTOMER_PHONE mobile FROM `t_acc_bu_activity_customer` WHERE ACTIVITY_ID = #{param.activityId} and IS_ENABLE = '1' and IS_CHECK_IN='1'
		GROUP BY CUSTOMER_PHONE
	</select>
	<select id="selectSmartIdByMobile" resultType="java.util.Map">
		SELECT
		    t.SMART_ID,
			t.CUST_NAME,
			t.CUST_ID,
			t1.DLR_CODE,
			t1.DLR_SHORT_NAME AS dlrName,
			t1.REVIEW_PERSON_ID,
			t1.REVIEW_PERSON_NAME
		FROM
			t_sac_onecust_info t
				LEFT JOIN t_sac_clue_info_dlr t1 ON t.PHONE = t1.PHONE
		WHERE
			1 = 1
		  AND t.PHONE = #{param.mobile}
		  -- AND t.SMART_ID != ''
		  -- AND t.SMART_ID IS NOT NULL
	</select>

	<select id="queryLookUpValue" resultType="java.util.Map">
		SELECT LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE=#{param.lookUpTypeCode} and LOOKUP_VALUE_CODE=#{param.lookUpValueCode}
	</select>

    <select id="findtestCarDriveSheet" resultType="java.util.Map">
		select  TEST_DRIVE_ORDER_NO from t_sac_test_drive_sheet where EVALUATE_FLAG is null and TEST_DRIVE_SHEET_ID =#{testDriveSheetId}
	</select>

	<select id="checkIsSendMessage" resultType="java.util.Map">
		SELECT EXISTS
		(
		SELECT 1 FROM
		t_sac_onecust_resume
		WHERE  SENCE_CODE  in ('2','16')
		and cust_id = #{param.custId}
		AND DLR_CODE_OWNER = #{param.dlrCode}
		) AS count
	</select>
</mapper>
