<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnecustChangeLogMapper">
	    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnecustChangeLog">
        <id column="CHANGE_LOG_ID" property="changeLogId" />
        <result column="CUST_ID" property="custId" />
        <result column="CHANGE_FILED" property="changeFiled" />
        <result column="CHANGE_CONTENT" property="changeContent" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CHANGE_LOG_ID, CUST_ID, CHANGE_FILED, CHANGE_CONTENT, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
    
    <!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.changeLogId !=null and param.changeLogId !=''">and CHANGE_LOG_ID=#{param.changeLogId}</if>
	    <if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
	    <if test="param.changeFiled !=null and param.changeFiled !=''">and CHANGE_FILED=#{param.changeFiled}</if>
	</sql>
    
    <!-- 客户信息变更记录查询 -->
	<select id="sacOnecustChangeLogFindInfo" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_onecust_change_log
	    where 1=1
	    <include refid="where_condition"></include>
		ORDER BY CREATED_DATE DESC
	</select>
	
	<!-- 客户信息变更-临时表是否存在待处理数据 -->
	<select id="queryChangLogExist" resultType="java.util.Map">
		select 1 from t_sac_onecust_change_log_temp a where a.STATUS = '0' limit 1
	</select>
	
	<!-- 客户信息变更-更新批次号 -->
	<update id="updateBanchNo">
		update t_sac_onecust_change_log_temp set BANCH_NO = #{banchNo},STATUS = '1' where STATUS = '0' limit 100
	</update>
	
	<!-- 客户信息变更-获取待处理数据100T -->
	<select id="queryChangLogData" resultType="java.util.Map">
		SELECT * FROM t_sac_onecust_change_log_temp a  WHERE a.BANCH_NO = #{banchNo} AND a.STATUS = '1'
	</select>
	
	<!-- 客户信息变更-更新处理状态 -->
	<update id="updateStatus" parameterType="java.util.Map">
		update t_sac_onecust_change_log_temp set STATUS = #{param.status},DEAL_DESC = #{param.dealDesc} where BANCH_NO = #{param.banchNo}
	</update>
	
	<!-- 客户信息变更-清理处理成功数据 -->
	<update id="deleteTem" parameterType="java.util.Map">
		DELETE FROM t_sac_onecust_change_log_temp where STATUS = '2' and BANCH_NO = #{param.banchNo}
	</update>
	
	<!-- 客户信息变更新增-临时表 -->
	<insert id="createsacOnecustChangeLogTemp">
		insert into t_sac_onecust_change_log_temp(
		CHANGE_LOG_TEMP_ID,
		CUST_ID,
		OLD_VALUE,
		NEW_VALUE,
		STATUS,
		BANCH_NO,
		DEAL_DESC,
		OEM_ID,/*厂商标识ID*/
		GROUP_ID,/*集团标识ID*/
		OEM_CODE,/*厂商标识*/
		GROUP_CODE,/*集团标识*/
		CREATOR,/*创建人ID*/
		CREATED_NAME,/*创建人*/
		CREATED_DATE,/*创建日期*/
		MODIFIER,/*修改人ID*/
		MODIFY_NAME,/*修改人*/
		LAST_UPDATED_DATE,/*最后更新日期*/
		IS_ENABLE,/*是否可用*/
		SDP_USER_ID,/*SDP用户ID*/
		SDP_ORG_ID,/*SDP组织ID*/
		UPDATE_CONTROL_ID/*并发控制ID*/
		)value
		(
		uuid(),
		#{param.custId},
		#{param.oldValue},
		#{param.newValue},
		'0',
		'',
		'',
		#{param.oemId},
		#{param.groupId},
		#{param.oemCode},
		#{param.groupCode},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		'1',
		#{param.sdpUserId},
		#{param.sdpOrgId},
		#{param.updateControlId}
		)
	</insert>
	
	<!-- 客户信息变更新增-临时表 -->
	<insert id="createsacOnecustChangeLog">
		insert into t_sac_onecust_change_log(
		CHANGE_LOG_ID,
		CUST_ID,
		CHANGE_FILED,
		CHANGE_CONTENT,
		OEM_ID,/*厂商标识ID*/
		GROUP_ID,/*集团标识ID*/
		OEM_CODE,/*厂商标识*/
		GROUP_CODE,/*集团标识*/
		CREATOR,/*创建人ID*/
		CREATED_NAME,/*创建人*/
		CREATED_DATE,/*创建日期*/
		MODIFIER,/*修改人ID*/
		MODIFY_NAME,/*修改人*/
		LAST_UPDATED_DATE,/*最后更新日期*/
		IS_ENABLE,/*是否可用*/
		SDP_USER_ID,/*SDP用户ID*/
		SDP_ORG_ID,/*SDP组织ID*/
		UPDATE_CONTROL_ID/*并发控制ID*/
		)value
		(
		uuid(),
		#{param.custId},
		#{param.changeFiled},
		#{param.changeContent},
		'1',
		'1',
		'1',
		'1',
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		'1',
		'1',
		'1',
		uuid()
		)
	</insert>
</mapper>