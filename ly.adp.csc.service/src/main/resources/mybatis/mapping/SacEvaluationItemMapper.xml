<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacEvaluationItemMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacEvaluationItem">
		<id column="EVALUATION_ITEM_ID" property="evaluationItemId" />
		<result column="DLR_CODE" property="dlrCode" />
		<result column="DLR_NAME" property="dlrName" />
		<result column="EVALUATION_TYPE" property="evaluationType" />
		<result column="EVALUATION_ITEM" property="evaluationItem" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="COLUMN1" property="column1" />
		<result column="COLUMN2" property="column2" />
		<result column="COLUMN3" property="column3" />
		<result column="COLUMN4" property="column4" />
		<result column="COLUMN5" property="column5" />
		<result column="COLUMN6" property="column6" />
		<result column="COLUMN7" property="column7" />
		<result column="COLUMN8" property="column8" />
		<result column="COLUMN9" property="column9" />
		<result column="COLUMN10" property="column10" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		EVALUATION_ITEM_ID, DLR_CODE, DLR_NAME, EVALUATION_TYPE,
		EVALUATION_ITEM, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME,
		CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,IS_ENABLE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5,
		COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10
	</sql>

	<!-- 选择项查询 -->
	<select id="selectSacEvaluationItem" resultType="java.util.Map">
		select
		EVALUATION_ITEM_ID,
		DLR_CODE,
		DLR_NAME,
		IS_ENABLE,
		(case IS_ENABLE when '1' then '是' else '否' end) as isEnableName,
		EVALUATION_TYPE,
		EVALUATION_ITEM,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID
		from
		t_sac_evaluation_item
		where 1=1
		<if test="param.evaluationItemId !=null and param.evaluationItemId !=''"> and EVALUATION_ITEM_ID=#{param.evaluationItemId}</if>
		<if test="param.evaluationType !=null and param.evaluationType !=''"> and EVALUATION_TYPE=#{param.evaluationType}</if>
		<if test="param.evaluationItem !=null and param.evaluationItem !=''"> and EVALUATION_ITEM like concat('%',#{param.evaluationItem},'%')</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> and DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> and DLR_NAME=#{param.dlrName}</if>
		<if test="param.oemId !=null and param.oemId !=''"> and OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''"> and GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''"> and CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''"> and CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null"> and CREATED_DATE=#{param.createdDate}</if>
		<if test="param.createdDateMin !=null"> <![CDATA[ and CREATED_DATE>=#{param.createdDateMin}]]></if>
		<if test="param.createdDateMax !=null"><![CDATA[ and CREATED_DATE<date_add(#{param.createdDateMax}, INTERVAL 1 day)]]></if>
		<if test="param.modifier !=null and param.modifier !=''"> and MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> and MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null"> and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''"> and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		order by EVALUATION_TYPE
	</select>


	<!-- 新增选择项 -->
	<insert id="insertSacEvaluationItem">
		insert into t_sac_evaluation_item(
		EVALUATION_ITEM_ID,
		DLR_CODE,
		DLR_NAME,
		EVALUATION_TYPE,
		EVALUATION_ITEM,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		IS_ENABLE)
		values(
		#{param.evaluationItemId},
		#{param.dlrCode},
		#{param.dlrName},
		#{param.evaluationType},
		#{param.evaluationItem},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		#{param.updateControlId},
		#{param.isEnable})
	</insert>

	<!-- 选择项保存 -->
	<update id="updateSacEvaluationItem">
		update t_sac_evaluation_item set
		<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> DLR_NAME=#{param.dlrName},</if>
		<if test="param.evaluationType !=null and param.evaluationType !=''"> EVALUATION_TYPE=#{param.evaluationType},</if>
		<if test="param.evaluationItem !=null and param.evaluationItem !=''"> EVALUATION_ITEM=#{param.evaluationItem},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		where EVALUATION_ITEM_ID=#{param.evaluationItemId}
	</update>

	<select id="checkRepeat" resultType="int">
		select count(1)
		from t_sac_evaluation_item
		where 
		DLR_CODE=#{param.dlrCode}
		and EVALUATION_TYPE=#{param.evaluationType}
		and EVALUATION_ITEM=#{param.evaluationItem}
		<if test="param.evaluationItemId !=null and param.evaluationItemId !=''"> and EVALUATION_ITEM_ID!=#{param.evaluationItemId}</if>
	</select>
</mapper>