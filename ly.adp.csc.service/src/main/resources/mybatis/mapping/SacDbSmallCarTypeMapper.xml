<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbSmallCarTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.DbSmallCarType">
        <id column="SMALL_CAR_TYPE_ID" property="smallCarTypeId" />
        <result column="SMALL_CAR_TYPE_CODE" property="smallCarTypeCode" />
        <result column="SMALL_CAR_TYPE_CN" property="smallCarTypeCn" />
        <result column="SMALL_CAR_TYPE_EN" property="smallCarTypeEn" />
        <result column="CAR_BRAND_CODE" property="carBrandCode" />
        <result column="CAR_SERIES_ID" property="carSeriesId" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="REMARK" property="remark" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SMALL_CAR_TYPE_ID, SMALL_CAR_TYPE_CODE, SMALL_CAR_TYPE_CN, SMALL_CAR_TYPE_EN, CAR_BRAND_CODE, CAR_SERIES_ID, ORDER_NO, REMARK, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10
    </sql>
    <!-- 车辆类型查询 -->
	<select id="selectDbSmallCarType" resultType="java.util.Map">
	    select
	        t.SMALL_CAR_TYPE_ID,
	        t.SMALL_CAR_TYPE_CODE,
	        t.SMALL_CAR_TYPE_CN,
	        t.SMALL_CAR_TYPE_EN,
	        t.CAR_BRAND_CODE,
	        t2.CAR_BRAND_CN,
	        t.CAR_SERIES_ID,
	        t1.CAR_SERIES_CODE,
	        t1.CAR_SERIES_CN,
	        t.ORDER_NO,
	        t.REMARK,
	        t.OEM_ID,
	        t.GROUP_ID,
	        t.CREATOR,
	        t.CREATED_NAME,
	        t.CREATED_DATE,
	        t.MODIFIER,
	        t.MODIFY_NAME,
	        t.LAST_UPDATED_DATE,
	        t.UPDATE_CONTROL_ID,
			t.IS_ENABLE
		from t_sac_db_small_car_type t
		left join t_sac_db_car_brand t2 on t2.CAR_BRAND_CODE=t.CAR_BRAND_CODE
		inner join t_sac_db_car_series t1 
		on (t.CAR_SERIES_ID=t1.CAR_SERIES_ID
		<if test="param.carSeriesCode !=null and param.carSeriesCode !=''"> and t1.CAR_SERIES_CODE like concat('%',#{param.carSeriesCode},'%')</if>)
		where 1=1
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> and t.SMALL_CAR_TYPE_CODE like concat('%',#{param.smallCarTypeCode},'%')</if>
		<if test="param.smallCarTypeCn !=null and param.smallCarTypeCn !=''"> and t.SMALL_CAR_TYPE_CN like concat('%',#{param.smallCarTypeCn},'%')</if>
		<if test="param.smallCarTypeEn !=null and param.smallCarTypeEn !=''"> and t.SMALL_CAR_TYPE_EN like concat('%',#{param.smallCarTypeEn},'%')</if>
		<if test="param.carBrandCode !=null and param.carBrandCode !=''"> and t.CAR_BRAND_CODE like concat('%',#{param.carBrandCode},'%')</if>
		<if test="param.carSeriesId !=null and param.carSeriesId !=''"> and t.CAR_SERIES_ID=#{param.carSeriesId}</if>
		<if test="param.orderNo !=null and param.orderNo !=''"> and t.ORDER_NO=#{param.orderNo}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.column1 !=null and param.column1 !=''"> and t.COLUMN1=#{param.column1}</if>
		<if test="param.column2 !=null and param.column2 !=''"> and t.COLUMN2=#{param.column2}</if>
		<if test="param.column3 !=null and param.column3 !=''"> and t.COLUMN3=#{param.column3}</if>
		<if test="param.column4 !=null and param.column4 !=''"> and t.COLUMN4=#{param.column4}</if>
		<if test="param.column5 !=null and param.column5 !=''"> and t.COLUMN5=#{param.column5}</if>
		<if test="param.column6 !=null and param.column6 !=''"> and t.COLUMN6=#{param.column6}</if>
		<if test="param.column7 !=null and param.column7 !=''"> and t.COLUMN7=#{param.column7}</if>
		<if test="param.column8 !=null and param.column8 !=''"> and t.COLUMN8=#{param.column8}</if>
		<if test="param.column9 !=null and param.column9 !=''"> and t.COLUMN9=#{param.column9}</if>
		<if test="param.column10 !=null and param.column10 !=''"> and t.COLUMN10=#{param.column10}</if>
		order by t.ORDER_NO
	</select>
</mapper>
