<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBuMenusControlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacBuMenusControl">
        <id column="MENUS_CONTROL_ID" property="menusControlId" />
        <result column="MENUS_NAME" property="menusName" />
        <result column="MENUS_PATH" property="menusPath" />
        <result column="STATION_TYPE" property="stationType" />
        <result column="STATION_TYPE_NAME" property="stationTypeName" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MENUS_CONTROL_ID, MENUS_NAME, MENUS_PATH, STATION_TYPE, STATION_TYPE_NAME, ORDER_NO, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.menusControlId !=null and param.menusControlId !=''">and MENUS_CONTROL_ID=#{param.menusControlId}</if>
    	<if test="param.menusName !=null and param.menusName !=''">and INSTR(MENUS_NAME,#{param.menusName})</if>
    	<if test="param.menusPath !=null and param.menusPath !=''">and INSTR(MENUS_PATH,#{param.menusPath})</if>
    	<if test="param.stationType !=null and param.stationType !=''">and STATION_TYPE=#{param.stationType}</if>
    	<if test="param.stationTypeName !=null and param.stationTypeName !=''">and INSTR(STATION_TYPE_NAME,#{param.stationTypeName})</if>
    	<if test="param.orderNo !=null and param.orderNo !=''">and ORDER_NO=#{param.orderNo}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null ">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null ">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- APP菜单权限控制 信息查询 -->
	<select id="querySacBuMenusControl" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_bu_menus_control
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<!-- APP菜单权限控制 信息删除（物理删除） -->
	<delete id="deleteSacBuMenusControl">
		DELETE 
		FROM
			t_sac_bu_menus_control
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- APP菜单权限控制 信息新增 -->
	<insert id="createSacBuMenusControl">
		insert into t_sac_bu_menus_control(<include refid="Base_Column_List"></include>)
		values(
        	uuid(),
			#{param.menusName},
			#{param.menusPath},
			#{param.stationType},
			#{param.stationTypeName},
			#{param.orderNo},
			#{param.remark},
			#{entity.userID},
			#{entity.empName},
			now(),
			#{entity.userID},
			#{entity.empName},
			now(),
			'1',
			uuid()
		)
	</insert>
	
	<!-- APP菜单权限控制 信息更新 -->
	<update id="updateSacBuMenusControl">
		update t_sac_bu_menus_control  set 
			<!-- 更新列表 -->
			<if test="param.menusName !=null and param.menusName !=''">MENUS_NAME=#{param.menusName},</if>
			<if test="param.menusPath !=null and param.menusPath !=''">MENUS_PATH=#{param.menusPath},</if>
			<if test="param.stationType !=null and param.stationType !=''">STATION_TYPE=#{param.stationType},</if>
			<if test="param.stationTypeName !=null and param.stationTypeName !=''">STATION_TYPE_NAME=#{param.stationTypeName},</if>
			<if test="param.orderNo !=null and param.orderNo !=''">ORDER_NO=#{param.orderNo},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null ">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null ">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">UPDATE_CONTROL_ID=#{param.updateControlId},</if>
			<!-- 结束无逗号 -->
			MENUS_CONTROL_ID=MENUS_CONTROL_ID
			where 1=1 
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</update>
	<update id="updateSacBuMenusControlBymenusControlId">
		update
			t_sac_bu_menus_control  set
		<if test="param.menusName !=null and param.menusName !=''">MENUS_NAME=#{param.menusName},</if>
		<if test="param.menusPath !=null and param.menusPath !=''">MENUS_PATH=#{param.menusPath},</if>
		<if test="param.stationType !=null and param.stationType !=''">STATION_TYPE=#{param.stationType},</if>
		<if test="param.stationTypeName !=null and param.stationTypeName !=''">STATION_TYPE_NAME=#{param.stationTypeName},</if>
		<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
		MODIFIER=#{entity.userID},
		MODIFY_NAME=#{entity.empName},
		LAST_UPDATED_DATE=now(),
		UPDATE_CONTROL_ID=uuid()
		WHERE
		1=1
		AND MENUS_CONTROL_ID =#{param.menusControlId}
		AND UPDATE_CONTROL_ID =#{param.updateControlId}

	</update>
</mapper>
