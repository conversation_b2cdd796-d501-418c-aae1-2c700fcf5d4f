<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.OutboundConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.OutboundConfig">
        <id column="OUTBOUND_CONFIG_ID" property="outboundConfigId" />
        <result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
        <result column="INFO_CHAN_M_NAME" property="infoChanMName" />
        <result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
        <result column="INFO_CHAN_D_NAME" property="infoChanDName" />
        <result column="INFO_CHAN_DD_CODE" property="infoChanDDCode" />
        <result column="INFO_CHAN_DD_NAME" property="infoChanDDName" />
        <result column="CHANNEL_CODE" property="channelCode" />
        <result column="CHANNEL_NAME" property="channelName" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_OUTBOUND" property="isOutbound" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        OUTBOUND_CONFIG_ID, INFO_CHAN_M_CODE, INFO_CHAN_M_NAME, INFO_CHAN_D_CODE, INFO_CHAN_D_NAME, INFO_CHAN_DD_CODE, INFO_CHAN_DD_NAME, CHANNEL_CODE, CHANNEL_NAME, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, IS_OUTBOUND, UPDATE_CONTROL_ID
    </sql>
    
    <!-- 查询商机外呼配置 -->
    <select id="selectOutboundConfig" resultType="java.util.Map">
	    select 
	    	OUTBOUND_CONFIG_ID, 
	    	INFO_CHAN_M_CODE, 
	    	INFO_CHAN_M_NAME, 
	    	INFO_CHAN_D_CODE, 
	    	INFO_CHAN_D_NAME, 
	    	INFO_CHAN_DD_CODE, 
	    	INFO_CHAN_DD_NAME, 
	    	CHANNEL_CODE, 
	    	CHANNEL_NAME, 
	    	OEM_ID, 
	    	GROUP_ID, 
	    	CREATOR, 
	    	CREATED_NAME, 
	    	CREATED_DATE, 
	    	MODIFIER, 
	    	MODIFY_NAME, 
	    	LAST_UPDATED_DATE, 
	    	IS_ENABLE, 
	    	IS_OUTBOUND, 
	    	UPDATE_CONTROL_ID
	    from t_sac_outbound_config
	    where 1=1
	    	<if test="param.outboundConfigId !=null and param.outboundConfigId !=''"> and OUTBOUND_CONFIG_ID=#{param.outboundConfigId}</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''"> and INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''"> and INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''"> and INFO_CHAN_D_CODE=#{param.infoChanDCode}</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''"> and INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
			<if test="param.infoChanDDCode !=null and param.infoChanDDCode !=''"> and INFO_CHAN_DD_CODE=#{param.infoChanDDCode}</if>
			<if test="param.infoChanDDName !=null and param.infoChanDDName !=''"> and INFO_CHAN_DD_NAME=#{param.infoChanDDName}</if>
			<if test="param.channelCode !=null and param.channelCode !=''"> and CHANNEL_CODE=#{param.channelCode}</if>
			<if test="param.channelName !=null and param.channelName !=''"> and CHANNEL_NAME=#{param.channelName}</if>
			<if test="param.oemId !=null and param.oemId !=''"> and OEM_ID=#{param.oemId}</if>
			<if test="param.groupId !=null and param.groupId !=''"> and GROUP_ID=#{param.groupId}</if>
			<if test="param.creator !=null and param.creator !=''"> and CREATOR=#{param.creator}</if>
			<if test="param.createdName !=null and param.createdName !=''"> and CREATED_NAME=#{param.createdName}</if>
			<if test="param.createdDate !=null and param.createdDate !=''"> and CREATED_DATE=#{param.createdDate}</if>
			<if test="param.modifier !=null and param.modifier !=''"> and MODIFIER=#{param.modifier}</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> and MODIFY_NAME=#{param.modifyName}</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''"> and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
			<if test="param.isOutbound !=null and param.isOutbound !=''"> and IS_OUTBOUND=#{param.isOutbound}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''"> and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> and IS_ENABLE=#{param.isEnable}</if>
		 order by LAST_UPDATED_DATE desc
    </select>
    
    <!-- 插入商机外呼配置 -->
	<insert id="insertOutboundConfig">
	    insert into t_sac_outbound_config(
        OUTBOUND_CONFIG_ID,
        INFO_CHAN_M_CODE,
        INFO_CHAN_M_NAME,
        INFO_CHAN_D_CODE,
        INFO_CHAN_D_NAME,
        INFO_CHAN_DD_CODE,
        INFO_CHAN_DD_NAME,
        CHANNEL_CODE,
        CHANNEL_NAME,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        IS_OUTBOUND,
        UPDATE_CONTROL_ID,
		IS_ENABLE)
		values(
               #{param.outboundConfigId},
               #{param.infoChanMCode},
               #{param.infoChanMName},
               #{param.infoChanDCode},
               #{param.infoChanDName},
               #{param.infoChanDDCode},
               #{param.infoChanDDName},
               #{param.channelCode},
               #{param.channelName},
               #{param.oemId},
               #{param.groupId},
               #{param.creator},
               #{param.createdName},
               #{param.createdDate},
               #{param.modifier},
               #{param.modifyName},
               #{param.lastUpdatedDate},
               #{param.isOutbound},
               #{param.updateControlId},
			   #{param.isEnable})
	</insert>
	
    <!-- 更新商机外呼配置 -->
	<update id="updateOutboundConfig">
		update t_sac_outbound_config set
			<if test="param.outboundConfigId !=null and param.outboundConfigId !=''"> OUTBOUND_CONFIG_ID=#{param.outboundConfigId},</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''"> INFO_CHAN_M_CODE=#{param.infoChanMCode},</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''"> INFO_CHAN_M_NAME=#{param.infoChanMName},</if>
			<if test="param.infoChanDCode !=null"> INFO_CHAN_D_CODE=#{param.infoChanDCode},</if>
			<if test="param.infoChanDName !=null"> INFO_CHAN_D_NAME=#{param.infoChanDName},</if>
			<if test="param.infoChanDDCode !=null"> INFO_CHAN_DD_CODE=#{param.infoChanDDCode},</if>
			<if test="param.infoChanDDName !=null"> INFO_CHAN_DD_NAME=#{param.infoChanDDName},</if>
			<if test="param.channelCode !=null and param.channelCode !=''"> CHANNEL_CODE=#{param.channelCode},</if>
			<if test="param.channelName !=null and param.channelName !=''"> CHANNEL_NAME=#{param.channelName},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isOutbound !=null and param.isOutbound !=''"> IS_OUTBOUND=#{param.isOutbound},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable}</if>
	 	where OUTBOUND_CONFIG_ID=#{param.outboundConfigId}
	 </update>
	 
	 <!-- 校验商机外呼配置是否已存在 -->
	 <select id="checkRepeat" resultType="int">
      select count(1) as countNo
      from t_sac_outbound_config t
      where 1=1
      and t.IS_ENABLE='1'
      and t.INFO_CHAN_M_CODE=#{param.infoChanMCode}
      <choose>
	      <when test="param.infoChanDCode !=null and param.infoChanDCode !=''"> and t.INFO_CHAN_D_CODE=#{param.infoChanDCode}</when>
	      <otherwise>and (t.INFO_CHAN_D_CODE is null or t.INFO_CHAN_D_CODE='')</otherwise>
      </choose>
      <if test="param.outboundConfigId !=null and param.outboundConfigId !=''">
      	and t.OUTBOUND_CONFIG_ID!=#{param.outboundConfigId}
      </if>
  </select>
  
  <select id="checkOutBoundExists" resultType="int">
        select count(1)
        from t_sac_outbound_config
        where OUTBOUND_CONFIG_ID=#{outboundConfigId}
    </select>
</mapper>
