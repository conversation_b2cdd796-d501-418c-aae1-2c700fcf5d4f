<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.csc.clue.idal.mapper.SacClueHatchPoolMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.mp.csc.clue.entities.SacClueHatchPool">
		<id column="HATCH_POOL_ID" property="hatchPoolId" />
		<result column="CLUE_DLR_ID" property="clueDlrId" />
		<result column="SERVER_ORDER" property="serverOrder" />
		<result column="CUST_ID" property="custId" />
		<result column="CUST_NAME" property="custName" />
		<result column="PHONE" property="phone" />
		<result column="GENDER_CODE" property="genderCode" />
		<result column="GENDER_NAME" property="genderName" />
		<result column="COLUMN1" property="column1" />
		<result column="COLUMN2" property="column2" />
		<result column="COLUMN3" property="column3" />
		<result column="COLUMN4" property="column4" />
		<result column="COLUMN5" property="column5" />
		<result column="COLUMN6" property="column6" />
		<result column="COLUMN7" property="column7" />
		<result column="COLUMN8" property="column8" />
		<result column="COLUMN9" property="column9" />
		<result column="COLUMN10" property="column10" />
		<result column="COLUMN11" property="column11" />
		<result column="COLUMN12" property="column12" />
		<result column="COLUMN13" property="column13" />
		<result column="COLUMN14" property="column14" />
		<result column="COLUMN15" property="column15" />
		<result column="COLUMN16" property="column16" />
		<result column="COLUMN17" property="column17" />
		<result column="COLUMN18" property="column18" />
		<result column="COLUMN19" property="column19" />
		<result column="COLUMN20" property="column20" />
		<result column="COLUMN21" property="column21" />
		<result column="COLUMN22" property="column22" />
		<result column="COLUMN23" property="column23" />
		<result column="COLUMN24" property="column24" />
		<result column="COLUMN25" property="column25" />
		<result column="COLUMN26" property="column26" />
		<result column="COLUMN27" property="column27" />
		<result column="COLUMN28" property="column28" />
		<result column="COLUMN29" property="column29" />
		<result column="COLUMN30" property="column30" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		HATCH_POOL_ID, CLUE_DLR_ID, SERVER_ORDER, CUST_ID,
		CUST_NAME, PHONE, GENDER_CODE,GENDER_NAME, 
		COLUMN1, COLUMN2, COLUMN3,COLUMN4, COLUMN5, COLUMN6,COLUMN7, 
		COLUMN8,COLUMN9, COLUMN10,COLUMN11, COLUMN12, COLUMN13, 
		COLUMN14, COLUMN15, COLUMN16,COLUMN17,COLUMN18, COLUMN19, 
		COLUMN20, COLUMN21, COLUMN22, COLUMN23, COLUMN24,COLUMN25,
		COLUMN26,COLUMN27, COLUMN28, COLUMN29, COLUMN30,EXTENDS_JSON,
		OEM_ID, GROUP_ID, CREATOR,
		CREATED_NAME, CREATED_DATE,MODIFIER,
		MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
	</sql>
	<!-- 线索孵化池新增 -->
	<insert id="insertSacClueHatchPool">
		insert into t_sac_clue_hatch_pool(
		HATCH_POOL_ID,
		CLUE_DLR_ID,
		SERVER_ORDER,
		CUST_ID,
		CUST_NAME,
		PHONE,
		GENDER_CODE,
		GENDER_NAME,
		EXTENDS_JSON,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		COLUMN6,
		COLUMN7,
		COLUMN8,
		COLUMN9,
		COLUMN10,
		COLUMN11,
		COLUMN12,
		COLUMN13,
		COLUMN14,
		COLUMN15,
		COLUMN16,
		COLUMN17,
		COLUMN18,
		COLUMN19,
		COLUMN20,
		COLUMN21,
		COLUMN22,
		COLUMN23,
		COLUMN24,
		COLUMN25,
		COLUMN26,
		COLUMN27,
		COLUMN28,
		COLUMN29,
		COLUMN30,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		IS_ENABLE)
		values(
		#{param.hatchPoolId},
		#{param.clueDlrId},
		#{param.serverOrder},
		#{param.custId},
		#{param.custName},
		#{param.phone},
		#{param.genderCode},
		#{param.genderName},
		#{param.extendsJson},
		#{param.column1},
		#{param.column2},
		#{param.column3},
		#{param.column4},
		#{param.column5},
		#{param.column6},
		#{param.column7},
		#{param.column8},
		#{param.column9},
		#{param.column10},
		#{param.column11},
		#{param.column12},
		#{param.column13},
		#{param.column14},
		#{param.column15},
		#{param.column16},
		#{param.column17},
		#{param.column18},
		#{param.column19},
		#{param.column20},
		#{param.column21},
		#{param.column22},
		#{param.column23},
		#{param.column24},
		#{param.column25},
		#{param.column26},
		#{param.column27},
		#{param.column28},
		#{param.column29},
		#{param.column30},
		#{param.oemId},
		#{param.groupId},
		#{param.creator},
		#{param.createdName},
		#{param.createdDate},
		#{param.modifier},
		#{param.modifyName},
		#{param.lastUpdatedDate},
		#{param.updateControlId},
		#{param.isEnable})
	</insert>
	<!-- 线索孵化池查找 -->
	<select id="selectSacClueHatchPool" resultType="java.util.Map">
		select
		HATCH_POOL_ID, CLUE_DLR_ID, SERVER_ORDER, CUST_ID, CUST_NAME,
		PHONE, GENDER_CODE,GENDER_NAME,EXTENDS_JSON, 
		OEM_ID, GROUP_ID,CREATOR,
		CREATED_NAME, CREATED_DATE,MODIFIER, MODIFY_NAME,
		LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
		from
		t_sac_clue_hatch_pool
		where 1=1
		<if test="param.hatchPoolId !=null and param.hatchPoolId !=''"> and HATCH_POOL_ID=#{param.hatchPoolId}</if>
		<if test="param.clueDlrId !=null and param.clueDlrId !=''"> and CLUE_DLR_ID=#{param.clueDlrId}</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and SERVER_ORDER=#{param.serverOrder}</if>
		<if test="param.custId !=null and param.custId !=''"> and CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''"> and CUST_NAME like concat('%',#{param.custName},'%')</if>
		<if test="param.phone !=null and param.phone !=''"> and PHONE=#{param.phone}</if>
		<if test="param.genderCode !=null and param.genderCode !=''"> and GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''"> and GENDER_NAME=#{param.genderName}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''"> and EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''"> and OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''"> and GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''"> and CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''"> and CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null"> and CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''"> and MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> and MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null"> and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and IS_ENABLE=#{param.isEnable}</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</select>
	<!-- 线索孵化池更新 -->
	<update id="updateSacClueHatchPool">
		update t_sac_clue_hatch_pool set
		<if test="param.clueDlrId !=null and param.clueDlrId !=''"> CLUE_DLR_ID=#{param.clueDlrId},</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> SERVER_ORDER=#{param.serverOrder},</if>
		<if test="param.custId !=null and param.custId !=''"> CUST_ID=#{param.custId},</if>
		<if test="param.custName !=null and param.custName !=''"> CUST_NAME=#{param.custName},</if>
		<if test="param.phone !=null and param.phone !=''"> PHONE=#{param.phone},</if>
		<if test="param.genderCode !=null and param.genderCode !=''"> GENDER_CODE=#{param.genderCode},</if>
		<if test="param.genderName !=null and param.genderName !=''"> GENDER_NAME=#{param.genderName},</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if
			test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		where HATCH_POOL_ID=#{param.hatchPoolId}
	</update>

</mapper>
