<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBuBoutiqueSetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacBuBoutiqueSet">
        <id column="BOUTIQUE_ID" property="boutiqueId" />
        <result column="BOUTIQUE_CODE" property="boutiqueCode" />
        <result column="BOUTIQUE_NAME" property="boutiqueName" />
        <result column="DLR_TYPE" property="dlrType" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BOUTIQUE_ID, BOUTIQUE_CODE, BOUTIQUE_NAME, DLR_TYPE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.boutiqueId !=null and param.boutiqueId !=''">and BOUTIQUE_ID=#{param.boutiqueId}</if>
    	<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">and BOUTIQUE_CODE=#{param.boutiqueCode}</if>
    	<if test="param.boutiqueName !=null and param.boutiqueName !=''">and BOUTIQUE_NAME=#{param.boutiqueName}</if>
    	<if test="param.dlrType !=null and param.dlrType !=''">and DLR_TYPE=#{param.dlrType}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 精品基本数据维护表 信息查询 -->
	<select id="querySacBuBoutiqueSet" resultType="java.util.Map">
		SELECT
			t.BOUTIQUE_ID,
			t.BOUTIQUE_CODE,
			t.BOUTIQUE_NAME,
		    t.UPDATE_CONTROL_ID,
		    t.IS_ENABLE,
			CASE
				t.IS_ENABLE
				WHEN '1' THEN
					'启用' ELSE '禁用'
				END AS IS_ENABLE_STATUS
		FROM
			t_sac_bu_boutique_set t
		WHERE
			1 =1
		<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">
		  and CONCAT(t.BOUTIQUE_CODE,t.BOUTIQUE_NAME) like CONCAT('%',#{param.boutiqueCode},'%')
		  </if>
		<if test="param.boutiqueName !=null and param.boutiqueName !=''">and INSTR( t.BOUTIQUE_NAME,#{param.boutiqueName})</if>
		order by  t.LAST_UPDATED_DATE desc
	</select>
	
	<!-- 精品基本数据维护表 信息删除（物理删除） -->
	<delete id="deleteSacBuBoutiqueSet">
		DELETE 
		FROM
			t_sac_bu_boutique_set
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 精品基本数据维护表 信息新增 -->
	<insert id="createSacBuBoutiqueSet">
		insert into t_sac_bu_boutique_set(<include refid="Base_Column_List"></include>)
		value(
        	#{param.boutiqueId},
			#{param.boutiqueCode},
			#{param.boutiqueName},
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			#{entity.userID},
			#{entity.empName},
			now(),
			#{entity.userID},
			#{entity.empName},
			now(),
			#{param.isEnable},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 精品基本数据维护表 信息更新 -->
	<update id="updateSacBuBoutiqueSet">
		update t_sac_bu_boutique_set  set 
			<!-- 更新列表 -->
			<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">BOUTIQUE_CODE=#{param.boutiqueCode},</if>
			<if test="param.boutiqueName !=null and param.boutiqueName !=''">BOUTIQUE_NAME=#{param.boutiqueName},</if>
			<if test="param.dlrType !=null and param.dlrType !=''">DLR_TYPE=#{param.dlrType},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{entity.userID},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{entity.empName},</if>
			CREATED_DATE=now(),
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{entity.userID},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{entity.empName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			UPDATE_CONTROL_ID=uuid()
			<!-- 结束无逗号 -->

			where 1=1 
			<!-- 描述条件 -->
			AND	BOUTIQUE_ID =#{param.boutiqueId}
			AND	UPDATE_CONTROL_ID =#{param.updateControlId}
			<!--<include refid="where_condition"></include>-->
	</update>
	
	<!-- 精品门店信息表新增 -->
	<insert id="insertSacBuBoutiqueDlrInfo" parameterType="map">
		INSERT INTO t_sac_bu_boutique_dlr (
			BOUTIQUE_DLR_ID,
			DLR_ID,
			DLR_CODE,
			CONSUGNEE,
			CONSUGNEE_PHONE,
			CONSUGNEE_ADDRESS,
			PROVINCE_ID,
			PROVINCE_NAME,
			CITY_ID,
			CITY_NAME,
			COUNTY_ID,
			COUNTY_NAME,
			COLUMN1,
			COLUMN2,
			COLUMN3,
			COLUMN4,
			COLUMN5,
			REMARK,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID)
		VALUES(
			uuid(),
			#{param.dlrId},
			#{param.dlrCode},
			#{param.consugnee},
			#{param.consugneePhone},
			#{param.consugneeAddress},
			#{param.provinceId},
			#{param.provinceName},
			#{param.cityId},
			#{param.cityName},
			#{param.countyId},
			#{param.countyName},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.remark},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			#{param.isEnable},
			uuid()
		)
	</insert>
	
	<!-- 精品门店信息表 信息查询 -->
	<select id="querySacBuBoutiqueDlr" resultType="map">
		SELECT *
	    FROM t_sac_bu_boutique_dlr
	    where 1=1
	    <if test="param.boutiqueDlrId !=null and param.boutiqueDlrId !=''">and BOUTIQUE_DLR_ID=#{param.boutiqueDlrId}</if>
    	<if test="param.dlrId !=null and param.dlrId !=''">and DLR_ID=#{param.dlrId}</if>
    	<if test="param.dlrCode !=null and param.dlrCode !=''">and DLR_CODE=#{param.dlrCode}</if>
    	<if test="param.consugnee !=null and param.consugnee !=''">and CONSUGNEE=#{param.consugnee}</if>
    	<if test="param.consugneePhone !=null and param.consugneePhone !=''">and CONSUGNEE_PHONE=#{param.consugneePhone}</if>
    	<if test="param.consugneeAddress !=null and param.consugneeAddress !=''">and CONSUGNEE_ADDRESS=#{param.consugneeAddress}</if>
    	<if test="param.provinceId !=null and param.provinceId !=''">and PROVINCE_ID=#{param.provinceId}</if>
    	<if test="param.provinceName !=null and param.provinceName !=''">and PROVINCE_NAME=#{param.provinceName}</if>
    	<if test="param.cityId !=null and param.cityId !=''">and CITY_ID=#{param.cityId}</if>
    	<if test="param.cityName !=null and param.cityName !=''">and CITY_NAME=#{param.cityName}</if>
    	<if test="param.countyId !=null and param.countyId !=''">and COUNTY_ID=#{param.countyId}</if>
    	<if test="param.countyName !=null and param.countyName !=''">and COUNTY_NAME=#{param.countyName}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</select>
	
	<!-- 精品门店信息表 信息删除（物理删除） -->
	<delete id="deleteSacBuBoutiqueDlr">
		DELETE 
		FROM
			t_sac_bu_boutique_dlr
		WHERE 1=1
	    <if test="param.boutiqueDlrId !=null and param.boutiqueDlrId !=''">and BOUTIQUE_DLR_ID=#{param.boutiqueDlrId}</if>
    	<if test="param.dlrId !=null and param.dlrId !=''">and DLR_ID=#{param.dlrId}</if>
    	<if test="param.dlrCode !=null and param.dlrCode !=''">and DLR_CODE=#{param.dlrCode}</if>
    	<if test="param.consugnee !=null and param.consugnee !=''">and CONSUGNEE=#{param.consugnee}</if>
    	<if test="param.consugneePhone !=null and param.consugneePhone !=''">and CONSUGNEE_PHONE=#{param.consugneePhone}</if>
    	<if test="param.consugneeAddress !=null and param.consugneeAddress !=''">and CONSUGNEE_ADDRESS=#{param.consugneeAddress}</if>
    	<if test="param.provinceId !=null and param.provinceId !=''">and PROVINCE_ID=#{param.provinceId}</if>
    	<if test="param.provinceName !=null and param.provinceName !=''">and PROVINCE_NAME=#{param.provinceName}</if>
    	<if test="param.cityId !=null and param.cityId !=''">and CITY_ID=#{param.cityId}</if>
    	<if test="param.cityName !=null and param.cityName !=''">and CITY_NAME=#{param.cityName}</if>
    	<if test="param.countyId !=null and param.countyId !=''">and COUNTY_ID=#{param.countyId}</if>
    	<if test="param.countyName !=null and param.countyName !=''">and COUNTY_NAME=#{param.countyName}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>	
    </delete>
    
    <!-- 精品门店信息表 信息更新 -->
	<update id="updateSacBuBoutiqueDlr">
	update t_sac_bu_boutique_dlr set 
		<!-- 更新列表 -->
		<if test="param.dlrId !=null and param.dlrId !=''">DLR_ID=#{param.dlrId},</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
		<if test="param.consugnee !=null and param.consugnee !=''">CONSUGNEE=#{param.consugnee},</if>
		<if test="param.consugneePhone !=null and param.consugneePhone !=''">CONSUGNEE_PHONE=#{param.consugneePhone},</if>
		<if test="param.consugneeAddress !=null and param.consugneeAddress !=''">CONSUGNEE_ADDRESS=#{param.consugneeAddress},</if>
		<if test="param.provinceId !=null and param.provinceId !=''">PROVINCE_ID=#{param.provinceId},</if>
		<if test="param.provinceName !=null and param.provinceName !=''">PROVINCE_NAME=#{param.provinceName},</if>
		<if test="param.cityId !=null and param.cityId !=''">CITY_ID=#{param.cityId},</if>
		<if test="param.cityName !=null and param.cityName !=''">CITY_NAME=#{param.cityName},</if>
		<if test="param.countyId !=null and param.countyId !=''">COUNTY_ID=#{param.countyId},</if>
		<if test="param.countyName !=null and param.countyName !=''">COUNTY_NAME=#{param.countyName},</if>
		<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
		<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
		<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
		<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
		<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
		<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
		<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null ">CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null ">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
		UPDATE_CONTROL_ID = UUID()
		where 1=1 
		<!-- 描述条件 -->
		<if test="param.boutiqueDlrId !=null and param.boutiqueDlrId !=''">and BOUTIQUE_DLR_ID=#{param.boutiqueDlrId}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
	
	<!-- 精品门店信息查询 -->
	<select id="querySacBuBoutiqueDlrInfo" parameterType="map" resultType="map">
		SELECT
			d.DLR_ID,/*门店ID*/
			d.DLR_CODE,/*门店编码*/
			d.DLR_TYPE,/*门店类型*/
			d.DLR_SHORT_NAME, /*门店名称*/
			e.EMP_ID,
			e.EMP_NAME ,
			e.EMP_CODE,
			sb.BOUTIQUE_DLR_ID,
			sb.CONSUGNEE,
			sb.CONSUGNEE_PHONE,
			sb.CONSUGNEE_ADDRESS,
			sb.PROVINCE_ID,
			sb.PROVINCE_NAME,
			sb.CITY_ID,
			sb.CITY_NAME,
			sb.COUNTY_ID,
			sb.COUNTY_NAME,
		    sb.UPDATE_CONTROL_ID
		FROM t_usc_mdm_org_dlr d
			INNER JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
			INNER JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
			LEFT JOIN t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
  			LEFT JOIN t_usc_mdm_org_province h ON d.PROVINCE_ID = h.PROVINCE_ID
			LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
			LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
			LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
			LEFT JOIN t_usc_bigarea_user bu ON bu.BIG_AREA_ID = t3.AREA_ID /*大区、用户 关系*/
			LEFT JOIN t_usc_mdm_org_employee e ON bu.USER_ID = e.USER_ID
			LEFT JOIN t_sac_bu_boutique_dlr sb ON d.DLR_ID = sb.DLR_ID
		WHERE e.USER_ID = #{param.userId}
			<if test="param.cityCode !=null and param.cityCode !=''">
				AND INSTR(g.CITY_CODE, #{param.cityCode}) <![CDATA[>]]> 0
			</if>
			<if test="param.cityId !=null and param.cityId !=''">
				AND g.CITY_ID = #{param.cityId}
			</if>
			<if test="param.cityName !=null and param.cityName !=''">
				AND INSTR(g.CITY_NAME, #{param.cityName}) <![CDATA[>]]> 0
			</if>
			<if test="param.dlrCode != null and param.dlrCode != ''">
			    AND d.DLR_CODE = #{param.dlrCode}
			</if>
			<if test="param.dlrShortName != null and param.dlrShortName != ''">
				AND INSTR(d.DLR_SHORT_NAME, #{param.dlrShortName}) <![CDATA[>]]> 0
			</if>
			<if test="param.consugnee != null and param.consugnee != ''">
				AND INSTR(sb.CONSUGNEE , #{param.consugnee})<![CDATA[>]]> 0
			</if>
			<if test="param.consugneePhone != null and param.consugneePhone != ''">
				AND INSTR(sb.CONSUGNEE_PHONE , #{param.consugneePhone})<![CDATA[>]]> 0
			</if>
		UNION  <!-- 人(品牌大使等)所管辖门店 -->
		SELECT
			d.DLR_ID, /*门店ID*/
			d.DLR_CODE, /*门店编码*/
			d.DLR_TYPE, /*门店类型*/
			d.DLR_SHORT_NAME, /*门店名称*/
			ue.EMP_ID,
			ue.EMP_NAME,
			ue.EMP_CODE,
			sb.BOUTIQUE_DLR_ID,
			sb.CONSUGNEE,
			sb.CONSUGNEE_PHONE,
			sb.CONSUGNEE_ADDRESS,
			sb.PROVINCE_ID,
			sb.PROVINCE_NAME,
			sb.CITY_ID,
			sb.CITY_NAME,
			sb.COUNTY_ID,
			sb.COUNTY_NAME,
			sb.UPDATE_CONTROL_ID
		FROM `t_usc_mdm_user_dlr` ud
			LEFT JOIN t_usc_mdm_org_employee ue ON ud.USER_ID = ue.USER_ID 
			LEFT JOIN t_usc_mdm_org_dlr d ON ud.DLR_ID = d.DLR_ID
			LEFT JOIN t_sac_bu_boutique_dlr sb ON d.DLR_ID = sb.DLR_ID
		WHERE ud.USER_ID = #{param.userId}
		<if test="param.dlrCode != null and param.dlrCode != ''">
		    AND d.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.dlrShortName != null and param.dlrShortName != ''">
			AND INSTR(d.DLR_SHORT_NAME, #{param.dlrShortName}) <![CDATA[>]]> 0
		</if>
		<if test="param.consugnee != null and param.consugnee != ''">
		    AND INSTR(sb.CONSUGNEE , #{param.consugnee})<![CDATA[>]]> 0
		</if>
		<if test="param.consugneePhone != null and param.consugneePhone != ''">
		    AND INSTR(sb.CONSUGNEE_PHONE , #{param.consugneePhone})<![CDATA[>]]> 0
		</if>
	</select>
	
	<select id="selectByBoutiqueCode" resultType="int">
		SELECT count(*) FROM t_sac_bu_boutique_set WHERE BOUTIQUE_CODE=#{param.boutiqueCode}
		<if test="param.boutiqueId != null and param.boutiqueId != ''">
			AND BOUTIQUE_ID != #{param.boutiqueId}
		</if>
	</select>
	
	<select id="selectByBoutiqueName" resultType="int">
		SELECT count(*) FROM t_sac_bu_boutique_set WHERE BOUTIQUE_NAME=#{param.boutiqueName}
		<if test="param.boutiqueId != null and param.boutiqueId != ''">
			AND BOUTIQUE_ID != #{param.boutiqueId}
		</if>
	</select>

	<!-- 精品基本数据维护表 批量新增 -->
	<insert id="insertBoutiqueSetImport">
	    insert into t_sac_bu_boutique_set(<include refid="Base_Column_List"></include>)
	    value
	    <foreach collection="dataList" item="param" index="index" separator=",">
	    (
	        uuid(),
	        #{param.boutiqueCode},
	        #{param.boutiqueName},
	        #{param.dlrType},
	        #{param.column1},
	        #{param.column2},
	        #{param.column3},
	        #{param.column4},
	        #{param.column5},
	        #{param.remark},
	        #{param.creator},
	        #{param.createdName},
	        now(),
	        #{param.modifier},
	        #{param.modifyName},
	        now(),
	        #{param.isEnable},
	        uuid()
	    )
	    </foreach>
	</insert>

	<!-- 精品明细信息维护表 批量新增 -->
	<insert id="insertBoutiqueDetailImport">
		insert into t_sac_bu_boutique_detail(
		DETAIL_ID,
		BOUTIQUE_CODE,
		BOUTIQUE_NAME,
		DLR_ID,
		DLR_CODE,
		DLR_NAME,
		BOUTIQUE_PUTAWAY_NUM,
		BOUTIQUE_STATUS,
		REMARK,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		IS_ENABLE,
		UPDATE_CONTROL_ID
		)
		value
		<!--<foreach collection="dataList" item="param" index="index" separator=",">-->
			(
			#{param.detailId},
			#{param.boutiqueCode},
			#{param.boutiqueName},
			#{param.dlrId},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.boutiquePutawayNum},
			#{param.boutiqueStatus},
			#{param.remark},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			"1",
			uuid()
			)
<!--		</foreach>-->
	</insert>
	<insert id="insertDoutiqueDetail">
		INSERT INTO t_sac_bu_boutique_detail
		(
			DETAIL_ID,
			BOUTIQUE_CODE,
			BOUTIQUE_NAME,
			DLR_ID,
			DLR_CODE,
			DLR_NAME,
			BOUTIQUE_PUTAWAY_NUM,
			BOUTIQUE_CURRENT_NUM,
			BOUTIQUE_SOLD_OUT_NUM,
			BOUTIQUE_STATUS,
			REMARK,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
		)
		VALUES
			(
			 #{param.detailId},
			 #{param.boutiqueCode},
			 #{param.boutiqueName},
			 #{param.dlrId},
			 #{param.dlrCode},
			 #{param.dlrName},
			 #{param.boutiquePutawayNum},
			 #{param.boutiqueCurrentNum},
			 #{param.boutiqueSoldOutNum},
			 #{param.boutiqueStatus},
			 #{param.remark},
			 #{entity.userID},
			 #{entity.empName},
			 NOW(),
			 #{entity.userID},
			 #{entity.empName},
			 NOW(),
			 '1',
			 UUID()
			)
	</insert>
	<select id="selectByDlrCodeAndBoutiCode" resultType="java.util.Map">
		SELECT
			DETAIL_ID,
			BOUTIQUE_CODE,
			BOUTIQUE_NAME,
			DLR_ID,
			DLR_CODE,
			DLR_ID,
			BOUTIQUE_PUTAWAY_NUM,
			BOUTIQUE_CURRENT_NUM,
			BOUTIQUE_SOLD_OUT_NUM,
			BOUTIQUE_STATUS,
			UPDATE_CONTROL_ID
		FROM
			`t_sac_bu_boutique_detail`
		WHERE
			DLR_CODE = #{param.dlrCode}
		  AND BOUTIQUE_CODE = #{param.boutiqueCode}
	</select>
	<update id="updateByDatailId">
		UPDATE t_sac_bu_boutique_detail
		SET
			BOUTIQUE_PUTAWAY_NUM =#{param.boutiquePutawayNum},
			BOUTIQUE_STATUS=#{param.boutiqueStatus},
			MODIFIER=#{entity.userID},
			MODIFY_NAME=#{entity.empName},
			LAST_UPDATED_DATE=NOW(),
			UPDATE_CONTROL_ID=UUID()
		WHERE 1=1
		  AND DETAIL_ID =#{param.detailId}
		  AND UPDATE_CONTROL_ID =#{param.updateControlId}
	</update>
	<insert id="insertRecord">
		insert into t_sac_bu_boutique_detail_record
		(
			RECORD_ID,
			DETAIL_ID,
			RECORD_TYPE,
			`TYPE`,
			OPERATOR,
			NUM,
			REMARK,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
		)
		values (
				    UUID(),
					#{param.detailId},
					#{param.recordType},
					#{param.type},
					#{param.operator},
					#{param.num},
					#{param.remark},
					#{entity.userID},
					#{entity.empName},
					NOW(),
					#{entity.userID},
					#{entity.empName},
					NOW(),
					'1',
					UUID()
			   )
	</insert>
	<select id="querySacBuBoutiqueReceiving" resultType="java.util.Map">
				SELECT
				t.DLR_CODE,
				t.DLR_NAME,
				t.BOUTIQUE_CODE,
				t.BOUTIQUE_NAME,
				t.BOUTIQUE_PUTAWAY_NUM,
				'2' AS BOUTIQUE_STATUS,
				'已收货' AS BOUTIQUE_STATUS_NAME
				FROM
				t_sac_bu_boutique_receiving t
				WHERE
				1 = 1
		  <if test="param!=null">
			  <if test="param.dlrCode!=null and ''!=param.dlrCode">
			  	AND T.DLR_CODE=#{param.dlrCode}
			  </if>
			  <if test="param.boutiqueCode!=null and ''!=param.boutiqueCode">
				  AND INSTR(T.BOUTIQUE_CODE,#{param.boutiqueCode})
			  </if>
			  <if test="param.boutiqueName!=null and ''!=param.boutiqueName">
				  AND INSTR(T.BOUTIQUE_NAME,#{param.boutiqueName})
			  </if>
		  </if>
		ORDER BY T.LAST_UPDATED_DATE DESC
	</select>
	<select id="querySacBuBoutiqueStatement" resultType="java.util.Map">
		SELECT
			t4.RECORD_TYPE,
			case t4.RECORD_TYPE when '1' then '使用日志' when '0' then '上下架日志' end as RECORD_TYPE_CN,
			t4.TYPE,
			case t4.TYPE when '1' then '上架' when '0' then '下架' end as TYPE_CN,
			t4.OPERATOR,
			t4.NUM,
		    T4.REMARK,
			DATE_FORMAT(t4.CREATED_DATE,'%y-%m-%d %H:%i:%s') as USE_DATE ,
			T.DETAIL_ID,
			t.BOUTIQUE_CODE,
			t.BOUTIQUE_NAME,
			t.DLR_ID,
			t.DLR_CODE,
			d.DLR_SHORT_NAME DLR_NAME,
			t.BOUTIQUE_STATUS,
			-- t.REMARK,
			IFNULL( T.BOUTIQUE_PUTAWAY_NUM, '0' ) AS BOUTIQUE_PUTAWAY_NUM,
			IFNULL( T.BOUTIQUE_CURRENT_NUM, '0' ) AS BOUTIQUE_CURRENT_NUM,
			IFNULL( T.BOUTIQUE_SOLD_OUT_NUM, '0' ) AS BOUTIQUE_SOLD_OUT_NUM,
			t.UPDATE_CONTROL_ID,
			t3.AREA_ID,
			t3.AREA_NAME,
			t3.area_code AS AREA_CODE,
			C2.AGENT_NAME,
			C1.AGENT_COMPANY_NAME,
			h.PROVINCE_NAME,
			g.CITY_NAME
		FROM
			t_sac_bu_boutique_detail t
				LEFT JOIN t_sac_bu_boutique_detail_record t4 ON t.DETAIL_ID = t4.DETAIL_ID
				LEFT JOIN t_usc_mdm_org_dlr d ON T.DLR_ID = d.DLR_ID
				LEFT JOIN t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
				LEFT JOIN t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
				LEFT JOIN t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
				LEFT JOIN t_usc_mdm_org_province h ON d.PROVINCE_ID = h.PROVINCE_ID
				LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID
				AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
				LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID
				AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
				LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID
				AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/

		WHERE
			T.IS_ENABLE = '1'
		<if test="param.recordType!=null and ''!=param.recordType">
			AND t4.RECORD_TYPE = #{param.recordType}
		</if>
		<if test="param.type!=null and ''!=param.type">
			AND t4.TYPE = #{param.type}
		</if>
		<if test="param.operator!=null and ''!=param.operator">
			AND t4.OPERATOR = #{param.operator}
		</if>
		<if test="param.provinceId!=null and ''!=param.provinceId">
			AND d.PROVINCE_ID = #{param.provinceId}
		</if>
		<if test="param.cityId!=null and ''!=param.cityId">
			AND d.CITY_ID = #{param.cityId}
		</if>
		<if test="param.agentId!=null and ''!=param.agentId">
			AND C1.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.companyId!=null and ''!=param.companyId">
			AND d.COMPANY_ID = #{param.companyId}
		</if>
		<if test="param.boutiqueCode != null and param.boutiqueCode != ''">
			AND INSTR(T.BOUTIQUE_CODE ,#{param.boutiqueCode})
		</if>
		<if test="param.boutiqueName != null and param.boutiqueName != ''">
			AND INSTR(T.BOUTIQUE_NAME , #{param.boutiqueName})
		</if>
		<if test="param.boutiqueStatus != null and param.boutiqueStatus != ''">
			AND T.BOUTIQUE_STATUS = #{param.boutiqueStatus}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrCodeList != null and param.dlrCodeList != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCodeList.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrId != null and param.dlrId != ''">
			AND T.DLR_ID IN
			<foreach collection="param.dlrId.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrName != null and param.dlrName != ''">
			AND INSTR(T.DLR_NAME, #{param.dlrName}) <![CDATA[>]]> 0
		</if>
		<if test="param.areaCode != null and param.areaCode != ''">
			AND t3.AREA_CODE = #{param.areaCode}
		</if>
		<if test="param.areaId != null and param.areaId != ''">
			AND t3.AREA_ID = #{param.areaId}
		</if>
		<if test="param.areaName != null and param.areaName != ''">
			AND INSTR(t3.AREA_NAME, #{param.areaName}) <![CDATA[>]]> 0
		</if>
		ORDER BY T.LAST_UPDATED_DATE DESC
	</select>
</mapper>
