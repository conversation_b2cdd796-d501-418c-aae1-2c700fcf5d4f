<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacClueInfoDlrActiveMapper">

    <select id="getClueInfoWithOrderStates" resultType="com.ly.mp.csc.clue.entities.dto.ClueOrderInfoDTO">
        SELECT
            d.id,
            d.phone,
            d.status_code,
            d.review_id,
            d.column19 AS dccFlag,
            GROUP_CONCAT(c.SALE_ORDER_STATE SEPARATOR ',') AS orderStates
        FROM adp_leads.t_sac_clue_info_dlr d
                 LEFT JOIN orc.t_orc_ve_bu_sale_order_to_c c ON d.CUST_ID = c.BUY_CUST_ID
        WHERE d.id IN
            <foreach collection="clueIdList" item="clueId" open="(" separator="," close=")">
                #{clueId}
            </foreach>
        GROUP BY d.id
    </select>
</mapper>