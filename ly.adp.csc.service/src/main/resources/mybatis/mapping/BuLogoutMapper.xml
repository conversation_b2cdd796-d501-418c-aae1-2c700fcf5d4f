<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.BuLogoutMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.BuLogout">
        <id column="LOGOUT_ID" property="logoutId"/>
        <result column="TYPE" property="type"/>
        <result column="DLR_CODE" property="dlrCode"/>
        <result column="DLR_NAME" property="dlrName"/>
        <result column="AGENT_COMPANY_CODE" property="agentCompanyCode"/>
        <result column="AGENT_COMPANY_NAME" property="agentCompanyName"/>
        <result column="AGENT_CODE" property="agentCode"/>
        <result column="AGENT_NAME" property="agentName"/>
        <result column="LOGOUT_STATUS" property="logoutStatus"/>
        <result column="BPM_URL" property="bpmUrl"/>
        <result column="RO_URL" property="roUrl"/>
        <result column="CS_URL" property="csUrl"/>
        <result column="CUD_URL" property="cudUrl"/>
        <result column="SP_URL" property="spUrl"/>
        <result column="FINANCIAL_URL" property="financialUrl"/>
        <result column="COLUMN1" property="column1"/>
        <result column="COLUMN2" property="column2"/>
        <result column="COLUMN3" property="column3"/>
        <result column="COLUMN4" property="column4"/>
        <result column="COLUMN5" property="column5"/>
        <result column="REMARK" property="remark"/>
        <result column="CREATOR" property="creator"/>
        <result column="CREATED_NAME" property="createdName"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="MODIFIER" property="modifier"/>
        <result column="MODIFY_NAME" property="modifyName"/>
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate"/>
        <result column="IS_ENABLE" property="isEnable"/>
        <result column="UPDATE_CONTROL_ID" property="updateControlId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        LOGOUT_ID
        , `TYPE`, DLR_CODE, DLR_NAME, AGENT_COMPANY_CODE, AGENT_COMPANY_NAME, AGENT_CODE, AGENT_NAME, LOGOUT_STATUS,CACHET_URL, BPM_URL, RO_URL, CS_URL, CUD_URL, SP_URL, FINANCIAL_URL, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
    <select id="sacBuLogOut" resultType="java.util.Map">
        select  <include refid="Base_Column_List"></include> from t_sac_bu_logout  where 1=1 and LOGOUT_ID =#{param.logoutId}
    </select>
    <select id="sacBuLogOutQueryByDlr" resultType="java.util.Map">
        SELECT
        v2.LOOKUP_VALUE_NAME as logout_status_name,
        T5.AREA_NAME,
        a.AGENT_CODE,
        a.AGENT_NAME,
        c.AGENT_COMPANY_CODE,
        c.AGENT_COMPANY_NAME,
        T2.DLR_CODE,
        T2.DLR_SHORT_NAME as DLR_NAME,
        t2.ONLINE_FLAG,
        T.LOGOUT_ID,
        IFNULL( t.logout_status,'0') AS logout_status,
        t.UPDATE_CONTROL_ID,
        t.AUDIT_TIME,
        t.AUDIT_OPINION,
        t1.LOOKUP_VALUE_NAME as logout_status_name1,
        v1.LOOKUP_VALUE_NAME as online_flag_cn
        FROM
        t_usc_mdm_org_dlr t2
        LEFT JOIN t_usc_mdm_agent_company C ON t2.COMPANY_ID = C.AGENT_COMPANY_ID
        LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
        LEFT JOIN t_usc_mdm_agent_area t7 ON t2.DLR_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
        LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
        LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
        LEFT JOIN t_sac_bu_logout t ON t2.DLR_CODE = t.DLR_CODE
        LEFT JOIN t_prc_mds_lookup_value t1 on IFNULL(t.LOGOUT_STATUS,'0') =t1.LOOKUP_VALUE_CODE and t1.LOOKUP_TYPE_CODE='TW5001'
        LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),t2.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
        LEFT JOIN t_prc_mds_lookup_value v1 on  t2.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
        WHERE
        1 = 1
          AND  t2.DLR_CODE NOT LIKE 'HOST%'
        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND t2.DLR_CODE =#{param.dlrCode}
        </if>
        <if test="param.dlrName!=null and ''!=param.dlrName">
            AND INSTR(t2.DLR_SHORT_NAME ,#{param.dlrName})
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.agentId!=null and ''!=param.agentId">
            AND a.AGENT_ID = #{param.agentId}
        </if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus!='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS =#{param.logoutStatus}
        </if>
        <if test="param.logoutStatusIn != null and param.logoutStatusIn!=''">
            AND  ifnull(t.LOGOUT_STATUS,'0') IN
            <foreach item="item" collection=" param.logoutStatusIn.split(',')" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t2.DLR_CODE
        ORDER BY T.LAST_UPDATED_DATE DESC
    </select>
    <insert id="creatLogOut">
        INSERT INTO t_sac_bu_logout (LOGOUT_ID,
                TYPE,
                DLR_CODE,
                DLR_NAME,
                AGENT_COMPANY_CODE,
                AGENT_COMPANY_NAME,
                AGENT_CODE,
                AGENT_NAME,
                LOGOUT_STATUS,
                CACHET_URL,
                BPM_URL,
                RO_URL,
                CS_URL,
                CUD_URL,
                SP_URL,
                FINANCIAL_URL,
                CREATOR,
                CREATED_NAME,
                CREATED_DATE,
                MODIFIER,
                MODIFY_NAME,
                LAST_UPDATED_DATE,
                UPDATE_CONTROL_ID)
        VALUES (#{param.logoutId},
                #{param.type},
                #{param.dlrCode},
                #{param.dlrName},
                #{param.agentCompanyCode},
                #{param.agentCompanyName},
                #{param.agentCode},
                #{param.agenName},
                #{param.logoutStatus},
                #{param.cachetUrl},
                #{param.bpmUrl},
                #{param.roUrl},
                #{param.csUrl},
                #{param.cudUrl},
                #{param.spUrl},
                #{param.financialUrl},
                #{param.creator},
                #{param.createdName},
                #{param.createdDate},
                #{param.modifier},
                #{param.modifyName},
                #{param.lastUpdatedDate},
                #{param.updateControlId})
    </insert>
    <update id="updateBuLogOutById">
        UPDATE t_sac_bu_logout SET
        <if test="param.dlrCode!=null and ''!=param.dlrCode">DLR_CODE=#{param.dlrCode},</if>
        <if test="param.dlrName!=null and ''!=param.dlrName">DLR_NAME=#{param.dlrName},</if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">AGENT_COMPANY_CODE=#{param.agentCompanyCode},</if>
        <if test="param.agentCompanyName!=null and ''!=param.agentCompanyName">AGENT_COMPANY_NAME=#{param.agentCompanyName},</if>
        <if test="param.agentCode!=null and ''!=param.agentCode">AGENT_CODE =#{param.agentCode},</if>
        <if test="param.agentName!=null and ''!=param.agentName">AGENT_NAME=#{param.agentName},</if>
        <if test="param.logoutStatus!=null and ''!=param.logoutStatus">LOGOUT_STATUS=#{param.logoutStatus},</if>
        <if test="param.cachetUrl!=null and ''!=param.cachetUrl">CACHET_URL=#{param.cachetUrl},</if>
        <if test="param.bpmUrl!=null and ''!=param.bpmUrl">BPM_URL=#{param.bpmUrl},</if>
        <if test="param.roUrl!=null and ''!=param.roUrl">RO_URL=#{param.roUrl},</if>
        <if test="param.csUrl!=null and ''!=param.csUrl">CS_URL=#{param.csUrl},</if>
        <if test="param.cudUrl!=null and ''!=param.cudUrl">CUD_URL=#{param.cudUrl},</if>
        <if test="param.spUrl!=null and ''!=param.spUrl">SP_URL=#{param.spUrl},</if>
        <if test="param.financialUrl!=null and ''!=param.financialUrl">FINANCIAL_URL=#{param.financialUrl},</if>
        <if test="param.auditTime!=null ">AUDIT_TIME=#{param.auditTime},</if>
        <if test="param.auditOpinion!=null and ''!=param.auditOpinion">AUDIT_OPINION=#{param.auditOpinion},</if>
        <if test="param.isLiquidation!=null and ''!=param.isLiquidation">IS_LIQUIDATION=#{param.isLiquidation},</if>
        <if test="param.column1!=null and ''!=param.column1">COLUMN1=#{param.column1},</if>
        <if test="param.column2!=null and ''!=param.column2">COLUMN2=#{param.column2},</if>
        <if test="param.column3!=null and ''!=param.column3">COLUMN3=#{param.column3},</if>
        <if test="param.column4!=null and ''!=param.column4">COLUMN4=#{param.column4},</if>
        <if test="param.column5!=null and ''!=param.column5">COLUMN5=#{param.column5},</if>
        <if test="param.remark!=null and ''!=param.remark">REMARK=#{param.remark},</if>
        <if test="param.modifier!=null and ''!=param.modifier">MODIFIER=#{param.modifier},</if>
        <if test="param.modifyName!=null and ''!=param.dlrCode">MODIFY_NAME=#{param.modifyName},</if>
        LAST_UPDATED_DATE=now(),
        <if test="param.isEnable!=null and ''!=param.isEnable">IS_ENABLE=#{param.isEnable},</if>
        UPDATE_CONTROL_ID=UUID()
        WHERE LOGOUT_ID = #{param.logoutId}
        <if test="param.updateControlId !=null and ''!=param.updateControlId">
            AND UPDATE_CONTROL_ID=#{param.updateControlId}
        </if>
    </update>
    <select id="sacBuLogOutQueryByCompanySponsor" resultType="java.util.Map">
        SELECT
        v2.LOOKUP_VALUE_NAME as logout_status_name,
            T5.AREA_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            c.AGENT_COMPANY_CODE,
            c.AGENT_COMPANY_NAME,
            c.ONLINE_FLAG,
            v1.LOOKUP_VALUE_NAME AS  ONLINE_FLAG_CN,
            T.LOGOUT_ID,
            t.UPDATE_CONTROL_ID,
            t.AUDIT_TIME,
            t.AUDIT_OPINION,
            IFNULL( t.logout_status,'0') AS logout_status,
            t1.LOOKUP_VALUE_NAME as logout_status_name1
        FROM
            t_usc_mdm_agent_company C
                LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
                LEFT JOIN t_usc_mdm_agent_area t7 ON a.AGENT_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
                LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
                LEFT JOIN t_sac_bu_logout t ON c.AGENT_COMPANY_CODE = t.AGENT_COMPANY_CODE
                LEFT JOIN t_prc_mds_lookup_value t1 on IFNULL(t.LOGOUT_STATUS,'0') =t1.LOOKUP_VALUE_CODE and t1.LOOKUP_TYPE_CODE='TW5001'
                LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),c.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
                LEFT JOIN t_prc_mds_lookup_value v1 on  c.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
        WHERE
            1 = 1
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatusIn != null and param.logoutStatusIn!=''">
            AND ifnull(t.LOGOUT_STATUS,'0') IN
            <foreach item="item" collection=" param.logoutStatusIn.split(',')" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus !='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS = #{param.logoutStatus}
        </if>
        <if test="param.agentCompanyName !=null and ''!=param.agentCompanyName">
            AND C.AGENT_COMPANY_NAME=#{param.agentCompanyName}
        </if>
        <if test="param.agentCompanyCode !=null and ''!=param.agentCompanyCode">
            AND C.AGENT_COMPANY_Code=#{param.agentCompanyCode}
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
        AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        GROUP BY c.AGENT_COMPANY_CODE
        ORDER BY T.LAST_UPDATED_DATE DESC
    </select>
    <select id="sacBuLogOutQueryByAgentSponsor" resultType="java.util.Map">
        SELECT
        v2.LOOKUP_VALUE_NAME as logout_status_name,
            T5.AREA_NAME,
            t6.REL_OBJ_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            a.ONLINE_FLAG,
            v1.LOOKUP_VALUE_NAME AS  ONLINE_FLAG_CN,
            T.LOGOUT_ID,
            t.UPDATE_CONTROL_ID,
            t.AUDIT_TIME,
            t.AUDIT_OPINION,
            IFNULL( t.logout_status, '0' ) AS logout_status,
            t1.LOOKUP_VALUE_NAME as logout_status_name1
        FROM
            t_usc_mdm_agent_info a
                LEFT JOIN t_usc_mdm_agent_area t7 ON a.AGENT_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
                LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
                LEFT JOIN t_sac_bu_logout t ON a.AGENT_CODE = t.AGENT_CODE
                LEFT JOIN t_prc_mds_lookup_value t1 ON IFNULL( t.LOGOUT_STATUS, '0' ) = t1.LOOKUP_VALUE_CODE AND t1.LOOKUP_TYPE_CODE = 'TW5001'
                LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),a.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
                LEFT JOIN t_prc_mds_lookup_value v1 on  a.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
        WHERE
            1 = 1
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus!='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS = #{param.logoutStatus}
        </if>
        <if test="param.logoutStatusIn != null and param.logoutStatusIn!=''">
            AND ifnull(t.LOGOUT_STATUS,'0') IN
            <foreach item="item" collection=" param.logoutStatusIn.split(',')" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY a.AGENT_CODE
        ORDER BY T.LAST_UPDATED_DATE DESC
    </select>
    <select id="sacBuLogOutQuery" resultType="java.util.Map">
        SELECT
            t.LOGOUT_ID,
        <if test="param.type!=null and param.type =='3'.toString">
            a.AGENT_CODE,
            a.AGENT_NAME,
            GROUP_CONCAT(t5.AREA_NAME) as AREA_NAME,
        </if>
            <if test="param.type!=null and param.type =='2'.toString">
            t5.AREA_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            C.AGENT_COMPANY_CODE,
            c.AGENT_COMPANY_NAME,
            </if>
            <if test="param.type!=null and param.type =='1'.toString">
            t5.AREA_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            C.AGENT_COMPANY_CODE,
            c.AGENT_COMPANY_NAME,
            t.DLR_CODE,
            t2.DLR_SHORT_NAME as DLR_NAME,
            </if>
            t.BPM_URL,
            t.LOGOUT_STATUS,
            t1.LOOKUP_VALUE_NAME AS LOGOUT_STATUS_NAME1,
            v2.LOOKUP_VALUE_NAME as logout_status_name,
            v1.LOOKUP_VALUE_NAME as online_flag_name,
            t.type,
            t.CACHET_URL,
            t.UPDATE_CONTROL_ID,
            t.IS_LIQUIDATION,
            DATE_FORMAT(t.created_date,'%Y-%m-%d %H:%i:%s') as CREATED_DATE
        FROM
            t_sac_bu_logout t
                <if test="param.type!=null and param.type =='1'.toString">
                    LEFT JOIN t_usc_mdm_org_dlr t2 ON t2.DLR_CODE = t.DLR_CODE
                    LEFT JOIN t_usc_mdm_agent_company C ON t2.COMPANY_ID = C.AGENT_COMPANY_ID
                    LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
                    LEFT JOIN t_usc_mdm_agent_area t7 ON t2.DLR_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                    LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),t2.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
                    LEFT JOIN t_prc_mds_lookup_value v1 on  t2.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
                </if>
                <if test="param.type!=null and param.type =='2'.toString">
                    LEFT JOIN t_usc_mdm_agent_company C ON t.AGENT_COMPANY_CODE = C.AGENT_COMPANY_CODE
                    LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
                    LEFT JOIN t_usc_mdm_agent_area t7 ON C.AGENT_COMPANY_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                    LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),c.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
                    LEFT JOIN t_prc_mds_lookup_value v1 on  c.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
                </if>

                <if test="param.type!=null and param.type =='3'.toString">
                    LEFT JOIN t_usc_mdm_agent_info a ON t.AGENT_CODE = a.AGENT_CODE
                    LEFT JOIN t_usc_mdm_agent_area t7 ON a.AGENT_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                    LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),a.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
                    LEFT JOIN t_prc_mds_lookup_value v1 on  a.ONLINE_FLAG=v1.LOOKUP_VALUE_CODE and V1.LOOKUP_TYPE_CODE='E3S001'
                </if>
                LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
                LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
                LEFT JOIN t_prc_mds_lookup_value t1 ON IFNULL( t.LOGOUT_STATUS, '0' ) = t1.LOOKUP_VALUE_CODE AND t1.LOOKUP_TYPE_CODE = 'TW5001'
        WHERE
            1 =1
        <if test="param.type!=null and ''!=param.type">
            AND T.`TYPE` =#{param.type}
        </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND T.`LOGOUT_ID` =#{param.logoutId}
        </if>
        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND t.DLR_CODE =#{param.dlrCode}
        </if>
        <if test="param.dlrName!=null and ''!=param.dlrName">
            AND INSTR(t2.DLR_SHORT_NAME ,#{param.dlrName})
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus!='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS = #{param.logoutStatus}
        </if>
        <if test="param.logoutStatusIn != null and param.logoutStatusIn!=''">
            AND t.LOGOUT_STATUS In
            <foreach item="item" collection=" param.logoutStatusIn.split(',')" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.type!=null and param.type =='3'.toString">
            GROUP BY a.AGENT_CODE
        </if>
        <if test="param.type!=null and param.type =='2'.toString">
            GROUP BY c.AGENT_COMPANY_CODE
        </if>
        <if test="param.type!=null and param.type =='1'.toString">
            GROUP BY t2.DLR_CODE
        </if>
        ORDER BY T.LAST_UPDATED_DATE DESC
    </select>
    <insert id="creatLogOutDetail">
        insert into t_sac_bu_logout_detail(
            LOGOUT_DETAIL_ID,
            LOGOUT_ID,
            `TYPE`,
            DLR_CODE,
            DLR_NAME,
            AGENT_COMPANY_CODE,
            AGENT_COMPANY_NAME,
            AGENT_CODE,
            AGENT_NAME,
            RO_URL,
            CS_URL,
            CUD_URL,
            SP_URL,
            FINANCIAL_URL,
            COLUMN1,
            COLUMN2,
            COLUMN3,
            COLUMN4,
            COLUMN5,
            REMARK,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            UPDATE_CONTROL_ID
        )VALUES (
            #{param.logoutDetailId},
            #{param.logoutId},
            #{param.type},
            #{param.dlrCode},
            #{param.dlrName},
            #{param.agentCompanyCode},
            #{param.agentCompanyName},
            #{param.agentCode},
            #{param.agentName},
            #{param.roUrl},
            #{param.csUrl},
            #{param.cudUrl},
            #{param.spUrl},
            #{param.financialUrl},
            #{param.column1},
            #{param.column2},
            #{param.column3},
            #{param.column4},
            #{param.column5},
            #{param.remark},
            #{param.creator},
            #{param.createdName},
            now(),
            #{param.modifier},
            #{param.modifyName},
            now(),
            '1',
            uuid()
                        )
    </insert>
    <update id="updateBuLogOutDetail">
        update t_sac_bu_logout_detail set
        <if test="param.roUrl!=null and ''!=param.roUrl">RO_URL=#{param.roUrl},</if>
        <if test="param.bdUrl!=null and ''!=param.bdUrl">BD_URL=#{param.bdUrl},</if>
        <if test="param.csUrl!=null and ''!=param.csUrl">CS_URL=#{param.csUrl},</if>
        <if test="param.cudUrl!=null and ''!=param.cudUrl">CUD_URL=#{param.cudUrl},</if>
        <if test="param.spUrl!=null and ''!=param.spUrl">SP_URL=#{param.spUrl},</if>
        <if test="param.financialUrl!=null and ''!=param.financialUrl">FINANCIAL_URL=#{param.financialUrl},</if>
        <if test="param.roStatus!=null and ''!=param.roStatus">RO_STATUS=#{param.roStatus},</if>
        <if test="param.bdStatus!=null and ''!=param.bdStatus">BD_STATUS=#{param.bdStatus},</if>
        <if test="param.csStatus!=null and ''!=param.csStatus">CS_STATUS=#{param.csStatus},</if>
        <if test="param.cudStatus!=null and ''!=param.cudStatus">CUD_STATUS=#{param.cudStatus},</if>
        <if test="param.spStatus!=null and ''!=param.spStatus">SP_STATUS=#{param.spStatus},</if>
        <if test="param.financialStatus!=null and ''!=param.financialStatus">FINANCIAL_STATUS=#{param.financialStatus},</if>
        MODIFIER=#{param.modifier},
        MODIFY_NAME=#{param.modifyName},
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        WHERE 1=1
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND LOGOUT_DETAIL_ID=#{param.logoutDetailId}
        </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND  LOGOUT_ID=#{param.logoutId}
        </if>
        <if test="param.updateControlId!=null and ''!=param.updateControlId">
            AND UPDATE_CONTROL_ID=#{param.updateControlId}
        </if>
    </update>
    <select id="sacBuLogOutClearingQueryByDlr" resultType="java.util.Map">
        SELECT
            t.LOGOUT_ID,
            t5.AREA_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            C.AGENT_COMPANY_CODE,
            c.AGENT_COMPANY_NAME,
            d1.DLR_CODE,
            t2.DLR_SHORT_NAME as DLR_NAME,
            t.LOGOUT_STATUS,
            t.TYPE AS TYPE_IN,
            t.UPDATE_CONTROL_ID as LOGOUT_UPDATE_CONTROL_ID,
            t.IS_LIQUIDATION,
            t.BPM_URL,
            t1.LOOKUP_VALUE_NAME AS LOGOUT_STATUS_NAME,
            d1.type,
            d1.UPDATE_CONTROL_ID,
            d1.LOGOUT_DETAIL_ID,
            d1.BD_URL,
            d1.BD_STATUS,
            CASE d1.BD_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS BD_STATUS_NAME ,
            d1.RO_URL,
            d1.RO_STATUS,
            CASE d1.RO_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS RO_STATUS_NAME ,
            d1.CS_URL,
            d1.CS_STATUS,
            CASE d1.CS_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS CS_STATUS_NAME ,
            d1.CUD_URL,
            d1.CUD_STATUS,
            CASE d1.CUD_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS CUD_STATUS_NAME ,
            d1.SP_URL,
            d1.SP_STATUS,
            CASE d1.SP_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS SP_STATUS_NAME ,
            d1.FINANCIAL_URL,
            d1.FINANCIAL_STATUS,
            CASE d1.FINANCIAL_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS FINANCIAL_STATUS_NAME ,
            DATE_FORMAT(d1.created_date,'%Y-%m-%d %H:%i:%s') as CREATED_DATE
        FROM
            t_sac_bu_logout_detail d1
                LEFT JOIN t_usc_mdm_org_dlr t2 ON t2.DLR_CODE = d1.DLR_CODE
                LEFT JOIN t_sac_bu_logout t ON D1.LOGOUT_ID=t.LOGOUT_ID
                LEFT JOIN t_usc_mdm_agent_company C ON t2.COMPANY_ID = C.AGENT_COMPANY_ID
                LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
                LEFT JOIN t_usc_mdm_agent_area t7 ON t2.DLR_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
                LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
                LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
                LEFT JOIN t_prc_mds_lookup_value t1 ON IFNULL( t.LOGOUT_STATUS, '0' ) = t1.LOOKUP_VALUE_CODE AND t1.LOOKUP_TYPE_CODE = 'TW5001'
        WHERE
            1 =1

        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND d1.DLR_CODE =#{param.dlrCode}
        </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND d1.LOGOUT_ID =#{param.logoutId}
        </if>
        <if test="param.dlrName!=null and ''!=param.dlrName">
            AND INSTR(t2.DLR_SHORT_NAME ,#{param.dlrName})
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus!='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS = #{param.logoutStatus}
        </if>
        <if test="param.bdStatus!=null and ''!=param.bdStatus">
            AND d1.BD_STATUS = #{param.bdStatus}
        </if>
        <if test="param.roStatus!=null and ''!=param.roStatus">
            AND d1.RO_STATUS = #{param.roStatus}
        </if>
        <if test="param.csStatus!=null and ''!=param.csStatus">
            AND d1.CS_STATUS = #{param.csStatus}
        </if>
        <if test="param.cudStatus!=null and ''!=param.cudStatus">
            AND d1.CUD_STATUS = #{param.cudStatus}
        </if>
        <if test="param.spStatus!=null and ''!=param.spStatus">
            AND d1.SP_STATUS = #{param.spStatus}
        </if>
        <if test="param.financialStatus!=null and ''!=param.financialStatus">
            AND d1.FINANCIAL_STATUS = #{param.financialStatus}
        </if>
        ORDER BY d1.LAST_UPDATED_DATE DESC
    </select>
    <select id="selectLogoutDetailByStatus" resultType="java.util.Map">
        SELECT
            LOGOUT_ID
        FROM
            t_sac_bu_logout_detail
        WHERE
            RO_STATUS = '1'
          AND BD_STATUS='1'
          AND CS_STATUS = '1'
          AND CUD_STATUS = '1'
          AND SP_STATUS = '1'
          AND FINANCIAL_STATUS = '1'
          <if test="param.logoutDetailId!=null and ''!=param.logoutDetailId">
              AND LOGOUT_DETAIL_ID = #{param.logoutDetailId}
          </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND LOGOUT_ID = #{param.logoutId}
        </if>
          <if test="param.dlrCode=null and ''!=param.dlrCode">
              AND DLR_CODE IN
              <foreach item="item" collection=" param.dlrCode.split(',')" index="index" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>

    </select>
    <select id="selectLogoutByCompanyCode" resultType="java.util.Map">
        SELECT
            t.*
        FROM
            t_sac_bu_logout t
                LEFT JOIN t_usc_mdm_org_dlr d ON t.DLR_CODE = d.DLR_CODE
                LEFT JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
        <if test="param.agentCode != null and ''!=param.agentCode">
            LEFT JOIN t_usc_mdm_agent_info t2 ON c.AGENT_ID =t2.AGENT_ID
        </if>
        WHERE
            1 = 1
          AND t.LOGOUT_STATUS = '2'
        <if test="param.agentCompanyCode != null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.agentCode != null and ''!=param.agentCode">
            AND t2.AGENT_CODE = #{param.agentCode}
        </if>
    </select>
    <select id="selectCompanyAndAgent" resultType="java.util.Map">
        SELECT
            c.AGENT_COMPANY_CODE,c.AGENT_COMPANY_NAME,
        <if test="param.agentCode != null and ''!=param.agentCode">
            t1.AGENT_CODE ,t1.AGENT_NAME,
        </if>
        d.DLR_CODE,
        d.DLR_SHORT_NAME AS DLR_NAME
        FROM
            t_usc_mdm_org_dlr d
                LEFT JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
        <if test="param.agentCode != null and ''!=param.agentCode">
                LEFT JOIN t_usc_mdm_agent_info t1 ON c.AGENT_ID =t1.AGENT_ID
        </if>
        WHERE
            1 = 1
        <if test="param.agentCompanyCode != null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.agentCode != null and ''!=param.agentCode">
            AND t1.AGENT_CODE = #{param.agentCode}
        </if>
    </select>
    <select id="selectLogoutCompanyAndAgent" resultType="java.util.Map">
        SELECT
            t.*
        FROM
            t_sac_bu_logout_detail t
                LEFT JOIN t_usc_mdm_org_dlr d ON t.DLR_CODE = d.DLR_CODE
                LEFT JOIN t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
        <if test="param.agentCode != null and ''!=param.agentCode">
                LEFT JOIN t_usc_mdm_agent_info t1 ON c.AGENT_ID = t1.AGENT_ID
        </if>
        WHERE 1=1
        <if test="param.agentCompanyCode != null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.agentCode != null and ''!=param.agentCode">
            AND t1.AGENT_CODE = #{param.agentCode}
        </if>
    </select>
    <select id="selectLogoutDetailByDlrCode" resultType="java.util.Map">
        SELECT DLR_CODE,DLR_NAME FROM t_sac_bu_logout_detail
        WHERE 1=1
        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND DLR_CODE=#{param.dlrCode}
        </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND LOGOUT_ID=#{param.logoutId}
        </if>
    </select>
    <insert id="creatLogoutRecord">
        INSERT INTO t_sac_bu_logout_record (
            LOGOUT_ID,
            AGENT_CODE,
            AGENT_COMPANY_CODE,
            AGENT_COMPANY_NAME,
            AGENT_NAME,
            AUDIT_OPINION,
            AUDIT_STATUS,
            AUDIT_TIME,
            CREATED_DATE,
            CREATED_NAME,
            CREATOR,
            DLR_CODE,
            DLR_NAME,
            IS_ENABLE,
            LAST_UPDATED_DATE,
            LOGOUT_RECORD_ID,
            MODIFIER,
            MODIFY_NAME,
            REMARK,
            `TYPE`,
            UPDATE_CONTROL_ID
        ) VALUES (
                    #{param.logoutId},
                    #{param.agentCode},
                    #{param.agentCompanyCode},
                    #{param.agentCompanyName},
                    #{param.agentName},
                    #{param.auditOpinion},
                    #{param.auditStatus},
                    #{param.auditTime},
                    #{param.createdDate},
                    #{param.createdName},
                    #{param.creator},
                    #{param.dlrCode},
                    #{param.dlrName},
                    #{param.isEnable},
                    #{param.lastUpdatedDate},
                    #{param.logoutRecordId},
                    #{param.modifier},
                    #{param.modifyName},
                    #{param.remark},
                    #{param.type},
                    #{param.updateControlId}
                         )
    </insert>
    <select id="sacBuLogOutRecordQuery" resultType="java.util.Map">
        SELECT
            LOGOUT_ID,
            AGENT_CODE,
            AGENT_COMPANY_CODE,
            AGENT_COMPANY_NAME,
            AGENT_NAME,
            AUDIT_OPINION,
            CASE AUDIT_STATUS WHEN 'B' then '驳回' WHEN '1' THEN '通过' END as AUDIT_STATUS_NAME,
            AUDIT_TIME,
            CREATED_NAME,
            CREATOR,
            DLR_CODE,
            DLR_NAME,
            LOGOUT_RECORD_ID,
            TYPE
        FROM `t_sac_bu_logout_record`
            WHERE 1=1
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND LOGOUT_ID=#{param.logoutId}
        </if>
        <if test="param.type!=null and ''!=param.type">
            AND `TYPE`=#{param.type}
        </if>
        <if test="param.auditStatus!=null and ''!=param.auditStatus">
            AND AUDIT_STATUS=#{param.auditStatus}
        </if>
        <if test="param.userId!=null and ''!=param.userId">
            AND CREATOR=#{param.userId}
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND AGENT_CODE=#{param.agentCode}
        </if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">
            AND AGENT_COMPANY_CODE=#{param.agentCompanyCode}
        </if>
        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND DLR_CODE=#{param.dlrCode}
        </if>
        <if test="param.agentName!=null and ''!=param.agentName">
            AND INSTR(AGENT_NAME,#{param.agentName})
        </if>
        <if test="param.agentCompanyName!=null and ''!=param.agentCompanyName">
            AND INSTR(AGENT_COMPANY_NAME,#{param.agentCompanyName})
        </if>
        <if test="param.dlrName!=null and ''!=param.dlrName">
            AND INSTR(DLR_NAME,#{param.dlrName})
        </if>
        ORDER BY CREATED_DATE DESC
    </select>
    <select id="sacBuLogOutQueryByDlrLogout" resultType="java.util.Map">
        SELECT
            d1.DLR_CODE,
            t2.DLR_SHORT_NAME AS DLR_NAME,
            t.LOGOUT_STATUS,
            t2.ONLINE_FLAG,
            t.LOGOUT_ID,
            t5.AREA_NAME,
            a.AGENT_CODE,
            a.AGENT_NAME,
            C.AGENT_COMPANY_CODE,
            c.AGENT_COMPANY_NAME,
            t.LAST_UPDATED_DATE,
            t.TYPE AS TYPE_IN,
            t.IS_LIQUIDATION,
            t.CREATED_DATE,
            t1.LOOKUP_VALUE_NAME AS LOGOUT_STATUS_NAME1,
            v2.LOOKUP_VALUE_NAME AS LOGOUT_STATUS_NAME,
            d1.type,
            t.UPDATE_CONTROL_ID,
            d1.BD_URL,
            d1.BD_STATUS,
            CASE d1.BD_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS BD_STATUS_NAME ,
            d1.RO_URL,
            d1.RO_STATUS,
            CASE d1.RO_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS RO_STATUS_NAME ,
            d1.CS_URL,
            d1.CS_STATUS,
            CASE d1.CS_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS CS_STATUS_NAME ,
            d1.CUD_URL,
            d1.CUD_STATUS,
            CASE d1.CUD_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS CUD_STATUS_NAME ,
            d1.SP_URL,
            d1.SP_STATUS,
            CASE d1.SP_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS SP_STATUS_NAME ,
            d1.FINANCIAL_URL,
            d1.FINANCIAL_STATUS,
            CASE d1.FINANCIAL_STATUS WHEN '1' THEN '已上传' ELSE '未上传' END AS FINANCIAL_STATUS_NAME ,
            d1.LOGOUT_DETAIL_ID
        FROM
            t_sac_bu_logout_detail d1
                LEFT JOIN t_usc_mdm_org_dlr t2 ON t2.DLR_CODE = d1.DLR_CODE
                LEFT JOIN t_sac_bu_logout t ON D1.LOGOUT_ID = t.LOGOUT_ID
                LEFT JOIN t_usc_mdm_agent_company C ON t2.COMPANY_ID = C.AGENT_COMPANY_ID
                LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
                LEFT JOIN t_usc_mdm_agent_area t7 ON t2.DLR_ID = t7.AGENT_ID  AND t7.AREA_TYPE = '0'
                LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID  AND T6.REL_OBJ_TYPE = '1'
                LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID  AND T5.AREA_TYPE = '1'
                LEFT JOIN t_prc_mds_lookup_value t1 ON IFNULL( t.LOGOUT_STATUS, '0' ) = t1.LOOKUP_VALUE_CODE AND t1.LOOKUP_TYPE_CODE = 'TW5001'
                LEFT JOIN t_prc_mds_lookup_value v2 on concat(IFNULL(t.LOGOUT_STATUS,'0'),t2.ONLINE_FLAG) =v2.LOOKUP_VALUE_CODE and v2.LOOKUP_TYPE_CODE='TW5004'
        WHERE
            1 = 1
        <if test="param.dlrCode!=null and ''!=param.dlrCode">
            AND d1.DLR_CODE =#{param.dlrCode}
        </if>
        <if test="param.logoutId!=null and ''!=param.logoutId">
            AND d1.LOGOUT_ID =#{param.logoutId}
        </if>
        <if test="param.dlrName!=null and ''!=param.dlrName">
            AND INSTR(t2.DLR_SHORT_NAME ,#{param.dlrName})
        </if>
        <if test="param.agentCode!=null and ''!=param.agentCode">
            AND a.AGENT_CODE = #{param.agentCode}
        </if>
        <if test="param.agentCompanyCode!=null and ''!=param.agentCompanyCode">
            AND c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
        </if>
        <if test="param.areaCode!=null and ''!=param.areaCode">
            AND t5.area_code = #{param.areaCode}
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus =='0'.toString">
            AND (t.LOGOUT_STATUS is null or t.LOGOUT_STATUS ='0' )
        </if>
        <if test="param.logoutStatus != null and param.logoutStatus!='' and param.logoutStatus !='0'.toString">
            AND t.LOGOUT_STATUS = #{param.logoutStatus}
        </if>
        <if test="param.logoutStatusIn != null and param.logoutStatusIn!=''">
            AND  ifnull(t.LOGOUT_STATUS,'0') IN
            <foreach item="item" collection=" param.logoutStatusIn.split(',')" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t2.DLR_CODE
        ORDER BY d1.LAST_UPDATED_DATE DESC
    </select>

</mapper>
