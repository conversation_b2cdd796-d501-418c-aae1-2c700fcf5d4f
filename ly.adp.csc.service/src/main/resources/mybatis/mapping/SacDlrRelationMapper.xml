<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDlrRelationMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacDlrRelation">
  	<id column="RELATION_ID" property="relationId" />
    <result column="OLD_DLR_CODE" property="oldDlrCode" />
    <result column="OLD_DLR_NAME" property="oldDlrName" />
    <result column="NEW_DLR_CODE" property="newDlrCode" />
    <result column="NEW_DLR_NAME" property="newDlrName" />
    <result column="CHAN_CODE" property="chanCode" />
    <result column="CHAN_NAME" property="chanName" />
    <result column="WHERESTR" property="wherestr" />
    <result column="COLUMN1" property="column1" />
    <result column="COLUMN2" property="column2" />
    <result column="COLUMN3" property="column3" />
    <result column="COLUMN4" property="column4" />
    <result column="COLUMN5" property="column5" />
    <result column="COLUMN6" property="column6" />
    <result column="COLUMN7" property="column7" />
    <result column="COLUMN8" property="column8" />
    <result column="COLUMN9" property="column9" />
    <result column="COLUMN10" property="column10" />
    <result column="OEM_ID" property="oemId" />
    <result column="GROUP_ID" property="groupId" />
    <result column="OEM_CODE" property="oemCode" />
    <result column="GROUP_CODE" property="groupCode" />
    <result column="CREATOR" property="creator" />
    <result column="CREATED_NAME" property="createdName" />
    <result column="CREATED_DATE" property="createdDate" />
    <result column="MODIFIER" property="modifier" />
    <result column="MODIFY_NAME" property="modifyName" />
    <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
    <result column="IS_ENABLE" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    <result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
    <result column="INFO_CHAN_M_NAME" property="infoChanMName" />
    <result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
    <result column="INFO_CHAN_D_NAME" property="infoChanDName" />
    <result column="INFO_CHAN_DD_CODE" property="infoChanDDCode" />
    <result column="INFO_CHAN_DD_NAME" property="infoChanDDName" />
  </resultMap>

  <sql id="Base_Column_List">
   RELATION_ID, OLD_DLR_CODE, OLD_DLR_NAME, NEW_DLR_CODE, NEW_DLR_NAME, CHAN_CODE, 
   CHAN_NAME, WHERESTR, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7,
    COLUMN8, COLUMN9, COLUMN10, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, 
    CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID, INFO_CHAN_M_CODE, 
    INFO_CHAN_M_NAME, INFO_CHAN_D_CODE, INFO_CHAN_D_NAME, INFO_CHAN_DD_CODE, INFO_CHAN_DD_NAME
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-08-26-->
  <select id="selectByAll" resultType="java.util.Map">
      select
             RELATION_ID as relationId,
             OLD_DLR_CODE as oldDlrCode,
             OLD_DLR_NAME as oldDlrName,
             NEW_DLR_CODE as newDlrCode,
             NEW_DLR_NAME as newDlrName,
             CHAN_CODE as chanCode,
             CHAN_NAME as chanName,
             WHERESTR as wherestr,
             OEM_ID as oemId,
             GROUP_ID as groupId,
             OEM_CODE as oemCode,
             GROUP_CODE as groupCode,
             CREATOR as creator,
             CREATED_NAME as createdName,
             CREATED_DATE as createdDate,
             MODIFIER as modifier,
             MODIFY_NAME as modifyName,
             LAST_UPDATED_DATE as lastUpdatedDate,
             IS_ENABLE as isEnable,
             UPDATE_CONTROL_ID as updateControlId,
             INFO_CHAN_M_CODE as infoChanMCode,
             INFO_CHAN_M_NAME as infoChanMName,
             INFO_CHAN_D_CODE as infoChanDCode,
             INFO_CHAN_D_NAME as infoChanDName,
             INFO_CHAN_DD_CODE as infoChanDDCode,
             INFO_CHAN_DD_NAME as infoChanDDName
      from t_sac_dlr_relation
      <where>
          <if test="map.relationId != null and map.relationId != ''">
              and RELATION_ID = #{map.relationId}
          </if>
          <if test="map.oldDlrCode != null and map.oldDlrCode != ''">
              and OLD_DLR_CODE = #{map.oldDlrCode}
          </if>
          <if test="map.oldDlrName != null and map.oldDlrName != ''">
              and OLD_DLR_NAME like concat('%',#{map.oldDlrName},'%')
          </if>
          <if test="map.newDlrCode != null and map.newDlrCode != ''">
              and NEW_DLR_CODE = #{map.newDlrCode}
          </if>
          <if test="map.newDlrName != null and map.newDlrName != ''">
              and NEW_DLR_NAME like concat('%',#{map.newDlrName},'%')
          </if>
          <if test="map.chanCode != null and map.chanCode != ''">
              and CHAN_CODE = #{map.chanCode}
          </if>
          <if test="map.chanName != null and map.chanName != ''">
              and CHAN_NAME = #{map.chanName}
          </if>
          <if test="map.wherestr != null and map.wherestr != ''">
              and WHERESTR = #{map.wherestr}
          </if>
          <if test="map.oemId != null and map.oemId != ''">
              and OEM_ID = #{map.oemId}
          </if>
          <if test="map.groupId != null and map.groupId != ''">
              and GROUP_ID = #{map.groupId}
          </if>
          <if test="map.oemCode != null and map.oemCode != ''">
              and OEM_CODE = #{map.oemCode}
          </if>
          <if test="map.groupCode != null and map.groupCode != ''">
              and GROUP_CODE = #{map.groupCode}
          </if>
          <if test="map.creator != null and map.creator != ''">
              and CREATOR = #{map.creator}
          </if>
          <if test="map.createdName != null and map.createdName != ''">
              and CREATED_NAME = #{map.createdName}
          </if>
          <if test="map.modifier != null and map.modifier != ''">
              and MODIFIER = #{map.modifier}
          </if>
          <if test="map.modifyName != null and map.modifyName != ''">
              and MODIFY_NAME = #{map.modifyName}
          </if>
          <if test="map.isEnable != null and map.isEnable != ''">
              and IS_ENABLE = #{map.isEnable}
          </if>
          <if test="map.updateControlId != null and map.updateControlId != ''">
              and UPDATE_CONTROL_ID = #{map.updateControlId}
          </if>
          <if test="map.infoChanMCode != null and ''!= map.infoChanMCode ">
            AND INFO_CHAN_M_CODE = #{map.infoChanMCode}
          </if>
          <if test="map.infoChanDCode != null and ''!= map.infoChanDCode ">
              AND INFO_CHAN_D_CODE = #{map.infoChanDCode}
          </if>
          <if test="map.infoChanDDCode != null and ''!= map.infoChanDDCode ">
              AND INFO_CHAN_DD_CODE = #{map.infoChanDDCode}
          </if>
      </where>
      order by LAST_UPDATED_DATE desc
  </select>
  
  <select id="checkDlrRelationExists" resultType="int">
        select count(1)
        from t_sac_dlr_relation
        where RELATION_ID=#{relationId}
    </select>
  
<!--auto generated by MybatisCodeHelper on 2021-08-26-->
  <insert id="insertSacDlrRelation">
        INSERT INTO t_sac_dlr_relation(
            RELATION_ID,
            OLD_DLR_CODE,
            OLD_DLR_NAME,
            NEW_DLR_CODE,
            NEW_DLR_NAME,
            CHAN_CODE,
            CHAN_NAME,
            WHERESTR,
            OEM_ID,
            GROUP_ID,
            OEM_CODE,
            GROUP_CODE,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            UPDATE_CONTROL_ID,
            INFO_CHAN_M_CODE ,
            INFO_CHAN_M_NAME ,
            INFO_CHAN_D_CODE ,
            INFO_CHAN_D_NAME ,
            INFO_CHAN_DD_CODE,
            INFO_CHAN_DD_NAME
        )VALUES
        (
            #{map.relationId},
            #{map.oldDlrCode},
            #{map.oldDlrName},
            #{map.newDlrCode},
            #{map.newDlrName},
            #{map.chanCode},
            #{map.chanName},
            #{map.wherestr},
            #{map.oemId},
            #{map.groupId},
            #{map.oemCode},
            #{map.groupCode},
            #{map.creator},
            #{map.createdName},
            #{map.createdDate},
            #{map.modifier},
            #{map.modifyName},
            #{map.lastUpdatedDate},
            #{map.isEnable},
            #{map.updateControlId},
            #{map.infoChanMCode},
            #{map.infoChanMName},
            #{map.infoChanDCode},
            #{map.infoChanDName},
            #{map.infoChanDDCode},
            #{map.infoChanDDName}
        )
    </insert>

    <select id="checkSacDlrRelation" resultType="int">
        select count(1)
        from t_sac_dlr_relation s
        where s.OLD_DLR_CODE = #{param.oldDlrCode}
        and s.IS_ENABLE='1'
        <choose>
	      <when test="param.infoChanMCode !=null and param.infoChanMCode !=''"> AND s.INFO_CHAN_M_CODE=#{param.infoChanMCode}</when>
	      <otherwise>and (s.INFO_CHAN_M_CODE is null or s.INFO_CHAN_M_CODE='')</otherwise>
	    </choose>
	    <choose>
		  <when test="param.infoChanDCode !=null and param.infoChanDCode !=''"> AND s.INFO_CHAN_D_CODE=#{param.infoChanDCode}</when>
		  <otherwise>and (s.INFO_CHAN_D_CODE is null or s.INFO_CHAN_D_CODE='')</otherwise>
	    </choose>
	    <choose>
		  <when test="param.infoChanDDCode !=null and param.infoChanDDCode !=''"> AND s.INFO_CHAN_DD_CODE=#{param.infoChanDDCode}</when>
		  <otherwise>and (s.INFO_CHAN_DD_CODE is null or s.INFO_CHAN_DD_CODE='')</otherwise>
	    </choose>
	    <choose>
		  <when test="param.chanCode !=null and param.chanCode !=''"> AND s.CHAN_CODE = #{param.chanCode}</when>
		  <otherwise>and (s.CHAN_CODE is null or s.CHAN_CODE='')</otherwise>
	    </choose> 
        <if test="param.relationId != null and param.relationId != ''">
            AND s.RELATION_ID != #{param.relationId}
        </if>
    </select>
<!--auto generated by MybatisCodeHelper on 2021-08-26-->
  <update id="updateByRelationId">
    update t_sac_dlr_relation
    <set>
      <if test="updated.orgCode != null">
        INFO_CHAN_M_CODE = #{updated.infoChanMCode},
      </if>
      <if test="updated.orgName != null">
        INFO_CHAN_M_NAME = #{updated.infoChanMName},
      </if>
       <if test="updated.infoChanDCode != null">
        INFO_CHAN_D_CODE = #{updated.infoChanDCode},
      </if>
      <if test="updated.infoChanMCode != null">
        INFO_CHAN_M_CODE = #{updated.infoChanMCode},
      </if>
       <if test="updated.infoChanMName != null">
        INFO_CHAN_M_NAME = #{updated.infoChanMName},
      </if>
      <if test="updated.infoChanDName != null">
        INFO_CHAN_D_NAME = #{updated.infoChanDName},
      </if>
       <if test="updated.infoChanDDCode != null">
        INFO_CHAN_DD_CODE = #{updated.infoChanDDCode},
      </if>
      <if test="updated.infoChanDDName != null">
        INFO_CHAN_DD_NAME = #{updated.infoChanDDName},
      </if>
      <if test="updated.relationId != null and updated.relationId != ''">
        RELATION_ID = #{updated.relationId,jdbcType=VARCHAR},
      </if>
      <if test="updated.oldDlrCode != null and updated.oldDlrCode != ''">
        OLD_DLR_CODE = #{updated.oldDlrCode,jdbcType=VARCHAR},
      </if>
      <if test="updated.oldDlrName != null and updated.oldDlrName != ''">
        OLD_DLR_NAME = #{updated.oldDlrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.newDlrCode != null and updated.newDlrCode != ''">
        NEW_DLR_CODE = #{updated.newDlrCode,jdbcType=VARCHAR},
      </if>
      <if test="updated.newDlrName != null and updated.newDlrName != ''">
        NEW_DLR_NAME = #{updated.newDlrName,jdbcType=VARCHAR},
      </if>
      <if test="updated.chanCode != null">
        CHAN_CODE = #{updated.chanCode,jdbcType=VARCHAR},
      </if>
      <if test="updated.chanName != null">
        CHAN_NAME = #{updated.chanName,jdbcType=VARCHAR},
      </if>
      <if test="updated.wherestr != null">
        WHERESTR = #{updated.wherestr,jdbcType=VARCHAR},
      </if>
      <if test="updated.modifier != null and updated.modifier != ''">
        MODIFIER = #{updated.modifier,jdbcType=VARCHAR},
      </if>
      <if test="updated.modifyName != null and updated.modifyName != ''">
        MODIFY_NAME = #{updated.modifyName,jdbcType=VARCHAR},
      </if>
      <if test="updated.isEnable != null and updated.isEnable != ''">
        IS_ENABLE = #{updated.isEnable,jdbcType=VARCHAR},
      </if>
      <if test="updated.lastUpdatedDate != null">
        LAST_UPDATED_DATE = #{updated.lastUpdatedDate},
      </if>
       UPDATE_CONTROL_ID = #{updated.updateControlId}
    </set>
    where RELATION_ID=#{updated.relationId}
  </update>

   <select id="machDlrRelation" resultType="java.util.Map">
    WITH recursive classTb(channel_code) AS (
		SELECT CHANNEL_CODE FROM t_sac_channel_info WHERE CHANNEL_CODE=#{channelCode}
		UNION ALL
		SELECT s.PARENT_CHANNEL_CODE FROM t_sac_channel_info s,classTb WHERE s.CHANNEL_CODE=classTb.CHANNEL_CODE
	),
	classTb2 as(
		SELECT  s.CHANNEL_CODE,s.CHANNEL_LEVEL_CODE FROM classTb C 
		INNER JOIN t_sac_channel_info s ON c.CHANNEL_CODE=s.CHANNEL_CODE
		UNION ALL
		SELECT '全部' AS  CHANNEL_CODE,'-1' AS CHANNEL_LEVEL_CODE FROM DUAL
	)
      select
             m.RELATION_ID,
             m.OLD_DLR_CODE,  m.OLD_DLR_NAME,
             m.NEW_DLR_CODE, m.NEW_DLR_NAME,
             m.CHAN_CODE , m.CHAN_NAME,
             m.WHERESTR ,
             m.IS_ENABLE
      from t_sac_dlr_relation m
      inner JOIN classTb2 C ON c.CHANNEL_CODE=if(ifnull(m.CHAN_CODE,'')='','全部',m.CHAN_CODE)
      where m.IS_ENABLE = 1 and m.OLD_DLR_CODE=#{oldDlrCode}
	  ORDER BY C.CHANNEL_LEVEL_CODE DESC
   </select>

</mapper>