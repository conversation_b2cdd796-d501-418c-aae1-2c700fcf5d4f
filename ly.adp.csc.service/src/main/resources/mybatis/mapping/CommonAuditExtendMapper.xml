<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.adp.csc.idal.mapper.CommonAuditExtendMapper">

    <select id="queryCommonAudit" resultType="com.ly.adp.csc.entities.vo.CommonAuditVO">
        SELECT
        a.audit_id,
        a.node_code,
        a.node_name,
        a.update_control_id,
        a.apply_person_id,
        a.apply_person_name,
        t11.agent_code,
        act.dlr_region_name,
        act.column14 AS bhReason,
        t10.agent_company_code AS agentCompanyCode,
        t10.agent_company_name AS agentCompanyName,
        a.business_type_name,
        act.dlr_region_code,
        act.dlr_code,
        act.activity_type_code,
        act.activity_subtype_code,
        act.activity_subtype_name,
        act.begin_time,
        a.apply_time,
        act.end_time,
        a.bill_type,
        act.activity_name,
        t11.agent_name agentName,
        a.bill_code,
        a.bill_type_name,
        a.business_type,
        act.activity_purpose,
        act.dlr_address_detail,
        act.dlr_short_name,
        a.org_code,
        a.org_name,
        a.relation_bill_code,
        a.apply_desc,
        a.sh_person_id,
        a.sh_person_name,
        a.sh_desc,
        a.sh_time,
        a.sh_status,
        a.sh_status_name,
        act.activity_type_name,
        act.activity_cover_page_url,
        act.apply_end_time,
        act.number_of_person_supported,
        act.column5 AS applyIdentityLimit,
        act.column6 AS numOfApplicationOfApps,
        act.column7 AS numOfApplicationOffline,
        act.column15 AS hxBhReason,
        act.publish_time,
        act.activity_id
        from
        t_sac_common_audit a
        left join t_acc_bu_activity act on act.activity_id = a.bill_code
        left join mp.t_usc_mdm_org_dlr t2 on act.dlr_code = t2.dlr_code
        left join mp.t_usc_mdm_agent_company t10 on t2.company_id =t10.agent_company_id
        left join mp.t_usc_mdm_agent_info t11 on t10.agent_id = t11.agent_id
        where
        1 = 1
        AND a.is_enable = '1'
        AND a.bill_type = #{param.billType}
        <if test="param.orgCode != null and ''!= param.orgCode ">
            and a.org_code in
            <foreach collection="param.orgCode.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.activityName != null and ''!= param.activityName ">
            and act.activity_name like concat('%', #{param.activityName}, '%')
        </if>
        <if test="param.beginTimeMin != null and ''!= param.beginTimeMin ">
            and act.begin_time &gt;= #{param.beginTimeMin}
        </if>
        <if test="param.beginTimeMax != null and ''!= param.beginTimeMax ">
            and act.begin_time &lt;= #{param.beginTimeMax}
        </if>
        <if test="param.dlrCodeIn != null and ''!= param.dlrCodeIn ">
            and act.dlr_code in
            <foreach collection="param.dlrCodeIn.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.inCreateTypeCode != null and ''!= param.inCreateTypeCode ">
            and act.create_type_code in
            <foreach collection="param.inCreateTypeCode.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.areaId != null and ''!= param.areaId ">
            and act.dlr_code in (select dlr.dlr_code from mp.t_usc_mdm_org_dlr dlr
            left join mp.t_usc_mdm_agent_area t7 on dlr.dlr_id =t7.agent_id and t7.area_type='0'
            left join mp.t_usc_area_relation t6 on t7.area_id =t6.rel_obj_id and t6.rel_obj_type = '1'
            left join mp.t_usc_area_info t5 on t6.area_id=t5.area_id and t5.area_type='1'
            where t5.area_id = #{param.areaId})
        </if>
        <if test="param.activitySubtypeCode != null and ''!= param.activitySubtypeCode ">
            and act.activity_subtype_code = #{param.activitySubtypeCode}
        </if>
        <if test="param.activityTypeCode != null and ''!= param.activityTypeCode ">
            and act.activity_type_code = #{param.activityTypeCode}
        </if>
        <if test="param.agentId != null and ''!= param.agentId ">
            and t11.agent_id = #{param.agentId}
        </if>
        <if test="param.companyId != null and ''!= param.companyId ">
            and t10.agent_company_id = #{param.companyId}
        </if>
        <if test="param.nodeCode != null and ''!= param.nodeCode ">
            and a.node_code = #{param.nodeCode}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            and a.business_type = #{param.businessType}
        </if>
        <if test="param.shStatus != null and ''!= param.shStatus ">
            and a.sh_status in
            <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.sh_time desc,a.apply_time desc
    </select>
</mapper>