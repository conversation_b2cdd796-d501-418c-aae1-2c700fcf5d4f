<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnetaskAuditRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnetaskAuditRecord">
        <id column="AUDIT_ID" property="auditId" />
        <result column="TASK_ID" property="taskId" />
        <result column="AUDITOR_ID" property="auditorId" />
        <result column="AUDITOR_NAME" property="auditorName" />
        <result column="AUDIT_TIME" property="auditTime" />
        <result column="IS_AUTOMATIC" property="isAutomatic" />
        <result column="AUDIT_STATUS_CODE" property="auditStatusCode" />
        <result column="AUDIT_STATUS_NAME" property="auditStatusName" />
        <result column="REMARK" property="remark" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        AUDIT_ID, TASK_ID, AUDITOR_ID, AUDITOR_NAME, AUDIT_TIME, IS_AUTOMATIC, AUDIT_STATUS_CODE, AUDIT_STATUS_NAME, REMARK, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.auditId !=null and param.auditId !=''">and AUDIT_ID=#{param.auditId}</if>
    	<if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
    	<if test="param.auditorId !=null and param.auditorId !=''">and AUDITOR_ID=#{param.auditorId}</if>
    	<if test="param.auditorName !=null and param.auditorName !=''">and AUDITOR_NAME=#{param.auditorName}</if>
    	<if test="param.auditTime !=null and param.auditTime !=''">and AUDIT_TIME=#{param.auditTime}</if>
    	<if test="param.isAutomatic !=null and param.isAutomatic !=''">and IS_AUTOMATIC=#{param.isAutomatic}</if>
    	<if test="param.auditStatusCode !=null and param.auditStatusCode !=''">and AUDIT_STATUS_CODE=#{param.auditStatusCode}</if>
    	<if test="param.auditStatusName !=null and param.auditStatusName !=''">and AUDIT_STATUS_NAME=#{param.auditStatusName}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.extendJson !=null and param.extendJson !=''">and EXTEND_JSON=#{param.extendJson}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
    	<!-- 模糊查询 -->
    	<if test="param.auditorNamePaste !=null and param.auditorNamePaste !=''">and INSTR(AUDITOR_NAME,#{param.auditorNamePaste})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.auditTimeStart !=null and param.auditTimeStart !=''">and AUDIT_TIME>=#{param.auditTimeStart}</if>
	    <if test="param.auditTimeEnd !=null and param.auditTimeEnd !=''"><![CDATA[and AUDIT_TIME<=#{param.auditTimeEnd}]]></if>
   	</sql>
 	
 	<!-- 任务审核表 信息查询 -->
	<select id="querySacOnetaskAuditRecord" resultType="map">
		SELECT
		info.TASK_TITLE,
		info.TASK_DESCRIBE,
		info.TASK_TYPE_CODE,
		info.TASK_TYPE_NAME,
		info.TASK_STATE_CODE,
		info.TASK_STATE_NAME,
		info.TASK_ATTESTATION_IS,
		CASE WHEN info.TASK_REPEAT_IS = '0' THEN '否'  WHEN info.TASK_REPEAT_IS = '1' THEN '是'  END taskRepeatIsName,
		info.TASK_REPEAT_IS,
		info.BUSS_TIME,
		info.BUSS_START_TIME,
		info.BUSS_END_TIME,
		info.EXTEND_JSON AS extendJsonInfo,
		info.CREATED_NAME AS createdNameInfo,
		record.AUDIT_ID,
		record.TASK_ID,
		record.AUDITOR_ID,
		record.AUDITOR_NAME,
		record.AUDIT_TIME,
		record.IS_AUTOMATIC,
		record.AUDIT_STATUS_CODE,
		record.AUDIT_STATUS_NAME,
		record.REMARK,
		record.EXTEND_JSON,
		record.COLUMN1,
		record.COLUMN2,
		record.COLUMN3,
		record.COLUMN4,
		record.COLUMN5,
		record._MYCAT_OP_TIME,
		record.OEM_ID,
		record.GROUP_ID,
		record.OEM_CODE,
		record.GROUP_CODE,
		record.CREATOR,
		record.CREATED_NAME,
		record.CREATED_DATE,
		record.MODIFIER,
		record.MODIFY_NAME,
		record.LAST_UPDATED_DATE,
		record.IS_ENABLE,
		record.SDP_USER_ID,
		record.SDP_ORG_ID,
		record.UPDATE_CONTROL_ID 
	FROM
		t_sac_onetask_info info 
		LEFT JOIN t_sac_onetask_audit_record record ON info.task_id = record.task_id 
	WHERE
		1 =1
		<!-- 任务表 -->
    	<if test="param.taskTitle !=null and param.taskTitle !=''">and info.TASK_TITLE=#{param.taskTitle}</if>
    	<if test="param.taskDescribe !=null and param.taskDescribe !=''">and info.TASK_DESCRIBE=#{param.taskDescribe}</if>
    	<if test="param.taskTypeCode !=null and param.taskTypeCode !=''">and info.TASK_TYPE_CODE=#{param.taskTypeCode}</if>
    	<if test="param.taskTypeName !=null and param.taskTypeName !=''">and info.TASK_TYPE_NAME=#{param.taskTypeName}</if>
    	<if test="param.taskStateCode !=null and param.taskStateCode !=''">and info.TASK_STATE_CODE=#{param.taskStateCode}</if>
    	<if test="param.taskStateName !=null and param.taskStateName !=''">and info.TASK_STATE_NAME=#{param.taskStateName}</if>
    	<if test="param.taskAttestationIs !=null and param.taskAttestationIs !=''">and info.TASK_ATTESTATION_IS=#{param.taskAttestationIs}</if>
    	<if test="param.taskRepeatIs !=null and param.taskRepeatIs !=''">and info.TASK_REPEAT_IS=#{param.taskRepeatIs}</if>
    	<!-- 模糊查询 -->
    	<if test="param.taskTitlePaste !=null and param.taskTitlePaste !=''">and INSTR(info.TASK_TITLE,#{param.taskTitlePaste})>0</if>
    	<if test="param.createdNameInfoPaste !=null and param.createdNameInfoPaste !=''">and INSTR(info.CREATED_NAME,#{param.createdNameInfoPaste})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.bussTimeStart !=null and param.bussTimeStart !=''">and info.BUSS_TIME>=#{param.bussTimeStart}</if>
	    <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''"><![CDATA[and info.BUSS_TIME<=#{param.bussTimeEnd}]]></if>
		<!-- 审核表 -->
	    <if test="param.auditId !=null and param.auditId !=''">and record.AUDIT_ID=#{param.auditId}</if>
    	<if test="param.taskId !=null and param.taskId !=''">and record.TASK_ID=#{param.taskId}</if>
    	<if test="param.auditorId !=null and param.auditorId !=''">and record.AUDITOR_ID=#{param.auditorId}</if>
    	<if test="param.auditorName !=null and param.auditorName !=''">and record.AUDITOR_NAME=#{param.auditorName}</if>
    	<if test="param.isAutomatic !=null and param.isAutomatic !=''">and record.IS_AUTOMATIC=#{param.isAutomatic}</if>
    	<if test="param.auditStatusCode !=null and param.auditStatusCode !=''">and record.AUDIT_STATUS_CODE=#{param.auditStatusCode}</if>
    	<if test="param.auditStatusName !=null and param.auditStatusName !=''">and record.AUDIT_STATUS_NAME=#{param.auditStatusName}</if>
    	<!-- 模糊查询 -->
    	<if test="param.auditorNamePaste !=null and param.auditorNamePaste !=''">and INSTR(record.AUDITOR_NAME,#{param.auditorNamePaste})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.auditTimeStart !=null and param.auditTimeStart !=''">and record.AUDIT_TIME>=#{param.auditTimeStart}</if>
	    <if test="param.auditTimeEnd !=null and param.auditTimeEnd !=''"><![CDATA[and record.AUDIT_TIME<=#{param.auditTimeEnd}]]></if>
	</select>
	
	<!-- 任务审核表 信息删除（物理删除） -->
	<delete id="deleteSacOnetaskAuditRecord">
		DELETE 
		FROM
			t_sac_onetask_audit_record
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 任务审核表 信息新增 -->
	<insert id="createSacOnetaskAuditRecord">
		insert into t_sac_onetask_audit_record(<include refid="Base_Column_List"></include>)
		value(
        	#{param.auditId},
			#{param.taskId},
			#{param.auditorId},
			#{param.auditorName},
			#{param.auditTime},
			#{param.isAutomatic},
			#{param.auditStatusCode},
			#{param.auditStatusName},
			#{param.remark},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 任务审核表 信息更新 -->
	<update id="updateSacOnetaskAuditRecord">
		update t_sac_onetask_audit_record  set 
			<!-- 更新列表 -->
			<if test="param.taskId !=null and param.taskId !=''">TASK_ID=#{param.taskId},</if>
			<if test="param.auditorId !=null and param.auditorId !=''">AUDITOR_ID=#{param.auditorId},</if>
			<if test="param.auditorName !=null and param.auditorName !=''">AUDITOR_NAME=#{param.auditorName},</if>
			<if test="param.auditTime !=null and param.auditTime !=''">AUDIT_TIME=#{param.auditTime},</if>
			<if test="param.isAutomatic !=null and param.isAutomatic !=''">IS_AUTOMATIC=#{param.isAutomatic},</if>
			<if test="param.auditStatusCode !=null and param.auditStatusCode !=''">AUDIT_STATUS_CODE=#{param.auditStatusCode},</if>
			<if test="param.auditStatusName !=null and param.auditStatusName !=''">AUDIT_STATUS_NAME=#{param.auditStatusName},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.auditId !=null and param.auditId !=''">and AUDIT_ID=#{param.auditId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
</mapper>
