<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewDlrClueMapper">
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewQuery">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="replace_order_num" property="replaceOrderNum" jdbcType="VARCHAR"/>
        <result column="bought_car_order_num" property="boughtCarOrderNum" jdbcType="VARCHAR"/>
        <result column="create_time" property="orderCreateTime" jdbcType="TIMESTAMP"/>
        <result column="smart_id" property="smartID" jdbcType="VARCHAR"/>
        <result column="agency_code" property="agencyCode" jdbcType="VARCHAR"/>
        <result column="earnest_time" property="earnestTime" jdbcType="VARCHAR"/>
        <result column="evaluate_price_min" property="evaluatePriceMin" jdbcType="DECIMAL"/>
        <result column="evaluate_price_max" property="evaluatePriceMax" jdbcType="DECIMAL"/>
        <result column="service_start_time" property="serviceStartTime" jdbcType="TIMESTAMP"/>
        <result column="service_end_time" property="serviceEndTime" jdbcType="TIMESTAMP"/>
        <result column="order_status" property="orderStatus" jdbcType="INTEGER"/>
        <result column="vin" property="vin" jdbcType="VARCHAR"/>
        <result column="mileage" property="mileage" jdbcType="INTEGER"/>
        <result column="license_time" property="licenseTime" jdbcType="VARCHAR"/>
        <result column="car_licence" property="carLicence" jdbcType="VARCHAR"/>
        <result column="license_city" property="licenseCity" jdbcType="VARCHAR"/>
        <result column="car_brand" property="carBrand" jdbcType="VARCHAR"/>
        <result column="car_type" property="carType" jdbcType="VARCHAR"/>
        <result column="car_version" property="carVersion" jdbcType="VARCHAR"/>
        <result column="car_date" property="carDate" jdbcType="VARCHAR"/>
        <result column="contacts_name" property="contactsName" jdbcType="VARCHAR"/>
        <result column="contacts_phone" property="contactsPhone" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="customer_phone" property="customerPhone" jdbcType="VARCHAR"/>
        <result column="province_code" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="city_code" property="cityCode" jdbcType="VARCHAR"/>
        <result column="distinct_code" property="distinctCode" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="address_lat" property="addressLat" jdbcType="VARCHAR"/>
        <result column="address_log" property="addressLog" jdbcType="VARCHAR"/>
        <result column="close_reason" property="closeReason" jdbcType="VARCHAR"/>
        <result column="close_time" property="closeTime" jdbcType="TIMESTAMP"/>
        <result column="close_operator" property="closeOperator" jdbcType="VARCHAR"/>
        <result column="store_detection_code" property="storeDetectionCode" jdbcType="VARCHAR"/>
        <result column="detection_time" property="detectionTime" jdbcType="TIMESTAMP"/>
        <result column="customer_accept_price_time" property="customerAcceptPriceTime" jdbcType="TIMESTAMP"/>
        <result column="replace_relation" property="replaceRelation" jdbcType="VARCHAR"/>
        <result column="recommend_channel" property="recommendChannel" jdbcType="VARCHAR"/>
        <result column="service_type" property="serviceType" jdbcType="INTEGER"/>
        <result column="sale_provider_code" property="saleProviderCode" jdbcType="VARCHAR"/>
        <result column="short_address" property="shortAddress" jdbcType="VARCHAR"/>
        <result column="platform_source" property="platformSource" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="product_name" property="empName" jdbcType="VARCHAR"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="VARCHAR"/>
        <result column="agent_company_name" property="agentCompanyName" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="queryListMeReviewInfo" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
        r.INFO_CHAN_M_NAME,
        r.INFO_CHAN_D_CODE,
        r.INFO_CHAN_D_NAME,
        r.INFO_CHAN_DD_CODE,
        r.INFO_CHAN_DD_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        T.CREATED_DATE as createdDate,
        T.COLUMN20,
        i.attr83,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate,
        T.GENDER_CODE,
        T.GENDER_NAME,
        ifnull(r.COLUMN15,'定期跟进') followReason,
        case when r.send_time is not null then '是' else '否' end as isPvSend,
        case when ifnull(r.OVER_REVIEW_TIME,now()) &lt; now() then '1' else '0' end as isOverTime,
        e.EMP_CODE reviewPersonCode,
        T.CUS_SOURCE,
        <![CDATA[
        CASE WHEN r.OVER_REVIEW_TIME<=NOW() THEN 
		CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,r.OVER_REVIEW_TIME,NOW())) ,'小时'),
		CONCAT(cast(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()))-
		(floor(TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) / 1440) *1440)-
		(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) % 1440)/60)*60)) as char),'分钟' ))	 ELSE '' end beyondTimes]]>
        ,
        <![CDATA[
		case when TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) < 0 then 0
		else TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) end beyondDay]]>
        from t_sac_review r
        INNER JOIN csc.t_sac_clue_info_dlr T ON R.BILL_CODE = T.SERVER_ORDER
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=T.CUST_ID
        LEFT JOIN mp.t_usc_mdm_org_employee e on e.USER_ID=r.REVIEW_PERSON_ID
        <if test="param.saleOrderState !=null and param.saleOrderState != ''">
            LEFT JOIN T_ORC_VE_BU_SALE_ORDER_TO_C C ON r.CUST_ID = C.BUY_CUST_ID
            LEFT JOIN t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND
            der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
            LEFT join t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
            concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
            C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
        </if>
        where r.REVIEW_PERSON_ID=#{param.userId}
        and r.REVIEW_STATUS in('0','1','3')
        and r.COLUMN19 IS NULL
        <if test="param.genderCode != null and ''!= param.genderCode">
            AND T.GENDER_CODE = #{param.genderCode}
        </if>
        <if test="param.attr83 != null and ''!= param.attr83 ">
            <![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
        </if>
        <if test="param.saleOrderState !=null and param.saleOrderState != ''">
            AND VE8080.LOOKUP_VALUE_CODE IN
            <foreach item="item" index="index" open="(" separator="," close=")"
                     collection="param.saleOrderState.split(',')">
                #{item}
            </foreach>
        </if>
        <if test="param.genderName != null and ''!= param.genderName">
            AND INSTR(T.GENDER_NAME, #{param.genderName}) <![CDATA[ > ]]> 0
        </if>
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 = #{param.businessHeatCode}
        </if>
        <if test="param.beyondDay != null and ''!= param.beyondDay">
            <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
        </if>
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
            <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
        </if>
        <if test="param.assignEndTime != null and ''!= param.assignEndTime">
            <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
            <![CDATA[ AND r.OVER_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
            <![CDATA[ AND r.OVER_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
            AND r.ASSIGN_STATUS = #{param.assignStatus1}
            AND r.LAST_REVIEW_TIME is not null
            AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
        </if>
        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
            <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
        </if>
        <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
            <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
        </if>

        <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
            <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
        </if>
        <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
            <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
        </if>

        <if test="param.sendStartTime != null and ''!= param.sendStartTime">
            <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
        </if>
        <if test="param.sendEndTime != null and ''!= param.sendEndTime">
            <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.createdStartTime != null and ''!= param.createdStartTime">
            <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
        </if>
        <if test="param.createdEndTime != null and ''!= param.createdEndTime">
            <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
        </if>

        <if test="param.billType != null and ''!= param.billType ">
            AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
            AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.channelName != null and ''!= param.channelName ">
            AND r.CHANNEL_NAME in
            <foreach item="item" index="index" open="(" separator="," close=")"
                     collection="param.channelName.split(',')">
                #{item}
            </foreach>
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.orgCode != null and ''!= param.orgCode ">
            AND r.ORG_CODE = #{param.orgCode}
        </if>
        <if test="param.sendDlrCode != null and ''!= param.sendDlrCode ">
            AND r.send_dlr_code = #{param.sendDlrCode}
        </if>
        <if test="param.isCome != null and ''!= param.isCome ">
            AND r.is_come = #{param.isCome}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
            AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
            AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
            AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test='param.isPvSend != null and "1"== param.isPvSend'>
            AND r.SEND_TIME IS NOT NULL
        </if>
        <if test='param.isPvSend != null and "0"== param.isPvSend'>
            AND ifnull(r.SEND_TIME,'')=''
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
            AND r.REVIEW_STATUS = #{param.reviewStatus}
        </if>
        <if test='param.isOverTime != null and "1"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
        </if>
        <if test='param.isOverTime != null and "0"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
        </if>
        <if test="param.BeginTime != null and ''!= param.BeginTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
        </if>
        <if test="param.EndTime != null and ''!= param.EndTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
        </if>
        <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
            AND T.FIRST_REVIEW_TIME is null
        </if>
        <if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
            AND r.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
        </if>
        <if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
            AND r.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
        </if>
        <if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
            <![CDATA[	AND instr(r.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
        </if>
        <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">
            and T.phone in (SELECT sheet.CUSTOMER_PHONE
            from  csc.t_sac_test_drive_sheet sheet
            WHERE  sheet.TEST_TYPE in ('1','2')
            and sheet.TEST_STATUS='2')
        </if>
        <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">
            and T.phone not in (SELECT sheet.CUSTOMER_PHONE
            from  csc.t_sac_test_drive_sheet sheet
            WHERE  sheet.TEST_TYPE in ('1','2')
            and sheet.TEST_STATUS='2')
        </if>
        ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
    </select>

    <select id="queryListByDlr" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
        r.INFO_CHAN_M_NAME,
        r.INFO_CHAN_D_CODE,
        r.INFO_CHAN_D_NAME,
        r.INFO_CHAN_DD_CODE,
        r.INFO_CHAN_DD_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        e.emp_code as reviewPersonCode,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        <!--r.CREATED_DATE as createdDate,-->
        c1.CREATED_DATE as createdDate,
        c1.COLUMN20,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate,
        r.MANAGE_LABEL_CODE,
        r.MANAGE_LABEL_NAME,
        r.COLUMN14,
        i.attr83,
        c1.CUS_SOURCE,
        ifnull(r.COLUMN15,'定期跟进') followReason,
        case when r.send_time is not null then '是' else '否' end as isPvSend,
        case when ifnull(r.OVER_REVIEW_TIME,now()) &lt; now() then '1' else '0' end as isOverTime,
        <![CDATA[
        CASE WHEN r.OVER_REVIEW_TIME<=NOW() THEN 
		CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,r.OVER_REVIEW_TIME,NOW())) ,'小时'),
		CONCAT(cast(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()))-
		(floor(TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) / 1440) *1440)-
		(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) % 1440)/60)*60)) as char),'分钟' ))	 ELSE '' end beyondTimes]]>
        ,
        <![CDATA[
		case when TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) < 0 then 0
		else TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) end beyondDay]]>
        from csc.t_sac_review r
        INNER JOIN csc.t_sac_clue_info_dlr c1 on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN csc.t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
        LEFT JOIN mp.t_usc_mdm_org_employee e on e.USER_ID= r.REVIEW_PERSON_ID
        <if test="param.saleOrderState !=null and param.saleOrderState != ''">
            LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON r.CUST_ID = C.BUY_CUST_ID
            LEFT JOIN orc.t_orc_ve_bu_ec_return_order der ON C.SALE_ORDER_CODE = der.RETAIL_NO AND der.REFUND_TYPE = '2' AND
            der.REFUND_STATUS = '1' and der.IS_ENABLE = '1'
            LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
            concat(C.SALE_ORDER_STATE,IFNULL(der.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
            C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
        </if>
        WHERE 
        r.REVIEW_STATUS in('0','1','3')
        <if test="param.statusCodes !=null and param.statusCodes != ''">
            AND c1.STATUS_CODE = #{param.statusCodes}
        </if>
        <if test="param.attr83 != null and ''!= param.attr83 ">
            <![CDATA[	AND instr(i.attr83,#{param.attr83})>0 ]]>
        </if>
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
            and r.org_code IN
            <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
            and r.MANAGE_LABEL_CODE IN
            <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.saleOrderState !=null and param.saleOrderState != ''">
            AND VE8080.LOOKUP_VALUE_CODE IN
            <foreach item="item" index="index" open="(" separator="," close=")"
                     collection="param.saleOrderState.split(',')">
                #{item}
            </foreach>
        </if>
        <if test="param.orgCode !=null and param.orgCode != ''">
            AND r.org_code = #{param.orgCode}
        </if>
        <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
            AND c1.FIRST_REVIEW_TIME is null
        </if>
        <if test="param.column14 !=null and param.column14 != ''">
            AND r.COLUMN14 = #{param.column14}
        </if>
        <if test="param.planBuyDate !=null and param.planBuyDate !=''">
            and r.COLUMN5=#{param.planBuyDate}
        </if>
        <if test="param.gender !=null and param.gender != ''">
            AND r.GENDER = #{param.gender}
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 = #{param.businessHeatCode}
        </if>
        <if test="param.beyondDay != null and ''!= param.beyondDay">
            <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
        </if>
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
            <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
        </if>
        <if test="param.assignEndTime != null and ''!= param.assignEndTime">
            <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
            <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
        </if>
        <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
            <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
            <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
        </if>
        <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
            <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.sendStartTime != null and ''!= param.sendStartTime">
            <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
        </if>
        <if test="param.sendEndTime != null and ''!= param.sendEndTime">
            <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.createdStartTime != null and ''!= param.createdStartTime">
            <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
        </if>
        <if test="param.createdEndTime != null and ''!= param.createdEndTime">
            <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.billType != null and ''!= param.billType ">
            AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND r.INFO_CHAN_D_CODE IN
                <foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
            AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.channelName != null and ''!= param.channelName ">
            AND r.CHANNEL_NAME in
            <foreach item="item" index="index" open="(" separator="," close=")"
                     collection="param.channelName.split(',')">
                #{item}
            </foreach>
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            AND r.review_person_name = #{param.reviewPersonName}
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.isCome != null and ''!= param.isCome">
            AND r.is_come = #{param.isCome}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
            AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
            AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
            AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test='param.isPvSend != null and "1"== param.isPvSend'>
            AND r.SEND_TIME IS NOT NULL
        </if>
        <if test='param.isPvSend != null and "0"== param.isPvSend'>
            AND ifnull(r.SEND_TIME,'')=''
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
            AND r.REVIEW_STATUS = #{param.reviewStatus}
        </if>
        <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
            AND r.ASSIGN_STATUS = #{param.assignStatus1}
            AND r.LAST_REVIEW_TIME is not null
            AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
        </if>
        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        <if test='param.isOverTime != null and "1"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
        </if>
        <if test='param.isOverTime != null and "0"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
        </if>
        <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
            AND r.review_person_name IN
            <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.BeginTime != null and ''!= param.BeginTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
        </if>
        <if test="param.EndTime != null and ''!= param.EndTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
        </if>
        <if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
            AND r.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
        </if>
        <if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
            AND r.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
        </if>
        <if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
            <![CDATA[	AND instr(r.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
        </if>
        <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">
            and c1.phone in (SELECT sheet.CUSTOMER_PHONE
            from  csc.t_sac_test_drive_sheet sheet
            WHERE  sheet.TEST_TYPE in ('1','2')
            and sheet.TEST_STATUS='2')
        </if>
        <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">
            and c1.phone not in (SELECT sheet.CUSTOMER_PHONE
            from  csc.t_sac_test_drive_sheet sheet
            WHERE  sheet.TEST_TYPE in ('1','2')
            and sheet.TEST_STATUS='2')
        </if>
        GROUP BY R.PHONE
<!--        <if test="param.flag !=null and param.flag == '1'.toString">-->
<!--            ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE-->
<!--        </if>-->
<!--        <if test="param.flag ==null or param.flag == '' or param.flag != '1'.toString">-->
<!--            ORDER BY r.LAST_UPDATED_DATE,r.CREATED_DATE-->
<!--        </if>-->
        ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
    </select>

    <select id="queryTOrcVeBuSaleOrderToC" resultType="java.util.Map">
        SELECT
            <foreach item="column" index="index" collection="param.needColumn" open="" separator="," close="">
                ${column}
            </foreach>
        FROM orc.T_ORC_VE_BU_SALE_ORDER_TO_C
        WHERE BUY_CUST_ID IN
        <foreach collection="param.buyCustId" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryTOrcVeBuEcReturnOrder" resultType="java.util.Map">
        SELECT
            <foreach item="column" index="index" collection="param.needColumn" open="" separator="," close="">
                ${column}
            </foreach>
        FROM orc.t_orc_ve_bu_ec_return_order
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="param.retailNO != null and param.retailNO.size()!=0">
                AND RETAIL_NO IN
                <foreach collection="param.retailNO" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.refundType != null">
                AND REFUND_TYPE = #{param.refundType}
            </if>
            <if test="param.refundStatus != null">
                AND REFUND_STATUS = #{param.refundStatus}
            </if>
            <if test="param.isEnable != null">
                AND IS_ENABLE = #{param.isEnable}
            </if>
        </trim>
    </select>
    
    <select id="queryListByDlrNew" resultType="java.util.Map">
        SELECT
            r.REVIEW_ID as reviewId,
            r.ORG_CODE as orgCode,
            r.ORG_NAME as orgName,
            r.BILL_TYPE as billType,
            r.BILL_TYPE_NAME as billTypeName,
            r.BUSINESS_TYPE as businessType,
            r.BUSINESS_TYPE_NAME as businessTypeName,
            r.INFO_CHAN_M_CODE,
            r.INFO_CHAN_M_NAME,
            r.INFO_CHAN_D_CODE,
            r.INFO_CHAN_D_NAME,
            r.INFO_CHAN_DD_CODE,
            r.INFO_CHAN_DD_NAME,
            r.CHANNEL_CODE,
            r.CHANNEL_NAME,
            r.BILL_CODE as billCode,

            r.PLAN_REVIEW_TIME AS planReviewTime,
            r.PLAN_COME_TIME AS planComeTime,
            r.FACT_COME_TIME AS factComeTime,

            r.IS_COME,
            r.REVIEW_TIME as reviewTime,
            r.LAST_REVIEW_TIME as lastReviewTime,
            r.OVER_REVIEW_TIME as overReviewTime,
            r.ASSIGN_STATUS as assignStatus,
            r.ASSIGN_STATUS_NAME as assignStatusName,
            r.ASSIGN_TIME as assignTime,
            r.ASSIGN_PERSON_ID as assignPersonId,
            r.ASSIGN_PERSON_NAME as assignPersonName,
            r.REVIEW_PERSON_ID as reviewPersonId,
            r.REVIEW_PERSON_NAME as reviewPersonName,
            r.REVIEW_DESC as reviewDesc,
            r.REVIEW_STATUS as reviewStatus,
            r.REVIEW_STATUS_NAME as reviewStatusName,
            r.CUST_ID as custId,
            r.CUST_NAME as custName,
            r.PHONE as phone,
            r.GENDER as gender,
            r.GENDER_NAME as genderName,
            r.TOUCH_STATUS as touchStatus,
            r.TOUCH_STATUS_NAME as touchStatusName,
            r.NODE_CODE as nodeCode,
            r.NODE_NAME as nodeName,
            r.SEND_DLR_CODE as sendDlrCode,
            r.SEND_DLR_SHORT_NAME as sendDlrShortName,
            r.SEND_TIME,
            r.INTEN_LEVEL_CODE,
            r.INTEN_LEVEL_NAME,
            r.INTEN_BRAND_CODE,
            r.INTEN_BRAND_NAME,
            r.INTEN_SERIES_CODE,
            r.INTEN_SERIES_NAME,
            r.INTEN_CAR_TYPE_CODE,
            r.INTEN_CAR_TYPE_NAME,
            r.EXTENDS_JSON as extendsJson,
            r.UPDATE_CONTROL_ID as updateControlId,
            r.CREATED_NAME as createdName,
            r.LAST_UPDATED_DATE as lastUpdatedDate,
            r.MANAGE_LABEL_CODE,
            r.MANAGE_LABEL_NAME,
            r.COLUMN14,

            r.COLUMN15 as followReason,

            r.send_time as isPvSend,

            r.OVER_REVIEW_TIME as isOverTime,

            c1.CUS_SOURCE as cusSource,
            c1.CREATED_DATE as createdDate,
            c1.COLUMN20,
            r.COLUMN19 as dccFlag
        FROM csc.t_sac_review r INNER JOIN csc.t_sac_clue_info_dlr c1 on r.REVIEW_ID =c1.REVIEW_ID
        WHERE
            r.REVIEW_STATUS in('0','1','3')
            <if test="param.statusCodes !=null and param.statusCodes != ''">
                AND c1.STATUS_CODE = #{param.statusCodes}
            </if>
            <if test="param.searchCondition !=null and param.searchCondition != ''">
                and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
            </if>
            <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
                and r.org_code IN
                <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
                and r.MANAGE_LABEL_CODE IN
                <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.orgCode !=null and param.orgCode != ''">
                AND r.org_code = #{param.orgCode}
            </if>
            <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
                AND c1.FIRST_REVIEW_TIME is null
            </if>
            <if test="param.column14 !=null and param.column14 != ''">
                AND r.COLUMN14 = #{param.column14}
            </if>
            <if test="param.planBuyDate !=null and param.planBuyDate !=''">
                and r.COLUMN5=#{param.planBuyDate}
            </if>
            <if test="param.gender !=null and param.gender != ''">
                AND r.GENDER = #{param.gender}
            </if>
            <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                AND r.column7 = #{param.businessHeatCode}
            </if>
            <if test="param.beyondDay != null and ''!= param.beyondDay">
                <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
            </if>
            <if test="param.assignStartTime != null and ''!= param.assignStartTime">
                <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
            </if>
            <if test="param.assignEndTime != null and ''!= param.assignEndTime">
                <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.planStartTime != null and ''!= param.planStartTime">
                <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
            </if>
            <if test="param.planEndTime != null and ''!= param.planEndTime">
                <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
                <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
            </if>
            <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
                <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
                <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
            </if>
            <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
                <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.lastStartTime != null and ''!= param.lastStartTime">
                <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
            </if>
            <if test="param.lastEndTime != null and ''!= param.lastEndTime">
                <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.sendStartTime != null and ''!= param.sendStartTime">
                <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
            </if>
            <if test="param.sendEndTime != null and ''!= param.sendEndTime">
                <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.createdStartTime != null and ''!= param.createdStartTime">
                <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
            </if>
            <if test="param.createdEndTime != null and ''!= param.createdEndTime">
                <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.billType != null and ''!= param.billType ">
                AND r.bill_type = #{param.billType}
            </if>
            <if test="param.businessType != null and ''!= param.businessType ">
                AND r.business_type = #{param.businessType}
            </if>
            <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
                AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
            </if>
            <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
                AND r.INFO_CHAN_D_CODE IN
                <foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
                AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
            </if>
            <if test="param.channelCode != null and ''!= param.channelCode ">
                AND r.CHANNEL_CODE = #{param.channelCode}
            </if>
            <if test="param.channelName != null and ''!= param.channelName ">
                AND r.CHANNEL_NAME in
                <foreach item="item" index="index" open="(" separator="," close=")" collection="param.channelName.split(',')">
                    #{item}
                </foreach>
            </if>
            <if test="param.custName != null and ''!= param.custName ">
                <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
            </if>
            <if test="param.phone != null and ''!= param.phone ">
                <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
            </if>
            <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
                AND r.review_person_name = #{param.reviewPersonName}
            </if>
            <if test="param.billCode != null and ''!= param.billCode ">
                AND r.bill_code = #{param.billCode}
            </if>
            <if test="param.isCome != null and ''!= param.isCome">
                AND r.is_come = #{param.isCome}
            </if>
            <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
            </if>
            <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
                AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
            </if>
            <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
                AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
            </if>
            <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
                AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
            </if>
            <if test='param.isPvSend != null and "1"== param.isPvSend'>
                AND r.SEND_TIME IS NOT NULL
            </if>
            <if test='param.isPvSend != null and "0"== param.isPvSend'>
                AND ifnull(r.SEND_TIME,'')=''
            </if>
            <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
                <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
            </if>
            <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
                AND r.REVIEW_STATUS = #{param.reviewStatus}
            </if>
            <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
                AND r.ASSIGN_STATUS = #{param.assignStatus1}
                AND r.LAST_REVIEW_TIME is not null
                AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
            </if>
            <if test="param.outCallReview != null and ''!= param.outCallReview ">
                AND r.LAST_REVIEW_TIME is not null
                AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
            </if>
            <if test="param.assignStatus!=null and ''!=param.assignStatus">
                AND r.ASSIGN_STATUS = #{param.assignStatus}
            </if>
            <if test='param.isOverTime != null and "1"== param.isOverTime'>
                <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
            </if>
            <if test='param.isOverTime != null and "0"== param.isOverTime'>
                <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
            </if>
            <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
                AND r.review_person_name IN
                <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.BeginTime != null and ''!= param.BeginTime">
                AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
            </if>
            <if test="param.EndTime != null and ''!= param.EndTime">
                AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
            </if>
            <if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
                AND r.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
            </if>
            <if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
                AND r.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
            </if>
            <if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
                <![CDATA[	AND instr(r.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
            </if>
            <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">
                and c1.phone in (SELECT sheet.CUSTOMER_PHONE
                from csc.t_sac_test_drive_sheet sheet
                WHERE sheet.TEST_TYPE in ('1','2') and sheet.TEST_STATUS='2')
            </if>
            <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">
                and c1.phone not in (SELECT sheet.CUSTOMER_PHONE
                from csc.t_sac_test_drive_sheet sheet
                WHERE sheet.TEST_TYPE in ('1','2') and sheet.TEST_STATUS='2')
            </if>
            <if test="param.filterDccFlag">
                AND r.COLUMN19 IS NULL
            </if>
        GROUP BY R.PHONE
        ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
        <if test="param.pageNo != null and param.pageSize != null and param.pageSize >= 0">
            LIMIT #{param.pageNo}, #{param.pageSize}
        </if>
    </select>

    <select id="queryListByDlrCount" resultType="java.lang.Long">
    SELECT COUNT(*) FROM(
        SELECT
            count(*)
        FROM csc.t_sac_review r INNER JOIN csc.t_sac_clue_info_dlr c1 on r.REVIEW_ID =c1.REVIEW_ID
        WHERE
            r.REVIEW_STATUS in('0','1','3')
            <if test="param.statusCodes !=null and param.statusCodes != ''">
                AND c1.STATUS_CODE = #{param.statusCodes}
            </if>
            <if test="param.searchCondition !=null and param.searchCondition != ''">
                and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
            </if>
            <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
                and r.org_code IN
                <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
                and r.MANAGE_LABEL_CODE IN
                <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.orgCode !=null and param.orgCode != ''">
                AND r.org_code = #{param.orgCode}
            </if>
            <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
                AND c1.FIRST_REVIEW_TIME is null
            </if>
            <if test="param.column14 !=null and param.column14 != ''">
                AND r.COLUMN14 = #{param.column14}
            </if>
            <if test="param.planBuyDate !=null and param.planBuyDate !=''">
                and r.COLUMN5=#{param.planBuyDate}
            </if>
            <if test="param.gender !=null and param.gender != ''">
                AND r.GENDER = #{param.gender}
            </if>
            <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                AND r.column7 = #{param.businessHeatCode}
            </if>
            <if test="param.beyondDay != null and ''!= param.beyondDay">
                <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
            </if>
            <if test="param.assignStartTime != null and ''!= param.assignStartTime">
                <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
            </if>
            <if test="param.assignEndTime != null and ''!= param.assignEndTime">
                <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.planStartTime != null and ''!= param.planStartTime">
                <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
            </if>
            <if test="param.planEndTime != null and ''!= param.planEndTime">
                <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
                <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
            </if>
            <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
                <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
                <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
            </if>
            <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
                <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.lastStartTime != null and ''!= param.lastStartTime">
                <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
            </if>
            <if test="param.lastEndTime != null and ''!= param.lastEndTime">
                <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.sendStartTime != null and ''!= param.sendStartTime">
                <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
            </if>
            <if test="param.sendEndTime != null and ''!= param.sendEndTime">
                <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.createdStartTime != null and ''!= param.createdStartTime">
                <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
            </if>
            <if test="param.createdEndTime != null and ''!= param.createdEndTime">
                <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.billType != null and ''!= param.billType ">
                AND r.bill_type = #{param.billType}
            </if>
            <if test="param.businessType != null and ''!= param.businessType ">
                AND r.business_type = #{param.businessType}
            </if>
            <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
                AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
            </if>
            <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
                AND r.INFO_CHAN_D_CODE IN
                <foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
                AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
            </if>
            <if test="param.channelCode != null and ''!= param.channelCode ">
                AND r.CHANNEL_CODE = #{param.channelCode}
            </if>
            <if test="param.channelName != null and ''!= param.channelName ">
                AND r.CHANNEL_NAME in
                <foreach item="item" index="index" open="(" separator="," close=")" collection="param.channelName.split(',')">
                    #{item}
                </foreach>
            </if>
            <if test="param.custName != null and ''!= param.custName ">
                <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
            </if>
            <if test="param.phone != null and ''!= param.phone ">
                <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
            </if>
            <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
                AND r.review_person_name = #{param.reviewPersonName}
            </if>
            <if test="param.billCode != null and ''!= param.billCode ">
                AND r.bill_code = #{param.billCode}
            </if>
            <if test="param.isCome != null and ''!= param.isCome">
                AND r.is_come = #{param.isCome}
            </if>
            <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
            </if>
            <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
                AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
            </if>
            <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
                AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
            </if>
            <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
                AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
            </if>
            <if test='param.isPvSend != null and "1"== param.isPvSend'>
                AND r.SEND_TIME IS NOT NULL
            </if>
            <if test='param.isPvSend != null and "0"== param.isPvSend'>
                AND ifnull(r.SEND_TIME,'')=''
            </if>
            <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
                <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
            </if>
            <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
                AND r.REVIEW_STATUS = #{param.reviewStatus}
            </if>
            <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
                AND r.ASSIGN_STATUS = #{param.assignStatus1}
                AND r.LAST_REVIEW_TIME is not null
                AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
            </if>
            <if test="param.outCallReview != null and ''!= param.outCallReview ">
                AND r.LAST_REVIEW_TIME is not null
                AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
            </if>
            <if test="param.assignStatus!=null and ''!=param.assignStatus">
                AND r.ASSIGN_STATUS = #{param.assignStatus}
            </if>
            <if test='param.isOverTime != null and "1"== param.isOverTime'>
                <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
            </if>
            <if test='param.isOverTime != null and "0"== param.isOverTime'>
                <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
            </if>
            <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
                AND r.review_person_name IN
                <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.BeginTime != null and ''!= param.BeginTime">
                AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
            </if>
            <if test="param.EndTime != null and ''!= param.EndTime">
                AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
            </if>
            <if test="param.lastReviewBeginTime != null and ''!= param.lastReviewBeginTime">
                AND r.LAST_REVIEW_TIME >= STR_TO_DATE(#{param.lastReviewBeginTime},'%Y-%m-%d')
            </if>
            <if test="param.lastReviewEndTime != null and ''!= param.lastReviewEndTime">
                AND r.LAST_REVIEW_TIME <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{param.lastReviewEndTime},'%Y-%m-%d'),INTERVAL 1 DAY)
            </if>
            <if test="param.intenCarTypeName != null and ''!= param.intenCarTypeName ">
                <![CDATA[	AND instr(r.INTEN_CAR_TYPE_NAME,#{param.intenCarTypeName})>0 ]]>
            </if>
            <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">
                and c1.phone in (SELECT sheet.CUSTOMER_PHONE
                from csc.t_sac_test_drive_sheet sheet
                WHERE sheet.TEST_TYPE in ('1','2') and sheet.TEST_STATUS='2')
            </if>
            <if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">
                and c1.phone not in (SELECT sheet.CUSTOMER_PHONE
                from csc.t_sac_test_drive_sheet sheet
                WHERE sheet.TEST_TYPE in ('1','2') and sheet.TEST_STATUS='2')
            </if>
            <if test="param.filterDccFlag">
                AND r.COLUMN19 IS NULL
            </if>
         GROUP BY R.PHONE) as temp
    </select>

    <select id="queryListAuditReviewInfo" resultType="java.util.Map">
        select
        a.audit_id,
        R.CUST_ID,
        R.REVIEW_ID,
        a.apply_type_code,
        a.apply_type_name,
        r.cust_name,
        r.phone,
        r.bill_type_name,
        r.business_type_name,
        r.bill_code,
        r.bill_type,
        a.created_date,
        a.created_name,
        r.touch_status_name,
        r.error_reason_name,
        a.apply_desc,
        r.GENDER,
        r.GENDER_NAME,
        r.REVIEW_PERSON_ID,
        r.REVIEW_PERSON_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON,
        A.UPDATE_CONTROL_ID
        from t_sac_review_audit a
        inner join t_sac_review r on a.review_id=r.review_id
        where
        a.is_enable='1'
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.reviewId == null">
            and r.org_code=#{param.orgCode}
            and (a.sh_person_id is null or a.sh_person_id =#{param.shPersonId})
        </if>
        and a.sh_status='0'
        <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
            AND A.apply_type_code = #{param.applyTypeCode}
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.applyPerson != null and ''!= param.applyPerson ">
            AND a.created_name = #{param.applyPerson}
        </if>
        <if test="param.creator != null and ''!= param.creator ">
            AND a.CREATOR in
            <foreach collection="param.creator.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            AND r.cust_name = #{param.custName}
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            AND r.phone = #{param.phone}
        </if>
        <if test="param.startTime != null and ''!= param.startTime">
            <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
        </if>
        <if test="param.endTime != null and ''!= param.endTime">
            <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 IN
            <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE IN
            <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
            AND r.REVIEW_PERSON_ID IN
            <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.reviewId != null and ''!= param.reviewId ">
            and r.REVIEW_ID=#{param.reviewId}
        </if>
        order by a.CREATED_DATE
    </select>
    <sql id="queryListAuditReviewRecordField">
        a.AUDIT_ID,
        r.REVIEW_ID,
        a.apply_type_name,
        r.cust_id,
        r.cust_name,
        r.phone,
        r.BILL_TYPE,
        r.bill_type_name,
        r.business_type,
        r.business_type_name,
        r.bill_code,
        a.created_date,
        a.created_name,
        r.touch_status_name,
        r.error_reason_name,
        a.apply_desc,
        a.sh_status,
        a.sh_status_name,
        a.sh_desc,
        a.sh_time,
        r.GENDER,
        r.GENDER_NAME,
        r.REVIEW_PERSON_ID,
        r.REVIEW_PERSON_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.COLUMN1,
        r.COLUMN2,
        r.COLUMN3,
        r.COLUMN4,
        r.COLUMN5,
        r.COLUMN6,
        r.COLUMN7,
        r.COLUMN8,
        r.COLUMN9,
        r.COLUMN10,
        r.COLUMN11,
        r.COLUMN12,
        r.COLUMN13,
        r.COLUMN14,
        r.COLUMN15,
        r.COLUMN16,
        r.COLUMN17,
        r.COLUMN18,
        r.COLUMN19,
        r.COLUMN20,
        r.BIG_COLUMN1,
        r.BIG_COLUMN2,
        r.BIG_COLUMN3,
        r.BIG_COLUMN4,
        r.BIG_COLUMN5,
        r.EXTENDS_JSON,
        A.UPDATE_CONTROL_ID
    </sql>

    <select id="queryListAuditReviewRecordInfoPerformance" resultType="java.util.Map">
        select * from
        (
            select <include refid="queryListAuditReviewRecordField"></include>
            from t_sac_review_audit a
            inner join t_sac_review r on a.review_id=r.review_id
            where
                a.is_enable='1'
                and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
                <if test="param.searchCondition !=null and param.searchCondition != ''">
                    and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                </if>
                <if test="param.auditId != null and ''!= param.auditId ">
                    AND A.AUDIT_ID = #{param.auditId}
                </if>
                <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                    AND A.apply_type_code = #{param.applyTypeCode}
                </if>
                <if test="param.billCode != null and ''!= param.billCode ">
                    AND r.bill_code = #{param.billCode}
                </if>
                <if test="param.applyPerson != null and ''!= param.applyPerson ">
                    AND r.created_name = #{param.applyPerson}
                </if>
                <if test="param.custName != null and ''!= param.custName ">
                    AND r.cust_name = #{param.custName}
                </if>
                <if test="param.phone != null and ''!= param.phone ">
                    AND r.phone = #{param.phone}
                </if>
                <if test="param.shStatus != null and ''!= param.shStatus ">
                    AND A.sh_status IN
                    <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.startTime != null and ''!= param.startTime">
                    <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                </if>
                <if test="param.endTime != null and ''!= param.endTime">
                    <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.shStartTime != null and ''!= param.shStartTime">
                    <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                </if>
                <if test="param.shEndTime != null and ''!= param.shEndTime">
                    <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                    AND r.column7 IN
                    <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                    AND r.INTEN_LEVEL_CODE IN
                    <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                    AND r.REVIEW_PERSON_ID IN
                    <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            union all
            select
                <include refid="queryListAuditReviewRecordField"></include>
            from t_sac_review_audit a
            inner join t_sac_review_his r on a.review_id=r.review_id
            where
                a.is_enable='1'
                and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
                <if test="param.searchCondition !=null and param.searchCondition != ''">
                    and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                </if>
                <if test="param.auditId != null and ''!= param.auditId ">
                    AND A.AUDIT_ID = #{param.auditId}
                </if>
                <if test="param.custName != null and ''!= param.custName ">
                    AND r.cust_name = #{param.custName}
                </if>
                <if test="param.phone != null and ''!= param.phone ">
                    AND r.phone = #{param.phone}
                </if>
                <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                    AND A.apply_type_code = #{param.applyTypeCode}
                </if>
                <if test="param.billCode != null and ''!= param.billCode ">
                    AND r.bill_code = #{param.billCode}
                </if>
                <if test="param.applyPerson != null and ''!= param.applyPerson ">
                    AND r.created_name = #{param.applyPerson}
                </if>
                <if test="param.shStatus != null and ''!= param.shStatus ">
                    AND A.sh_status IN
                    <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.startTime != null and ''!= param.startTime">
                    <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                </if>
                <if test="param.endTime != null and ''!= param.endTime">
                    <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.shStartTime != null and ''!= param.shStartTime">
                    <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                </if>
                <if test="param.shEndTime != null and ''!= param.shEndTime">
                    <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                    AND r.column7 IN
                    <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                    AND r.INTEN_LEVEL_CODE IN
                    <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                    AND r.REVIEW_PERSON_ID IN
                    <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            union all
            select
                <include refid="queryListAuditReviewRecordField"></include>
            from t_sac_review_audit a
            inner join t_sac_review_his r on a.review_id=r.review_id
            where
                a.is_enable='1'
                and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                and (r.REVIEW_PERSON_ID=#{param.personId})
                <if test="param.searchCondition !=null and param.searchCondition != ''">
                    and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                </if>
                <if test="param.auditId != null and ''!= param.auditId ">
                    AND A.AUDIT_ID = #{param.auditId}
                </if>
                <if test="param.custName != null and ''!= param.custName ">
                    AND r.cust_name = #{param.custName}
                </if>
                <if test="param.phone != null and ''!= param.phone ">
                    AND r.phone = #{param.phone}
                </if>
                <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                    AND A.apply_type_code = #{param.applyTypeCode}
                </if>
                <if test="param.billCode != null and ''!= param.billCode ">
                    AND r.bill_code = #{param.billCode}
                </if>
                <if test="param.applyPerson != null and ''!= param.applyPerson ">
                    AND r.created_name = #{param.applyPerson}
                </if>
                <if test="param.shStatus != null and ''!= param.shStatus ">
                    AND A.sh_status IN
                    <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.startTime != null and ''!= param.startTime">
                    <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                </if>
                <if test="param.endTime != null and ''!= param.endTime">
                    <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.shStartTime != null and ''!= param.shStartTime">
                    <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                </if>
                <if test="param.shEndTime != null and ''!= param.shEndTime">
                    <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                    AND r.column7 IN
                    <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                    AND r.INTEN_LEVEL_CODE IN
                    <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                    AND r.REVIEW_PERSON_ID IN
                    <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            ) A
        GROUP BY A.REVIEW_ID
        ORDER BY A.created_date DESC

    </select>

    <select id="queryListAuditReviewRecordInfo" resultType="java.util.Map">
        select * from
        (
            select
            a.AUDIT_ID,
            r.REVIEW_ID,
            a.apply_type_name,
            r.cust_id,
            r.cust_name,
            r.phone,
            r.BILL_TYPE,
            r.bill_type_name,
            r.business_type,
            r.business_type_name,
            r.bill_code,
            a.created_date,
            a.created_name,
            r.touch_status_name,
            r.error_reason_name,
            a.apply_desc,
            a.sh_status,
            a.sh_status_name,
            a.sh_desc,
            a.sh_time,
            r.GENDER,
            r.GENDER_NAME,
            r.REVIEW_PERSON_ID,
            r.REVIEW_PERSON_NAME,
            r.CHANNEL_CODE,
            r.CHANNEL_NAME,
            r.INTEN_LEVEL_CODE,
            r.INTEN_LEVEL_NAME,
            r.INTEN_BRAND_CODE,
            r.INTEN_BRAND_NAME,
            r.INTEN_SERIES_CODE,
            r.INTEN_SERIES_NAME,
            r.INTEN_CAR_TYPE_CODE,
            r.INTEN_CAR_TYPE_NAME,
            r.COLUMN1,
            r.COLUMN2,
            r.COLUMN3,
            r.COLUMN4,
            r.COLUMN5,
            r.COLUMN6,
            r.COLUMN7,
            r.COLUMN8,
            r.COLUMN9,
            r.COLUMN10,
            r.COLUMN11,
            r.COLUMN12,
            r.COLUMN13,
            r.COLUMN14,
            r.COLUMN15,
            r.COLUMN16,
            r.COLUMN17,
            r.COLUMN18,
            r.COLUMN19,
            r.COLUMN20,
            r.BIG_COLUMN1,
            r.BIG_COLUMN2,
            r.BIG_COLUMN3,
            r.BIG_COLUMN4,
            r.BIG_COLUMN5,
            r.EXTENDS_JSON,
            A.UPDATE_CONTROL_ID
            from t_sac_review_audit a
            inner join t_sac_review r on a.review_id=r.review_id
            where
            a.is_enable='1'
            and r.org_code=#{param.orgCode}
            and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
            <if test="param.searchCondition !=null and param.searchCondition != ''">
                and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
            </if>
            <if test="param.auditId != null and ''!= param.auditId ">
                AND A.AUDIT_ID = #{param.auditId}
            </if>
            <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                AND A.apply_type_code = #{param.applyTypeCode}
            </if>
            <if test="param.billCode != null and ''!= param.billCode ">
                AND r.bill_code = #{param.billCode}
            </if>
            <if test="param.applyPerson != null and ''!= param.applyPerson ">
                AND r.created_name = #{param.applyPerson}
            </if>
            <if test="param.custName != null and ''!= param.custName ">
                AND r.cust_name = #{param.custName}
            </if>
            <if test="param.phone != null and ''!= param.phone ">
                AND r.phone = #{param.phone}
            </if>
            <if test="param.shStatus != null and ''!= param.shStatus ">
                AND A.sh_status IN
                <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.startTime != null and ''!= param.startTime">
                <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
            </if>
            <if test="param.endTime != null and ''!= param.endTime">
                <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.shStartTime != null and ''!= param.shStartTime">
                <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
            </if>
            <if test="param.shEndTime != null and ''!= param.shEndTime">
                <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
            </if>
            <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                AND r.column7 IN
                <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                AND r.INTEN_LEVEL_CODE IN
                <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                AND r.REVIEW_PERSON_ID IN
                <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        union all
            select
                a.AUDIT_ID,
                r.REVIEW_ID,
                a.apply_type_name,
                r.cust_id,
                r.cust_name,
                r.phone,
                r.BILL_TYPE,
                r.bill_type_name,
                r.business_type,
                r.business_type_name,
                r.bill_code,
                a.created_date,
                a.created_name,
                r.touch_status_name,
                r.error_reason_name,
                a.apply_desc,
                a.sh_status,
                a.sh_status_name,
                a.sh_desc,
                a.sh_time,
                r.GENDER,
                r.GENDER_NAME,
                r.REVIEW_PERSON_ID,
                r.REVIEW_PERSON_NAME,
                r.CHANNEL_CODE,
                r.CHANNEL_NAME,
                r.INTEN_LEVEL_CODE,
                r.INTEN_LEVEL_NAME,
                r.INTEN_BRAND_CODE,
                r.INTEN_BRAND_NAME,
                r.INTEN_SERIES_CODE,
                r.INTEN_SERIES_NAME,
                r.INTEN_CAR_TYPE_CODE,
                r.INTEN_CAR_TYPE_NAME,
                r.COLUMN1,
                r.COLUMN2,
                r.COLUMN3,
                r.COLUMN4,
                r.COLUMN5,
                r.COLUMN6,
                r.COLUMN7,
                r.COLUMN8,
                r.COLUMN9,
                r.COLUMN10,
                r.COLUMN11,
                r.COLUMN12,
                r.COLUMN13,
                r.COLUMN14,
                r.COLUMN15,
                r.COLUMN16,
                r.COLUMN17,
                r.COLUMN18,
                r.COLUMN19,
                r.COLUMN20,
                r.BIG_COLUMN1,
                r.BIG_COLUMN2,
                r.BIG_COLUMN3,
                r.BIG_COLUMN4,
                r.BIG_COLUMN5,
                r.EXTENDS_JSON,
                A.UPDATE_CONTROL_ID
            from t_sac_review_audit a
            inner join t_sac_review_his r on a.review_id=r.review_id
            where
                a.is_enable='1'
                and r.org_code=#{param.orgCode}
                and ((a.creator=#{param.personId} or a.sh_person_id=#{param.personId}) OR r.REVIEW_PERSON_ID=#{param.personId})

                <if test="param.searchCondition !=null and param.searchCondition != ''">
                    and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                </if>
                <if test="param.auditId != null and ''!= param.auditId ">
                    AND A.AUDIT_ID = #{param.auditId}
                </if>
                <if test="param.custName != null and ''!= param.custName ">
                    AND r.cust_name = #{param.custName}
                </if>
                <if test="param.phone != null and ''!= param.phone ">
                    AND r.phone = #{param.phone}
                </if>
                <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                    AND A.apply_type_code = #{param.applyTypeCode}
                </if>
                <if test="param.billCode != null and ''!= param.billCode ">
                    AND r.bill_code = #{param.billCode}
                </if>
                <if test="param.applyPerson != null and ''!= param.applyPerson ">
                    AND r.created_name = #{param.applyPerson}
                </if>
                <if test="param.shStatus != null and ''!= param.shStatus ">
                    AND A.sh_status IN
                    <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.startTime != null and ''!= param.startTime">
                    <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                </if>
                <if test="param.endTime != null and ''!= param.endTime">
                    <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.shStartTime != null and ''!= param.shStartTime">
                    <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                </if>
                <if test="param.shEndTime != null and ''!= param.shEndTime">
                    <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                </if>
                <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                    AND r.column7 IN
                    <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                    AND r.INTEN_LEVEL_CODE IN
                    <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                    AND r.REVIEW_PERSON_ID IN
                    <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
        ) A
        ORDER BY A.created_date DESC
    </select>

    <select id="queryListAuditReviewRecordInfoCount" resultType="java.lang.Long">
        SELECT count(*) from
        (
            SELECT DISTINCT A.REVIEW_ID
            FROM (
                SELECT
                    r.REVIEW_ID
                FROM t_sac_review_audit a
                inner join t_sac_review r on a.review_id=r.review_id
                where
                    a.is_enable='1'
                    and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                    and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
                    <if test="param.searchCondition !=null and param.searchCondition != ''">
                      and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)AND A.AUDIT_ID = #{param.auditId}
                    </if>
                    <if test="param.auditId != null and ''!= param.auditId "></if>
                    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                        AND A.apply_type_code = #{param.applyTypeCode}
                    </if>
                    <if test="param.billCode != null and ''!= param.billCode ">
                        AND r.bill_code = #{param.billCode}
                    </if>
                    <if test="param.applyPerson != null and ''!= param.applyPerson ">
                        AND r.created_name = #{param.applyPerson}
                    </if>
                    <if test="param.custName != null and ''!= param.custName ">
                        AND r.cust_name = #{param.custName}
                    </if>
                    <if test="param.phone != null and ''!= param.phone ">
                        AND r.phone = #{param.phone}
                    </if>
                    <if test="param.shStatus != null and ''!= param.shStatus ">
                        AND A.sh_status IN
                        <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="param.startTime != null and ''!= param.startTime">
                        <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                    </if>
                    <if test="param.endTime != null and ''!= param.endTime">
                        <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                    </if>
                    <if test="param.shStartTime != null and ''!= param.shStartTime">
                        <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                    </if>
                    <if test="param.shEndTime != null and ''!= param.shEndTime">
                        <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                    </if>
                    <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                        AND r.column7 IN
                        <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                        AND r.INTEN_LEVEL_CODE IN
                        <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                        AND r.REVIEW_PERSON_ID IN
                        <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                    </if>
                union all
                    SELECT
                        r.REVIEW_ID
                    FROM t_sac_review_audit a
                    inner join t_sac_review r on a.review_id=r.review_id
                    where
                        a.is_enable='1'
                        and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                        and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})

                        <if test="param.searchCondition !=null and param.searchCondition != ''">
                            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                        </if>
                        <if test="param.auditId != null and ''!= param.auditId ">
                            AND A.AUDIT_ID = #{param.auditId}
                        </if>
                        <if test="param.custName != null and ''!= param.custName ">
                            AND r.cust_name = #{param.custName}
                        </if>
                        <if test="param.phone != null and ''!= param.phone ">
                            AND r.phone = #{param.phone}
                        </if>
                        <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                            AND A.apply_type_code = #{param.applyTypeCode}
                        </if>
                        <if test="param.billCode != null and ''!= param.billCode ">
                            AND r.bill_code = #{param.billCode}
                        </if>
                        <if test="param.applyPerson != null and ''!= param.applyPerson ">
                            AND r.created_name = #{param.applyPerson}
                        </if>
                        <if test="param.shStatus != null and ''!= param.shStatus ">
                            AND A.sh_status IN
                            <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.startTime != null and ''!= param.startTime">
                            <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                        </if>
                        <if test="param.endTime != null and ''!= param.endTime">
                            <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                        </if>
                        <if test="param.shStartTime != null and ''!= param.shStartTime">
                            <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                        </if>
                        <if test="param.shEndTime != null and ''!= param.shEndTime">
                            <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                        </if>
                        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                            AND r.column7 IN
                            <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                            AND r.INTEN_LEVEL_CODE IN
                            <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                            AND r.REVIEW_PERSON_ID IN
                            <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                union all
                    SELECT
                        r.REVIEW_ID
                    FROM t_sac_review_audit a
                    inner join t_sac_review_his r on a.review_id=r.review_id
                    where
                        a.is_enable='1'
                        and a.org_code=#{param.orgCode} and r.org_code=#{param.orgCode}
                        and (r.REVIEW_PERSON_ID=#{param.personId})
                        <if test="param.searchCondition !=null and param.searchCondition != ''">
                            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
                        </if>
                        <if test="param.auditId != null and ''!= param.auditId ">
                            AND A.AUDIT_ID = #{param.auditId}
                        </if>
                        <if test="param.custName != null and ''!= param.custName ">
                            AND r.cust_name = #{param.custName}
                        </if>
                        <if test="param.phone != null and ''!= param.phone ">
                            AND r.phone = #{param.phone}
                        </if>
                        <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
                            AND A.apply_type_code = #{param.applyTypeCode}
                        </if>
                        <if test="param.billCode != null and ''!= param.billCode ">
                            AND r.bill_code = #{param.billCode}
                        </if>
                        <if test="param.applyPerson != null and ''!= param.applyPerson ">
                            AND r.created_name = #{param.applyPerson}
                        </if>
                        <if test="param.shStatus != null and ''!= param.shStatus ">
                            AND A.sh_status IN
                            <foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.startTime != null and ''!= param.startTime">
                            <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
                        </if>
                        <if test="param.endTime != null and ''!= param.endTime">
                            <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
                        </if>
                        <if test="param.shStartTime != null and ''!= param.shStartTime">
                            <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
                        </if>
                        <if test="param.shEndTime != null and ''!= param.shEndTime">
                            <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
                        </if>
                        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
                            AND r.column7 IN
                            <foreach collection="param.businessHeatCode.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
                            AND r.INTEN_LEVEL_CODE IN
                            <foreach collection="param.intenLevelCode.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
                            AND r.REVIEW_PERSON_ID IN
                            <foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
                                     close=")">
                                #{item}
                            </foreach>
                        </if>
                ) A
            )B
    </select>

    <select id="queryReviewRecord" resultType="java.util.Map">
        select r.REVIEW_ID                                          as reviewId,
               r.ORG_CODE                                           as orgCode,
               r.ORG_NAME                                           as orgName,
               r.BILL_TYPE                                          as billType,
               r.BILL_TYPE_NAME                                     as billTypeName,
               r.BUSINESS_TYPE                                      as businessType,
               r.BUSINESS_TYPE_NAME                                 as businessTypeName,
               r.INFO_CHAN_M_CODE,
               r.INFO_CHAN_M_NAME,
               r.INFO_CHAN_D_CODE,
               r.INFO_CHAN_D_NAME,
               r.INFO_CHAN_DD_CODE,
               r.INFO_CHAN_DD_NAME,
               r.CHANNEL_CODE,
               r.CHANNEL_NAME,
               r.BILL_CODE                                          as billCode,
               date_format(r.PLAN_REVIEW_TIME, '%Y-%m-%d %H:%i:%s') AS planReviewTime,
               date_format(r.PLAN_COME_TIME, '%Y-%m-%d %H:%i:%s')   AS planComeTime,
               date_format(r.FACT_COME_TIME, '%Y-%m-%d %H:%i:%s')   AS factComeTime,
               r.IS_COME                                            as isCome,
               case when r.IS_COME = '1' then '是' else '否' end      as IS_COME_NAME,
               r.REVIEW_TIME                                        as reviewTime,
               r.LAST_REVIEW_TIME                                   as lastReviewTime,
               r.OVER_REVIEW_TIME                                   as overReviewTime,
               r.REVIEW_PERSON_ID                                   as reviewPersonId,
               r.REVIEW_PERSON_NAME                                 as reviewPersonName,
               r.REVIEW_DESC                                        as reviewDesc,
               r.REVIEW_STATUS                                      as reviewStatus,
               r.REVIEW_STATUS_NAME                                 as reviewStatusName,
               r.CUST_ID                                            as custId,
               r.CUST_NAME                                          as custName,
               r.PHONE                                              as phone,
               r.GENDER                                             as gender,
               r.GENDER_NAME                                        as genderName,
               r.TOUCH_STATUS                                       as touchStatus,
               r.TOUCH_STATUS_NAME                                  as touchStatusName,
               r.NODE_CODE                                          as nodeCode,
               r.NODE_NAME                                          as nodeName,
               r.SEND_DLR_CODE                                      as sendDlrCode,
               r.SEND_DLR_SHORT_NAME                                as sendDlrShortName,
               r.INTEN_LEVEL_CODE,
               r.INTEN_LEVEL_NAME,
               r.INTEN_BRAND_CODE,
               r.INTEN_BRAND_NAME,
               r.INTEN_SERIES_CODE,
               r.INTEN_SERIES_NAME,
               r.INTEN_CAR_TYPE_CODE,
               r.INTEN_CAR_TYPE_NAME,
               r.EXTENDS_JSON                                       as extendsJson,
               r.CREATED_DATE                                       as createdDate,
               r.UPDATE_CONTROL_ID                                  as updateControlId,
               r.CREATED_NAME
        from t_sac_review_his r
        where r.bill_code = #{param.billCode}
        ORDER BY r.CREATED_DATE desc
    </select>
    <select id="queryReviewId" resultType="java.util.Map">
        SELECT c.SALE_ORDER_CODE,/*订单号*/
        sr.REVIEW_ID,/*回访单id*/
        DATE_FORMAT(log.CREATED_DATE,'%m-%d'),/*交车纪念日*/
        sr.LAST_REVIEW_TIME,/*上次回访时间*/
        sr.PLAN_REVIEW_TIME/*下次回访时间*/
        FROM orc.t_orc_ve_bu_sale_order_to_c c
        inner join orc.t_orc_ve_bu_sale_order_log log on log.SALE_ORDER_STATE='800' and
        c.SALE_ORDER_ID=log.SALE_ORDER_ID
        left join csc.t_sac_clue_info_dlr clue on clue.CUST_ID = c.BUY_CUST_ID
        left join csc.t_sac_review sr on sr.REVIEW_ID = clue.REVIEW_ID

        where 1=1
        <!-- 历史交车时间跟明天同月同日 -->
        and DATE_FORMAT(log.CREATED_DATE,'%m-%d') = DATE_FORMAT(DATE_ADD(NOW(),INTERVAL 1 DAY),'%m-%d')
        <!-- 排除交车时间为今天 -->
        <!-- and DATE_FORMAT(log.CREATED_DATE,'%Y-%m-%d') != DATE_FORMAT(now(),'%Y-%m-%d')-->
        <!-- 排除上次回访时间为今天 -->
        and (DATE_FORMAT(sr.LAST_REVIEW_TIME,'%Y-%m-%d') != DATE_FORMAT(now(),'%Y-%m-%d') or sr.LAST_REVIEW_TIME is
        null)
        <!-- 排除下次跟进时间为今天 -->
        and (DATE_FORMAT(sr.PLAN_REVIEW_TIME,'%Y-%m-%d') != DATE_FORMAT(now(),'%Y-%m-%d') or sr.PLAN_REVIEW_TIME is
        null)
    </select>
    <select id="queryTwoReviewId" resultType="java.util.Map">
        SELECT c.SALE_ORDER_CODE,/*订单号*/
        sr.REVIEW_ID,/*回访单id*/
        DATE_FORMAT(log.CREATED_DATE,'%m-%d'),/*交车纪念日*/
        sr.LAST_REVIEW_TIME,/*上次回访时间*/
        sr.PLAN_REVIEW_TIME/*下次回访时间*/
        FROM orc.t_orc_ve_bu_sale_order_to_c c
        inner join orc.t_orc_ve_bu_sale_order_log log on log.SALE_ORDER_STATE='800' and
        c.SALE_ORDER_ID=log.SALE_ORDER_ID
        left join csc.t_sac_clue_info_dlr clue on clue.CUST_ID = c.BUY_CUST_ID
        left join csc.t_sac_review sr on sr.REVIEW_ID = clue.REVIEW_ID

        where 1=1
        <!-- 交车时间据今天前两天 -->
        AND DATE_FORMAT(log.CREATED_DATE,'%Y-%m-%d') = DATE_FORMAT(DATE_SUB(now(),INTERVAL 2 DAY),'%Y-%m-%d')
    </select>

    <update id="updateSacReview">
        UPDATE csc.t_sac_review
        set PLAN_REVIEW_TIME=#{param.planReviewTime},
            OVER_REVIEW_TIME=#{param.overReviewTime},
            COLUMN15=#{param.followReason}
        where REVIEW_ID = #{param.reviewId}
    </update>

    <!-- clue 更新 -->
    <update id="updateClueInfoDlr">
        update adp_leads.t_sac_clue_info_dlr
        set LAST_UPDATED_DATE=now(),
        MODIFIER=#{userName},
        REVIEW_PERSON_NAME =#{empName},
        REVIEW_PERSON_ID =#{userId}
        where ID in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateReview">
        update t_sac_review
        set LAST_UPDATED_DATE=now(),
        MODIFIER=#{userName},
        REVIEW_PERSON_NAME =#{empName},
        REVIEW_PERSON_ID =#{empId}
        where REVIEW_ID in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item.reviewId}
        </foreach>
    </update>

    <select id="departClueTransfer" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
        r.INFO_CHAN_M_NAME,
        r.INFO_CHAN_D_CODE,
        r.INFO_CHAN_D_NAME,
        r.INFO_CHAN_DD_CODE,
        r.INFO_CHAN_DD_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        c1.CUST_ID,
        c1.CUST_NAME as custName,
        c1.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        c1.CREATED_DATE as createdDate,
        c1.COLUMN20,
        r.CREATED_NAME as createdName,
        c1.MANAGE_LABEL_CODE,
        c1.MANAGE_LABEL_NAME,
        r.COLUMN14,
        i.attr83,
        ifnull(r.COLUMN15,'定期跟进') followReason
        from t_sac_clue_info_dlr c1
        left JOIN t_sac_review_his r on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
        left JOIN mp.t_usc_mdm_org_employee e on e.USER_ID=c1.REVIEW_PERSON_ID

        where

        c1.STATUS_CODE='10'
        <if test="param.statusCodes != null and param.statusCodes != ''">
            AND c1.STATUS_CODE = #{param.statusCodes}
        </if>
        <if test="param.attr83 != null and ''!= param.attr83 ">
            <![CDATA[AND instr(i.attr83,#{param.attr83})>0 ]]>
        </if>
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
            and r.org_code IN
            <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
            and c1.MANAGE_LABEL_CODE IN
            <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.orgCode !=null and param.orgCode != ''">
            AND r.org_code = #{param.orgCode}
        </if>
        <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
            AND c1.FIRST_REVIEW_TIME is null
        </if>
        <if test="param.column14 !=null and param.column14 != ''">
            AND r.COLUMN14 = #{param.column14}
        </if>
        <if test="param.planBuyDate !=null and param.planBuyDate !=''">
            and r.COLUMN5=#{param.planBuyDate}
        </if>
        <if test="param.gender !=null and param.gender != ''">
            AND r.GENDER = #{param.gender}
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 = #{param.businessHeatCode}
        </if>
        <if test="param.beyondDay != null and ''!= param.beyondDay">
            <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
        </if>
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
            <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
        </if>
        <if test="param.assignEndTime != null and ''!= param.assignEndTime">
            <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
            <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
        </if>
        <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
            <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
            <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
        </if>
        <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
            <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.sendStartTime != null and ''!= param.sendStartTime">
            <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
        </if>
        <if test="param.sendEndTime != null and ''!= param.sendEndTime">
            <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.createdStartTime != null and ''!= param.createdStartTime">
            <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
        </if>
        <if test="param.createdEndTime != null and ''!= param.createdEndTime">
            <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.billType != null and ''!= param.billType ">
            AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
            AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.channelName != null and ''!= param.channelName ">
            AND r.CHANNEL_NAME = #{param.channelName}
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            AND r.review_person_name = #{param.reviewPersonName}
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.isCome != null and ''!= param.isCome">
            AND r.is_come = #{param.isCome}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
            AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
            AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
            AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test='param.isPvSend != null and "1"== param.isPvSend'>
            AND r.SEND_TIME IS NOT NULL
        </if>
        <if test='param.isPvSend != null and "0"== param.isPvSend'>
            AND ifnull(r.SEND_TIME,'')=''
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
            AND r.REVIEW_STATUS = #{param.reviewStatus}
        </if>
        <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
            AND r.ASSIGN_STATUS = #{param.assignStatus1}
            AND r.LAST_REVIEW_TIME is not null
            AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
        </if>
        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        <if test='param.isOverTime != null and "1"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
        </if>
        <if test='param.isOverTime != null and "0"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
        </if>
        <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
            AND r.review_person_name IN
            <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.BeginTime != null and ''!= param.BeginTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
        </if>
        <if test="param.EndTime != null and ''!= param.EndTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
        </if>

        GROUP BY R.PHONE
        <if test="param.flag !=null and param.flag == '1'.toString">
            ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
        </if>
        <if test="param.flag ==null or param.flag == '' or param.flag != '1'.toString">
            ORDER BY R.LAST_UPDATED_DATE DESC
        </if>
            limit #{param.pageNo},#{param.pageSize}
    </select>

    <select id="departClueTransferNoPage" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
        r.INFO_CHAN_M_NAME,
        r.INFO_CHAN_D_CODE,
        r.INFO_CHAN_D_NAME,
        r.INFO_CHAN_DD_CODE,
        r.INFO_CHAN_DD_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        c1.CUST_ID,
        c1.CUST_NAME as custName,
        c1.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        c1.CREATED_DATE as createdDate,
        c1.COLUMN20,
        r.CREATED_NAME as createdName,
        c1.MANAGE_LABEL_CODE,
        c1.MANAGE_LABEL_NAME,
        r.COLUMN14,
        i.attr83,
        ifnull(r.COLUMN15,'定期跟进') followReason
        from t_sac_clue_info_dlr c1
        left JOIN t_sac_review_his r on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
        left JOIN mp.t_usc_mdm_org_employee e on e.USER_ID=c1.REVIEW_PERSON_ID
        where
        c1.STATUS_CODE='10'
        <if test="param.statusCodes != null and param.statusCodes != ''">
            AND c1.STATUS_CODE = #{param.statusCodes}
        </if>
        <if test="param.attr83 != null and ''!= param.attr83 ">
            <![CDATA[AND instr(i.attr83,#{param.attr83})>0 ]]>
        </if>
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
            and r.org_code IN
            <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
            and c1.MANAGE_LABEL_CODE IN
            <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.orgCode !=null and param.orgCode != ''">
            AND r.org_code = #{param.orgCode}
        </if>
        <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
            AND c1.FIRST_REVIEW_TIME is null
        </if>
        <if test="param.column14 !=null and param.column14 != ''">
            AND r.COLUMN14 = #{param.column14}
        </if>
        <if test="param.planBuyDate !=null and param.planBuyDate !=''">
            and r.COLUMN5=#{param.planBuyDate}
        </if>
        <if test="param.gender !=null and param.gender != ''">
            AND r.GENDER = #{param.gender}
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 = #{param.businessHeatCode}
        </if>
        <if test="param.beyondDay != null and ''!= param.beyondDay">
            <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
        </if>
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
            <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
        </if>
        <if test="param.assignEndTime != null and ''!= param.assignEndTime">
            <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
            <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
        </if>
        <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
            <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
            <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
        </if>
        <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
            <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.sendStartTime != null and ''!= param.sendStartTime">
            <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
        </if>
        <if test="param.sendEndTime != null and ''!= param.sendEndTime">
            <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.createdStartTime != null and ''!= param.createdStartTime">
            <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
        </if>
        <if test="param.createdEndTime != null and ''!= param.createdEndTime">
            <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.billType != null and ''!= param.billType ">
            AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
            AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.channelName != null and ''!= param.channelName ">
            AND r.CHANNEL_NAME = #{param.channelName}
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            AND r.review_person_name = #{param.reviewPersonName}
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.isCome != null and ''!= param.isCome">
            AND r.is_come = #{param.isCome}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
            AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
            AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
            AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test='param.isPvSend != null and "1"== param.isPvSend'>
            AND r.SEND_TIME IS NOT NULL
        </if>
        <if test='param.isPvSend != null and "0"== param.isPvSend'>
            AND ifnull(r.SEND_TIME,'')=''
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
            AND r.REVIEW_STATUS = #{param.reviewStatus}
        </if>
        <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
            AND r.ASSIGN_STATUS = #{param.assignStatus1}
            AND r.LAST_REVIEW_TIME is not null
            AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
        </if>
        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        <if test='param.isOverTime != null and "1"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
        </if>
        <if test='param.isOverTime != null and "0"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
        </if>
        <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
            AND r.review_person_name IN
            <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.BeginTime != null and ''!= param.BeginTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
        </if>
        <if test="param.EndTime != null and ''!= param.EndTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
        </if>

        GROUP BY R.PHONE
        <if test="param.flag !=null and param.flag == '1'.toString">
            ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
        </if>
        <if test="param.flag ==null or param.flag == '' or param.flag != '1'.toString">
            ORDER BY R.LAST_UPDATED_DATE DESC
        </if>
            LIMIT #{param.pageNo}, #{param.pageSize}
    </select>

    <select id="departClueTransferCount" resultType="java.lang.Long">
        select
        count(DISTINCT R.PHONE)
        from t_sac_clue_info_dlr c1
        left JOIN t_sac_review_his r on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
--         left JOIN mp.t_usc_mdm_org_employee e on e.USER_ID=c1.REVIEW_PERSON_ID
        where
        c1.STATUS_CODE='10'
        <if test="param.statusCodes != null and param.statusCodes != ''">
            AND c1.STATUS_CODE = #{param.statusCodes}
        </if>
        <if test="param.attr83 != null and ''!= param.attr83 ">
            <![CDATA[AND instr(i.attr83,#{param.attr83})>0 ]]>
        </if>
        <if test="param.searchCondition !=null and param.searchCondition != ''">
            and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
        </if>
        <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
            and r.org_code IN
            <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.manageLabelCodeIn !=null and param.manageLabelCodeIn !=''">
            and c1.MANAGE_LABEL_CODE IN
            <foreach collection="param.manageLabelCodeIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.orgCode !=null and param.orgCode != ''">
            AND r.org_code = #{param.orgCode}
        </if>
        <if test="param.firstReviewTimeStatus !=null and param.firstReviewTimeStatus == '1'.toString">
            AND c1.FIRST_REVIEW_TIME is null
        </if>
        <if test="param.column14 !=null and param.column14 != ''">
            AND r.COLUMN14 = #{param.column14}
        </if>
        <if test="param.planBuyDate !=null and param.planBuyDate !=''">
            and r.COLUMN5=#{param.planBuyDate}
        </if>
        <if test="param.gender !=null and param.gender != ''">
            AND r.GENDER = #{param.gender}
        </if>
        <if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
            AND r.column7 = #{param.businessHeatCode}
        </if>
        <if test="param.beyondDay != null and ''!= param.beyondDay">
            <![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
        </if>
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
            <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
        </if>
        <if test="param.assignEndTime != null and ''!= param.assignEndTime">
            <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
            <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
            <![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
        </if>
        <if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
            <![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
            <![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
        </if>
        <if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
            <![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
            <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.sendStartTime != null and ''!= param.sendStartTime">
            <![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
        </if>
        <if test="param.sendEndTime != null and ''!= param.sendEndTime">
            <![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.createdStartTime != null and ''!= param.createdStartTime">
            <![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
        </if>
        <if test="param.createdEndTime != null and ''!= param.createdEndTime">
            <![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.billType != null and ''!= param.billType ">
            AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
            AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
            AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.channelName != null and ''!= param.channelName ">
            AND r.CHANNEL_NAME = #{param.channelName}
        </if>
        <if test="param.custName != null and ''!= param.custName ">
            <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
            <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            AND r.review_person_name = #{param.reviewPersonName}
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
            AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.isCome != null and ''!= param.isCome">
            AND r.is_come = #{param.isCome}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
            AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
            AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
            AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test='param.isPvSend != null and "1"== param.isPvSend'>
            AND r.SEND_TIME IS NOT NULL
        </if>
        <if test='param.isPvSend != null and "0"== param.isPvSend'>
            AND ifnull(r.SEND_TIME,'')=''
        </if>
        <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
            <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
            AND r.REVIEW_STATUS = #{param.reviewStatus}
        </if>
        <if test="param.assignStatus1 != null and ''!= param.assignStatus1 ">
            AND r.ASSIGN_STATUS = #{param.assignStatus1}
            AND r.LAST_REVIEW_TIME is not null
            AND DATE_FORMAT(r.OVER_REVIEW_TIME ,'%Y-%m-%d') >DATE_FORMAT(now(),'%Y-%m-%d')
        </if>
        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        <if test='param.isOverTime != null and "1"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
        </if>
        <if test='param.isOverTime != null and "0"== param.isOverTime'>
            <![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
        </if>
        <if test="param.reviewPersonNameIn != null and ''!= param.reviewPersonNameIn ">
            AND r.review_person_name IN
            <foreach collection="param.reviewPersonNameIn.split(',')" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.BeginTime != null and ''!= param.BeginTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[>=]]> DATE_FORMAT( #{param.BeginTime}, '%Y-%m-%d' )
        </if>
        <if test="param.EndTime != null and ''!= param.EndTime">
            AND DATE_FORMAT( r.CREATED_DATE, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT( #{param.EndTime}, '%Y-%m-%d' )
        </if>
    </select>

    <select id="findClueReview" resultType="java.util.Map">
         select
                    r.REVIEW_ID as reviewId,
                    r.ORG_CODE as orgCode,
                    r.ORG_NAME as orgName,
                    r.BILL_TYPE as billType,
                    r.BILL_TYPE_NAME as billTypeName,
                    r.BUSINESS_TYPE as businessType,
                    r.BUSINESS_TYPE_NAME as businessTypeName,
                    r.INFO_CHAN_M_CODE,
                    r.INFO_CHAN_M_NAME,
                    r.INFO_CHAN_D_CODE,
                    r.INFO_CHAN_D_NAME,
                    r.INFO_CHAN_DD_CODE,
                    r.INFO_CHAN_DD_NAME,
                    r.CHANNEL_CODE,
                    r.CHANNEL_NAME,
                    r.BILL_CODE as billCode,
                    date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
                    date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
                    date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
                    r.IS_COME,
                    r.REVIEW_TIME as reviewTime,
                    r.LAST_REVIEW_TIME as lastReviewTime,
                    r.OVER_REVIEW_TIME as overReviewTime,
                    r.ASSIGN_STATUS as assignStatus,
                    r.ASSIGN_STATUS_NAME as assignStatusName,
                    r.ASSIGN_TIME as assignTime,
                    r.ASSIGN_PERSON_ID as assignPersonId,
                    r.ASSIGN_PERSON_NAME as assignPersonName,
                    r.REVIEW_PERSON_ID as reviewPersonId,
                    r.REVIEW_PERSON_NAME as reviewPersonName,
                    r.REVIEW_DESC as reviewDesc,
                    r.REVIEW_STATUS as reviewStatus,
                    r.REVIEW_STATUS_NAME as reviewStatusName,
                    r.CUST_ID as custId,
                    r.CUST_NAME as custName,
                    r.PHONE as phone,
                    r.GENDER as gender,
                    r.GENDER_NAME as genderName,
                    r.TOUCH_STATUS as touchStatus,

        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        c1.CREATED_DATE as createdDate,
        c1.COLUMN20,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate,
        r.MANAGE_LABEL_CODE,
        r.MANAGE_LABEL_NAME,
        r.COLUMN14,
        i.attr83,
        ifnull(r.COLUMN15,'定期跟进') followReason,
        case when r.send_time is not null then '是' else '否' end as isPvSend
        from t_sac_review r
        INNER JOIN t_sac_clue_info_dlr c1 on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
        where
        r.REVIEW_STATUS in('0','1','3')

       AND r.phone =#{param.phone}

        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        GROUP BY R.PHONE
    </select>

    <select id="findClueReviewHis" resultType="java.util.Map">

        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
        r.INFO_CHAN_M_NAME,
        r.INFO_CHAN_D_CODE,
        r.INFO_CHAN_D_NAME,
        r.INFO_CHAN_DD_CODE,
        r.INFO_CHAN_DD_NAME,
        r.CHANNEL_CODE,
        r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,

        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
        r.SEND_DLR_SHORT_NAME as sendDlrShortName,
        r.SEND_TIME,
        r.INTEN_LEVEL_CODE,
        r.INTEN_LEVEL_NAME,
        r.INTEN_BRAND_CODE,
        r.INTEN_BRAND_NAME,
        r.INTEN_SERIES_CODE,
        r.INTEN_SERIES_NAME,
        r.INTEN_CAR_TYPE_CODE,
        r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        c1.CREATED_DATE as createdDate,
        c1.COLUMN20,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate,
        r.MANAGE_LABEL_CODE,
        r.MANAGE_LABEL_NAME,
        r.COLUMN14,
        i.attr83,
        c1.STATUS_CODE,
        ifnull(r.COLUMN15,'定期跟进') followReason,
        case when r.send_time is not null then '是' else '否' end as isPvSend
        from  t_sac_clue_info_dlr c1
        INNER JOIN t_sac_review_his r on r.REVIEW_ID =c1.REVIEW_ID
        LEFT JOIN t_sac_onecust_info i on i.CUST_ID=c1.CUST_ID
        where
             c1.STATUS_CODE='10'
        and  r.phone =#{param.phone}

        <if test="param.assignStatus!=null and ''!=param.assignStatus">
            AND r.ASSIGN_STATUS = #{param.assignStatus}
        </if>
        GROUP BY R.PHONE
    </select>

    <select id="getDlrCodeByCompanyId" resultType="java.lang.String">
        SELECT
            coalesce(group_concat(dlr_code), '') dlrCode
        FROM
            mp.t_usc_mdm_org_dlr
        WHERE
            COMPANY_ID = #{orgId}
    </select>
</mapper>