<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnetaskLeadInTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnetaskLeadInTemp">
        <id column="TEMP_ID" property="tempId" />
        <result column="BANCH_NO" property="banchNo" />
        <result column="DEAL_FLAG" property="dealFlag" />
        <result column="DEAL_DESC" property="dealDesc" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TEMP_ID, BANCH_NO, DEAL_FLAG, DEAL_DESC, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.tempId !=null and param.tempId !=''">and TEMP_ID=#{param.tempId}</if>
    	<if test="param.banchNo !=null and param.banchNo !=''">and BANCH_NO=#{param.banchNo}</if>
    	<if test="param.dealFlag !=null and param.dealFlag !=''">and DEAL_FLAG=#{param.dealFlag}</if>
    	<if test="param.dealDesc !=null and param.dealDesc !=''">and DEAL_DESC=#{param.dealDesc}</if>
    	<if test="param.extendJson !=null and param.extendJson !=''">and EXTEND_JSON=#{param.extendJson}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 任务导入临时表 信息查询 -->
	<select id="querySacOnetaskLeadInTemp" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from 
    	<if test="param.sence != null and param.sence == 'temp'.toString()">t_sac_onetask_lead_in_temp</if>
		<if test="param.sence != null and param.sence == 'error'.toString()">t_sac_onetask_lead_in_temp_l</if>
		<if test="param.sence != null and param.sence == 'correct'.toString()">t_sac_onetask_lead_in_temp_h</if>
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<!-- 任务导入临时表 信息删除（物理删除） -->
	<delete id="deleteSacOnetaskLeadInTemp">
		DELETE 
		FROM
			t_sac_onetask_lead_in_temp
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 任务导入临时表 信息新增 -->
	<insert id="createSacOnetaskLeadInTemp">
		insert into t_sac_onetask_lead_in_temp(<include refid="Base_Column_List"></include>)
		value(
        	#{param.tempId},
			#{param.banchNo},
			#{param.dealFlag},
			#{param.dealDesc},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 任务导入临时表 信息更新 -->
	<update id="updateSacOnetaskLeadInTemp">
		update t_sac_onetask_lead_in_temp  set 
			<!-- 更新列表 -->
			<if test="param.banchNo !=null and param.banchNo !=''">BANCH_NO=#{param.banchNo},</if>
			<if test="param.dealFlag !=null and param.dealFlag !=''">DEAL_FLAG=#{param.dealFlag},</if>
			<if test="param.dealDesc !=null and param.dealDesc !=''">DEAL_DESC=#{param.dealDesc},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.tempId !=null and param.tempId !=''">and TEMP_ID=#{param.tempId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
	
	<!-- 保存任务信息临时表 -->
	<insert id="insertTaskImport">
		insert into t_sac_onetask_lead_in_temp(<include refid="Base_Column_List"></include>)
		value
		<foreach collection="dataList" item="param" index="index" separator=",">
		(
        	uuid(),
			#{param.banchNo},
			'0',
			#{param.dealDesc},
			#{param.extendsJson},
			#{param.taskId},
			#{param.taskType},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
		</foreach>
	</insert>
	
	<!-- 查询任务信息临时表否存在待处理数据 -->
	<select id="queryTaskImportExist" resultType="java.util.Map">
		select 1 from
		t_sac_onetask_lead_in_temp a where a.DEAL_FLAG = '0'
		limit 1
	</select>
	
	<!-- 更新任务信息临时表 -->
	<update id="updateBanchNo">
		update t_sac_onetask_lead_in_temp set
		BANCH_NO = #{banchNo},
		DEAL_FLAG = '1'
		where DEAL_FLAG = '0' limit 1000
	</update>
	
	<!-- 获取任务信息临时表待处理数据1000条 -->
	<select id="queryTaskImportData" resultType="java.util.Map">
		SELECT
		*
		FROM
		t_sac_onetask_lead_in_temp a
		WHERE
		a.BANCH_NO = #{banchNo}
		AND a.DEAL_FLAG = '1'
	</select>
	
	<!-- 保存正确任务信息临时表 -->
	<insert id="saveSussess">
		insert into t_sac_onetask_lead_in_temp_h(<include refid="Base_Column_List"></include>)
		value(
        	#{param.tempId},
			#{param.banchNo},
			#{param.dealFlag},
			#{param.dealDesc},
			#{param.extendJson},
			#{param.taskId},
			#{param.taskType},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 保存错误任务信息临时表 -->
	<insert id="saveFaile">
		insert into t_sac_onetask_lead_in_temp_l(<include refid="Base_Column_List"></include>)
		value(
        	#{param.tempId},
			#{param.banchNo},
			#{param.dealFlag},
			#{param.dealDesc},
			#{param.extendJson},
			#{param.taskId},
			#{param.taskType},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 删除导入任务信息临时表 -->
	<delete id="deleteTem">
		DELETE
		FROM
		t_sac_onetask_lead_in_temp a
		WHERE
		a.BANCH_NO = #{param.banchNo}
		AND a.DEAL_FLAG = '1';
	</delete>
	
	<!-- 获取线索信息 -->
	<select id="reviewList" resultType="java.util.Map">
		SELECT
			REVIEW_ID,
			REVIEW_PERSON_ID,
			REVIEW_PERSON_NAME,
			INTEN_LEVEL_CODE,
			INTEN_LEVEL_NAME,
			INTEN_CAR_TYPE_NAME,
			COLUMN6 as planBuyDateName,
			COLUMN7 as businessHeatCode,
			COLUMN8 as businessHeatName,
			CHANNEL_NAME,
			GENDER,
			GENDER_NAME,
			ORG_CODE,
			ORG_NAME,
			BILL_CODE,
			CUST_ID,
			CUST_NAME,
			PHONE,
			<![CDATA[CASE WHEN CREATED_DATE <= NOW( ) THEN CONCAT( ( TIMESTAMPDIFF( HOUR, CREATED_DATE, NOW( ) ) ) ) ELSE '0' END beyondTimes]]>
		FROM
			t_sac_review 
		WHERE
			1 = 1 
			AND REVIEW_STATUS IN ( '0', '1' )
			AND INSTR(ORG_CODE,"HOST" ) <![CDATA[ < ]]> 1
			AND COLUMN19 IS NULL
			<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID=#{param.reviewId}</if>
			<if test="param.assignStatus !=null and param.assignStatus !=''">and ASSIGN_STATUS=#{param.assignStatus}</if>
			<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and REVIEW_PERSON_ID=#{param.reviewPersonId}</if>
			<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and REVIEW_PERSON_NAME=#{param.reviewPersonName}</if>
			<if test="param.orgCode !=null and param.orgCode !=''">and ORG_CODE=#{param.orgCode}</if>
			<if test="param.orgName !=null and param.orgName !=''">and ORG_NAME=#{param.orgName}</if>
			<if test="param.billCode !=null and param.billCode !=''">and BILL_CODE=#{param.billCode}</if>
			<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
			<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
			<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
	</select>
	<select id="reviewListForTask" resultType="java.util.Map">
		SELECT
		REVIEW_ID,
		REVIEW_PERSON_ID,
		REVIEW_PERSON_NAME,
		INTEN_LEVEL_CODE,
		INTEN_LEVEL_NAME,
		INTEN_CAR_TYPE_NAME,
		COLUMN6 as planBuyDateName,
		COLUMN7 as businessHeatCode,
		COLUMN8 as businessHeatName,
		CHANNEL_NAME,
		GENDER,
		GENDER_NAME,
		ORG_CODE,
		ORG_NAME,
		BILL_CODE,
		CUST_ID,
		CUST_NAME,
		PHONE,
		LAST_UPDATED_DATE,
		<![CDATA[CASE WHEN CREATED_DATE <= NOW( ) THEN CONCAT( ( TIMESTAMPDIFF( HOUR, CREATED_DATE, NOW( ) ) ) ) ELSE '0' END beyondTimes]]>
		FROM
		t_sac_review
		WHERE
		1 = 1
		AND INSTR(ORG_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID=#{param.reviewId}</if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and REVIEW_PERSON_ID=#{param.reviewPersonId}</if>
		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and REVIEW_PERSON_NAME=#{param.reviewPersonName}</if>
		<if test="param.orgCode !=null and param.orgCode !=''">and ORG_CODE=#{param.orgCode}</if>
		<if test="param.orgName !=null and param.orgName !=''">and ORG_NAME=#{param.orgName}</if>
		<if test="param.billCode !=null and param.billCode !=''">and BILL_CODE=#{param.billCode}</if>
		<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
		union
		SELECT
		REVIEW_ID,
		REVIEW_PERSON_ID,
		REVIEW_PERSON_NAME,
		INTEN_LEVEL_CODE,
		INTEN_LEVEL_NAME,
		INTEN_CAR_TYPE_NAME,
		COLUMN6 as planBuyDateName,
		COLUMN7 as businessHeatCode,
		COLUMN8 as businessHeatName,
		CHANNEL_NAME,
		GENDER,
		GENDER_NAME,
		ORG_CODE,
		ORG_NAME,
		BILL_CODE,
		CUST_ID,
		CUST_NAME,
		PHONE,
		LAST_UPDATED_DATE,
		<![CDATA[CASE WHEN CREATED_DATE <= NOW( ) THEN CONCAT( ( TIMESTAMPDIFF( HOUR, CREATED_DATE, NOW( ) ) ) ) ELSE '0' END beyondTimes]]>
		FROM
		t_sac_review_his
		WHERE
		1 = 1
		AND INSTR(ORG_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID=#{param.reviewId}</if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and REVIEW_PERSON_ID=#{param.reviewPersonId}</if>
		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and REVIEW_PERSON_NAME=#{param.reviewPersonName}</if>
		<if test="param.orgCode !=null and param.orgCode !=''">and ORG_CODE=#{param.orgCode}</if>
		<if test="param.orgName !=null and param.orgName !=''">and ORG_NAME=#{param.orgName}</if>
		<if test="param.billCode !=null and param.billCode !=''">and BILL_CODE=#{param.billCode}</if>
		<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
		order by LAST_UPDATED_DATE desc
	</select>
	<select id="queryPhoneBySmartId" resultType="java.util.Map">
		SELECT t.SMART_ID,T.PHONE FROM t_sac_onecust_info t WHERE (t.SMART_ID in (
		<foreach collection="dataList" item="param" index="index" separator=",">
			#{param.smartId}
		</foreach>
		) or t.PHONE in (
		<foreach collection="dataList" item="param" index="index" separator=",">
			#{param.phone}
		</foreach>))
	</select>

    <select id="queryClueInfoForTask" resultType="com.ly.adp.csc.entities.dto.ClueInfoDto">
		select
		cust_id,
		cust_name,
		review_id,
		review_person_id,
		review_person_name,
		server_order,
		gender_code,
		gender_name,
		dlr_code,
		dlr_short_name,
		inten_level_code,
		inten_level_name,
		channel_name,
		inten_car_type_name,
		column1 as planBuyDateName,
		column5 as businessHeatCode,
		column6 as businessHeatName,
		phone,
		last_updated_date
		from
		t_sac_clue_info_dlr t1
		where
		1 = 1
		and instr( dlr_code, "HOST" ) <![CDATA[ < ]]> 1
		<if test = "phone !=null and phone !=''" >
			and phone = #{phone}
		</if>
	</select>

    <select id="queryClueInfoForTasks" resultType="com.ly.adp.csc.entities.dto.ClueInfoDto">
		select
		cust_id,
		cust_name,
		review_id,
		review_person_id,
		review_person_name,
		server_order,
		gender_code,
		gender_name,
		dlr_code,
		dlr_short_name,
		inten_level_code,
		inten_level_name,
		channel_name,
		inten_car_type_name,
		column1 as planBuyDateName,
		column5 as businessHeatCode,
		column6 as businessHeatName,
		phone,
		last_updated_date,
		column19 as dccFlag
		from
		t_sac_clue_info_dlr t1
		where
		1 = 1
		and instr( dlr_code, "HOST" ) <![CDATA[ < ]]> 1
		<if test = "phoneList !=null" >
			and phone in
			<foreach collection="phoneList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

    <select id="checkTaskPhone" resultType="boolean">
		SELECT
			EXISTS (
				SELECT 1
				FROM t_sac_onetask_info i
						 LEFT JOIN t_sac_onetask_detail d ON i.TASK_ID = d.TASK_ID
				WHERE
					i.BUSS_END_TIME > NOW()
				  AND i.TASK_STATE_CODE = '3'
				  AND JSON_UNQUOTE(JSON_EXTRACT(d.EXTEND_JSON, '$.phone')) = #{phone}
			) AS has_data
	</select>

    <select id="queryUserStatus" resultType="com.ly.adp.csc.entities.dto.UserStatusDTO">
		SELECT user_id, user_status
		FROM mp.t_usc_mdm_org_employee
		WHERE user_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
</mapper>
