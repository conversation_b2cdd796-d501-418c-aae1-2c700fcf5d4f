<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.MdmOrgEmployeeMapper">

    <select id="queryCallPerson" resultType="com.ly.mp.csc.clue.entities.dto.ReviewPersonDTO">
        select
            e.user_id as reviewPersonId,
            e.emp_name as reviewPersonName,
            b.agent_company_code,
            e.user_status
        from mp.t_usc_mdm_org_employee e
        left join mp.t_usc_mdm_agent_company b on e.dlr_id = b.agent_company_id
        left join mp.t_usc_mdm_org_dlr a on a.company_id = b.agent_company_id
        where a.dlr_code = #{dlrCode}
          and e.station_id = 'smart_bm_0075_C'
          and e.user_status IN ('1', '3')
    </select>

    <select id="findDlrsWithCallPerson" resultType="java.lang.String">
        SELECT dlr_code
        FROM mp.t_usc_mdm_org_dlr a
        WHERE a.dlr_code IN
        <foreach collection="list" item="dlrCode" open="(" separator="," close=")">
            #{dlrCode}
        </foreach>
        AND EXISTS (
        SELECT 1
        FROM mp.t_usc_mdm_org_employee e
        JOIN mp.t_usc_mdm_agent_company b ON e.dlr_id = b.agent_company_id
        WHERE a.company_id = b.agent_company_id
        AND e.station_id = 'smart_bm_0075_C'
        AND e.user_status IN ('1', '3')
        )
    </select>
</mapper>