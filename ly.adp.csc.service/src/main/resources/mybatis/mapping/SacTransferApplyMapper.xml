<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacTransferApplyMapper">


	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacTransferApply">
		<id column="APPLY_ID" property="applyId" />
		<result column="BILL_CODE" property="billCode" />
		<result column="CUST_ID" property="custId" />
		<result column="CUST_NAME" property="custName" />
		<result column="PHONE" property="phone" />
		<result column="OUT_DLR_CODE" property="outDlrCode" />
		<result column="OUT_DLR_NAME" property="outDlrName" />
		<result column="IN_DLR_CODE" property="inDlrCode" />
		<result column="IN_DLR_NAME" property="inDlrName" />
		<result column="APPLY_DESC" property="applyDesc" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
	    <result column="COLUMN1" jdbcType="VARCHAR" property="column1" />
	    <result column="COLUMN2" jdbcType="VARCHAR" property="column2" />
	    <result column="COLUMN3" jdbcType="VARCHAR" property="column3" />
	    <result column="COLUMN4" jdbcType="VARCHAR" property="column4" />
	    <result column="COLUMN5" jdbcType="VARCHAR" property="column5" />
	    <result column="COLUMN6" jdbcType="VARCHAR" property="column6" />
	    <result column="COLUMN7" jdbcType="VARCHAR" property="column7" />
	    <result column="COLUMN8" jdbcType="VARCHAR" property="column8" />
	    <result column="COLUMN9" jdbcType="VARCHAR" property="column9" />
	    <result column="COLUMN10" jdbcType="VARCHAR" property="column10" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.APPLY_ID, t1.BILL_CODE, t1.CUST_ID, t1.CUST_NAME, t1.PHONE
		, t1.OUT_DLR_CODE, t1.OUT_DLR_NAME, t1.IN_DLR_CODE, t1.IN_DLR_NAME
		, t1.APPLY_DESC ,t1.SOURCE
		, t1.EXTENDS_JSON, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE
		, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
		, t1.COLUMN1, t1.COLUMN2, t1.COLUMN3, t1.COLUMN4, t1.COLUMN5
		, t1.COLUMN6, t1.COLUMN7, t1.COLUMN8, t1.COLUMN9, t1.COLUMN10
	</sql>
	
	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.applyId !=null and param.applyId !=''">and t1.APPLY_ID=#{param.applyId}</if>
		<if test="param.billCode !=null and param.billCode !=''">and t1.BILL_CODE=#{param.billCode}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.outDlrCode !=null and param.outDlrCode !=''">and t1.OUT_DLR_CODE=#{param.outDlrCode}</if>
		<if test="param.outDlrName !=null and param.outDlrName !=''">and t1.OUT_DLR_NAME=#{param.outDlrName}</if>
		<if test="param.inDlrCode !=null and param.inDlrCode !=''">and t1.IN_DLR_CODE=#{param.inDlrCode}</if>
		<if test="param.inDlrName !=null and param.inDlrName !=''">and t1.IN_DLR_NAME=#{param.inDlrName}</if>
		<if test="param.applyDesc !=null and param.applyDesc !=''">and t1.APPLY_DESC=#{param.applyDesc}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.column1 !=null and param.column1 !=''">and t1.COLUMN1=#{param.column1}</if>
		<if test="param.column2 !=null and param.column2 !=''">and t1.COLUMN2=#{param.column2}</if>
		<if test="param.column3 !=null and param.column3 !=''">and t1.COLUMN3=#{param.column3}</if>
		<if test="param.column4 !=null and param.column4 !=''">and t1.COLUMN4=#{param.column4}</if>
		<if test="param.column5 !=null and param.column5 !=''">and t1.COLUMN5=#{param.column5}</if>
		<if test="param.column6 !=null and param.column6 !=''">and t1.COLUMN6=#{param.column6}</if>
		<if test="param.column7 !=null and param.column7 !=''">and t1.COLUMN7=#{param.column7}</if>
		<if test="param.column8 !=null and param.column8 !=''">and t1.COLUMN8=#{param.column8}</if>
		<if test="param.column9 !=null and param.column9 !=''">and t1.COLUMN9=#{param.column9}</if>
		<if test="param.column10 !=null and param.column10 !=''">and t1.COLUMN10=#{param.column10}</if>
		
	    <if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.custName !=null and param.custName !=''">and t1.CUST_NAME like concat('%', #{param.custName}, '%') </if>
		<if test="param.phone !=null and param.phone !=''">and t1.PHONE like concat('%', #{param.phone}, '%') </if>
	</sql>
	
	<!-- 新增 -->
	<insert id="insertSacTransferApply">
		insert into t_sac_transfer_apply(
		APPLY_ID
		, BILL_CODE
		, CUST_ID
		, CUST_NAME
		, PHONE
		, OUT_DLR_CODE
		, OUT_DLR_NAME
		, IN_DLR_CODE
		, IN_DLR_NAME
		, APPLY_DESC
		, SOURCE
		, EXTENDS_JSON
		, OEM_ID
		, GROUP_ID
		, CREATOR
		, CREATED_NAME
		, CREATED_DATE
		, MODIFIER
		, MODIFY_NAME
		, LAST_UPDATED_DATE
		, IS_ENABLE
		, UPDATE_CONTROL_ID

        ,COLUMN1
        ,COLUMN2
        ,COLUMN3
        ,COLUMN4
        ,COLUMN5
        ,COLUMN6
        ,COLUMN7
        ,COLUMN8
        ,COLUMN9
        ,COLUMN10
		) values (
		#{param.applyId}
		, #{param.billCode}
		, #{param.custId}
		, #{param.custName}
		, #{param.phone}
		, #{param.outDlrCode}
		, #{param.outDlrName}
		, #{param.inDlrCode}
		, #{param.inDlrName}
		, #{param.applyDesc}
		, #{param.source}
		, #{param.extendsJson}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		,#{param.column1}
		,#{param.column2}
		,#{param.column3}
		,#{param.column4}
		,#{param.column5}
		,#{param.column6}
		,#{param.column7}
		,#{param.column8}
		,#{param.column9}
		,#{param.column10}
		)
	</insert>

	<!-- 修改 -->
	<update id="updateSacTransferApply">
		update t_sac_transfer_apply
		<set>
		LAST_UPDATED_DATE=sysdate(),
		UPDATE_CONTROL_ID=uuid(),
			<if test="param.applyId !=null and param.applyId !=''"> APPLY_ID=#{param.applyId},</if>
			<if test="param.billCode !=null and param.billCode !=''"> BILL_CODE=#{param.billCode},</if>
			<if test="param.custId !=null and param.custId !=''"> CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''"> CUST_NAME=#{param.custName},</if>
			<if test="param.phone !=null and param.phone !=''"> PHONE=#{param.phone},</if>
			<if test="param.outDlrCode !=null and param.outDlrCode !=''"> OUT_DLR_CODE=#{param.outDlrCode},</if>
			<if test="param.outDlrName !=null and param.outDlrName !=''"> OUT_DLR_NAME=#{param.outDlrName},</if>
			<if test="param.inDlrCode !=null and param.inDlrCode !=''"> IN_DLR_CODE=#{param.inDlrCode},</if>
			<if test="param.inDlrName !=null and param.inDlrName !=''"> IN_DLR_NAME=#{param.inDlrName},</if>
			<if test="param.applyDesc !=null and param.applyDesc !=''"> APPLY_DESC=#{param.applyDesc},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId},</if>
			<if test = 'param.column1!=null'>COLUMN1 = #{param.column1},</if>
		    <if test = 'param.column2!=null'>COLUMN2 = #{param.column2},</if>
		    <if test = 'param.column3!=null'>COLUMN3 = #{param.column3},</if>
		    <if test = 'param.column4!=null'>COLUMN4 = #{param.column4},</if>
		    <if test = 'param.column5!=null'>COLUMN5 = #{param.column5},</if>
		    <if test = 'param.column6!=null'>COLUMN6 = #{param.column6},</if>
		    <if test = 'param.column7!=null'>COLUMN7 = #{param.column7},</if>
		    <if test = 'param.column8!=null'>COLUMN8 = #{param.column8},</if>
		    <if test = 'param.column9!=null'>COLUMN9 = #{param.column9},</if>
		    <if test = 'param.column10!=null'>COLUMN10 = #{param.column10},</if>
		</set>
		where 1=1
		and APPLY_ID=#{param.applyId}
		<if test = 'updateControlId!=null'>
		 	and UPDATE_CONTROL_ID = #{updateControlId}
		</if>
	</update>
	
	<select id="selectByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_sac_transfer_apply t1
		where 1=1
		<include refid="where_condition"></include>
	</select>
	
	<select id="selectApplyByPage" resultType="Map">
		select
		t1.APPLY_ID,
		t1.BILL_CODE,
		t1.CUST_ID,
		t1.CUST_NAME,
		t1.PHONE,
		t1.OUT_DLR_CODE,
		t1.OUT_DLR_NAME,
		t1.IN_DLR_CODE,
		t1.IN_DLR_NAME,
		t1.APPLY_DESC ,
		t1.SOURCE,
		t1.EXTENDS_JSON,
		t1.OEM_ID,
		t1.GROUP_ID,
		t1.CREATOR,
		t1.CREATED_NAME,
		t1.CREATED_DATE,
		t1.MODIFIER,
		t1.MODIFY_NAME,
		t1.LAST_UPDATED_DATE,
		t1.IS_ENABLE,
		t1.UPDATE_CONTROL_ID,
		t1.COLUMN1,
		t1.COLUMN2,
		t1.COLUMN3,
		t1.COLUMN4,
		t1.COLUMN5,
		t1.COLUMN6,
		t1.COLUMN7,
		t1.COLUMN8,
		t1.COLUMN9,
		t1.COLUMN10,
		INSERT(t1.PHONE,4,4,'****') as TMD_PHONE
		, t2.AUDIT_ID, t2.APPLY_TYPE_CODE, t2.APPLY_TYPE_NAME
		, t2.SH_PERSON_ID, t2.SH_PERSON_NAME
		, t2.SH_DESC, t2.SH_TIME
		, t2.SH_STATUS, t2.SH_STATUS_NAME,
		t3.CREATED_DATE CLUE_CREATED_DATE,
		t3.OUT_COLOR_NAME,
		t3.INNER_COLOR_NAME,
		t3.REVIEW_PERSON_NAME,
		t3.LAST_REVIEW_TIME,T3.COLUMN10 AS SMART_ID,
		t4.REVIEW_STATUS_NAME,
		t4.PLAN_REVIEW_TIME
		from t_sac_transfer_apply t1
		left join t_sac_transfer_audit t2 on t1.apply_id = t2.apply_id and t2.is_enable = 1
		LEFT JOIN t_sac_clue_info_dlr t3 ON t1.BILL_CODE = t3.SERVER_ORDER
		LEFT JOIN t_sac_review t4 on t1.BILL_CODE = t4.BILL_CODE
		where 1=1
		<if test="param.intenCarTypeName!=null and ''!=param.intenCarTypeName">
		 	AND JSON_CONTAINS(t1.EXTENDS_JSON, JSON_OBJECT('intenCarTypeName', #{param.intenCarTypeName}))
		</if>
		<if test="param.channelName!=null and ''!=param.channelName">
			AND JSON_CONTAINS(t1.EXTENDS_JSON, JSON_OBJECT('channelName', #{param.channelName}))
		</if>
		<if test="param.businessHeatCode!=null and ''!=param.businessHeatCode">
			AND JSON_CONTAINS(t1.EXTENDS_JSON, JSON_OBJECT('businessHeatCode', #{param.businessHeatCode}))
		</if>
		<if test="param.businessHeatName!=null and ''!=param.businessHeatName">
			AND JSON_CONTAINS(t1.EXTENDS_JSON, JSON_OBJECT('businessHeatName', #{param.businessHeatName}))
		</if>
		<include refid="where_condition"></include>
		<if test="param.orDlrCode !=null and param.orDlrCode !=''">
		and ( t1.IN_DLR_CODE in
			<foreach item="item" collection="param.orDlrCode.split(',')" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
		or t1.OUT_DLR_CODE IN
			<foreach item="item" collection="param.orDlrCode.split(',')" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
		)
		</if>

		<if test="param.dlrCodeLists !=null">
			and ( t1.IN_DLR_CODE in
			<foreach item="item" collection="param.dlrCodeLists" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
			or t1.OUT_DLR_CODE IN
			<foreach item="item" collection="param.dlrCodeLists" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<if test="param.shStatus !=null and param.shStatus !=''">and t2.SH_STATUS=#{param.shStatus}</if>
		<if test="param.smartId !=null and param.smartId !=''">and t3.COLUMN10=#{param.smartId}</if>
		<if test="param.custName !=null and param.custName !=''">and t1.CUST_NAME like concat('%', #{param.custName}, '%') </if>
		<if test="param.phone !=null and param.phone !=''">and t1.PHONE like concat('%', #{param.phone}, '%') </if>
		<if test="param.intenLevelName!=null and ''!=param.intenLevelName">
			AND INSTR(T1.COLUMN1,#{param.intenLevelName})
		</if>
		<if test="param.cust!=null and ''!=param.cust">
			and (t1.CUST_NAME like CONCAT('%',#{param.cust},'%') or t1.PHONE like CONCAT('%', #{param.cust},'%') )
		</if>
		<if test="param.beginDate!=null and ''!=param.beginDate">
			<![CDATA[  and date_format(t1.CREATED_DATE, '%Y-%m-%d') >= #{param.beginDate}  ]]>
		</if>
		<if test="param.endDate!=null and ''!=param.endDate">
			<![CDATA[  and date_format(t1.CREATED_DATE, '%Y-%m-%d') <= #{param.endDate}  ]]>
		</if>
		<if test="param.applyTypeCode!=null and ''!=param.applyTypeCode">
			and t2.APPLY_TYPE_CODE = #{param.applyTypeCode}
		</if>
		order by t1.CREATED_DATE desc
	</select>
	
	<select id="selectAuditByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		, t2.AUDIT_ID,
		t2.APPLY_TYPE_CODE,
		t2.APPLY_TYPE_NAME,
		t2.SH_PERSON_ID,
		t2.SH_PERSON_NAME,
		t2.SH_DESC,
		t2.SH_TIME,
		t2.SH_STATUS,
		t2.SH_STATUS_NAME
		from t_sac_transfer_apply t1
		left join t_sac_transfer_audit t2 on t1.apply_id = t2.apply_id and t2.is_enable = 1
			LEFT JOIN t_sac_clue_info_dlr t3 ON t1.PHONE = t3.PHONE 
		where 1=1
		<include refid="where_condition"></include>
		<if test="param.genderCode != null and param.genderCode != ''">
		    AND t3.GENDER_CODE = #{param.genderCode}
		</if>
		<!-- 值列表ADP_CLUE_049 -->
		<if test="param.channelName != null and param.channelName != ''">
		    AND t3.INFO_CHAN_M_NAME = #{param.channelName}
		</if>
		<if test="param.planBuyDate != null and param.planBuyDate != ''">
		    AND t3.COLUMN2 = #{param.planBuyDate}
		</if>
		<if test="param.intenLevelCode != null and param.intenLevelCode != ''">
		    AND t3.INTEN_LEVEL_CODE = #{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName != null and param.intenLevelName != ''">
		    AND t3.INTEN_LEVEL_NAME = #{param.intenLevelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.outDlrCodeList !=null  and param.outDlrCodeList !=''">
		and t1.OUT_DLR_CODE in 
		<foreach collection="param.outDlrCodeList.split(',')" index="i" item="item" open="(" separator=","  close=")">
		#{item}
		</foreach>
		</if>
		<if test="param.auditId !=null and param.auditId !=''">and t2.AUDIT_ID=#{param.auditId}</if>
		<if test="param.shPersonId !=null and param.shPersonId !=''">and t2.SH_PERSON_ID=#{param.shPersonId}</if>
		<if test="param.shStatus !=null and param.shStatus !=''">and t2.SH_STATUS=#{param.shStatus}</if>
		<if test="param.shStatusList !=null">
		and t2.SH_STATUS in 
		<foreach collection="param.shStatusList" index="i" item="item" open="(" separator=","  close=")">
		#{item}
		</foreach>
		</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">
		and ((t2.APPLY_TYPE_CODE = 1 and t1.IN_DLR_CODE = #{param.dlrCode})
			or (t2.APPLY_TYPE_CODE = 2 and t1.OUT_DLR_CODE = #{param.dlrCode})
			or t2.APPLY_TYPE_CODE = 3
		)
		</if>
		<if test="param.applyTypeCodeList != null">
		and t2.APPLY_TYPE_CODE in 
		<foreach collection="param.applyTypeCodeList" index="i" item="item" open="(" separator=","  close=")">
		#{item}
		</foreach>
		</if>
		<if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
		 AND t3.REVIEW_PERSON_ID IN
		<foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator="," close=")">
		#{item}
		</foreach>
		</if>
		order by t1.CREATED_DATE desc
	</select>

	<select id="findAgentDlrCodeList" resultType="java.lang.String">
		select
			DLR_CODE
		from mp.t_usc_mdm_org_dlr d
				 LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
				 LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
		where c2.AGENT_CODE = #{orgCode}
	</select>

	<select id="findAgentCompany" resultType="java.lang.String">
		SELECT
			DLR_CODE
		FROM
			mp.t_usc_mdm_org_dlr
		WHERE
			COMPANY_ID=#{orgId}
	</select>
</mapper>