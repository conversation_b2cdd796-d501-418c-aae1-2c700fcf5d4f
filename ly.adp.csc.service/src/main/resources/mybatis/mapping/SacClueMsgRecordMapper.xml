<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.ly.adp.csc.idal.mapper.SacClueMsgRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.ly.adp.csc.entities.SacClueMsgRecord">
        <id column="MESSAGE_ID" property="messageId"/>
        <result column="IS_READ" property="isRead"/>
        <result column="DLR_CODE" property="dlrCode"/>
        <result column="PHONE" property="phone"/>
        <result column="MESSAGE_TYPE" property="messageType"/>
        <result column="BUSI_KEYVALUE" property="busiKeyvalue"/>
        <result column="RECEIVE_EMP_ID" property="receiveEmpId"/>
        <result column="MESSAGE_CONTENT" property="messageContent"/>
        <result column="RELATION_BILL_ID" property="relationBillId"/>
        <result column="COLUMN1" property="column1"/>
        <result column="COLUMN2" property="column2"/>
        <result column="COLUMN3" property="column3"/>
        <result column="COLUMN4" property="column4"/>
        <result column="COLUMN5" property="column5"/>
        <result column="_MYCAT_OP_TIME" property="mycatOpTime"/>
        <result column="OEM_ID" property="oemId"/>
        <result column="GROUP_ID" property="groupId"/>
        <result column="OEM_CODE" property="oemCode"/>
        <result column="GROUP_CODE" property="groupCode"/>
        <result column="CREATOR" property="creator"/>
        <result column="CREATED_NAME" property="createdName"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="MODIFIER" property="modifier"/>
        <result column="MODIFY_NAME" property="modifyName"/>
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate"/>
        <result column="IS_ENABLE" property="isEnable"/>
        <result column="SDP_USER_ID" property="sdpUserId"/>
        <result column="SDP_ORG_ID" property="sdpOrgId"/>
        <result column="UPDATE_CONTROL_ID" property="updateControlId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MESSAGE_ID
        ,IS_READ,DLR_CODE,PHONE,MESSAGE_TYPE,
        CASE MESSAGE_TYPE
	WHEN '1' THEN '关键事件'
	WHEN '2' THEN '线索分配'
	WHEN '3' THEN '试乘试驾'
	WHEN '6' THEN '客户投诉'
	WHEN '7' THEN '客户投诉'
	WHEN '10' THEN '客户投诉'
	WHEN '8' THEN '战败驳回'
	WHEN '9' THEN '活动支持'
	WHEN '13' THEN '试驾取消'
	WHEN '14' THEN '划转申请'
	WHEN '15' THEN '置换服务'
	WHEN '16' THEN '金融状态变更 '
	WHEN '17' THEN '试驾车提醒'
	WHEN '18' THEN '展车提醒'
        END as messageName,BUSI_KEYVALUE,RECEIVE_EMP_ID,MESSAGE_CONTENT,RELATION_BILL_ID,EXTEND_JSON,CREATOR,CREATED_NAME,CREATED_DATE,MODIFIER,MODIFY_NAME,LAST_UPDATED_DATE,IS_ENABLE,UPDATE_CONTROL_ID
    </sql>

    <!-- 店端线索消息查询 -->
    <select id="sacMsgRecordFindInfo" resultType="map">
        select
        <include refid="Base_Column_List">
        </include>
        from t_sac_clue_msg_record
        where
        RECEIVE_EMP_ID = #{param.receiveEmpId}
        <if test="param.dlrCode != null and param.dlrCode !=''">
            and DLR_CODE = #{param.dlrCode}
        </if>
        <if test="param.messageTypeIn != null and '' != param.messageTypeIn">
            AND MESSAGE_TYPE IN
            <foreach collection="param.messageTypeIn.split(',')"
                     item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.isReadIn != null and '' != param.isReadIn">
            AND IS_READ IN
            <foreach collection="param.isReadIn.split(',')" item="item"
                     separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.messageType != null and param.messageType !=''">
            and MESSAGE_TYPE in (${param.messageType})
        </if>
        <if test="param.isRead != null and param.isRead !=''">
            and IS_READ = #{param.isRead}
        </if>
        <if test="param.relationBillId != null and param.relationBillId !=''">
            and RELATION_BILL_ID = #{param.relationBillId}
        </if>
        <!-- 时间查询 -->
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        ORDER BY CREATED_DATE DESC
    </select>

    <!-- 店端线索消息更新 -->
    <update id="updateMsgRecord" parameterType="java.util.Map">
        UPDATE
        t_sac_clue_msg_record
        SET
        <if test="param.busiKeyvalue != null and param.busiKeyvalue !=''">
            BUSI_KEYVALUE = #{param.busiKeyvalue},
        </if>
        <if test="param.isRead != null and param.isRead !=''">
            IS_READ = #{param.isRead},
        </if>
        MODIFIER=#{param.modifier},
        MODIFY_NAME = #{param.modifyName},
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        WHERE 1=1
        and DLR_CODE
        = #{param.dlrCode}
        and MESSAGE_TYPE = #{param.messageType}
        and
        RECEIVE_EMP_ID = #{param.receiveEmpId}
        <if test="param.isRead != null and param.isRead !=''">
            and IS_READ = #{param.isRead}
        </if>
        <if test="param.relationBillId != null and param.relationBillId !=''">
            and RELATION_BILL_ID = #{param.relationBillId}
        </if>
    </update>

    <!-- 店端线索消息已读 -->
    <update id="updateMsgRead" parameterType="java.util.Map">
        UPDATE
        t_sac_clue_msg_record
        SET
        <if test="param.isRead != null and param.isRead !=''">
            IS_READ = #{param.isRead},
        </if>
        MODIFIER=#{param.modifier},
        MODIFY_NAME = #{param.modifyName},
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        WHERE
        RECEIVE_EMP_ID = #{param.receiveEmpId}
        <if test="param.dlrCode != null and param.dlrCode !=''">
            and DLR_CODE = #{param.dlrCode}
        </if>
        <if test="param.messageTypeIn != null and '' != param.messageTypeIn">
            AND MESSAGE_TYPE IN
            <foreach collection="param.messageTypeIn.split(',')"
                     item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.messageType != null and param.messageType !=''">
            AND MESSAGE_TYPE IN
            <foreach collection="param.messageType.split(',')"
                     item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.relationBillId != null and param.relationBillId !=''">
            and RELATION_BILL_ID = #{param.relationBillId}
        </if>
    </update>

    <!-- 店端线索消息新增 -->
    <insert id="createMsgRecord">
        INSERT INTO `t_sac_clue_msg_record` (`MESSAGE_ID`,
                                             `IS_READ`,
                                             `DLR_CODE`,
                                             `PHONE`,
                                             `MESSAGE_TYPE`,
                                             `BUSI_KEYVALUE`,
                                             `RECEIVE_EMP_ID`,
                                             `MESSAGE_CONTENT`,
                                             `RELATION_BILL_ID`,
                                             `EXTEND_JSON`,
                                             `COLUMN1`,
                                             `OEM_ID`,
                                             `GROUP_ID`,
                                             `OEM_CODE`,
                                             `GROUP_CODE`,
                                             `CREATOR`,
                                             `CREATED_NAME`,
                                             `CREATED_DATE`,
                                             `MODIFIER`,
                                             `MODIFY_NAME`,
                                             `LAST_UPDATED_DATE`,
                                             `IS_ENABLE`,
                                             `SDP_USER_ID`,
                                             `SDP_ORG_ID`,
                                             `UPDATE_CONTROL_ID`)
        VALUES (#{param.messageId},
                '0',
                #{param.dlrCode},
                #{param.phone},
                #{param.messageType},
                '1',
                #{param.receiveEmpId},
                #{param.messageContent},
                #{param.relationBillId},
                #{param.extendsJson},
                #{param.column1},
                #{param.oemId},
                #{param.groupId},
                #{param.oemCode},
                #{param.groupCode},
                #{param.creator},
                #{param.createdName},
                now(),
                #{param.modifier},
                #{param.modifyName},
                now(),
                '1',
                '1',
                '1',
                uuid())
    </insert>

    <!-- 投诉工单表 报表查询 -->
    <select id="sacMsgRecordReport" resultType="map">
        with T1 as (
        SELECT
        msg.MESSAGE_ID,
        msg.IS_READ,
        msg.DLR_CODE,
        msg.MESSAGE_TYPE,
        msg.RECEIVE_EMP_ID,
        msg.MESSAGE_CONTENT,
        msg.RELATION_BILL_ID,
        <![CDATA[CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '5' ELSE info.TASK_STATE_CODE END TASK_STATE_CODE,]]>
        <![CDATA[CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '已结束' ELSE info.TASK_STATE_NAME END TASK_STATE_NAME,]]>
        <![CDATA[CASE WHEN TASK_REPEAT_IS = '0' THEN '否'  ELSE'是'  END taskRepeatIsName,]]>
        info.TASK_ID,
        info.TASK_TITLE,
        info.TASK_DESCRIBE,
        info.TASK_TYPE_CODE,
        info.TASK_TYPE_NAME,
        info.TASK_ATTESTATION_IS,
        info.TASK_REPEAT_IS,
        info.BUSS_TIME,
        info.BUSS_START_TIME,
        info.BUSS_END_TIME,
        info.EXTEND_JSON,
        info.CREATOR,
        info.CREATED_NAME,
        info.CREATED_DATE
        FROM t_sac_clue_msg_record msg
        LEFT JOIN t_sac_onetask_info info ON msg.RELATION_BILL_ID=info.TASK_ID
        where
        msg.IS_READ = '0'
        AND msg.DLR_CODE = #{param.dlrCode}
        AND msg.RECEIVE_EMP_ID = #{param.receiveEmpId}
        <![CDATA[AND msg.CREATED_DATE >= #{param.createdDateStart}]]>
        <![CDATA[AND msg.CREATED_DATE  < date_add(#{param.createdDateEnd}, INTERVAL 1 DAY)]]>
        )
        SELECT
        NOTICE_NUM,
        MISSION_NUM,
        PUSH_NUM,
        OVERDUE_NUM
        FROM
        (
        SELECT
        SUM(CASE WHEN T1.MESSAGE_TYPE = '5' AND T1.TASK_STATE_CODE = '3' THEN 1 ELSE 0 END) AS NOTICE_NUM,
        SUM(CASE WHEN T1.MESSAGE_TYPE = '4' AND T1.TASK_STATE_CODE = '3' THEN 1 ELSE 0 END) AS MISSION_NUM,
        SUM(CASE WHEN
        <if test="param.messageTypeIn != null and '' != param.messageTypeIn">
            T1.MESSAGE_TYPE IN
            <foreach collection="param.messageTypeIn.split(',')"
                     item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        THEN 1 ELSE 0 END) AS PUSH_NUM,
        (
        SELECT
        count(B.TASK_ID )
        FROM
        t_sac_onetask_detail A
        LEFT JOIN t_sac_onetask_info B ON B.TASK_ID = A.TASK_ID
        LEFT JOIN t_sac_clue_msg_record C ON C.RELATION_BILL_ID = B.TASK_ID
        WHERE
        1 = 1
        AND C.MESSAGE_TYPE = '4'
        AND C.DLR_CODE = #{param.dlrCode}
        AND C.RECEIVE_EMP_ID = #{param.receiveEmpId}
        AND B.TASK_STATE_CODE = '3'
        <![CDATA[AND C.CREATED_DATE >= #{param.createdDateStart}]]>
        <![CDATA[AND C.CREATED_DATE  < date_add(#{param.createdDateEnd}, INTERVAL 1 DAY) ]]>
        <![CDATA[AND ( CASE WHEN str_to_date( A.BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( A.BUSS_TIME, '%Y-%m-%d %H:%i:%s' ) THEN '1'  WHEN ifnull( A.BUSS_TIME, '' ) = ''  AND str_to_date( A.BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( NOW( ), '%Y-%m-%d %H:%i:%s' ) THEN '1' ELSE '0' END ) = "1"]]>
        ) AS OVERDUE_NUM
        FROM
        T1
        ) T2
    </select>
</mapper>