<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBuBoutiqueApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacBuBoutiqueApply">
        <id column="APPLY_ID" property="applyId" />
        <result column="APPLY_CODE" property="applyCode" />
        <result column="BOUTIQUE_CODE" property="boutiqueCode" />
        <result column="BOUTIQUE_NAME" property="boutiqueName" />
        <result column="APPLY_NUM" property="applyNum" />
        <result column="DLR_ID" property="dlrId" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="APPLY_STATUS" property="applyStatus" />
        <result column="ORDERNO" property="orderno" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        APPLY_ID, APPLY_CODE, BOUTIQUE_CODE, BOUTIQUE_NAME, APPLY_NUM, DLR_ID, DLR_CODE, DLR_NAME, APPLY_STATUS, ORDERNO, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.applyId !=null and param.applyId !=''">and APPLY_ID=#{param.applyId}</if>
    	<if test="param.applyCode !=null and param.applyCode !=''">and APPLY_CODE=#{param.applyCode}</if>
    	<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">and BOUTIQUE_CODE=#{param.boutiqueCode}</if>
    	<if test="param.boutiqueName !=null and param.boutiqueName !=''">and BOUTIQUE_NAME=#{param.boutiqueName}</if>
    	<if test="param.applyNum !=null and param.applyNum !=''">and APPLY_NUM=#{param.applyNum}</if>
    	<if test="param.dlrId !=null and param.dlrId !=''">and DLR_ID=#{param.dlrId}</if>
    	<if test="param.dlrCode !=null and param.dlrCode !=''">and DLR_CODE=#{param.dlrCode}</if>
    	<if test="param.dlrName !=null and param.dlrName !=''">and DLR_NAME=#{param.dlrName}</if>
    	<if test="param.applyStatus !=null and param.applyStatus !=''">and APPLY_STATUS=#{param.applyStatus}</if>
    	<if test="param.orderno !=null and param.orderno !=''">and ORDERNO=#{param.orderno}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>

 	<!-- 精品申请表 信息查询 -->
	<select id="querySacBuBoutiqueApply" resultType="map">
		select
	    <include refid="Base_Column_List"></include>
	    from t_sac_bu_boutique_apply
	    where 1=1
	    <include refid="where_condition"></include>
	</select>

	<!-- 精品申请表 信息删除（物理删除） -->
	<delete id="deleteSacBuBoutiqueApply">
		DELETE
		FROM
			t_sac_bu_boutique_apply
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>

	<!-- 精品申请表 信息新增 -->
	<insert id="createSacBuBoutiqueApply">
		insert into t_sac_bu_boutique_apply(<include refid="Base_Column_List"></include>)
		value(
        	#{param.applyId},
			#{param.applyCode},
			#{param.boutiqueCode},
			#{param.boutiqueName},
			#{param.applyNum},
			#{param.dlrId},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.applyStatus},
			#{param.orderno},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.remark},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.updateControlId}
		)
	</insert>

	<!-- 精品申请表 信息更新 -->
	<update id="updateSacBuBoutiqueApply">
		update t_sac_bu_boutique_apply  set
			<!-- 更新列表 -->
			<if test="param.applyCode !=null and param.applyCode !=''">APPLY_CODE=#{param.applyCode},</if>
			<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">BOUTIQUE_CODE=#{param.boutiqueCode},</if>
			<if test="param.boutiqueName !=null and param.boutiqueName !=''">BOUTIQUE_NAME=#{param.boutiqueName},</if>
			<if test="param.applyNum !=null and param.applyNum !=''">APPLY_NUM=#{param.applyNum},</if>
			<if test="param.dlrId !=null and param.dlrId !=''">DLR_ID=#{param.dlrId},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrName !=null and param.dlrName !=''">DLR_NAME=#{param.dlrName},</if>
			<if test="param.applyStatus !=null and param.applyStatus !=''">APPLY_STATUS=#{param.applyStatus},</if>
			<if test="param.orderno !=null and param.orderno !=''">ORDERNO=#{param.orderno},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null ">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">UPDATE_CONTROL_ID=#{param.updateControlId}</if>
			<!-- 结束无逗号 -->
			APPLY_ID=APPLY_ID
			where 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</update>
	<select id="selectBoutiqueCount" resultType="int">
		SELECT COUNT(*) FROM t_sac_bu_boutique_apply
	</select>
	<insert id="insertApplyDetail">
		INSERT INTO t_sac_bu_boutique_apply_detail
		(
			APPLY_DETAIL_ID,
			APPLY_ID,
			BOUTIQUE_CODE,
			BOUTIQUE_NAME,
		    DLR_ID,
			DLR_CODE,
			DLR_NAME ,
			DETAIL_NUM,
			SORT_NO,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
		)
		VALUES
			(
			#{param.applyDetailId},
			#{param.applyId},
			#{param.boutiqueCode},
			#{param.boutiqueName},
			#{param.dlrId},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.detailNum},
			#{param.sortNo},
			#{entity.userID},
			#{entity.empName},
			now(),
			#{entity.userID},
			#{entity.empName},
			now(),
			'1',
			#{param.updateControlId}
			)
	</insert>
	<insert id="insertApply">
		INSERT INTO  t_sac_bu_boutique_apply
		(
			APPLY_ID,
			APPLY_CODE,
			APPLY_NUM,
			DLR_ID,
			DLR_CODE,
			DLR_NAME,
			APPLY_STATUS,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
		)
		VALUES
			(
			#{param.applyId},
			#{param.applyCode},
			#{param.applyNum},
			#{param.dlrId},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.applyStatus},
			#{entity.userID},
			#{entity.empName},
			now(),
			#{entity.userID},
			#{entity.empName},
			now(),
			'1',
			#{param.updateControlId}
			)

	</insert>
	<delete id="deleteDetailList">
		DELETE
		FROM
			t_sac_bu_boutique_apply_detail d
		WHERE
			d.APPLY_ID = #{applyId}
	</delete>
	<update id="updateApply">
		update t_sac_bu_boutique_apply  set
		<if test="param.applyNum !=null and param.applyNum !=''">APPLY_NUM = #{param.applyNum},</if>
		<if test="param.applyStatus !=null and param.applyStatus !=''">APPLY_STATUS=#{param.applyStatus},</if>
		MODIFIER = #{entity.userID},
		MODIFY_NAME = #{entity.empName},
		LAST_UPDATED_DATE = now(),
        UPDATE_CONTROL_ID = uuid()
        where
        1=1
        and
              APPLY_ID = #{param.applyId}
        AND
              UPDATE_CONTROL_ID = #{param.updateControlId}
	</update>
	<select id="sacBuBoutiqueApplyFindInfoQuery" resultType="java.util.Map">
		SELECT
		t1.APPLY_ID,
		t1.APPLY_CODE,
		t5.AREA_NAME,
		t2.DLR_ID,
		t2.DLR_CODE,
		t2.DLR_SHORT_NAME AS DLR_NAME,
		DATE_FORMAT( t1.CREATED_DATE, '%Y-%m-%d %H:%i:%s' ) AS CREATED_DATE,
		t1.CREATED_NAME,
		t1.APPLY_NUM,
		t1.APPLY_STATUS,
		CASE
		t1.APPLY_STATUS
		WHEN '1' THEN
		'已提交'
		WHEN '2' THEN
		'已锁定' ELSE '暂存'
		END AS APPLY_STATUS_NAME,
		t1.UPDATE_CONTROL_ID,
		a.AGENT_NAME,
		c.AGENT_COMPANY_NAME,
		T8.PROVINCE_NAME,
		T9.CITY_NAME
		FROM
		t_sac_bu_boutique_apply t1
		LEFT JOIN t_usc_mdm_org_dlr t2 ON t1.DLR_ID = t2.DLR_ID
		LEFT JOIN t_usc_mdm_agent_company C ON t2.COMPANY_ID = C.AGENT_COMPANY_ID
		LEFT JOIN t_usc_mdm_agent_info a ON c.AGENT_ID = a.AGENT_ID
		LEFT JOIN t_usc_mdm_agent_area t7 ON t2.DLR_ID = t7.AGENT_ID AND t7.AREA_TYPE = '0'
		LEFT JOIN t_usc_area_relation T6 ON T7.AREA_ID = T6.REL_OBJ_ID AND T6.REL_OBJ_TYPE = '1'
		LEFT JOIN t_usc_area_info T5 ON T6.AREA_ID = T5.AREA_ID AND T5.AREA_TYPE = '1'
		LEFT JOIN t_usc_mdm_org_province T8 ON T8.PROVINCE_ID = t2.PROVINCE_ID
		LEFT JOIN t_usc_mdm_org_city T9 ON T9.CITY_ID = t2.CITY_ID
		WHERE 1=1
		<if test="param.provinceId!=null and ''!=param.provinceId">
			AND t2.PROVINCE_ID = #{param.provinceId}
		</if>
		<if test="param.cityId!=null and ''!=param.cityId">
			AND t2.CITY_ID = #{param.cityId}
		</if>
		<if test="param.agentId!=null and ''!=param.agentId">
			AND c.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.companyId!=null and ''!=param.companyId">
			AND t2.COMPANY_ID = #{param.companyId}
		</if>
		<if test="param.applyCode!=null and ''!=param.applyCode">
			AND INSTR(t1.APPLY_CODE,#{param.applyCode})
		</if>
		<if test="param.areaId!=null and ''!=param.areaId">
			AND T5.AREA_ID = #{param.areaId}
		</if>
		<if test="param.dlrName!=null and ''!=param.dlrName">
			AND INSTR(T2.DLR_SHORT_NAME,#{param.dlrName}) >0
		</if>
		<if test="param.dlrCode!=null and ''!=param.dlrCode " >
			AND T2.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.dlrId!=null and ''!=param.dlrId">
			AND T2.DLR_ID IN
			<foreach item="item" collection="param.dlrId.split(',')" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.applyStatus!=null and ''!=param.applyStatus">
			AND t1.APPLY_STATUS IN
			<foreach item="item" collection="param.applyStatus.split(',')" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.applyStatusIn!=null and ''!=param.applyStatusIn">
			AND t1.APPLY_STATUS = #{param.applyStatusIn}
		</if>
		<if test="param.applyBeginDate!=null and ''!=param.applyBeginDate " >
		<![CDATA[ AND DATE_FORMAT(T1.CREATED_DATE,'%Y-%m-%d') >= #{param.applyBeginDate}]]>
		<![CDATA[ AND DATE_FORMAT(T1.CREATED_DATE,'%Y-%m-%d') <= #{param.applyEndDate} ]]>
		</if>
		order by T1.CREATED_DATE desc
	</select>
	<select id="sacBuBoutiqueApplyByDetails" resultType="java.util.Map">
		SELECT
			APPLY_DETAIL_ID,
			APPLY_ID,
			BOUTIQUE_CODE,
			BOUTIQUE_NAME,
			DLR_ID,
			DLR_CODE,
			DLR_NAME,
			DETAIL_NUM
		FROM
			t_sac_bu_boutique_apply_detail
		WHERE 1=1
		  AND APPLY_ID =#{param.applyId}
		ORDER BY SORT_NO ASC
	</select>
	<select id="selectApplyInfo" resultType="java.util.Map">
		SELECT
			(@i:=@i+1) as `NO`,
			IFNULL(T1.BOUTIQUE_CODE,'') AS  BOUTIQUE_CODE,
			IFNULL(T1.BOUTIQUE_NAME,'') AS  BOUTIQUE_NAME,
			IFNULL(T1.DETAIL_NUM,'') AS  DETAIL_NUM,
			IFNULL(t.CONSUGNEE,'') AS  CONSUGNEE,
			IFNULL(t.CONSUGNEE_PHONE,'')AS CONSUGNEE_PHONE,
			IFNULL(t.PROVINCE_NAME,'') AS  PROVINCE_NAME,
			IFNULL(t.CITY_NAME,'') AS  CITY_NAME,
			IFNULL(t.COUNTY_NAME,'') AS COUNTY_NAME,
		    t1.DLR_CODE,
			t1.DLR_NAME,
			IFNULL(t.CONSUGNEE_ADDRESS,'') as CONSUGNEE_ADDRESS
		FROM  t_sac_bu_boutique_apply t2
		LEFT JOIN  t_sac_bu_boutique_apply_detail t1 ON t2.APPLY_ID=t1.APPLY_ID
		LEFT JOIN t_sac_bu_boutique_dlr t ON t2.DLR_CODE =t.DLR_CODE
		,(select   @i:=0)   as   it
		WHERE 1=1
		  AND t2.APPLY_STATUS=#{param.applyStatus}
		<if test="param.applyId!=null and ''!=param.applyId">
			AND t2.APPLY_ID IN
			<foreach item="item" collection="param.applyId.split(',')" index="index"  open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by t2.CREATED_DATE desc
	</select>
	<select id="selectDlrByDlrId" resultType="int">
		SELECT COUNT(*) FROM  t_sac_bu_boutique_dlr  WHERE 1=1 AND  DLR_ID =#{dlrId} AND  DLR_CODE =#{dlrCOde}
	</select>
	<select id="selectSacTestCarSendMessage" resultType="java.util.Map">
		SELECT
		    t.DLR_CODE,
		    t.DLR_NAME,
		    t.PLATE_NUMBER,
			t.APPOINTMENT_TEST_DATE,
			t.CUSTOMER_ID AS cust_id,
			t.CUSTOMER_NAME AS cust_name,
			t.CUSTOMER_PHONE AS phone,
			t1.SALES_CONSULTANT_ID as receiveEmpId,
			t1.SALES_CONSULTANT_NAME as emp_name,
			t1.TEST_DRIVE_METHOD
		FROM
			t_sac_appointment_sheet t
				LEFT JOIN t_sac_test_drive_sheet t1 ON t.APPOINTMENT_ID = t1.APPOINTMENT_ID
		WHERE
			t.APPOINTMENT_TEST_DATE IS NOT NULL
		  AND t.APPOINTMENT_TEST_DATE != ''
	AND t1.SALES_CONSULTANT_ID IS NOT NULL
	AND t.IS_TEST_DRIVE = '0'
	AND ( SELECT DATE_SUB( curdate( ), INTERVAL - 1 DAY ) ) = DATE_FORMAT( APPOINTMENT_TEST_DATE, '%Y-%m-%d' )
	</select>

    <select id="sacTestCarSheetSendMessage" resultType="java.util.Map">
		SELECT
			t.TEST_DRIVE_SHEET_ID,
			t.TEST_DRIVE_ORDER_NO,
			t.DLR_CODE,
			t.DLR_NAME,
			t.CUSTOMER_PHONE,
			t.TEST_START_ROAD_HAUL,
			DATE_FORMAT(t.RECEIVER_TIME,'%Y-%m-%d %H:%i:%s') RECEIVER_TIME,
			t.CUSTOMER_NAME,
			t.CUSTOMER_ID,
			t.SMALL_CAR_TYPE_CODE,
			DATE_FORMAT(t.START_TIME,'%Y-%m-%d %H:%i:%s') START_TIME,
			t.COLUMN4,
			t.COLUMN2 inviteCode,
			t.SMALL_CAR_TYPE_NAME,
			t.APPOINTMENT_ID,
			t.TEST_STATUS,
		INSERT(t.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM
		FROM
			t_sac_test_drive_sheet t
		WHERE
			t.TEST_STATUS = '2'
		  AND t.END_TIME IS NOT NULL
		  AND t.EVALUATE_FLAG IS  NULL
		  AND t.CREATED_DATE =
		 (
		SELECT
		min( CREATED_DATE )
		FROM t_sac_test_drive_sheet t2
		WHERE t.CUSTOMER_PHONE = t2.CUSTOMER_PHONE
		and  t.DLR_CODE = t2.DLR_CODE
		and t.SMALL_CAR_TYPE_NAME = t2.SMALL_CAR_TYPE_NAME
		and t2.TEST_STATUS = '2'
		)

	</select>

    <select id="evaluateMsgData" resultType="java.util.Map">
		SELECT
			t.TEMPLATE_ID templateId,
			t.MSG_ID msgId,
			t.TEMPLATE_ID serviceCode
		FROM
			mp.t_usc_bu_evaluate_msg_data t
		where t.SCENE_CODE='SC_002'
	</select>

    <update id="updateEvaluateFlag">
		update t_sac_test_drive_sheet
			set EVALUATE_FLAG='1',

			UPDATE_CONTROL_ID=uuid(),
			LAST_UPDATED_DATE=now()
			where TEST_DRIVE_SHEET_ID in
			<foreach collection="list" close=")" item="item" separator="," open="(" index="index">
				#{item.testDriveSheetId}
			</foreach>
	</update>

    <select id="findUrl" resultType="java.util.Map">
		select
			LOOKUP_VALUE_NAME url,
			IS_ENABLE
		from mp.t_prc_mds_lookup_value
		where
			LOOKUP_TYPE_CODE='VE1040'
		  and LOOKUP_VALUE_CODE='1'
    </select>

    <update id="updateDriveEnd">
		update t_sac_test_drive_sheet
		set EVALUATE_FLAG='1',
		COLUMN1='试驾自动结束',
		UPDATE_CONTROL_ID=uuid(),
		LAST_UPDATED_DATE=now(),
		TEST_STATUS= '2',
		TEST_ROAD_HAUL =1,
		<trim prefix="TEST_END_ROAD_HAUL = case" suffix="end,">
			<foreach collection="list" index="index" item="item">
				when TEST_DRIVE_SHEET_ID = #{item.testDriveSheetId} then #{item.testStartRoadHaul}+1
			</foreach>
		</trim>
		END_TIME=now()
		where TEST_DRIVE_SHEET_ID in
		<foreach collection="list" close=")" item="item" separator="," open="(" index="index">
			#{item.testDriveSheetId}
		</foreach>
	</update>
</mapper>