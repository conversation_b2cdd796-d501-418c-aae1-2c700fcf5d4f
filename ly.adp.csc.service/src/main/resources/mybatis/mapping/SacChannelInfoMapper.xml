<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacChannelInfoMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacChannelInfo">
    <!--@mbg.generated-->
    <!--@Table t_sac_channel_info-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
    <result column="PARENT_CHANNEL_CODE" jdbcType="VARCHAR" property="parentChannelCode" />
    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
    <result column="CHANNEL_LEVEL_CODE" jdbcType="VARCHAR" property="channelLevelCode" />
    <result column="CHANNEL_LEVEL_NAME" jdbcType="VARCHAR" property="channelLevelName" />
    <result column="LEVEL_CODE" jdbcType="VARCHAR" property="levelCode" />
    <result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
    <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CHANNEL_CODE, PARENT_CHANNEL_CODE, CHANNEL_NAME, CHANNEL_LEVEL_CODE, CHANNEL_LEVEL_NAME, 
    LEVEL_CODE, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, 
    LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
  </sql>
<!--auto generated by MybatisCodeHelper on 2021-08-25-->
  <select id="selectByAll" resultType="java.util.Map">
      select a.ID as id,
             a.CHANNEL_CODE as channelCode,
             a.PARENT_CHANNEL_CODE as parentChannelCode,
             b.CHANNEL_NAME as parentChannelName,
             a.CHANNEL_NAME as channelName,
             a.CHANNEL_LEVEL_CODE as channelLevelCode,
             a.CHANNEL_LEVEL_NAME as channelLevelName,
             a.LEVEL_CODE as levelCode,
             a.OEM_ID as oemId,
             a.GROUP_ID as groupId,
             a.CREATOR as creator,
             a.CREATED_NAME as createdName,
             a.CREATED_DATE as createdDate,
             a.MODIFIER as modifier,
             a.MODIFY_NAME as modifyName,
             a.LAST_UPDATED_DATE as lastUpdatedDate,
             a.IS_ENABLE as isEnable,
             a.UPDATE_CONTROL_ID as updateControlId
      from t_sac_channel_info a
      left join t_sac_channel_info b on b.CHANNEL_CODE = a.PARENT_CHANNEL_CODE
      <where>
          <if test="map.id != null and map.id != ''">
              and a.ID = #{map.id}
          </if>
          <if test="map.channelCode != null and map.channelCode != ''">
              and a.CHANNEL_CODE =#{map.channelCode}
          </if>
          <if test="map.parentChannelCode != null and map.parentChannelCode != ''">
              and a.PARENT_CHANNEL_CODE=#{map.parentChannelCode}
          </if>
          <if test="map.channelName != null and map.channelName != ''">
              and a.CHANNEL_NAME like concat('%',#{map.channelName},'%')
          </if>
          <if test="map.channelLevelCode != null and map.channelLevelCode != ''">
              and a.CHANNEL_LEVEL_CODE = #{map.channelLevelCode}
          </if>
          <if test="map.channelLevelName != null and map.channelLevelName != ''">
              and a.CHANNEL_LEVEL_NAME = #{map.channelLevelName}
          </if>
          <if test="map.levelCode != null and map.levelCode != ''">
              and a.LEVEL_CODE = #{map.levelCode}
          </if>
          <if test="map.oemId != null and map.oemId != ''">
              and a.OEM_ID = #{map.oemId}
          </if>
          <if test="map.groupId != null and map.groupId != ''">
              and a.GROUP_ID = #{map.groupId}
          </if>
          <if test="map.creator != null and map.creator != ''">
              and a.CREATOR = #{map.creator}
          </if>
          <if test="map.createdName != null and map.createdName != ''">
              and a.CREATED_NAME = #{map.createdName}
          </if>
          <if test="map.modifier != null and map.modifier != ''">
              and a.MODIFIER = #{map.modifier}
          </if>
          <if test="map.modifyName != null and map.modifyName != ''">
              and a.MODIFY_NAME = #{map.modifyName}
          </if>
          <if test="map.isEnable != null and map.isEnable != ''">
              and a.IS_ENABLE = #{map.isEnable}
          </if>
          <if test="map.updateControlId != null and map.updateControlId != ''">
              and a.UPDATE_CONTROL_ID = #{map.updateControlId}
          </if>
      </where>
  </select>
  
  <select id="selectChannelByAll" resultType="java.util.Map">
      select a.ID as id,
             a.CHANNEL_CODE as channelCode,
             a.PARENT_CHANNEL_CODE as parentChannelCode,
             b.CHANNEL_NAME as parentChannelName,
             a.CHANNEL_NAME as channelName,
             a.CHANNEL_LEVEL_CODE as channelLevelCode,
             a.CHANNEL_LEVEL_NAME as channelLevelName,
             a.LEVEL_CODE as levelCode,
             a.OEM_ID as oemId,
             a.GROUP_ID as groupId,
             a.CREATOR as creator,
             a.CREATED_NAME as createdName,
             a.CREATED_DATE as createdDate,
             a.MODIFIER as modifier,
             a.MODIFY_NAME as modifyName,
             a.LAST_UPDATED_DATE as lastUpdatedDate,
             a.IS_ENABLE as isEnable,
             a.UPDATE_CONTROL_ID as updateControlId
      from t_sac_channel_info a
      left join t_sac_channel_info b on b.CHANNEL_CODE = a.PARENT_CHANNEL_CODE
      <where>
          <if test="map.id != null and map.id != ''">
             and a.ID = #{map.id}
          </if>
          <if test="map.channelCode != null and map.channelCode != ''">
             and a.CHANNEL_CODE =#{map.channelCode}
          </if>
          <if test="map.parentChannelCode != null and map.parentChannelCode != ''">
             and a.PARENT_CHANNEL_CODE =#{map.parentChannelCode}
          </if>
          <if test="map.channelName != null and map.channelName != ''">
             and a.CHANNEL_NAME like concat('%',#{map.channelName},'%')
          </if>
          <if test="map.channelLevelCode != null and map.channelLevelCode != ''">
             and a.CHANNEL_LEVEL_CODE = #{map.channelLevelCode}
          </if>
          <if test="map.channelLevelName != null and map.channelLevelName != ''">
             and a.CHANNEL_LEVEL_NAME = #{map.channelLevelName}
          </if>
          <if test="map.levelCode != null and map.levelCode != ''">
             and a.LEVEL_CODE = #{map.levelCode}
          </if>
          <if test="map.oemId != null and map.oemId != ''">
             and a.OEM_ID = #{map.oemId}
          </if>
          <if test="map.groupId != null and map.groupId != ''">
             and a.GROUP_ID = #{map.groupId}
          </if>
          <if test="map.creator != null and map.creator != ''">
             and a.CREATOR = #{map.creator}
          </if>
          <if test="map.createdName != null and map.createdName != ''">
             and a.CREATED_NAME = #{map.createdName}
          </if>
          <if test="map.modifier != null and map.modifier != ''">
             and a.MODIFIER = #{map.modifier}
          </if>
          <if test="map.modifyName != null and map.modifyName != ''">
             and a.MODIFY_NAME = #{map.modifyName}
          </if>
          <if test="map.isEnable != null and map.isEnable != ''">
             and a.IS_ENABLE = #{map.isEnable}
          </if>
          <if test="map.updateControlId != null and map.updateControlId != ''">
             and a.UPDATE_CONTROL_ID = #{map.updateControlId}
          </if>
      </where>
  </select>
  
<!--auto generated by MybatisCodeHelper on 2021-08-25-->
  <insert id="insertChannel">
        INSERT INTO t_sac_channel_info(
            ID,
            CHANNEL_CODE,
            PARENT_CHANNEL_CODE,
            CHANNEL_NAME,
            CHANNEL_LEVEL_CODE,
            CHANNEL_LEVEL_NAME,
            LEVEL_CODE,
            OEM_ID,
            GROUP_ID,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            UPDATE_CONTROL_ID
        )VALUES
        (
            #{map.id},
            #{map.channelCode},
            #{map.parentChannelCode},
            #{map.channelName},
            #{map.channelLevelCode},
            #{map.channelLevelName},
            #{map.levelCode},
            #{map.oemId},
            #{map.groupId},
            #{map.creator},
            #{map.createdName},
            #{map.createdDate},
            #{map.modifier},
            #{map.modifyName},
            #{map.lastUpdatedDate},
            #{map.isEnable},
            #{map.updateControlId}
        )
</insert>
<!--auto generated by MybatisCodeHelper on 2021-08-25-->
  <update id="updateById">
    update t_sac_channel_info
    <set>
      <if test="updated.channelCode != null and updated.channelCode != ''">
        CHANNEL_CODE = #{updated.channelCode},
      </if>
      <if test="updated.parentChannelCode != null and updated.parentChannelCode != ''">
        PARENT_CHANNEL_CODE = #{updated.parentChannelCode},
      </if>
      <if test="updated.channelName != null and updated.channelName != ''">
        CHANNEL_NAME = #{updated.channelName},
      </if>
      <if test="updated.channelLevelCode != null and updated.channelLevelCode != ''">
        CHANNEL_LEVEL_CODE = #{updated.channelLevelCode},
      </if>
      <if test="updated.channelLevelName != null and updated.channelLevelName != ''">
        CHANNEL_LEVEL_NAME = #{updated.channelLevelName},
      </if>
      <if test="updated.levelCode != null and updated.levelCode != ''">
        LEVEL_CODE = #{updated.levelCode},
      </if>
      <if test="updated.modifier != null and updated.modifier != ''">
        MODIFIER = #{updated.modifier},
      </if>
      <if test="updated.modifyName != null and updated.modifyName != ''">
        MODIFY_NAME = #{updated.modifyName},
      </if>
      <if test="updated.isEnable != null and updated.isEnable != ''">
        IS_ENABLE = #{updated.isEnable},
      </if>
        LAST_UPDATED_DATE = #{updated.lastUpdatedDate},
        UPDATE_CONTROL_ID = uuid()
    </set>
    where ID=#{updated.id}
  </update>

  <select id="checkChannelCodeRepeat" resultType="int">
      select count(1) as countNo
      from t_sac_channel_info t
      where t.IS_ENABLE='1' and t.CHANNEL_CODE = #{param.channelCode}
      <if test="param.id != null and param.id != ''">
          AND t.ID != #{param.id}
      </if>
  </select>
  
  <select id="checkChannelInfoRepeat" resultType="int">
      select count(1) as countNo
      from t_sac_channel_info t
      where t.IS_ENABLE='1' and t.CHANNEL_NAME=#{param.channelName} and t.LEVEL_CODE = #{param.levelCode}
      <if test="param.id != null and param.id != ''">
          AND t.ID != #{param.id}
      </if>
  </select>
<!--auto generated by MybatisCodeHelper on 2021-08-25-->
  <select id="countById" resultType="int">
    select count(1)
    from t_sac_channel_info
    where ID=#{id}
  </select>
  
  <select id="queryChannelLinkByName" resultType="java.util.Map">
	WITH recursive classTb(channel_code) AS (
		SELECT C.CHANNEL_CODE
		FROM t_sac_channel_info C
		WHERE CHANNEL_NAME=#{param.channelName}
		UNION ALL
		SELECT s.PARENT_CHANNEL_CODE
		FROM t_sac_channel_info s,classTb
		WHERE 1=1
		AND s.CHANNEL_CODE=classTb.CHANNEL_CODE
	)
	SELECT C.CHANNEL_CODE,C.CHANNEL_NAME,C.CHANNEL_LEVEL_CODE,C.CHANNEL_LEVEL_NAME
	FROM classTb T
	INNER JOIN t_sac_channel_info C ON T.CHANNEL_CODE=C.CHANNEL_CODE
	order by CHANNEL_LEVEL_CODE
   </select>
   
</mapper>