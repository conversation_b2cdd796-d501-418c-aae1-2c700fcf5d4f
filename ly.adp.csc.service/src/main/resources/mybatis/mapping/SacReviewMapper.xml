<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReview">
    <!--@mbg.generated-->
    <!--@Table t_sac_review-->
    <id column="REVIEW_ID" jdbcType="VARCHAR" property="reviewId" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
    <result column="BILL_TYPE_NAME" jdbcType="VARCHAR" property="billTypeName" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_TYPE_NAME" jdbcType="VARCHAR" property="businessTypeName" />
    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
    <result column="BILL_CODE" jdbcType="VARCHAR" property="billCode" />
    <result column="PLAN_REVIEW_TIME" jdbcType="TIMESTAMP" property="planReviewTime" />
    <result column="PLAN_COME_TIME" jdbcType="TIMESTAMP" property="planComeTime" />
    <result column="FACT_COME_TIME" jdbcType="TIMESTAMP" property="factComeTime" />
    <result column="IS_COME" jdbcType="TIMESTAMP" property="isCome" />
    <result column="REVIEW_TIME" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="LAST_REVIEW_TIME" jdbcType="TIMESTAMP" property="lastReviewTime" />
    <result column="OVER_REVIEW_TIME" jdbcType="TIMESTAMP" property="overReviewTime" />
    <result column="ASSIGN_STATUS" jdbcType="VARCHAR" property="assignStatus" />
    <result column="ASSIGN_STATUS_NAME" jdbcType="VARCHAR" property="assignStatusName" />
    <result column="ASSIGN_TIME" jdbcType="TIMESTAMP" property="assignTime" />
    <result column="ASSIGN_PERSON_ID" jdbcType="VARCHAR" property="assignPersonId" />
    <result column="ASSIGN_PERSON_NAME" jdbcType="VARCHAR" property="assignPersonName" />
    <result column="REVIEW_PERSON_ID" jdbcType="VARCHAR" property="reviewPersonId" />
    <result column="REVIEW_PERSON_NAME" jdbcType="VARCHAR" property="reviewPersonName" />
    <result column="REVIEW_DESC" jdbcType="VARCHAR" property="reviewDesc" />
    <result column="REVIEW_STATUS" jdbcType="VARCHAR" property="reviewStatus" />
    <result column="REVIEW_STATUS_NAME" jdbcType="VARCHAR" property="reviewStatusName" />
    <result column="CUST_ID" jdbcType="VARCHAR" property="custId" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
    <result column="GENDER_NAME" jdbcType="VARCHAR" property="genderName" />
    <result column="TOUCH_STATUS" jdbcType="VARCHAR" property="touchStatus" />
    <result column="TOUCH_STATUS_NAME" jdbcType="VARCHAR" property="touchStatusName" />
    <result column="ERROR_REASON_CODE" jdbcType="VARCHAR" property="errorReasonCode" />
    <result column="ERROR_REASON_NAME" jdbcType="VARCHAR" property="errorReasonName" />
    <result column="NODE_CODE" jdbcType="VARCHAR" property="nodeCode" />
    <result column="NODE_NAME" jdbcType="VARCHAR" property="nodeName" />
    <result column="SEND_DLR_CODE" jdbcType="VARCHAR" property="sendDlrCode" />
    <result column="SEND_DLR_SHORT_NAME" jdbcType="VARCHAR" property="sendDlrShortName" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="INTEN_LEVEL_CODE" jdbcType="VARCHAR" property="intenLevelCode" />
	<result column="INTEN_LEVEL_NAME" jdbcType="VARCHAR" property="intenLevelName" />
    <result column="INTEN_BRAND_CODE" jdbcType="VARCHAR" property="intenBrandCode" />
	<result column="INTEN_BRAND_NAME" jdbcType="VARCHAR" property="intenBrandName" />
	<result column="INTEN_SERIES_CODE" jdbcType="VARCHAR" property="intenSeriesCode" />
	<result column="INTEN_SERIES_NAME" jdbcType="VARCHAR" property="intenSeriesName" />
	<result column="INTEN_CAR_TYPE_CODE" jdbcType="VARCHAR" property="intenCarTypeCode" />
	<result column="INTEN_CAR_TYPE_NAME" jdbcType="VARCHAR" property="intenCarTypeName" />
    <result column="COLUMN1" jdbcType="VARCHAR" property="column1" />
    <result column="COLUMN2" jdbcType="VARCHAR" property="column2" />
    <result column="COLUMN3" jdbcType="VARCHAR" property="column3" />
    <result column="COLUMN4" jdbcType="VARCHAR" property="column4" />
    <result column="COLUMN5" jdbcType="VARCHAR" property="column5" />
    <result column="COLUMN6" jdbcType="VARCHAR" property="column6" />
    <result column="COLUMN7" jdbcType="VARCHAR" property="column7" />
    <result column="COLUMN8" jdbcType="VARCHAR" property="column8" />
    <result column="COLUMN9" jdbcType="VARCHAR" property="column9" />
    <result column="COLUMN10" jdbcType="VARCHAR" property="column10" />
    <result column="COLUMN11" jdbcType="VARCHAR" property="column11" />
    <result column="COLUMN12" jdbcType="VARCHAR" property="column12" />
    <result column="COLUMN13" jdbcType="VARCHAR" property="column13" />
    <result column="COLUMN14" jdbcType="VARCHAR" property="column14" />
    <result column="COLUMN15" jdbcType="VARCHAR" property="column15" />
    <result column="COLUMN16" jdbcType="VARCHAR" property="column16" />
    <result column="COLUMN17" jdbcType="VARCHAR" property="column17" />
    <result column="COLUMN18" jdbcType="VARCHAR" property="column18" />
    <result column="COLUMN19" jdbcType="VARCHAR" property="column19" />
    <result column="COLUMN20" jdbcType="VARCHAR" property="column20" />
    <result column="BIG_COLUMN1" jdbcType="LONGVARCHAR" property="bigColumn1" />
    <result column="BIG_COLUMN2" jdbcType="LONGVARCHAR" property="bigColumn2" />
    <result column="BIG_COLUMN3" jdbcType="LONGVARCHAR" property="bigColumn3" />
    <result column="BIG_COLUMN4" jdbcType="LONGVARCHAR" property="bigColumn4" />
    <result column="BIG_COLUMN5" jdbcType="LONGVARCHAR" property="bigColumn5" />
    <result column="EXTENDS_JSON" jdbcType="VARCHAR" property="extendsJson" />
    <result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
    <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
    <result column="MANAGE_LABEL_CODE" property="manageLabelCode" />
    <result column="MANAGE_LABEL_NAME" property="manageLabelName" />
      <result column="CITY_FIRM_CODE" property="cityFirmCode" />
      <result column="CITY_FIRM_NAME" property="cityFirmName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    REVIEW_ID, ORG_CODE, ORG_NAME, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME,
    INFO_CHAN_M_CODE,INFO_CHAN_M_NAME,INFO_CHAN_D_CODE,INFO_CHAN_D_NAME,INFO_CHAN_DD_CODE,INFO_CHAN_DD_NAME,
	CHANNEL_CODE,CHANNEL_NAME, BILL_CODE, PLAN_REVIEW_TIME, PLAN_COME_TIME, FACT_COME_TIME, IS_COME,
	REVIEW_TIME, LAST_REVIEW_TIME,
    OVER_REVIEW_TIME, ASSIGN_STATUS, ASSIGN_STATUS_NAME, ASSIGN_TIME, ASSIGN_PERSON_ID,
    ASSIGN_PERSON_NAME, REVIEW_PERSON_ID, REVIEW_PERSON_NAME, REVIEW_DESC, REVIEW_STATUS,
    REVIEW_STATUS_NAME, CUST_ID, CUST_NAME, PHONE, GENDER, GENDER_NAME, TOUCH_STATUS,
    TOUCH_STATUS_NAME, ERROR_REASON_CODE, ERROR_REASON_NAME, NODE_CODE, NODE_NAME,SEND_DLR_CODE,SEND_DLR_SHORT_NAME,
    SEND_TIME,INTEN_LEVEL_CODE,INTEN_LEVEL_NAME,INTEN_BRAND_CODE,INTEN_BRAND_NAME,INTEN_SERIES_CODE,INTEN_SERIES_NAME,INTEN_CAR_TYPE_CODE,INTEN_CAR_TYPE_NAME,
    COLUMN1, COLUMN2, COLUMN3,
    COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, COLUMN11, COLUMN12,
    COLUMN13, COLUMN14, COLUMN15, COLUMN16, COLUMN17, COLUMN18, COLUMN19, COLUMN20, BIG_COLUMN1,
    BIG_COLUMN2, BIG_COLUMN3, BIG_COLUMN4, BIG_COLUMN5, EXTENDS_JSON, OEM_ID, GROUP_ID,
    CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE,
    UPDATE_CONTROL_ID
  </sql>
    <delete id="deleteReview">
        DELETE FROM t_sac_review where PHONE=#{param.phone}
    </delete>

    <select id="queryListMeReviewInfo" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
    	r.SEND_DLR_SHORT_NAME as sendDlrShortName,
    	r.INTEN_LEVEL_CODE,
    	r.INTEN_LEVEL_NAME,
    	r.INTEN_BRAND_CODE,
    	r.INTEN_BRAND_NAME,
    	r.INTEN_SERIES_CODE,
    	r.INTEN_SERIES_NAME,
    	r.INTEN_CAR_TYPE_CODE,
    	r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        r.CREATED_DATE as createdDate,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate
        from t_sac_review r
        where r.REVIEW_PERSON_ID=#{param.userId}
        and r.REVIEW_STATUS in('0','1')
        <if test="param.assignStartTime != null and ''!= param.assignStartTime">
	    <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
	    </if>
	    <if test="param.assignEndTime != null and ''!= param.assignEndTime">
	    <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
	    </if>
        <if test="param.planStartTime != null and ''!= param.planStartTime">
         <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
        </if>
        <if test="param.planEndTime != null and ''!= param.planEndTime">
         <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.lastStartTime != null and ''!= param.lastStartTime">
         <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
        </if>
        <if test="param.lastEndTime != null and ''!= param.lastEndTime">
         <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
        </if>
        <if test="param.billType != null and ''!= param.billType ">
        AND r.bill_type = #{param.billType}
        </if>
        <if test="param.businessType != null and ''!= param.businessType ">
        AND r.business_type = #{param.businessType}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
        AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
        AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
        AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
        AND r.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.custName != null and ''!= param.custName ">
        <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
        <if test="param.phone != null and ''!= param.phone ">
        <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
        </if>
        <if test="param.billCode != null and ''!= param.billCode ">
        AND r.bill_code = #{param.billCode}
        </if>
        <if test="param.sendDlrCode != null and ''!= param.sendDlrCode ">
        AND r.send_dlr_code = #{param.sendDlrCode}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
        AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
        <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
        AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
        </if>
        <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
        AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
        </if>
        <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
        AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
        </if>
        <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
	     AND r.REVIEW_STATUS = #{param.reviewStatus}
	     </if>
        ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
  </select>
  <select id="queryListGroupReviewInfo" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
        date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
        r.IS_COME,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.ASSIGN_STATUS as assignStatus,
        r.ASSIGN_STATUS_NAME as assignStatusName,
        r.ASSIGN_TIME as assignTime,
        r.ASSIGN_PERSON_ID as assignPersonId,
        r.ASSIGN_PERSON_NAME as assignPersonName,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
    	r.SEND_DLR_SHORT_NAME as sendDlrShortName,
    	r.INTEN_LEVEL_CODE,
    	r.INTEN_LEVEL_NAME,
    	r.INTEN_BRAND_CODE,
    	r.INTEN_BRAND_NAME,
    	r.INTEN_SERIES_CODE,
    	r.INTEN_SERIES_NAME,
    	r.INTEN_CAR_TYPE_CODE,
    	r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        r.CREATED_DATE as createdDate,
        r.UPDATE_CONTROL_ID as updateControlId,
        r.CREATED_NAME as createdName,
        r.LAST_UPDATED_DATE as lastUpdatedDate
    from t_sac_review r
    where 1=1
    and r.REVIEW_STATUS in('0','1')
    <if test="param.userIdList != null and ''!= param.userIdList">
    and (r.review_person_id is null or r.review_person_id in
    <foreach item="item" collection=" param.userIdList.split(',')"
	 index="index" open="(" separator="," close=")">
	 #{item}
     </foreach>
     )
     </if>
    <if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
    AND r.review_person_id = #{param.reviewPersonId}
    </if>
    <if test="param.assignStartTime != null and ''!= param.assignStartTime">
    <![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
    </if>
    <if test="param.assignEndTime != null and ''!= param.assignEndTime">
    <![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
    </if>
    <if test="param.planStartTime != null and ''!= param.planStartTime">
    <![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
    </if>
    <if test="param.planEndTime != null and ''!= param.planEndTime">
     <![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
    </if>
    <if test="param.lastStartTime != null and ''!= param.lastStartTime">
     <![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
    </if>
    <if test="param.lastEndTime != null and ''!= param.lastEndTime">
     <![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
    </if>
    <if test="param.billType != null and ''!= param.billType ">
    AND r.bill_type = #{param.billType}
    </if>
    <if test="param.businessType != null and ''!= param.businessType ">
    AND r.business_type = #{param.businessType}
    </if>
    <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
    AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
    </if>
    <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
    AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
    </if>
    <if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
    AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
    </if>
    <if test="param.channelCode != null and ''!= param.channelCode ">
     AND r.CHANNEL_CODE = #{param.channelCode}
    </if>
    <if test="param.custName != null and ''!= param.custName ">
        <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
        </if>
    <if test="param.phone != null and ''!= param.phone ">
    <![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
    </if>
    <if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
    <![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
    </if>
    <if test="param.billCode != null and ''!= param.billCode ">
    AND r.bill_code = #{param.billCode}
    </if>
    <if test="param.sendDlrCode != null and ''!= param.sendDlrCode ">
    AND r.send_dlr_code = #{param.sendDlrCode}
    </if>
    <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
    AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
    </if>
    <if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
    AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
    </if>
    <if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
    AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
    </if>
    <if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
    AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
    </if>
    <if test="param.reviewStatus != null and ''!= param.reviewStatus ">
     AND r.REVIEW_STATUS = #{param.reviewStatus}
     </if>
    ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
  </select>


  <select id="queryListAuditReviewInfo" resultType="java.util.Map">
        select
        a.audit_id,
        R.REVIEW_ID,
        a.apply_type_code,
        a.apply_type_name,
        r.cust_name,
        r.phone,
        r.bill_type_name,
        r.business_type_name,
        r.bill_code,
        a.created_date,
        a.created_name,
        r.touch_status_name,
        r.error_reason_name,
        a.apply_desc,
        r.GENDER,
        r.GENDER_NAME,
        r.REVIEW_PERSON_ID,
		r.REVIEW_PERSON_NAME,
		r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
		r.INTEN_LEVEL_CODE,
		r.INTEN_LEVEL_NAME,
		r.INTEN_BRAND_CODE,
		r.INTEN_BRAND_NAME,
		r.INTEN_SERIES_CODE,
		r.INTEN_SERIES_NAME,
		r.INTEN_CAR_TYPE_CODE,
		r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON,
        A.UPDATE_CONTROL_ID
    from t_sac_review_audit a
    inner join t_sac_review r on a.review_id=r.review_id
    where 1=1
    and r.org_code=#{param.orgCode}
    and (a.sh_person_id is null or a.sh_person_id =#{param.shPersonId})
    and a.sh_status='0'
    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
        AND A.apply_type_code = #{param.applyTypeCode}
    </if>
    <if test="param.billCode != null and ''!= param.billCode ">
        AND r.bill_code = #{param.billCode}
    </if>
    <if test="param.applyPerson != null and ''!= param.applyPerson ">
        AND r.created_name = #{param.applyPerson}
    </if>
    <if test="param.custName != null and ''!= param.custName ">
        AND r.cust_name = #{param.custName}
    </if>
    <if test="param.phone != null and ''!= param.phone ">
        AND r.phone = #{param.phone}
    </if>
    <if test="param.startTime != null and ''!= param.startTime">
        <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
    </if>
    <if test="param.endTime != null and ''!= param.endTime">
        <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
    </if>
  </select>

  <select id="queryListAuditReviewRecordInfo" resultType="java.util.Map">
  select * from
  (
    select
    a.AUDIT_ID,
    r.REVIEW_ID,
    a.apply_type_name,
    r.cust_name,
    r.phone,
    r.bill_type_name,
    r.business_type_name,
    r.bill_code,
    a.created_date,
    a.created_name,
    r.touch_status_name,
    r.error_reason_name,
    a.apply_desc,
    a.sh_status,
    a.sh_status_name,
    a.sh_desc,
    a.sh_time,
    r.GENDER,
    r.GENDER_NAME,
    r.REVIEW_PERSON_ID,
	r.REVIEW_PERSON_NAME,
	r.INFO_CHAN_M_CODE,
	r.INFO_CHAN_M_NAME,
	r.INFO_CHAN_D_CODE,
	r.INFO_CHAN_D_NAME,
	r.INFO_CHAN_DD_CODE,
	r.INFO_CHAN_DD_NAME,
	r.CHANNEL_CODE,
	r.CHANNEL_NAME,
	r.INTEN_LEVEL_CODE,
	r.INTEN_LEVEL_NAME,
	r.INTEN_BRAND_CODE,
	r.INTEN_BRAND_NAME,
	r.INTEN_SERIES_CODE,
	r.INTEN_SERIES_NAME,
	r.INTEN_CAR_TYPE_CODE,
	r.INTEN_CAR_TYPE_NAME,
	r.COLUMN1,
	r.COLUMN2,
	r.COLUMN3,
	r.COLUMN4,
	r.COLUMN5,
	r.COLUMN6,
	r.COLUMN7,
	r.COLUMN8,
	r.COLUMN9,
	r.COLUMN10,
	r.COLUMN11,
	r.COLUMN12,
	r.COLUMN13,
	r.COLUMN14,
	r.COLUMN15,
	r.COLUMN16,
	r.COLUMN17,
	r.COLUMN18,
	r.COLUMN19,
	r.COLUMN20,
	r.BIG_COLUMN1,
	r.BIG_COLUMN2,
	r.BIG_COLUMN3,
	r.BIG_COLUMN4,
	r.BIG_COLUMN5,
    r.EXTENDS_JSON,
    A.UPDATE_CONTROL_ID
    from t_sac_review_audit a
    inner join t_sac_review r on a.review_id=r.review_id
    where 1=1
    and a.is_enable='1'
    and r.org_code=#{param.orgCode}
    and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
    <if test="param.auditId != null and ''!= param.auditId ">
        AND A.AUDIT_ID = #{param.auditId}
    </if>
    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
        AND A.apply_type_code = #{param.applyTypeCode}
    </if>
    <if test="param.billCode != null and ''!= param.billCode ">
        AND r.bill_code = #{param.billCode}
    </if>
    <if test="param.applyPerson != null and ''!= param.applyPerson ">
        AND r.created_name = #{param.applyPerson}
    </if>
    <if test="param.custName != null and ''!= param.custName ">
        AND r.cust_name = #{param.custName}
    </if>
    <if test="param.phone != null and ''!= param.phone ">
        AND r.phone = #{param.phone}
    </if>
    <if test="param.shStatus != null and ''!= param.shStatus ">
	AND A.sh_status IN
	<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
	#{item}
	</foreach>
	</if>
    <if test="param.startTime != null and ''!= param.startTime">
        <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
    </if>
    <if test="param.endTime != null and ''!= param.endTime">
        <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
    </if>
    <if test="param.shStartTime != null and ''!= param.shStartTime">
        <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
    </if>
    <if test="param.shEndTime != null and ''!= param.shEndTime">
        <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
    </if>
    union all
    select
    a.AUDIT_ID,
    r.REVIEW_ID,
    a.apply_type_name,
    r.cust_name,
    r.phone,
    r.bill_type_name,
    r.business_type_name,
    r.bill_code,
    a.created_date,
    a.created_name,
    r.touch_status_name,
    r.error_reason_name,
    a.apply_desc,
    a.sh_status,
    a.sh_status_name,
    a.sh_desc,
    a.sh_time,
    r.GENDER,
    r.GENDER_NAME,
    r.REVIEW_PERSON_ID,
	r.REVIEW_PERSON_NAME,
	r.INFO_CHAN_M_CODE,
	r.INFO_CHAN_M_NAME,
	r.INFO_CHAN_D_CODE,
	r.INFO_CHAN_D_NAME,
	r.INFO_CHAN_DD_CODE,
	r.INFO_CHAN_DD_NAME,
	r.CHANNEL_CODE,
	r.CHANNEL_NAME,
	r.INTEN_LEVEL_CODE,
	r.INTEN_LEVEL_NAME,
	r.INTEN_BRAND_CODE,
	r.INTEN_BRAND_NAME,
	r.INTEN_SERIES_CODE,
	r.INTEN_SERIES_NAME,
	r.INTEN_CAR_TYPE_CODE,
	r.INTEN_CAR_TYPE_NAME,
	r.COLUMN1,
	r.COLUMN2,
	r.COLUMN3,
	r.COLUMN4,
	r.COLUMN5,
	r.COLUMN6,
	r.COLUMN7,
	r.COLUMN8,
	r.COLUMN9,
	r.COLUMN10,
	r.COLUMN11,
	r.COLUMN12,
	r.COLUMN13,
	r.COLUMN14,
	r.COLUMN15,
	r.COLUMN16,
	r.COLUMN17,
	r.COLUMN18,
	r.COLUMN19,
	r.COLUMN20,
	r.BIG_COLUMN1,
	r.BIG_COLUMN2,
	r.BIG_COLUMN3,
	r.BIG_COLUMN4,
	r.BIG_COLUMN5,
    r.EXTENDS_JSON,
    A.UPDATE_CONTROL_ID
    from t_sac_review_audit a
    inner join t_sac_review_his r on a.review_id=r.review_id
    where 1=1
    and a.is_enable='1'
    and r.org_code=#{param.orgCode}
    and (a.creator=#{param.personId} or a.sh_person_id=#{param.personId})
    <if test="param.auditId != null and ''!= param.auditId ">
        AND A.AUDIT_ID = #{param.auditId}
    </if>
    <if test="param.custName != null and ''!= param.custName ">
        AND r.cust_name = #{param.custName}
    </if>
    <if test="param.phone != null and ''!= param.phone ">
        AND r.phone = #{param.phone}
    </if>
    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
        AND A.apply_type_code = #{param.applyTypeCode}
    </if>
    <if test="param.billCode != null and ''!= param.billCode ">
        AND r.bill_code = #{param.billCode}
    </if>
    <if test="param.applyPerson != null and ''!= param.applyPerson ">
        AND r.created_name = #{param.applyPerson}
    </if>
    <if test="param.shStatus != null and ''!= param.shStatus ">
	 AND A.sh_status IN
	<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
	#{item}
	</foreach>
	</if>
    <if test="param.startTime != null and ''!= param.startTime">
        <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
    </if>
    <if test="param.endTime != null and ''!= param.endTime">
        <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
    </if>
    <if test="param.shStartTime != null and ''!= param.shStartTime">
        <![CDATA[ AND A.sh_time >= #{param.shStartTime} ]]>
    </if>
    <if test="param.shEndTime != null and ''!= param.shEndTime">
        <![CDATA[ AND A.sh_time < date_add(#{param.shEndTime}, INTERVAL 1 day) ]]>
    </if>
    ) A
    ORDER BY A.created_date DESC
  </select>

  <select id="queryReviewRecord" resultType="java.util.Map">
        select
        r.REVIEW_ID as reviewId,
        r.ORG_CODE as orgCode,
        r.ORG_NAME as orgName,
        r.BILL_TYPE as billType,
        r.BILL_TYPE_NAME as billTypeName,
        r.BUSINESS_TYPE as businessType,
        r.BUSINESS_TYPE_NAME as businessTypeName,
        r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
        r.BILL_CODE as billCode,
        date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
        r.REVIEW_TIME as reviewTime,
        r.LAST_REVIEW_TIME as lastReviewTime,
        r.OVER_REVIEW_TIME as overReviewTime,
        r.REVIEW_PERSON_ID as reviewPersonId,
        r.REVIEW_PERSON_NAME as reviewPersonName,
        r.REVIEW_DESC as reviewDesc,
        r.REVIEW_STATUS as reviewStatus,
        r.REVIEW_STATUS_NAME as reviewStatusName,
        r.CUST_ID as custId,
        r.CUST_NAME as custName,
        r.PHONE as phone,
        r.GENDER as gender,
        r.GENDER_NAME as genderName,
        r.TOUCH_STATUS as touchStatus,
        r.TOUCH_STATUS_NAME as touchStatusName,
        r.NODE_CODE as nodeCode,
        r.NODE_NAME as nodeName,
        r.SEND_DLR_CODE as sendDlrCode,
    	r.SEND_DLR_SHORT_NAME as sendDlrShortName,
    	r.SEND_TIME,
    	r.INTEN_LEVEL_CODE,
    	r.INTEN_LEVEL_NAME,
    	r.INTEN_BRAND_CODE,
    	r.INTEN_BRAND_NAME,
    	r.INTEN_SERIES_CODE,
    	r.INTEN_SERIES_NAME,
    	r.INTEN_CAR_TYPE_CODE,
    	r.INTEN_CAR_TYPE_NAME,
        r.EXTENDS_JSON as extendsJson,
        r.CREATED_DATE as createdDate,
        r.CREATED_NAME as createdName,
        r.UPDATE_CONTROL_ID as updateControlId
        from t_sac_review_his r
        where r.bill_code=#{param.billCode}
        ORDER BY r.CREATED_DATE desc
  </select>

    <!-- clue 更新 -->
    <update id="updateInfoDlr" parameterType="java.util.Map">
        update adp_leads.t_sac_clue_info_dlr set
        COUNTY_CODE='',
        COUNTY_NAME='',
        <if test = 'param.provinceCode !=null '>PROVINCE_CODE=#{param.provinceCode},</if>
        <if test = 'param.provinceName !=null '>PROVINCE_NAME=#{param.provinceName},</if>
        <if test = 'param.cityCode !=null '>CITY_CODE=#{param.cityCode},</if>
        <if test = 'param.cityName !=null '>CITY_NAME=#{param.cityName},</if>
        <if test = 'param.cityFirmCode !=null '>CITY_FIRM_CODE=#{param.cityFirmCode},</if>
        <if test = 'param.cityFirmName !=null '>CITY_FIRM_NAME=#{param.cityFirmName},</if>
        DLR_CODE=#{param.dlrCode},
        DLR_SHORT_NAME=#{param.dlrShortName},
        REVIEW_PERSON_ID='',
        REVIEW_PERSON_NAME='',
        STATUS_CODE='1',
        STATUS_NAME='待分配'
        where PHONE=#{param.phone}
    </update>

  <update id="updateSleepCluePool" parameterType="java.util.Map">
      update t_sac_review  set
      COUNTY_CODE=''
      ,COUNTY_NAME=''
      <if test = 'param.provinceCode !=null '> ,PROVINCE_CODE=#{param.provinceCode}</if>
      <if test = 'param.provinceName !=null '> ,PROVINCE_NAME=#{param.provinceName}</if>
      <if test = 'param.cityCode !=null '> , CITY_CODE=#{param.cityCode}</if>
      <if test = 'param.cityName !=null '> ,CITY_NAME=#{param.cityName}</if>

      <if test = 'param.cityFirmCode !=null '>  ,CITY_FIRM_CODE=#{param.cityFirmCode}</if>
      <if test = 'param.cityFirmName !=null '> ,CITY_FIRM_NAME=#{param.cityFirmName} </if>
      ,ORG_CODE=#{param.dlrCode}
      ,ORG_NAME=#{param.dlrShortName}
      ,REVIEW_PERSON_ID=''
      ,REVIEW_PERSON_NAME=''
      ,ASSIGN_STATUS='0'
      ,ASSIGN_STATUS_NAME='待分配'
      ,ASSIGN_TIME=null
      ,ASSIGN_PERSON_ID=''
      ,ASSIGN_PERSON_NAME=''
      where
      PHONE=#{param.phone}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-08-20-->
  <update id="updateReviewAssign">
      update t_sac_review
      set ASSIGN_PERSON_ID=#{param.assignPersonId},
          ASSIGN_PERSON_NAME=#{param.assignPersonName},
          ASSIGN_TIME=now(),
          ASSIGN_STATUS='1',
          ASSIGN_STATUS_NAME='已分配',
          ORG_CODE=#{param.dlrCode},
          ORG_NAME=#{param.dlrShortName},
          PLAN_REVIEW_TIME=case when PLAN_REVIEW_TIME is null then DATE_ADD(NOW(),INTERVAL 1 DAY) else PLAN_REVIEW_TIME end,
          OVER_REVIEW_TIME=case when OVER_REVIEW_TIME is null then DATE_ADD(NOW(),INTERVAL 1 DAY) else OVER_REVIEW_TIME end,
          REVIEW_PERSON_ID=#{param.personId},
          REVIEW_PERSON_NAME=#{param.personName},
          MODIFIER=#{param.assignPersonId},
          MODIFY_NAME=#{param.assignPersonName},
          LAST_UPDATED_DATE=now(),
          UPDATE_CONTROL_ID=uuid()
      where REVIEW_ID = #{param.reviewId}
  </update>

    <update id="updateReviewAssignBatch">
        <foreach collection="list" item="param" separator=";">
            update t_sac_review
            set
            <if test ='param.clearDcc'> COLUMN19 = NULL,</if>
            ASSIGN_PERSON_ID=#{param.assignPersonId},
            ASSIGN_PERSON_NAME=#{param.assignPersonName},
            ASSIGN_TIME=now(),
            ASSIGN_STATUS='1',
            ASSIGN_STATUS_NAME='已分配',
            ORG_CODE=#{param.dlrCode},
            ORG_NAME=#{param.dlrShortName},
            PLAN_REVIEW_TIME=case when PLAN_REVIEW_TIME is null then DATE_ADD(NOW(),INTERVAL 1 DAY) else
            PLAN_REVIEW_TIME end,
            OVER_REVIEW_TIME=case when OVER_REVIEW_TIME is null then DATE_ADD(NOW(),INTERVAL 1 DAY) else
            OVER_REVIEW_TIME end,
            REVIEW_PERSON_ID=#{param.personId},
            REVIEW_PERSON_NAME=#{param.personName},
            MODIFIER=#{param.assignPersonId},
            MODIFY_NAME=#{param.assignPersonName},
            LAST_UPDATED_DATE=now(),
            UPDATE_CONTROL_ID=uuid()
            where REVIEW_ID = #{param.reviewId}
        </foreach>
    </update>

  <insert id="insertSacReview">
        insert into t_sac_review(
         REVIEW_ID
        ,ORG_CODE
        ,ORG_NAME
        ,BILL_TYPE
        ,BILL_TYPE_NAME
        ,BUSINESS_TYPE
        ,BUSINESS_TYPE_NAME
        ,INFO_CHAN_M_CODE
		,INFO_CHAN_M_NAME
		,INFO_CHAN_D_CODE
		,INFO_CHAN_D_NAME
		,INFO_CHAN_DD_CODE
		,INFO_CHAN_DD_NAME
		,CHANNEL_CODE
		,CHANNEL_NAME
        ,BILL_CODE
        ,PLAN_REVIEW_TIME
        ,PLAN_COME_TIME
        ,FACT_COME_TIME
        ,IS_COME
        ,REVIEW_TIME
        ,LAST_REVIEW_TIME
        ,OVER_REVIEW_TIME
        ,ASSIGN_STATUS
        ,ASSIGN_STATUS_NAME
        ,ASSIGN_TIME
        ,ASSIGN_PERSON_ID
        ,ASSIGN_PERSON_NAME
        ,REVIEW_PERSON_ID
        ,REVIEW_PERSON_NAME
        ,REVIEW_DESC
        ,REVIEW_STATUS
        ,REVIEW_STATUS_NAME
        ,CUST_ID
        ,CUST_NAME
        ,PHONE
        ,GENDER
        ,GENDER_NAME
        ,TOUCH_STATUS
        ,TOUCH_STATUS_NAME
        ,ERROR_REASON_CODE
        ,ERROR_REASON_NAME
        ,NODE_CODE
        ,NODE_NAME
        ,SEND_DLR_CODE
        ,SEND_DLR_SHORT_NAME
        ,SEND_TIME
        ,INTEN_LEVEL_CODE
        ,INTEN_LEVEL_NAME
        ,INTEN_BRAND_CODE
        ,INTEN_BRAND_NAME
        ,INTEN_SERIES_CODE
        ,INTEN_SERIES_NAME
        ,INTEN_CAR_TYPE_CODE
        ,INTEN_CAR_TYPE_NAME
        ,COLUMN1
        ,COLUMN2
        ,COLUMN3
        ,COLUMN4
        ,COLUMN5
        ,COLUMN6
        ,COLUMN7
        ,COLUMN8
        ,COLUMN9
        ,COLUMN10
        ,COLUMN11
        ,COLUMN12
        ,COLUMN13
        ,COLUMN14
        ,COLUMN15
        ,COLUMN16
        ,COLUMN17
        ,COLUMN18
        ,COLUMN19
        ,COLUMN20
        ,BIG_COLUMN1
        ,BIG_COLUMN2
        ,BIG_COLUMN3
        ,BIG_COLUMN4
        ,BIG_COLUMN5
        ,EXTENDS_JSON
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        ,PROVINCE_CODE
		,PROVINCE_NAME
		,CITY_CODE
		,CITY_NAME
		,COUNTY_CODE
		,COUNTY_NAME
        )
        values(
         #{param.reviewId}
		,#{param.orgCode}
		,#{param.orgName}
		,#{param.billType}
		,#{param.billTypeName}
		,#{param.businessType}
		,#{param.businessTypeName}
		,#{param.infoChanMCode}
		,#{param.infoChanMName}
		,#{param.infoChanDCode}
		,#{param.infoChanDName}
		,#{param.infoChanDdCode}
		,#{param.infoChanDdName}
		,#{param.channelCode}
		,#{param.channelName}
		,#{param.billCode}
		,#{param.planReviewTime}
		,#{param.planComeTime}
		,#{param.factComeTime}
		,#{param.isCome}
		,#{param.reviewTime}
		,#{param.lastReviewTime}
		,#{param.planReviewTime}
		,#{param.assignStatus}
		,#{param.assignStatusName}
		,#{param.assignTime}
		,#{param.assignPersonId}
		,#{param.assignPersonName}
		,#{param.reviewPersonId}
		,#{param.reviewPersonName}
		,#{param.reviewDesc}
		,#{param.reviewStatus}
		,#{param.reviewStatusName}
		,#{param.custId}
		,#{param.custName}
		,#{param.phone}
		,#{param.gender}
		,#{param.genderName}
		,#{param.touchStatus}
		,#{param.touchStatusName}
		,#{param.errorReasonCode}
		,#{param.errorReasonName}
		,#{param.nodeCode}
		,#{param.nodeName}
		,#{param.sendDlrCode}
		,#{param.sendDlrShortName}
		,#{param.sendTime}
		,#{param.intenLevelCode}
		,#{param.intenLevelName}
		,#{param.intenBrandCode}
		,#{param.intenBrandName}
		,#{param.intenSeriesCode}
		,#{param.intenSeriesName}
		,#{param.intenCarTypeCode}
		,#{param.intenCarTypeName}
		,#{param.column1}
		,#{param.column2}
		,#{param.column3}
		,#{param.column4}
		,#{param.column5}
		,#{param.column6}
		,#{param.column7}
		,#{param.column8}
		,#{param.column9}
		,#{param.column10}
		,#{param.column11}
		,#{param.column12}
		,#{param.column13}
		,#{param.column14}
		,#{param.column15}
		,#{param.column16}
		,#{param.column17}
		,#{param.column18}
		,#{param.dccFlag}
		,#{param.column20}
		,#{param.bigColumn1}
		,#{param.bigColumn2}
		,#{param.bigColumn3}
		,#{param.bigColumn4}
		,#{param.bigColumn5}
		,#{param.extendsJson}
		,#{param.oemId}
		,#{param.groupId}
		,#{param.creator}
		,#{param.createdName}
		,#{param.createdDate}
		,#{param.modifier}
		,#{param.modifyName}
		,#{param.lastUpdatedDate}
		,'1'
		,#{param.updateControlId}
		,#{param.provinceCode}
		,#{param.provinceName}
		,#{param.cityCode}
		,#{param.cityName}
		,#{param.countyCode}
		,#{param.countyName}
		)
    </insert>

    <update id="updateSacReview">
    	update t_sac_review  set
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.orgCode!=null'> ,ORG_CODE = #{param.orgCode}</if>
	    <if test = 'param.orgName!=null'> ,ORG_NAME = #{param.orgName}</if>
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.billTypeName!=null'> ,BILL_TYPE_NAME = #{param.billTypeName}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{param.businessTypeName}</if>
	    <if test = 'param.infoChanMCode!=null'> ,INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
	    <if test = 'param.infoChanMName!=null'> ,INFO_CHAN_M_NAME = #{param.infoChanMName}</if>
	    <if test = 'param.infoChanDCode!=null'> ,INFO_CHAN_D_CODE = #{param.infoChanDCode}</if>
	    <if test = 'param.infoChanDName!=null'> ,INFO_CHAN_D_NAME = #{param.infoChanDName}</if>
	    <if test = 'param.infoChanDdCode!=null'> ,INFO_CHAN_DD_CODE = #{param.infoChanDdCode}</if>
	    <if test = 'param.infoChanDdName!=null'> ,INFO_CHAN_DD_NAME = #{param.infoChanDdName}</if>
	    <if test = 'param.channelCode!=null'> ,CHANNEL_CODE = #{param.channelCode}</if>
	    <if test = 'param.channelName!=null'> ,CHANNEL_NAME = #{param.channelName}</if>
	    <if test = 'param.billCode!=null'> ,BILL_CODE = #{param.billCode}</if>
	    <if test = 'param.planReviewTime!=null'>
	    ,PLAN_REVIEW_TIME = case when #{param.planReviewTime}='' then null else #{param.planReviewTime} end
        ,OVER_REVIEW_TIME = case when #{param.planReviewTime}='' then null else #{param.planReviewTime} end
	    </if>
	    <if test = 'param.planComeTime!=null'>
	    ,PLAN_COME_TIME = case when #{param.planComeTime}='' then null else #{param.planComeTime} end
	    </if>
	    <if test = 'param.factComeTime!=null'>
	    ,FACT_COME_TIME = case when #{param.factComeTime}='' then null else #{param.factComeTime} end
	    </if>
	    <if test = 'param.isCome!=null'> ,IS_COME = #{param.isCome}</if>
	    <if test = 'param.reviewTime!=null'>
	    ,REVIEW_TIME = case when #{param.reviewTime}='' then null else #{param.reviewTime} end
	    </if>
	    <if test = 'param.lastReviewTime!=null'>
	    ,LAST_REVIEW_TIME = case when #{param.lastReviewTime}='' then null else #{param.lastReviewTime} end
	    </if>
	    <!-- <if test = 'param.overReviewTime!=null'>
	    ,OVER_REVIEW_TIME = case when #{param.overReviewTime}='' then null else #{param.overReviewTime} end
	    </if> -->
	    <if test = 'param.assignStatus!=null'> ,ASSIGN_STATUS = #{param.assignStatus}</if>
	    <if test = 'param.assignStatusName!=null'> ,ASSIGN_STATUS_NAME = #{param.assignStatusName}</if>
	    <if test = 'param.assignTime!=null'>
	    ,ASSIGN_TIME = case when #{param.assignTime}='' then null else #{param.assignTime} end
	    </if>
	    <if test = 'param.assignPersonId!=null'> ,ASSIGN_PERSON_ID = #{param.assignPersonId}</if>
	    <if test = 'param.assignPersonName!=null'> ,ASSIGN_PERSON_NAME = #{param.assignPersonName}</if>
	    <if test = 'param.reviewPersonId!=null'> ,REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
	    <if test = 'param.reviewPersonName!=null'> ,REVIEW_PERSON_NAME = #{param.reviewPersonName}</if>
	    <if test = 'param.reviewDesc!=null'> ,REVIEW_DESC = #{param.reviewDesc}</if>
	    <if test = 'param.reviewStatus!=null'> ,REVIEW_STATUS = #{param.reviewStatus}</if>
	    <if test = 'param.reviewStatusName!=null'> ,REVIEW_STATUS_NAME = #{param.reviewStatusName}</if>
	    <if test = 'param.custId!=null'> ,CUST_ID = #{param.custId}</if>
	    <if test = 'param.custName!=null'> ,CUST_NAME = #{param.custName}</if>
	    <if test = 'param.phone!=null'> ,PHONE = #{param.phone}</if>
	    <if test = 'param.gender!=null'> ,GENDER = #{param.gender}</if>
	    <if test = 'param.genderName!=null'> ,GENDER_NAME = #{param.genderName}</if>
	    <if test = 'param.touchStatus!=null'> ,TOUCH_STATUS = #{param.touchStatus}</if>
	    <if test = 'param.touchStatusName!=null'> ,TOUCH_STATUS_NAME = #{param.touchStatusName}</if>
	    <if test = 'param.errorReasonCode!=null'> ,ERROR_REASON_CODE = #{param.errorReasonCode}</if>
	    <if test = 'param.errorReasonName!=null'> ,ERROR_REASON_NAME = #{param.errorReasonName}</if>
        <if test = 'param.firstReasonCode!=null'> ,FIRST_REASON_CODE = #{param.firstReasonCode}</if>
        <if test = 'param.firstReasonName!=null'> ,FIRST_REASON_NAME = #{param.firstReasonName}</if>
	    <if test = 'param.nodeCode!=null'> ,NODE_CODE = #{param.nodeCode}</if>
	    <if test = 'param.nodeName!=null'> ,NODE_NAME = #{param.nodeName}</if>
	    <if test = 'param.sendDlrCode!=null'> ,SEND_DLR_CODE = #{param.sendDlrCode}</if>
	    <if test = 'param.sendDlrShortName!=null'> ,SEND_DLR_SHORT_NAME = #{param.sendDlrShortName}</if>
	    <if test = 'param.sendTime!=null'>
	    ,SEND_TIME = case when #{param.sendTime}='' then null else #{param.sendTime} end
	    </if>
	    <if test = 'param.intenLevelCode!=null'> ,INTEN_LEVEL_CODE = #{param.intenLevelCode}</if>
		<if test = 'param.intenLevelName!=null'> ,INTEN_LEVEL_NAME = #{param.intenLevelName}</if>
	    <if test = 'param.intenBrandCode!=null'> ,INTEN_BRAND_CODE = #{param.intenBrandCode}</if>
		<if test = 'param.intenBrandName!=null'> ,INTEN_BRAND_NAME = #{param.intenBrandName}</if>
		<if test = 'param.intenSeriesCode!=null'> ,INTEN_SERIES_CODE = #{param.intenSeriesCode}</if>
		<if test = 'param.intenSeriesName!=null'> ,INTEN_SERIES_NAME = #{param.intenSeriesName}</if>
		<if test = 'param.intenCarTypeCode!=null'> ,INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}</if>
		<if test = 'param.intenCarTypeName!=null'> ,INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName}</if>
	    <if test = 'param.column1!=null'> ,COLUMN1 = #{param.column1}</if>
	    <if test = 'param.column2!=null'> ,COLUMN2 = #{param.column2}</if>
	    <if test = 'param.column3!=null'> ,COLUMN3 = #{param.column3}</if>
	    <if test = 'param.column4!=null'> ,COLUMN4 = #{param.column4}</if>
	    <if test = 'param.column5!=null'> ,COLUMN5 = #{param.column5}</if>
	    <if test = 'param.column6!=null'> ,COLUMN6 = #{param.column6}</if>
	    <if test = 'param.column7!=null'> ,COLUMN7 = #{param.column7}</if>
	    <if test = 'param.column8!=null'> ,COLUMN8 = #{param.column8}</if>
	    <if test = 'param.column9!=null'> ,COLUMN9 = #{param.column9}</if>
	    <if test = 'param.column10!=null'> ,COLUMN10 = #{param.column10}</if>
	    <if test = 'param.column11!=null'> ,COLUMN11 = #{param.column11}</if>
	    <if test = 'param.column12!=null'> ,COLUMN12 = #{param.column12}</if>
	    <if test = 'param.column13!=null'> ,COLUMN13 = #{param.column13}</if>
	    <if test = 'param.column14!=null'> ,COLUMN14 = #{param.column14}</if>
	    <if test = 'param.column15!=null'> ,COLUMN15 = #{param.column15}</if>
	    <if test = 'param.column16!=null'> ,COLUMN16 = #{param.column16}</if>
	    <if test = 'param.column17!=null'> ,COLUMN17 = #{param.column17}</if>
	    <if test = 'param.column18!=null'> ,COLUMN18 = #{param.column18}</if>
	    <if test = 'param.column19!=null'> ,COLUMN19 = #{param.column19}</if>
	    <if test = 'param.column20!=null'> ,COLUMN20 = #{param.column20}</if>
	    <if test = 'param.bigColumn1!=null'> ,BIG_COLUMN1 = #{param.bigColumn1}</if>
	    <if test = 'param.bigColumn2!=null'> ,BIG_COLUMN2 = #{param.bigColumn2}</if>
	    <if test = 'param.bigColumn3!=null'> ,BIG_COLUMN3 = #{param.bigColumn3}</if>
	    <if test = 'param.bigColumn4!=null'> ,BIG_COLUMN4 = #{param.bigColumn4}</if>
	    <if test = 'param.bigColumn5!=null'> ,BIG_COLUMN5 = #{param.bigColumn5}</if>
	    <if test = 'param.extendsJson!=null'> ,EXTENDS_JSON = #{param.extendsJson}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
		<if test = 'param.provinceCode !=null '> ,PROVINCE_CODE=#{param.provinceCode}</if>
		<if test = 'param.provinceName !=null '> ,PROVINCE_NAME=#{param.provinceName}</if>
		<if test = 'param.cityCode !=null '> , CITY_CODE=#{param.cityCode}</if>
		<if test = 'param.cityName !=null '> ,CITY_NAME=#{param.cityName}</if>
		<if test = 'param.countyCode !=null'> ,COUNTY_CODE=#{param.countyCode}</if>
		<if test = 'param.countyName !=null'> ,COUNTY_NAME=#{param.countyName}</if>

    	where 1=1
         and REVIEW_ID = #{param.reviewId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>

    </update>

    <!-- clue 更新 -->
    <update id="updateClue">
        UPDATE t_sac_clue_info_dlr set
        FIRST_REVIEW_TIME=case when FIRST_REVIEW_TIME is null then now() else FIRST_REVIEW_TIME end,
        LAST_REVIEW_TIME=now(),LAST_UPDATED_DATE=now(),
        STATUS_CODE='10',STATUS_NAME='战败' where PHONE=#{param.phone}
    </update>

    <select id="queryTaskNumByPerson" resultType="java.util.Map">
	    SELECT *
	    FROM(
		    SELECT
			r.REVIEW_PERSON_ID,
			r.REVIEW_PERSON_NAME,
			COUNT(1) AS TASK_NUM
			FROM t_sac_review r
			WHERE 1=1
			AND R.ORG_CODE=#{param.orgCode}
			<if test="param.planReviewTime != null and ''!=param.planReviewTime">
			AND (DATE(r.PLAN_REVIEW_TIME)=DATE(#{param.planReviewTime}) OR ifnull(r.PLAN_REVIEW_TIME,'')='')
			</if>
			AND r.REVIEW_PERSON_ID is not null
			<if test="param.personIdList != null and ''!=param.personIdList">
			and r.REVIEW_PERSON_ID IN
			<foreach item="item" collection=" param.personIdList.split(',')"
			 index="index" open="(" separator="," close=")">
			 #{item}
			</foreach>
			</if>
			<if test="param.reviewIdList != null and ''!=param.reviewIdList">
			and r.REVIEW_ID NOT IN
			<foreach item="item" collection=" param.reviewIdList.split(',')"
			 index="index" open="(" separator="," close=")">
			 #{item}
			</foreach>
			</if>
			GROUP BY r.REVIEW_PERSON_ID,r.REVIEW_PERSON_NAME
		) A
		ORDER BY A.TASK_NUM
    </select>

    <select id="queryTaskNumByPersonNoPage" resultType="java.util.Map">
	    SELECT *
	    FROM(
		    SELECT
			r.REVIEW_PERSON_ID,
			r.REVIEW_PERSON_NAME,
			COUNT(1) AS TASK_NUM
			FROM t_sac_review r
			WHERE 1=1
			AND R.ORG_CODE=#{param.orgCode}
			<if test="param.planReviewTime != null and ''!=param.planReviewTime">
			AND (DATE(r.PLAN_REVIEW_TIME)=DATE(#{param.planReviewTime}) OR ifnull(r.PLAN_REVIEW_TIME,'')='')
			</if>
			AND r.REVIEW_PERSON_ID is not null
			<if test="param.personIdList != null and ''!=param.personIdList">
			and r.REVIEW_PERSON_ID IN
			<foreach item="item" collection=" param.personIdList.split(',')"
			 index="index" open="(" separator="," close=")">
			 #{item}
			</foreach>
			</if>
			<if test="param.reviewIdList != null and ''!=param.reviewIdList">
			and r.REVIEW_ID NOT IN
			<foreach item="item" collection=" param.reviewIdList.split(',')"
			 index="index" open="(" separator="," close=")">
			 #{item}
			</foreach>
			</if>
			GROUP BY r.REVIEW_PERSON_ID,r.REVIEW_PERSON_NAME
		) A
    </select>

    <select id="queryReviewInfoById" resultType="java.util.Map">
    select
	REVIEW_ID,
	ORG_CODE,
	ORG_NAME,
	BILL_TYPE,
	BILL_TYPE_NAME,
	BUSINESS_TYPE,
	BUSINESS_TYPE_NAME,
	INFO_CHAN_M_CODE,
	INFO_CHAN_M_NAME,
	INFO_CHAN_D_CODE,
	INFO_CHAN_D_NAME,
	INFO_CHAN_DD_CODE,
	INFO_CHAN_DD_NAME,
	CHANNEL_CODE,
	CHANNEL_NAME,
	BILL_CODE,
	PLAN_REVIEW_TIME,
	REVIEW_TIME,
	LAST_REVIEW_TIME,
	OVER_REVIEW_TIME,
	PLAN_COME_TIME,
	FACT_COME_TIME,
	IS_COME,
	ASSIGN_STATUS,
	ASSIGN_STATUS_NAME,
	ASSIGN_TIME,
	ASSIGN_PERSON_ID,
	ASSIGN_PERSON_NAME,
	REVIEW_PERSON_ID,
	REVIEW_PERSON_NAME,
	REVIEW_DESC,
	REVIEW_STATUS,
	REVIEW_STATUS_NAME,
	CUST_ID,
	CUST_NAME,
	PHONE,
	GENDER,
	GENDER_NAME,
	TOUCH_STATUS,
	TOUCH_STATUS_NAME,
	ERROR_REASON_CODE,
	ERROR_REASON_NAME,
	NODE_CODE,
	NODE_NAME,
	SEND_DLR_CODE,
	SEND_DLR_SHORT_NAME,
	SEND_TIME,
	INTEN_LEVEL_CODE,
	INTEN_LEVEL_NAME,
	INTEN_BRAND_CODE,
	INTEN_BRAND_NAME,
	INTEN_SERIES_CODE,
	INTEN_SERIES_NAME,
	INTEN_CAR_TYPE_CODE,
	INTEN_CAR_TYPE_NAME,
	CREATED_NAME,
	CREATED_DATE,
	UPDATE_CONTROL_ID,
	COLUMN1,
	COLUMN2,
	COLUMN3,
	COLUMN4,
	COLUMN5,
	COLUMN6,
	COLUMN7,
	COLUMN8,
	COLUMN9,
	COLUMN10,
	COLUMN11,
	COLUMN12,
	COLUMN13,
	COLUMN14,
	COLUMN15,
	COLUMN16,
	COLUMN17,
	COLUMN18,
	COLUMN19,
	COLUMN20,
	BIG_COLUMN1,
	BIG_COLUMN2,
	BIG_COLUMN3,
	BIG_COLUMN4,
	BIG_COLUMN5,
	EXTENDS_JSON
	from t_sac_review r
	where 1=1
	and r.review_id=#{param.reviewId}
    </select>

    <select id="queryReviewInfoHisById" resultType="java.util.Map">
    select
	REVIEW_ID,
	ORG_CODE,
	ORG_NAME,
	BILL_TYPE,
	BILL_TYPE_NAME,
	BUSINESS_TYPE,
	BUSINESS_TYPE_NAME,
	INFO_CHAN_M_CODE,
	INFO_CHAN_M_NAME,
	INFO_CHAN_D_CODE,
	INFO_CHAN_D_NAME,
	INFO_CHAN_DD_CODE,
	INFO_CHAN_DD_NAME,
	CHANNEL_CODE,
	CHANNEL_NAME,
	BILL_CODE,
	PLAN_REVIEW_TIME,
	REVIEW_TIME,
	LAST_REVIEW_TIME,
	OVER_REVIEW_TIME,
	PLAN_COME_TIME,
	FACT_COME_TIME,
	IS_COME,
	ASSIGN_STATUS,
	ASSIGN_STATUS_NAME,
	ASSIGN_TIME,
	ASSIGN_PERSON_ID,
	ASSIGN_PERSON_NAME,
	REVIEW_PERSON_ID,
	REVIEW_PERSON_NAME,
	REVIEW_DESC,
	REVIEW_STATUS,
	REVIEW_STATUS_NAME,
	CUST_ID,
	CUST_NAME,
	PHONE,
	GENDER,
	GENDER_NAME,
	TOUCH_STATUS,
	TOUCH_STATUS_NAME,
	ERROR_REASON_CODE,
	ERROR_REASON_NAME,
	NODE_CODE,
	NODE_NAME,
	SEND_DLR_CODE,
	SEND_DLR_SHORT_NAME,
	SEND_TIME,
	INTEN_LEVEL_CODE,
	INTEN_LEVEL_NAME,
	INTEN_BRAND_CODE,
	INTEN_BRAND_NAME,
	INTEN_SERIES_CODE,
	INTEN_SERIES_NAME,
	INTEN_CAR_TYPE_CODE,
	INTEN_CAR_TYPE_NAME,
	CREATED_NAME,
	CREATED_DATE,
	UPDATE_CONTROL_ID,
	COLUMN1,
	COLUMN2,
	COLUMN3,
	COLUMN4,
	COLUMN5,
	COLUMN6,
	COLUMN7,
	COLUMN8,
	COLUMN9,
	COLUMN10,
	COLUMN11,
	COLUMN12,
	COLUMN13,
	COLUMN14,
	COLUMN15,
	COLUMN16,
	COLUMN17,
	COLUMN18,
	COLUMN19,
	COLUMN20,
	BIG_COLUMN1,
	BIG_COLUMN2,
	BIG_COLUMN3,
	BIG_COLUMN4,
	BIG_COLUMN5,
	EXTENDS_JSON
	from t_sac_review_his r
	where 1=1
	and r.review_id=#{param.reviewId}
    </select>
    <select id="selectReviewByID" resultType="java.util.Map">
        SELECT
               t.REVIEW_PERSON_ID,
               t.REVIEW_PERSON_NAME,
               t.ORG_CODE as dlrCode,
               t.ORG_NAME as dlrName,
               t.BILL_CODE as serverOrder,
               t.REVIEW_ID ,
               t.CUST_ID,
               t.CUST_NAME
        FROM t_sac_review t
        WHERE 1=1
        <if test="reviewIdListString != null and ''!=reviewIdListString">
            and  t.REVIEW_ID IN
            <foreach item="item" collection=" reviewIdListString.split(',')"
                     index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectEmpByDlrCode" resultType="java.util.Map">
        SELECT
            EMP_ID,
            EMP_NAME,
            EMP_CODE,
            DLR_CODE
        FROM
            t_usc_mdm_org_employee
        WHERE
            DLR_CODE = #{param.dlrCode}
            and USER_STATUS = '1'
          AND STATION_ID IN ( 'smart_bm_0005', 'smart_bm_0016' )
    </select>
    <!-- 店端线索消息新增 -->
    <insert id="createMsgRecord">
        INSERT INTO `t_sac_clue_msg_record` (
            `MESSAGE_ID`,
            `IS_READ`,
            `DLR_CODE`,
            `PHONE`,
            `MESSAGE_TYPE`,
            `BUSI_KEYVALUE`,
            `RECEIVE_EMP_ID`,
            `MESSAGE_CONTENT`,
            `RELATION_BILL_ID`,
            `EXTEND_JSON`,
            `COLUMN1`,
            `OEM_ID`,
            `GROUP_ID`,
            `OEM_CODE`,
            `GROUP_CODE`,
            `CREATOR`,
            `CREATED_NAME`,
            `CREATED_DATE`,
            `MODIFIER`,
            `MODIFY_NAME`,
            `LAST_UPDATED_DATE`,
            `IS_ENABLE`,
            `SDP_USER_ID`,
            `SDP_ORG_ID`,
            `UPDATE_CONTROL_ID`
        )
        VALUES
            (
                #{param.messageId},
                '0',
                #{param.dlrCode},
                #{param.phone},
                #{param.messageType},
                '1',
                #{param.receiveEmpId},
                #{param.messageContent},
                #{param.relationBillId},
                #{param.extendsJson},
                #{param.column1},
                #{param.oemId},
                #{param.groupId},
                #{param.oemCode},
                #{param.groupCode},
                #{param.creator},
                #{param.createdName},
                now(),
                #{param.modifier},
                #{param.modifyName},
                now(),
                '1',
                '1',
                '1',
                uuid()
            )
    </insert>
    <select id="selectDlrByPhone" resultType="java.util.Map">
        SELECT ID,CUST_ID,DLR_CODE,DLR_SHORT_NAME as dlr_name  FROM t_sac_clue_info_dlr WHERE PHONE =#{param.phone}
    </select>

  <select id="findPhone" resultType="java.lang.String">
      select PHONE from t_sac_clue_info_dlr where ID=#{id}
    </select>

    <!-- clue 更新 -->
  <update id="updateClueInfoDlr">
      update
      t_sac_clue_info_dlr
      set LAST_UPDATED_DATE=now(),
      MODIFIER=#{modifier},
      ALLOCATE_TIME=now(),
      REVIEW_PERSON_NAME =#{personName},
      REVIEW_PERSON_ID =#{personId},
      DLR_CODE=#{dlrCode},
          <if test='isFlag =="2"'>
              MANAGE_LABEL_CODE='1',
              MANAGE_LABEL_NAME='线索移交-已处理',
          </if>
      DLR_SHORT_NAME = #{dlrShortName}

      where REVIEW_ID=#{reviewId}
    </update>

    <update id="updateClueInfoDlrBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            update
            t_sac_clue_info_dlr
            set LAST_UPDATED_DATE=now(),
            MODIFIER=#{param.modifier},
            ALLOCATE_TIME=now(),
            REVIEW_PERSON_NAME =#{param.personName},
            REVIEW_PERSON_ID =#{param.personId},
            DLR_CODE=#{param.dlrCode},
            <if test='param.isFlag =="2"'>
                MANAGE_LABEL_CODE='1',
                MANAGE_LABEL_NAME='线索移交-已处理',
            </if>
            <if test ='param.clearDcc'>COLUMN19 = NULL,</if>
            DLR_SHORT_NAME = #{param.dlrShortName}
            where REVIEW_ID=#{param.reviewId}
        </foreach>
    </update>

  <select id="findClueInfoDlr" resultType="java.util.Map">
      SELECT *
      FROM(
      SELECT
      r.REVIEW_PERSON_ID,
      r.REVIEW_PERSON_NAME,
      COUNT(1) AS TASK_NUM
      FROM t_sac_clue_info_dlr r
      WHERE  r.DLR_CODE=#{param.orgCode}
      AND r.REVIEW_PERSON_ID is not null
      <if test="param.personIdList != null and ''!=param.personIdList">
          and r.REVIEW_PERSON_ID IN
          <foreach item="item" collection=" param.personIdList.split(',')"
                   index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      <if test="param.reviewIdList != null and ''!=param.reviewIdList">
          and r.REVIEW_ID NOT IN
          <foreach item="item" collection=" param.reviewIdList.split(',')"
                   index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      GROUP BY r.REVIEW_PERSON_ID,r.REVIEW_PERSON_NAME
      ) A
      ORDER BY A.TASK_NUM
    </select>

    <!-- clue 更新 -->
  <update id="updateClueDlr">
      update
          adp_leads.t_sac_clue_info_dlr
          set
              ALLOCATE_TIME=now()
              where
                  REVIEW_ID=#{reviewId}
    </update>

    <!-- clue 更新 -->
    <update id="updateClueDlrBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            UPDATE
            adp_leads.t_sac_clue_info_dlr
            SET
            ALLOCATE_TIME=#{param.allocateTime}
            <if test ='param.clearDcc'> ,COLUMN19 = NULL</if>
            WHERE REVIEW_ID=#{param.reviewId}
        </foreach>
    </update>

    <select id="selectOneProductByRand" resultType="java.util.Map">
        SELECT
        *
        FROM
        (
        SELECT
        emp_name,
        user_id
        FROM
        mp.t_usc_mdm_org_employee
        WHERE
        ORG_TYPE = '1'
        AND ( STATION_ID IN ( 'smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064' )
        OR COLUMN2 IN ( 'smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064' ) )
        AND USER_STATUS = '1'
        AND dlr_code = #{param.dlrCode}
        and user_id != #{param.reviewPersonId}
        ) T
        ORDER BY
        rand( )
        LIMIT 1;
    </select>

  <delete id="deleteNewClue">
      DELETE FROM adp_leads.t_sac_clue_info_dlr
      WHERE PHONE = #{param.phone}
  </delete>

  <update id="batchUpdateReview">
        <foreach collection="list" item="item"  index="index" separator=";">
            update t_sac_review  set
            MODIFIER=#{item.modifier},
            MODIFY_NAME=#{item.modifyName},
            LAST_UPDATED_DATE=sysdate(),
            UPDATE_CONTROL_ID=uuid()
            <if test = 'item.orgCode!=null'> ,ORG_CODE = #{item.orgCode}</if>
            <if test = 'item.orgName!=null'> ,ORG_NAME = #{item.orgName}</if>
            <if test = 'item.billType!=null'> ,BILL_TYPE = #{item.billType}</if>
            <if test = 'item.billTypeName!=null'> ,BILL_TYPE_NAME = #{item.billTypeName}</if>
            <if test = 'item.businessType!=null'> ,BUSINESS_TYPE = #{item.businessType}</if>
            <if test = 'item.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{item.businessTypeName}</if>
            <if test = 'item.infoChanMCode!=null'> ,INFO_CHAN_M_CODE = #{item.infoChanMCode}</if>
            <if test = 'item.infoChanMName!=null'> ,INFO_CHAN_M_NAME = #{item.infoChanMName}</if>
            <if test = 'item.infoChanDCode!=null'> ,INFO_CHAN_D_CODE = #{item.infoChanDCode}</if>
            <if test = 'item.infoChanDName!=null'> ,INFO_CHAN_D_NAME = #{item.infoChanDName}</if>
            <if test = 'item.infoChanDdCode!=null'> ,INFO_CHAN_DD_CODE = #{item.infoChanDdCode}</if>
            <if test = 'item.infoChanDdName!=null'> ,INFO_CHAN_DD_NAME = #{item.infoChanDdName}</if>
            <if test = 'item.channelCode!=null'> ,CHANNEL_CODE = #{item.channelCode}</if>
            <if test = 'item.channelName!=null'> ,CHANNEL_NAME = #{item.channelName}</if>
            <if test = 'item.billCode!=null'> ,BILL_CODE = #{item.billCode}</if>
            <if test = 'item.planReviewTime!=null'>
                ,PLAN_REVIEW_TIME = case when #{item.planReviewTime}='' then null else #{item.planReviewTime} end
                ,OVER_REVIEW_TIME = case when #{item.planReviewTime}='' then null else #{item.planReviewTime} end
            </if>
            <if test = 'item.planComeTime!=null'>
                ,PLAN_COME_TIME = case when #{item.planComeTime}='' then null else #{item.planComeTime} end
            </if>
            <if test = 'item.factComeTime!=null'>
                ,FACT_COME_TIME = case when #{item.factComeTime}='' then null else #{item.factComeTime} end
            </if>
            <if test = 'item.isCome!=null'> ,IS_COME = #{item.isCome}</if>
            <if test = 'item.reviewTime!=null'>
                ,REVIEW_TIME = case when #{item.reviewTime}='' then null else #{item.reviewTime} end
            </if>
            <if test = 'item.lastReviewTime!=null'>
                ,LAST_REVIEW_TIME = case when #{item.lastReviewTime}='' then null else #{item.lastReviewTime} end
            </if>
            <!-- <if test = 'item.overReviewTime!=null'>
            ,OVER_REVIEW_TIME = case when #{item.overReviewTime}='' then null else #{item.overReviewTime} end
            </if> -->
            <if test = 'item.assignStatus!=null'> ,ASSIGN_STATUS = #{item.assignStatus}</if>
            <if test = 'item.assignStatusName!=null'> ,ASSIGN_STATUS_NAME = #{item.assignStatusName}</if>
            <if test = 'item.assignTime!=null'>
                ,ASSIGN_TIME = case when #{item.assignTime}='' then null else #{item.assignTime} end
            </if>
            <if test = 'item.assignPersonId!=null'> ,ASSIGN_PERSON_ID = #{item.assignPersonId}</if>
            <if test = 'item.assignPersonName!=null'> ,ASSIGN_PERSON_NAME = #{item.assignPersonName}</if>
            <if test = 'item.reviewPersonId!=null'> ,REVIEW_PERSON_ID = #{item.reviewPersonId}</if>
            <if test = 'item.reviewPersonName!=null'> ,REVIEW_PERSON_NAME = #{item.reviewPersonName}</if>
            <if test = 'item.reviewDesc!=null'> ,REVIEW_DESC = #{item.reviewDesc}</if>
            <if test = 'item.reviewStatus!=null'> ,REVIEW_STATUS = #{item.reviewStatus}</if>
            <if test = 'item.reviewStatusName!=null'> ,REVIEW_STATUS_NAME = #{item.reviewStatusName}</if>
            <if test = 'item.custId!=null'> ,CUST_ID = #{item.custId}</if>
            <if test = 'item.custName!=null'> ,CUST_NAME = #{item.custName}</if>
            <if test = 'item.phone!=null'> ,PHONE = #{item.phone}</if>
            <if test = 'item.gender!=null'> ,GENDER = #{item.gender}</if>
            <if test = 'item.genderName!=null'> ,GENDER_NAME = #{item.genderName}</if>
            <if test = 'item.touchStatus!=null'> ,TOUCH_STATUS = #{item.touchStatus}</if>
            <if test = 'item.touchStatusName!=null'> ,TOUCH_STATUS_NAME = #{item.touchStatusName}</if>
            <if test = 'item.errorReasonCode!=null'> ,ERROR_REASON_CODE = #{item.errorReasonCode}</if>
            <if test = 'item.errorReasonName!=null'> ,ERROR_REASON_NAME = #{item.errorReasonName}</if>
            <if test = 'item.firstReasonCode!=null'> ,FIRST_REASON_CODE = #{item.firstReasonCode}</if>
            <if test = 'item.firstReasonName!=null'> ,FIRST_REASON_NAME = #{item.firstReasonName}</if>
            <if test = 'item.nodeCode!=null'> ,NODE_CODE = #{item.nodeCode}</if>
            <if test = 'item.nodeName!=null'> ,NODE_NAME = #{item.nodeName}</if>
            <if test = 'item.sendDlrCode!=null'> ,SEND_DLR_CODE = #{item.sendDlrCode}</if>
            <if test = 'item.sendDlrShortName!=null'> ,SEND_DLR_SHORT_NAME = #{item.sendDlrShortName}</if>
            <if test = 'item.sendTime!=null'>
                ,SEND_TIME = case when #{item.sendTime}='' then null else #{item.sendTime} end
            </if>
            <if test = 'item.intenLevelCode!=null'> ,INTEN_LEVEL_CODE = #{item.intenLevelCode}</if>
            <if test = 'item.intenLevelName!=null'> ,INTEN_LEVEL_NAME = #{item.intenLevelName}</if>
            <if test = 'item.intenBrandCode!=null'> ,INTEN_BRAND_CODE = #{item.intenBrandCode}</if>
            <if test = 'item.intenBrandName!=null'> ,INTEN_BRAND_NAME = #{item.intenBrandName}</if>
            <if test = 'item.intenSeriesCode!=null'> ,INTEN_SERIES_CODE = #{item.intenSeriesCode}</if>
            <if test = 'item.intenSeriesName!=null'> ,INTEN_SERIES_NAME = #{item.intenSeriesName}</if>
            <if test = 'item.intenCarTypeCode!=null'> ,INTEN_CAR_TYPE_CODE = #{item.intenCarTypeCode}</if>
            <if test = 'item.intenCarTypeName!=null'> ,INTEN_CAR_TYPE_NAME = #{item.intenCarTypeName}</if>
            <if test = 'item.column1!=null'> ,COLUMN1 = #{item.column1}</if>
            <if test = 'item.column2!=null'> ,COLUMN2 = #{item.column2}</if>
            <if test = 'item.column3!=null'> ,COLUMN3 = #{item.column3}</if>
            <if test = 'item.column4!=null'> ,COLUMN4 = #{item.column4}</if>
            <if test = 'item.column5!=null'> ,COLUMN5 = #{item.column5}</if>
            <if test = 'item.column6!=null'> ,COLUMN6 = #{item.column6}</if>
            <if test = 'item.column7!=null'> ,COLUMN7 = #{item.column7}</if>
            <if test = 'item.column8!=null'> ,COLUMN8 = #{item.column8}</if>
            <if test = 'item.column9!=null'> ,COLUMN9 = #{item.column9}</if>
            <if test = 'item.column10!=null'> ,COLUMN10 = #{item.column10}</if>
            <if test = 'item.column11!=null'> ,COLUMN11 = #{item.column11}</if>
            <if test = 'item.column12!=null'> ,COLUMN12 = #{item.column12}</if>
            <if test = 'item.column13!=null'> ,COLUMN13 = #{item.column13}</if>
            <if test = 'item.column14!=null'> ,COLUMN14 = #{item.column14}</if>
            <if test = 'item.column15!=null'> ,COLUMN15 = #{item.column15}</if>
            <if test = 'item.column16!=null'> ,COLUMN16 = #{item.column16}</if>
            <if test = 'item.column17!=null'> ,COLUMN17 = #{item.column17}</if>
            <if test = 'item.column18!=null'> ,COLUMN18 = #{item.column18}</if>
            <if test = 'item.column19!=null'> ,COLUMN19 = #{item.column19}</if>
            <if test = 'item.column20!=null'> ,COLUMN20 = #{item.column20}</if>
            <if test = 'item.bigColumn1!=null'> ,BIG_COLUMN1 = #{item.bigColumn1}</if>
            <if test = 'item.bigColumn2!=null'> ,BIG_COLUMN2 = #{item.bigColumn2}</if>
            <if test = 'item.bigColumn3!=null'> ,BIG_COLUMN3 = #{item.bigColumn3}</if>
            <if test = 'item.bigColumn4!=null'> ,BIG_COLUMN4 = #{item.bigColumn4}</if>
            <if test = 'item.bigColumn5!=null'> ,BIG_COLUMN5 = #{item.bigColumn5}</if>
            <if test = 'item.extendsJson!=null'> ,EXTENDS_JSON = #{item.extendsJson}</if>
            <if test = 'item.oemId!=null'> ,OEM_ID = #{item.oemId}</if>
            <if test = 'item.groupId!=null'> ,GROUP_ID = #{item.groupId}</if>
            <if test = 'item.isEnable!=null'> ,IS_ENABLE = #{item.isEnable}</if>
            <if test = 'item.provinceCode !=null '> ,PROVINCE_CODE=#{item.provinceCode}</if>
            <if test = 'item.provinceName !=null '> ,PROVINCE_NAME=#{item.provinceName}</if>
            <if test = 'item.cityCode !=null '> , CITY_CODE=#{item.cityCode}</if>
            <if test = 'item.cityName !=null '> ,CITY_NAME=#{item.cityName}</if>
            <if test = 'item.countyCode !=null'> ,COUNTY_CODE=#{item.countyCode}</if>
            <if test = 'item.countyName !=null'> ,COUNTY_NAME=#{item.countyName}</if>

            where 1=1
            and REVIEW_ID = #{item.reviewId}
            <if test = 'item.updateControlId!=null'>
                and UPDATE_CONTROL_ID = #{item.updateControlId}
            </if>
        </foreach>
    </update>

    <update id="saveReviewCust">
        UPDATE t_sac_onecust_info
        SET
        GENDER_CODE = #{param.gender},
        GENDER_NAME = #{param.genderName},
        EXTEND_JSON = JSON_SET(
        EXTEND_JSON
        <trim prefix="," suffixOverrides=",">
            <if test="param.isAddBuy != null and param.isAddBuy != ''">
                '$.isAddBuy', #{param.isAddBuy},
            </if>
            <if test="param.focusOfCarPurchase != null and param.focusOfCarPurchase != ''">
                '$.focusOfCarPurchase', #{param.focusOfCarPurchase},
            </if>
        </trim>
        ),
        MODIFIER=#{userInfo.userID},
        MODIFY_NAME=#{userInfo.empName},
        LAST_UPDATED_DATE=#{param.updateTime},
        UPDATE_CONTROL_ID=uuid()
        WHERE phone = #{param.phone}
    </update>

    <update id="transferEmployeeDimissionReview">
        <foreach collection="list" item="param" separator=";">
            update csc.t_sac_review
            <set>
                LAST_UPDATED_DATE=now(),
                UPDATE_CONTROL_ID=uuid(),
                MODIFIER='dimissionCluesJob',
                MODIFY_NAME='dimissionCluesJob',
                <if test="param.smName !=null">
                    REVIEW_PERSON_NAME=#{param.smName},
                </if>
                <if test="param.smId !=null">
                    REVIEW_PERSON_ID=#{param.smId},
                </if>
            </set>
            where REVIEW_ID = #{param.reviewId}
        </foreach>
    </update>
</mapper>