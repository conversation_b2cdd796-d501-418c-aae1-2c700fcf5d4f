<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.adp.csc.idal.mapper.BaseQueryMapper">
	<select id="queryDlrCodeByDlrId" resultType="java.lang.String">
		select group_concat(dlr_code separator ',') as dlrCode
		from mp.t_usc_mdm_org_dlr
		where
		1=1
		<if test="dlrId !=null and dlrId !=''">
			and dlr_id in
			<foreach collection="dlrId.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="queryDlrIdByDlrCode" resultType="java.lang.String">
		select group_concat(dlr_id separator ',') as dlrId
		from mp.t_usc_mdm_org_dlr
		where
		1=1
		<if test="dlrCode !=null and dlrCode !=''">
			and dlr_code in
			<foreach collection="dlrCode.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="queryEmployeeUserStatus" resultType="java.lang.String">
		select user_status
		from mp.t_usc_mdm_org_employee
		where
			emp_id = #{empId}
    </select>

	<select id="getOverdueTime" resultType="java.lang.Integer">
		select
			lookup_value_code overdueTime
		from mp.t_prc_mds_lookup_value
		where lookup_type_code = 'VE1130'
	</select>

	<select id="queryLookupValueName" resultType="java.lang.String">
		select
			lookup_value_name
		from mp.t_prc_mds_lookup_value
		where
			is_enable = '1'
		and lookup_type_code =#{typeCode}
		and lookup_value_code = #{valueCode}
    </select>

	<!-- CSC根据多个ID获取员工信息 -->
	<select id="newGetEmpByIdList" resultType="java.util.HashMap">
		SELECT
		t.EMP_ID,
		t.EMP_CODE,
		t.EMP_NAME,
		t.USER_ID,
		t.USER_NAME,
		t.MOBILE,
		t.DLR_CODE
		FROM mp.t_usc_mdm_org_employee t
		WHERE 1 = 1
		<if test="emp.userIdList != null and emp.userIdList != '' ">
			and t.USER_ID IN
			<foreach item="item" collection=" emp.userIdList.split(',')"
					 index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
			and t.USER_STATUS in ('1',"在职")
		<if test="emp.dlrCode != null and emp.dlrCode != '' ">
			and t.DLR_CODE=#{emp.dlrCode}
		</if>
		<if test="emp.empIdList != null and emp.empIdList != '' ">
			and t.EMP_ID IN
			<foreach item="item" collection=" emp.empIdList.split(',')"
					 index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY CREATED_DATE DESC
	</select>
</mapper>
