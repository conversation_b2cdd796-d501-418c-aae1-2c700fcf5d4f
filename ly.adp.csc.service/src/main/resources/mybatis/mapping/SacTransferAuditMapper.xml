<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacTransferAuditMapper">


	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacTransferAudit">
		<id column="AUDIT_ID" property="auditId" />
		<result column="APPLY_ID" property="applyId" />
		<result column="APPLY_TYPE_CODE" property="applyTypeCode" />
		<result column="APPLY_TYPE_NAME" property="applyTypeName" />
		<result column="SH_PERSON_ID" property="shPersonId" />
		<result column="SH_PERSON_NAME" property="shPersonName" />
		<result column="SH_DESC" property="shDesc" />
		<result column="SH_TIME" property="shTime" />
		<result column="SH_STATUS" property="shStatus" />
		<result column="SH_STATUS_NAME" property="shStatusName" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.AUDIT_ID, t1.APPLY_ID
		, t1.APPLY_TYPE_CODE, t1.APPLY_TYPE_NAME
		, t1.SH_PERSON_ID, t1.SH_PERSON_NAME
		, t1.SH_DESC, t1.SH_TIME
		, t1.SH_STATUS, t1.SH_STATUS_NAME
		, t1.EXTENDS_JSON
		, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE
		, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
	</sql>

	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.auditId !=null and param.auditId !=''">and t1.AUDIT_ID=#{param.auditId}</if>
		<if test="param.applyId !=null and param.applyId !=''">and t1.APPLY_ID=#{param.applyId}</if>
		<if test="param.applyTypeCode !=null and param.applyTypeCode !=''">and t1.APPLY_TYPE_CODE=#{param.applyTypeCode}</if>
		<if test="param.applyTypeName !=null and param.applyTypeName !=''">and t1.APPLY_TYPE_NAME=#{param.applyTypeName}</if>
		<if test="param.shPersonId !=null and param.shPersonId !=''">and t1.SH_PERSON_ID=#{param.shPersonId}</if>
		<if test="param.shPersonName !=null and param.shPersonName !=''">and t1.SH_PERSON_NAME=#{param.shPersonName}</if>
		<if test="param.shDesc !=null and param.shDesc !=''">and t1.SH_DESC=#{param.shDesc}</if>
		<if test="param.shTime !=null">and t1.SH_TIME=#{param.shTime}</if>
		<if test="param.shStatus !=null and param.shStatus !=''">and t1.SH_STATUS=#{param.shStatus}</if>
		<if test="param.shStatusName !=null and param.shStatusName !=''">and t1.SH_STATUS_NAME=#{param.shStatusName}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</sql>

	<!-- 新增 -->
	<insert id="insertSacTransferAudit">
		insert into t_sac_transfer_audit(
		AUDIT_ID
		, APPLY_ID
		, APPLY_TYPE_CODE
		, APPLY_TYPE_NAME
		, SH_PERSON_ID
		, SH_PERSON_NAME
		, SH_DESC
		, SH_TIME
		, SH_STATUS
		, SH_STATUS_NAME
		, EXTENDS_JSON
		, OEM_ID
		, GROUP_ID
		, CREATOR
		, CREATED_NAME
		, CREATED_DATE
		, MODIFIER
		, MODIFY_NAME
		, LAST_UPDATED_DATE
		, IS_ENABLE
		, UPDATE_CONTROL_ID
		) values (
		#{param.auditId}
		, #{param.applyId}
		, #{param.applyTypeCode}
		, #{param.applyTypeName}
		, #{param.shPersonId}
		, #{param.shPersonName}
		, #{param.shDesc}
		, #{param.shTime}
		, #{param.shStatus}
		, #{param.shStatusName}
		, #{param.extendsJson}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		)
	</insert>

	<!-- 修改 -->
	<update id="updateSacTransferAudit">
		update t_sac_transfer_audit
		<set>
		LAST_UPDATED_DATE=sysdate(),
		UPDATE_CONTROL_ID=uuid(),
			<if test="param.auditId !=null and param.auditId !=''"> AUDIT_ID=#{param.auditId},</if>
			<if test="param.applyId !=null and param.applyId !=''"> APPLY_ID=#{param.applyId},</if>
			<if test="param.applyTypeCode !=null and param.applyTypeCode !=''"> APPLY_TYPE_CODE=#{param.applyTypeCode},</if>
			<if test="param.applyTypeName !=null and param.applyTypeName !=''"> APPLY_TYPE_NAME=#{param.applyTypeName},</if>
			<if test="param.shPersonId !=null and param.shPersonId !=''"> SH_PERSON_ID=#{param.shPersonId},</if>
			<if test="param.shPersonName !=null and param.shPersonName !=''"> SH_PERSON_NAME=#{param.shPersonName},</if>
			<if test="param.shDesc !=null and param.shDesc !=''"> SH_DESC=#{param.shDesc},</if>
			<if test="param.shTime !=null"> SH_TIME=#{param.shTime},</if>
			<if test="param.shStatus !=null and param.shStatus !=''"> SH_STATUS=#{param.shStatus},</if>
			<if test="param.shStatusName !=null and param.shStatusName !=''"> SH_STATUS_NAME=#{param.shStatusName},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		</set>
		where 1=1
		and AUDIT_ID=#{param.auditId}
	</update>

	<!--批量修改-->
	<update id="updateSacTransferAuditBatch">
		update t_sac_transfer_audit
		<set>
			<if test="param.shPersonId !=null and param.shPersonId !=''"> SH_PERSON_ID=#{param.shPersonId},</if>
			<if test="param.shPersonName !=null and param.shPersonName !=''"> SH_PERSON_NAME=#{param.shPersonName},</if>
			<if test="param.shDesc !=null and param.shDesc !=''"> SH_DESC=#{param.shDesc},</if>
			<if test="param.shTime !=null"> SH_TIME=#{param.shTime},</if>
			<if test="param.shStatus !=null and param.shStatus !=''"> SH_STATUS=#{param.shStatus},</if>
			<if test="param.shStatusName !=null and param.shStatusName !=''"> SH_STATUS_NAME=#{param.shStatusName},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE = #{param.lastUpdatedDate},
			UPDATE_CONTROL_ID = uuid()
		</set>
		where AUDIT_ID in
		<foreach collection="param.auditIdList" item="item" open="(" separator="," close=")">
				#{item}
        </foreach>
	</update>



	<select id="selectByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
			, t2.BILL_CODE, t2.CUST_ID, t2.CUST_NAME, t2.PHONE
			, t2.OUT_DLR_CODE, t2.OUT_DLR_NAME, t2.IN_DLR_CODE, t2.IN_DLR_NAME
			, t2.APPLY_DESC
		from t_sac_transfer_audit t1
		left join t_sac_transfer_apply t2 on t2.apply_id = t1.apply_id
		where 1=1
		<include refid="where_condition"></include>
	</select>

	<select id="selectByPageNoPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
			, t2.BILL_CODE, t2.CUST_ID, t2.CUST_NAME, t2.PHONE
			, t2.OUT_DLR_CODE, t2.OUT_DLR_NAME, t2.IN_DLR_CODE, t2.IN_DLR_NAME
			, t2.APPLY_DESC
		from t_sac_transfer_audit t1
		left join t_sac_transfer_apply t2 on t2.apply_id = t1.apply_id
		where 1=1
		<include refid="where_condition"></include>
	</select>

	<select id="selectTransferapplyByPhone" resultType="java.util.Map">
		SELECT a.APPLY_ID FROM t_sac_transfer_apply a where a.PHONE =#{param.phone}
	</select>
	<delete id="deleteAudit">
		DELETE FROM t_sac_transfer_audit WHERE APPLY_ID =#{param.applyId}
	</delete>
	<delete id="deleteTransferapply">
		DELETE FROM t_sac_transfer_apply WHERE APPLY_ID =#{param.applyId}
	</delete>
	<select id="selectReviewByPhone" resultType="java.util.Map">
		SELECT REVIEW_ID FROM t_sac_review WHERE PHONE =#{param.phone}
	</select>
	<delete id="deleteReviewAudit">
		DELETE FROM t_sac_review_audit WHERE REVIEW_ID =#{param.reviewId}
	</delete>

	<select id="queryAgentInfoByDlrCode" resultType="com.ly.mp.csc.clue.entities.dto.AgentCompanyInfoDTO">
		SELECT
			b.agent_id,
			b.AGENT_COMPANY_CODE,
			b.AGENT_COMPANY_NAME,
			a.DLR_CODE,
			a.DLR_SHORT_NAME
		FROM mp.t_usc_mdm_org_dlr a
				 INNER JOIN mp.t_usc_mdm_agent_company b ON a.COMPANY_ID = b.AGENT_COMPANY_ID
		WHERE a.dlr_code in
		<foreach collection="dlrCode" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="queryClueInfoByAuditId" resultType="com.ly.mp.csc.clue.entities.dto.TransferClueInfo">
		SELECT
		t.audit_id,
		a.creator AS transferCreator,
		a.created_name AS transferCreatorName,
		a.in_dlr_code,
		a.in_dlr_name,
		d.id AS clueId,
		d.review_id,
		t.sh_status
		FROM
		t_sac_transfer_audit t
		INNER JOIN t_sac_transfer_apply a ON t.apply_id = a.apply_id
		INNER JOIN adp_leads.t_sac_clue_info_dlr d ON a.bill_code = d.server_order
		WHERE
		t.AUDIT_ID IN
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<update id="batchUpdateTransferCLue">
		<foreach collection="list" item="param" separator=";">
			update adp_leads.t_sac_clue_info_dlr
			set
			<if test="param.column14 !=null and param.column14 !=''"> COLUMN14=#{param.column14},</if>
			LAST_UPDATED_DATE=now(),
			UPDATE_CONTROL_ID=uuid(),
			ASSIGN_TIME=now(),
			STATUS_CODE = '2',
			STATUS_NAME = '待回访',
			DLR_CODE=#{param.dlrCode},
			DLR_SHORT_NAME=#{param.dlrName},
			REVIEW_PERSON_NAME=#{param.reviewPersonName},
			REVIEW_PERSON_ID=#{param.reviewPersonId},
			MODIFIER=#{param.modifier},
			MODIFY_NAME=#{param.modifyName}
			where id = #{param.clueId}
        </foreach>
    </update>

	<update id="batchUpdateTransferReview">
		<foreach collection="list" item="param" separator=";">
			update csc.t_sac_review
			set
			<if test="param.column14 !=null and param.column14 !=''"> COLUMN14=#{param.column14},</if>
			<if test = 'param.planReviewTime!=null'>
				PLAN_REVIEW_TIME = #{param.planReviewTime},
				OVER_REVIEW_TIME = #{param.planReviewTime},
			</if>
			LAST_UPDATED_DATE=now(),
			UPDATE_CONTROL_ID=uuid(),
			ASSIGN_TIME=now(),
			ASSIGN_STATUS='1',
			ASSIGN_STATUS_NAME='已分配',
			REVIEW_STATUS = '0',
			REVIEW_STATUS_NAME = '待回访',
			REVIEW_DESC=#{param.reviewDesc},
			ORG_CODE=#{param.dlrCode},
			ORG_NAME=#{param.dlrName},
			REVIEW_PERSON_NAME=#{param.reviewPersonName},
			REVIEW_PERSON_ID=#{param.reviewPersonId},
			MODIFIER=#{param.modifier},
			MODIFY_NAME=#{param.modifyName}
			where REVIEW_ID = #{param.reviewId}
		</foreach>
	</update>

	<select id="queryPcTransferDlrInfo" resultType="com.ly.mp.csc.clue.entities.dto.DlrInfoDTO">
		SELECT DISTINCT t1.OUT_DLR_CODE as dlrCode,t1.OUT_DLR_NAME as dlrName
		FROM t_sac_transfer_apply t1
		WHERE t1.IN_DLR_CODE = #{dlrCode}
	</select>
</mapper>