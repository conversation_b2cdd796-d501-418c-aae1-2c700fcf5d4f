<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewBakMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewBak">
	    <id column="REVIEW_ID" jdbcType="VARCHAR" property="reviewId" />
	    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
	    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
	    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
	    <result column="BILL_TYPE_NAME" jdbcType="VARCHAR" property="billTypeName" />
	    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
	    <result column="BUSINESS_TYPE_NAME" jdbcType="VARCHAR" property="businessTypeName" />
	    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
	    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
	    <result column="BILL_CODE" jdbcType="VARCHAR" property="billCode" />
	    <result column="PLAN_REVIEW_TIME" jdbcType="TIMESTAMP" property="planReviewTime" />
	    <result column="PLAN_COME_TIME" jdbcType="TIMESTAMP" property="planComeTime" />
    	<result column="FACT_COME_TIME" jdbcType="TIMESTAMP" property="factComeTime" />
    	<result column="IS_COME" jdbcType="TIMESTAMP" property="isCome" />
	    <result column="REVIEW_TIME" jdbcType="TIMESTAMP" property="reviewTime" />
	    <result column="LAST_REVIEW_TIME" jdbcType="TIMESTAMP" property="lastReviewTime" />
	    <result column="OVER_REVIEW_TIME" jdbcType="TIMESTAMP" property="overReviewTime" />
	    <result column="ASSIGN_STATUS" jdbcType="VARCHAR" property="assignStatus" />
	    <result column="ASSIGN_STATUS_NAME" jdbcType="VARCHAR" property="assignStatusName" />
	    <result column="ASSIGN_TIME" jdbcType="TIMESTAMP" property="assignTime" />
	    <result column="ASSIGN_PERSON_ID" jdbcType="VARCHAR" property="assignPersonId" />
	    <result column="ASSIGN_PERSON_NAME" jdbcType="VARCHAR" property="assignPersonName" />
	    <result column="REVIEW_PERSON_ID" jdbcType="VARCHAR" property="reviewPersonId" />
	    <result column="REVIEW_PERSON_NAME" jdbcType="VARCHAR" property="reviewPersonName" />
	    <result column="REVIEW_DESC" jdbcType="VARCHAR" property="reviewDesc" />
	    <result column="REVIEW_STATUS" jdbcType="VARCHAR" property="reviewStatus" />
	    <result column="REVIEW_STATUS_NAME" jdbcType="VARCHAR" property="reviewStatusName" />
	    <result column="CUST_ID" jdbcType="VARCHAR" property="custId" />
	    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
	    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
	    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
	    <result column="GENDER_NAME" jdbcType="VARCHAR" property="genderName" />
	    <result column="TOUCH_STATUS" jdbcType="VARCHAR" property="touchStatus" />
	    <result column="TOUCH_STATUS_NAME" jdbcType="VARCHAR" property="touchStatusName" />
	    <result column="ERROR_REASON_CODE" jdbcType="VARCHAR" property="errorReasonCode" />
	    <result column="ERROR_REASON_NAME" jdbcType="VARCHAR" property="errorReasonName" />
	    <result column="NODE_CODE" jdbcType="VARCHAR" property="nodeCode" />
	    <result column="NODE_NAME" jdbcType="VARCHAR" property="nodeName" />
	    <result column="SEND_DLR_CODE" jdbcType="VARCHAR" property="sendDlrCode" />
    	<result column="SEND_DLR_SHORT_NAME" jdbcType="VARCHAR" property="sendDlrShortName" />
    	<result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    	<result column="INTEN_LEVEL_CODE" jdbcType="VARCHAR" property="intenLevelCode" />
		<result column="INTEN_LEVEL_NAME" jdbcType="VARCHAR" property="intenLevelName" />
		<result column="INTEN_BRAND_CODE" jdbcType="VARCHAR" property="intenBrandCode" />
		<result column="INTEN_BRAND_NAME" jdbcType="VARCHAR" property="intenBrandName" />
		<result column="INTEN_SERIES_CODE" jdbcType="VARCHAR" property="intenSeriesCode" />
		<result column="INTEN_SERIES_NAME" jdbcType="VARCHAR" property="intenSeriesName" />
		<result column="INTEN_CAR_TYPE_CODE" jdbcType="VARCHAR" property="intenCarTypeCode" />
		<result column="INTEN_CAR_TYPE_NAME" jdbcType="VARCHAR" property="intenCarTypeName" />
	    <result column="COLUMN1" jdbcType="VARCHAR" property="column1" />
	    <result column="COLUMN2" jdbcType="VARCHAR" property="column2" />
	    <result column="COLUMN3" jdbcType="VARCHAR" property="column3" />
	    <result column="COLUMN4" jdbcType="VARCHAR" property="column4" />
	    <result column="COLUMN5" jdbcType="VARCHAR" property="column5" />
	    <result column="COLUMN6" jdbcType="VARCHAR" property="column6" />
	    <result column="COLUMN7" jdbcType="VARCHAR" property="column7" />
	    <result column="COLUMN8" jdbcType="VARCHAR" property="column8" />
	    <result column="COLUMN9" jdbcType="VARCHAR" property="column9" />
	    <result column="COLUMN10" jdbcType="VARCHAR" property="column10" />
	    <result column="COLUMN11" jdbcType="VARCHAR" property="column11" />
	    <result column="COLUMN12" jdbcType="VARCHAR" property="column12" />
	    <result column="COLUMN13" jdbcType="VARCHAR" property="column13" />
	    <result column="COLUMN14" jdbcType="VARCHAR" property="column14" />
	    <result column="COLUMN15" jdbcType="VARCHAR" property="column15" />
	    <result column="COLUMN16" jdbcType="VARCHAR" property="column16" />
	    <result column="COLUMN17" jdbcType="VARCHAR" property="column17" />
	    <result column="COLUMN18" jdbcType="VARCHAR" property="column18" />
	    <result column="COLUMN19" jdbcType="VARCHAR" property="column19" />
	    <result column="COLUMN20" jdbcType="VARCHAR" property="column20" />
	    <result column="BIG_COLUMN1" jdbcType="LONGVARCHAR" property="bigColumn1" />
	    <result column="BIG_COLUMN2" jdbcType="LONGVARCHAR" property="bigColumn2" />
	    <result column="BIG_COLUMN3" jdbcType="LONGVARCHAR" property="bigColumn3" />
	    <result column="BIG_COLUMN4" jdbcType="LONGVARCHAR" property="bigColumn4" />
	    <result column="BIG_COLUMN5" jdbcType="LONGVARCHAR" property="bigColumn5" />
	    <result column="EXTENDS_JSON" jdbcType="VARCHAR" property="extendsJson" />
	    <result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
	    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
	    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
	    <result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
	    <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
	    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
	    <result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
	    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
	    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
	    <result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
	  </resultMap>

	  <sql id="Base_Column_List">
	    <!--@mbg.generated-->
	    REVIEW_ID, ORG_CODE, ORG_NAME, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, 
	    INFO_CHAN_M_CODE,INFO_CHAN_M_NAME,INFO_CHAN_D_CODE,INFO_CHAN_D_NAME,INFO_CHAN_DD_CODE,INFO_CHAN_DD_NAME,
		CHANNEL_CODE,CHANNEL_NAME, BILL_CODE, PLAN_REVIEW_TIME, PLAN_COME_TIME, FACT_COME_TIME, IS_COME, 
		REVIEW_TIME, LAST_REVIEW_TIME,
	    OVER_REVIEW_TIME, ASSIGN_STATUS, ASSIGN_STATUS_NAME, ASSIGN_TIME, ASSIGN_PERSON_ID, 
	    ASSIGN_PERSON_NAME, REVIEW_PERSON_ID, REVIEW_PERSON_NAME, REVIEW_DESC, REVIEW_STATUS, 
	    REVIEW_STATUS_NAME, CUST_ID, CUST_NAME, PHONE, GENDER, GENDER_NAME, TOUCH_STATUS, 
	    TOUCH_STATUS_NAME, ERROR_REASON_CODE, ERROR_REASON_NAME, NODE_CODE, NODE_NAME,SEND_DLR_CODE,SEND_DLR_SHORT_NAME,
    	SEND_TIME,INTEN_LEVEL_CODE,INTEN_LEVEL_NAME,INTEN_BRAND_CODE,INTEN_BRAND_NAME,INTEN_SERIES_CODE,INTEN_SERIES_NAME,INTEN_CAR_TYPE_CODE,INTEN_CAR_TYPE_NAME,
    	COLUMN1, COLUMN2, COLUMN3, 
	    COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, COLUMN11, COLUMN12, 
	    COLUMN13, COLUMN14, COLUMN15, COLUMN16, COLUMN17, COLUMN18, COLUMN19, COLUMN20, BIG_COLUMN1, 
	    BIG_COLUMN2, BIG_COLUMN3, BIG_COLUMN4, BIG_COLUMN5, EXTENDS_JSON, OEM_ID, GROUP_ID, 
	    CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, 
	    UPDATE_CONTROL_ID
	  </sql>

 	<insert id="insertSacReviewBak">
        insert into t_sac_review_bak(
         REVIEW_ID
        ,ORG_CODE
        ,ORG_NAME
        ,BILL_TYPE
        ,BILL_TYPE_NAME
        ,BUSINESS_TYPE
        ,BUSINESS_TYPE_NAME
        ,INFO_CHAN_M_CODE
		,INFO_CHAN_M_NAME
		,INFO_CHAN_D_CODE
		,INFO_CHAN_D_NAME
		,INFO_CHAN_DD_CODE
		,INFO_CHAN_DD_NAME
		,CHANNEL_CODE
		,CHANNEL_NAME
        ,BILL_CODE
        ,PLAN_REVIEW_TIME
        ,PLAN_COME_TIME
        ,FACT_COME_TIME
        ,IS_COME
        ,REVIEW_TIME
        ,LAST_REVIEW_TIME
        ,OVER_REVIEW_TIME
        ,ASSIGN_STATUS
        ,ASSIGN_STATUS_NAME
        ,ASSIGN_TIME
        ,ASSIGN_PERSON_ID
        ,ASSIGN_PERSON_NAME
        ,REVIEW_PERSON_ID
        ,REVIEW_PERSON_NAME
        ,REVIEW_DESC
        ,REVIEW_STATUS
        ,REVIEW_STATUS_NAME
        ,CUST_ID
        ,CUST_NAME
        ,PHONE
        ,GENDER
        ,GENDER_NAME
        ,TOUCH_STATUS
        ,TOUCH_STATUS_NAME
        ,ERROR_REASON_CODE
        ,ERROR_REASON_NAME
        ,NODE_CODE
        ,NODE_NAME
        ,SEND_DLR_CODE
        ,SEND_DLR_SHORT_NAME
        ,SEND_TIME
        ,INTEN_LEVEL_CODE
        ,INTEN_LEVEL_NAME
        ,INTEN_BRAND_CODE
        ,INTEN_BRAND_NAME
        ,INTEN_SERIES_CODE
        ,INTEN_SERIES_NAME
        ,INTEN_CAR_TYPE_CODE
        ,INTEN_CAR_TYPE_NAME
        ,COLUMN1
        ,COLUMN2
        ,COLUMN3
        ,COLUMN4
        ,COLUMN5
        ,COLUMN6
        ,COLUMN7
        ,COLUMN8
        ,COLUMN9
        ,COLUMN10
        ,COLUMN11
        ,COLUMN12
        ,COLUMN13
        ,COLUMN14
        ,COLUMN15
        ,COLUMN16
        ,COLUMN17
        ,COLUMN18
        ,COLUMN19
        ,COLUMN20
        ,BIG_COLUMN1
        ,BIG_COLUMN2
        ,BIG_COLUMN3
        ,BIG_COLUMN4
        ,BIG_COLUMN5
        ,EXTENDS_JSON
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.reviewId}
		,#{param.orgCode}
		,#{param.orgName}
		,#{param.billType}
		,#{param.billTypeName}
		,#{param.businessType}
		,#{param.businessTypeName}
		,#{param.infoChanMCode}
		,#{param.infoChanMName}
		,#{param.infoChanDCode}
		,#{param.infoChanDName}
		,#{param.infoChanDdCode}
		,#{param.infoChanDdName}
		,#{param.channelCode}
		,#{param.channelName}
		,#{param.billCode}
		,#{param.planReviewTime}
		,#{param.planComeTime}
		,#{param.factComeTime}
		,#{param.isCome}
		,#{param.reviewTime}
		,#{param.lastReviewTime}
		,#{param.overReviewTime}
		,#{param.assignStatus}
		,#{param.assignStatusName}
		,#{param.assignTime}
		,#{param.assignPersonId}
		,#{param.assignPersonName}
		,#{param.reviewPersonId}
		,#{param.reviewPersonName}
		,#{param.reviewDesc}
		,#{param.reviewStatus}
		,#{param.reviewStatusName}
		,#{param.custId}
		,#{param.custName}
		,#{param.phone}
		,#{param.gender}
		,#{param.genderName}
		,#{param.touchStatus}
		,#{param.touchStatusName}
		,#{param.errorReasonCode}
		,#{param.errorReasonName}
		,#{param.nodeCode}
		,#{param.nodeName}
		,#{param.sendDlrCode}
		,#{param.sendDlrShortName}
		,#{param.sendTime}
		,#{param.intenLevelCode}
		,#{param.intenLevelName}
		,#{param.intenBrandCode}
		,#{param.intenBrandName}
		,#{param.intenSeriesCode}
		,#{param.intenSeriesName}
		,#{param.intenCarTypeCode}
		,#{param.intenCarTypeName}
		,#{param.column1}
		,#{param.column2}
		,#{param.column3}
		,#{param.column4}
		,#{param.column5}
		,#{param.column6}
		,#{param.column7}
		,#{param.column8}
		,#{param.column9}
		,#{param.column10}
		,#{param.column11}
		,#{param.column12}
		,#{param.column13}
		,#{param.column14}
		,#{param.column15}
		,#{param.column16}
		,#{param.column17}
		,#{param.column18}
		,#{param.column19}
		,#{param.column20}
		,#{param.bigColumn1}
		,#{param.bigColumn2}
		,#{param.bigColumn3}
		,#{param.bigColumn4}
		,#{param.bigColumn5}
		,#{param.extendsJson}
		,#{param.oemId}
		,#{param.groupId}
		,#{param.creator}
		,#{param.createdName}
		,#{param.createdDate}
		,#{param.modifier}
		,#{param.modifyName}
		,#{param.lastUpdatedDate}
		,'1'
		,#{param.updateControlId}
		)
    </insert>
    
    <update id="updateSacReviewBak">
    	update t_sac_review_bak  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.orgCode!=null'> ,ORG_CODE = #{param.orgCode}</if>
	    <if test = 'param.orgName!=null'> ,ORG_NAME = #{param.orgName}</if>
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.billTypeName!=null'> ,BILL_TYPE_NAME = #{param.billTypeName}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{param.businessTypeName}</if>
	    <if test = 'param.infoChanMCode!=null'> ,INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
	    <if test = 'param.infoChanMName!=null'> ,INFO_CHAN_M_NAME = #{param.infoChanMName}</if>
	    <if test = 'param.infoChanDCode!=null'> ,INFO_CHAN_D_CODE = #{param.infoChanDCode}</if>
	    <if test = 'param.infoChanDName!=null'> ,INFO_CHAN_D_NAME = #{param.infoChanDName}</if>
	    <if test = 'param.infoChanDdCode!=null'> ,INFO_CHAN_DD_CODE = #{param.infoChanDdCode}</if>
	    <if test = 'param.infoChanDdName!=null'> ,INFO_CHAN_DD_NAME = #{param.infoChanDdName}</if>
	    <if test = 'param.channelCode!=null'> ,CHANNEL_CODE = #{param.channelCode}</if>
	    <if test = 'param.channelName!=null'> ,CHANNEL_NAME = #{param.channelName}</if>
	    <if test = 'param.billCode!=null'> ,BILL_CODE = #{param.billCode}</if>
	    <if test = 'param.planReviewTime!=null'> ,PLAN_REVIEW_TIME = #{param.planReviewTime}</if>
	    <if test = 'param.planComeTime!=null'> ,PLAN_COME_TIME = #{param.planComeTime}</if>
	    <if test = 'param.factComeTime!=null'> ,FACT_COME_TIME = #{param.factComeTime}</if>
	    <if test = 'param.isCome!=null'> ,IS_COME = #{param.isCome}</if>
	    <if test = 'param.reviewTime!=null'> ,REVIEW_TIME = #{param.reviewTime}</if>
	    <if test = 'param.lastReviewTime!=null'> ,LAST_REVIEW_TIME = #{param.lastReviewTime}</if>
	    <if test = 'param.overReviewTime!=null'> ,OVER_REVIEW_TIME = #{param.overReviewTime}</if>
	    <if test = 'param.assignStatus!=null'> ,ASSIGN_STATUS = #{param.assignStatus}</if>
	    <if test = 'param.assignStatusName!=null'> ,ASSIGN_STATUS_NAME = #{param.assignStatusName}</if>
	    <if test = 'param.assignTime!=null'> ,ASSIGN_TIME = #{param.assignTime}</if>
	    <if test = 'param.assignPersonId!=null'> ,ASSIGN_PERSON_ID = #{param.assignPersonId}</if>
	    <if test = 'param.assignPersonName!=null'> ,ASSIGN_PERSON_NAME = #{param.assignPersonName}</if>
	    <if test = 'param.reviewPersonId!=null'> ,REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
	    <if test = 'param.reviewPersonName!=null'> ,REVIEW_PERSON_NAME = #{param.reviewPersonName}</if>
	    <if test = 'param.reviewDesc!=null'> ,REVIEW_DESC = #{param.reviewDesc}</if>
	    <if test = 'param.reviewStatus!=null'> ,REVIEW_STATUS = #{param.reviewStatus}</if>
	    <if test = 'param.reviewStatusName!=null'> ,REVIEW_STATUS_NAME = #{param.reviewStatusName}</if>
	    <if test = 'param.custId!=null'> ,CUST_ID = #{param.custId}</if>
	    <if test = 'param.custName!=null'> ,CUST_NAME = #{param.custName}</if>
	    <if test = 'param.phone!=null'> ,PHONE = #{param.phone}</if>
	    <if test = 'param.gender!=null'> ,GENDER = #{param.gender}</if>
	    <if test = 'param.genderName!=null'> ,GENDER_NAME = #{param.genderName}</if>
	    <if test = 'param.touchStatus!=null'> ,TOUCH_STATUS = #{param.touchStatus}</if>
	    <if test = 'param.touchStatusName!=null'> ,TOUCH_STATUS_NAME = #{param.touchStatusName}</if>
	    <if test = 'param.errorReasonCode!=null'> ,ERROR_REASON_CODE = #{param.errorReasonCode}</if>
	    <if test = 'param.errorReasonName!=null'> ,ERROR_REASON_NAME = #{param.errorReasonName}</if>
	    <if test = 'param.nodeCode!=null'> ,NODE_CODE = #{param.nodeCode}</if>
	    <if test = 'param.nodeName!=null'> ,NODE_NAME = #{param.nodeName}</if>
	    <if test = 'param.sendDlrCode!=null'> ,SEND_DLR_CODE = #{param.sendDlrCode}</if>
	    <if test = 'param.sendDlrShortName!=null'> ,SEND_DLR_SHORT_NAME = #{param.sendDlrShortName}</if>
	    <if test = 'param.sendTime!=null'> ,SEND_TIME = #{param.sendTime}</if>
	    <if test = 'param.intenLevelCode!=null'> ,INTEN_LEVEL_CODE = #{param.intenLevelCode}</if>
		<if test = 'param.intenLevelName!=null'> ,INTEN_LEVEL_NAME = #{param.intenLevelName}</if>
	    <if test = 'param.intenBrandCode!=null'> ,INTEN_BRAND_CODE = #{param.intenBrandCode}</if>
		<if test = 'param.intenBrandName!=null'> ,INTEN_BRAND_NAME = #{param.intenBrandName}</if>
		<if test = 'param.intenSeriesCode!=null'> ,INTEN_SERIES_CODE = #{param.intenSeriesCode}</if>
		<if test = 'param.intenSeriesName!=null'> ,INTEN_SERIES_NAME = #{param.intenSeriesName}</if>
		<if test = 'param.intenCarTypeCode!=null'> ,INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}</if>
		<if test = 'param.intenCarTypeName!=null'> ,INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName}</if>
	    <if test = 'param.column1!=null'> ,COLUMN1 = #{param.column1}</if>
	    <if test = 'param.column2!=null'> ,COLUMN2 = #{param.column2}</if>
	    <if test = 'param.column3!=null'> ,COLUMN3 = #{param.column3}</if>
	    <if test = 'param.column4!=null'> ,COLUMN4 = #{param.column4}</if>
	    <if test = 'param.column5!=null'> ,COLUMN5 = #{param.column5}</if>
	    <if test = 'param.column6!=null'> ,COLUMN6 = #{param.column6}</if>
	    <if test = 'param.column7!=null'> ,COLUMN7 = #{param.column7}</if>
	    <if test = 'param.column8!=null'> ,COLUMN8 = #{param.column8}</if>
	    <if test = 'param.column9!=null'> ,COLUMN9 = #{param.column9}</if>
	    <if test = 'param.column10!=null'> ,COLUMN10 = #{param.column10}</if>
	    <if test = 'param.column11!=null'> ,COLUMN11 = #{param.column11}</if>
	    <if test = 'param.column12!=null'> ,COLUMN12 = #{param.column12}</if>
	    <if test = 'param.column13!=null'> ,COLUMN13 = #{param.column13}</if>
	    <if test = 'param.column14!=null'> ,COLUMN14 = #{param.column14}</if>
	    <if test = 'param.column15!=null'> ,COLUMN15 = #{param.column15}</if>
	    <if test = 'param.column16!=null'> ,COLUMN16 = #{param.column16}</if>
	    <if test = 'param.column17!=null'> ,COLUMN17 = #{param.column17}</if>
	    <if test = 'param.column18!=null'> ,COLUMN18 = #{param.column18}</if>
	    <if test = 'param.column19!=null'> ,COLUMN19 = #{param.column19}</if>
	    <if test = 'param.column20!=null'> ,COLUMN20 = #{param.column20}</if>
	    <if test = 'param.bigColumn1!=null'> ,BIG_COLUMN1 = #{param.bigColumn1}</if>
	    <if test = 'param.bigColumn2!=null'> ,BIG_COLUMN2 = #{param.bigColumn2}</if>
	    <if test = 'param.bigColumn3!=null'> ,BIG_COLUMN3 = #{param.bigColumn3}</if>
	    <if test = 'param.bigColumn4!=null'> ,BIG_COLUMN4 = #{param.bigColumn4}</if>
	    <if test = 'param.bigColumn5!=null'> ,BIG_COLUMN5 = #{param.bigColumn5}</if>
	    <if test = 'param.extendsJson!=null'> ,EXTENDS_JSON = #{param.extendsJson}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and REVIEW_ID = #{param.reviewId}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

</mapper>
