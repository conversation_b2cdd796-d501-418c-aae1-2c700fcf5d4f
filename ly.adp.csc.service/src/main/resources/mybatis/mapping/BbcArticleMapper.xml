<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.BbcArticleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.BbcArticle">
        <id column="ARTICLE_ID" property="articleId" />
        <result column="ARTICLE_TITELE" property="articleTitele" />
        <result column="ARTICLE_BUILD" property="articleBuild" />
        <result column="ARTICLE_BUILD_NAME" property="articleBuildName" />
        <result column="ARTICLE_CONTENT" property="articleContent" />
        <result column="ARTICLE_IMAGE" property="articleImage" />
        <result column="ARTICLE_STATUS" property="articleStatus" />
        <result column="ARTICLE_STATUS_NAME" property="articleStatusName" />
        <result column="ARTICLE_TYPE" property="articleType" />
        <result column="ARTICLE_TYPE_NAME" property="articleTypeName" />
        <result column="IS_COMMENT" property="isComment" />
        <result column="IS_PERSON_COMMENT" property="isPersonComment" />
        <result column="IS_ANONYMOUS" property="isAnonymous" />
        <result column="SH_PERSON_ID" property="shPersonId" />
        <result column="SH_PERSON_NAME" property="shPersonName" />
        <result column="SH_TIME" property="shTime" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ARTICLE_ID, ARTICLE_TITELE, ARTICLE_BUILD, ARTICLE_BUILD_NAME, ARTICLE_CONTENT, ARTICLE_IMAGE, ARTICLE_STATUS, ARTICLE_STATUS_NAME, ARTICLE_TYPE, ARTICLE_TYPE_NAME, IS_COMMENT, IS_PERSON_COMMENT, IS_ANONYMOUS, SH_PERSON_ID, SH_PERSON_NAME, SH_TIME, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
     <insert id="insertArticle" parameterType="java.util.Map">
          INSERT INTO `t_sac_bu_bbc_article`
          (`ARTICLE_ID`, `ARTICLE_TITELE`, `ARTICLE_BUILD`, `ARTICLE_BUILD_NAME`, `ARTICLE_CONTENT`, `ARTICLE_IMAGE`, `ARTICLE_STATUS`, `ARTICLE_STATUS_NAME`, `ARTICLE_TYPE`, `ARTICLE_TYPE_NAME`, `IS_COMMENT`, `IS_PERSON_COMMENT`, `IS_ANONYMOUS`, `SH_PERSON_ID`, `SH_PERSON_NAME`, `SH_TIME`, `REMARK`, `CREATOR`, `CREATED_NAME`, `CREATED_DATE`, `MODIFIER`, `MODIFY_NAME`, `LAST_UPDATED_DATE`, `IS_ENABLE`, `UPDATE_CONTROL_ID`,ARTICLE_ORG_CODE) VALUES
          (#{articleId}, #{articleTitele}, #{articleBuild},  #{articleBuildName},  #{articleContent},  #{articleImage},  #{articleStatus},  #{articleStatusName},  #{articleType},  #{articleTypeName},  #{isComment},  #{isPersonComment}, #{isAnonymous}, #{shPersonId},  #{shPersonName},  #{shTime},   #{remark},  #{creator},  #{createdName},  #{createdDate},  #{modifier},  #{modifyName},  #{lastUpdatedDate},  #{isEnable}, #{updateControlId},#{articleOrgCode});
     </insert>
    <select id="queryArticleBuild" resultType="java.util.Map" >

       	select IFNULL(bl.ct,0) ct,lv.LOOKUP_VALUE_CODE,lv.LOOKUP_VALUE_NAME from (select * from t_prc_mds_lookup_value  where LOOKUP_TYPE_CODE='ADP_CLUE_056') lv
	left join (select count(*) as ct,bl.ARTICLE_BUILD from  t_sac_bu_bbc_article bl
        where    bl.ARTICLE_STATUS='22'
        and bl.IS_ENABLE='1'
        <if test="param.objectId1=='' or  param.objectId1==null">
            and (bl.CREATOR=#{param.objectId2}
            or not
            exists ( select 1 from t_sac_bu_bbc_audience bc where bc.ARTICLE_ID=bl.ARTICLE_ID)
            or
            bl.ARTICLE_ID in ( select bc.ARTICLE_ID from t_sac_bu_bbc_audience bc where
            FIND_IN_SET(bc.OBJECT_ID,#{param.posCode})

            )
            )
        </if>
        group by bl.ARTICLE_BUILD
        )bl on lv.LOOKUP_VALUE_CODE=bl.ARTICLE_BUILD

    </select>
    <insert id="insertAudience" parameterType="java.util.Map">
INSERT INTO `t_sac_bu_bbc_audience`
(`ARTICLE_ID`, `OBJECT_TYPE`, `OBJECT_ID`, `OBJECT_FULLNAME`, `AUDIENCE_ID`) VALUES
(#{articleId},'1', #{objectId}, #{objectFullname}, UUID());
    </insert>
<update id="delArticle" parameterType="java.util.Map" >
    update  t_sac_bu_bbc_article set `IS_ENABLE`='0'  where ARTICLE_ID=#{articleId}
</update>
    <delete id="delAudience" parameterType="java.util.Map">
       delete from t_sac_bu_bbc_audience  where ARTICLE_ID=#{articleId}
    </delete>
    <update id="updateArticle" parameterType="java.util.Map">
         update `t_sac_bu_bbc_article` set
        <if test="articleTitele !='' and articleTitele !=null ">ARTICLE_TITELE=#{articleTitele},</if>
        <if test="articleBuild !=null and articleBuild!=''">ARTICLE_BUILD = #{articleBuild},</if>
        <if test="articleBuildName !=null and articleBuildName!=''">ARTICLE_BUILD_NAME=#{articleBuildName},</if>
        <if test="articleContent !=null and articleContent!=''">ARTICLE_CONTENT= #{articleContent},</if>
        <if test="articleImage !=null and articleImage!=''">ARTICLE_IMAGE= #{articleImage},</if>
        <if test="articleStatus !=null and articleStatus!=''">ARTICLE_STATUS=#{articleStatus},</if>
        <if test="articleStatusName !=null and articleStatusName!=''">ARTICLE_STATUS_NAME=#{articleStatusName},</if>
        <if test="articleType !=null and articleType!=''">ARTICLE_TYPE=#{articleType},</if>
        <if test="articleTypeName !=null and articleTypeName!=''">ARTICLE_TYPE_NAME= #{articleTypeName},</if>
        <if test="isComment !=null and isComment!=''">IS_COMMENT=#{isComment},</if>
        <if test="isPersonComment !=null and isPersonComment!=''">IS_PERSON_COMMENT=#{isPersonComment},</if>
        <if test="isAnonymous !=null and isAnonymous!=''">IS_ANONYMOUS=#{isAnonymous},</if>
        <if test="shPersonId !=null and shPersonId!=''">SH_PERSON_ID=#{shPersonId},</if>
        <if test="shPersonName !=null and shPersonName!=''">SH_PERSON_NAME=#{shPersonName},</if>
        <if test="articleOrgCode !=null and articleOrgCode!=''">ARTICLE_ORG_CODE=#{articleOrgCode},</if>
        <if test="shTime !=null">SH_TIME=#{shTime},</if>
          REMARK=#{remark},
        `MODIFIER`= #{modifier},
        `MODIFY_NAME`= #{modifyName},
        `LAST_UPDATED_DATE`= #{lastUpdatedDate},
        `IS_ENABLE`= #{isEnable},
        `UPDATE_CONTROL_ID`=UUID()
         where
         ARTICLE_ID = #{articleId}
         and UPDATE_CONTROL_ID=#{updateControlId}
             </update>
   <select id="queryArticleCt" parameterType="java.util.Map" resultType="java.lang.Integer">
       select COUNT(1) from t_sac_bu_bbc_article_count where
       CREATOR=#{creator}
       and ARTICLE_ID = #{articleId}
   </select>
    <insert id="saveArticleCt" parameterType="java.util.Map" >

 INSERT INTO t_sac_bu_bbc_article_count (`COUNT_ID`, `ARTICLE_ID`, `CREATOR`, `CREATED_DATE`) VALUES
 (UUID(),#{articleId} , #{creator}, #{createdDate});
    </insert>
    <select id="queryArticle" resultType="java.util.Map" parameterType="java.util.Map">

               with LAST_CREATED_DATE as (
        select MAX(bc.CREATED_DATE) as LAST_CREATED_DATE,bc.ARTICLE_ID  from
        t_sac_bu_bbc_comment bc
        where  bc.COMMENT_STATUS='1'
        group by bc.ARTICLE_ID
        ), comment as (
        select COUNT(1) as comment ,cm.ARTICLE_ID  from
        t_sac_bu_bbc_comment cm
        where cm.COMMENT_STATUS='2'   and cm.IS_ENABLE='1'
        group by cm.ARTICLE_ID
        ) , article_count as (
        select  COUNT(1) as count,ct.ARTICLE_ID from t_sac_bu_bbc_article_count ct   group by  ct.ARTICLE_ID)
        <if test="param !=null and param.objectId2 !=null and param.objectId2 !=''">
            , ARTICLE_ID as (
            select al.ARTICLE_ID from t_sac_bu_bbc_article al
            where 1=1
            <if test="param.objectId1!='' and  param.objectId1!=null">
                and al.ARTICLE_TYPE &lt;&gt; 1
            </if>


            <if test="param.objectId1=='' or param.objectId1==null">
                and al.ARTICLE_STATUS='22'
                and (
                not
                exists ( select 1 from t_sac_bu_bbc_audience bc where bc.ARTICLE_ID=al.ARTICLE_ID) or
                al.ARTICLE_ID in ( select bc.ARTICLE_ID from t_sac_bu_bbc_audience bc where
                FIND_IN_SET(bc.OBJECT_ID,#{param.posCode})))
            </if>


            )
        </if>
        select
        a.ARTICLE_ID,
        a.ARTICLE_TITELE,
        a.ARTICLE_BUILD,
        a.ARTICLE_BUILD_NAME,
        a.ARTICLE_CONTENT,
        a.ARTICLE_IMAGE,
        a.ARTICLE_STATUS,
        a.ARTICLE_STATUS_NAME,
        a.ARTICLE_TYPE,
        a.ARTICLE_TYPE_NAME,
        a.IS_COMMENT,
        a.IS_PERSON_COMMENT,
        a.IS_ANONYMOUS,
        a.SH_PERSON_ID,
        a.SH_PERSON_NAME,
        a.SH_TIME,
        CONCAT(a.ARTICLE_STATUS_NAME ,',',a.REMARK)  REMARK,
        a.CREATOR,
        a.CREATED_NAME,
        a.CREATED_DATE,
        a.MODIFIER,
        a.MODIFY_NAME,
        a.LAST_UPDATED_DATE,
        a.IS_ENABLE,
        a.UPDATE_CONTROL_ID,
        if(GROUP_CONCAT(ad.OBJECT_FULLNAME) is not null,GROUP_CONCAT(ad.OBJECT_FULLNAME),'全部') AS PUBLIC_TARGET,
        GROUP_CONCAT(ad.OBJECT_ID) AS OBJECT_ID,
        ar.count,
        cm.comment,
        le.LAST_CREATED_DATE,
        case when a.ARTICLE_STATUS='21' and a.ARTICLE_TYPE='2' then '发布审核中'
        when a.ARTICLE_STATUS='22' and a.ARTICLE_TYPE='2' then '已发布'
        when a.ARTICLE_STATUS='23' and a.ARTICLE_TYPE='2' then '发布驳回'
        else '草稿' end articleName,
        a.ARTICLE_ORG_CODE
        from csc.t_sac_bu_bbc_article  a
        left join t_sac_bu_bbc_audience ad on a.ARTICLE_ID=ad.ARTICLE_ID
        left join LAST_CREATED_DATE le on a.ARTICLE_ID=le.ARTICLE_ID
        left join comment cm on a.ARTICLE_ID=cm.ARTICLE_ID
        left join  article_count  ar on a.ARTICLE_ID=ar.ARTICLE_ID
        where 1=1
        and a.IS_ENABLE='1'
        <if test="param !=null and param.userId !=null and param.userId !=''">
            and a.CREATOR=#{param.userId}
        </if>
        <if test="param !=null and param.articleBuild !=null and param.articleBuild !=''">
            and a.ARTICLE_BUILD=#{param.articleBuild}
        </if>
        <if test="param !=null and param.articleType !=null and param.articleType !=''">
            and concat(a.ARTICLE_TYPE,a.ARTICLE_STATUS)=#{param.articleType}
        </if>
        <if test="param !=null and param.articleTitele !=null and param.articleTitele !=''">
            and ( INSTR(a.ARTICLE_TITELE,#{param.articleTitele})>0
            or INSTR(a.ARTICLE_CONTENT,#{param.articleTitele})>0 )
        </if>
        <if test="param !=null and param.isPersonComment !=null and param.isPersonComment !=''">
            and a.IS_PERSON_COMMENT=#{param.isPersonComment}
        
        </if>
        <if test="param !=null and param.beginDate !=null and param.beginDate !=''">
            and date (a.CREATED_DATE) &gt;= date (#{param.beginDate})
        </if>
        <if test="param !=null and param.endDate !=null and param.endDate !=''">
            and date (a.CREATED_DATE) &lt;= date (#{param.endDate})
        </if>

        <if test="param !=null and param.objectId2 !=null and param.objectId2 !=''">
            and (a.CREATOR=#{param.objectId2}  or exists (
            select 1 from ARTICLE_ID le where le.ARTICLE_ID=a.ARTICLE_ID
            )
            )
        </if>

        <if test="param !=null and param.articleTNC !=null and param.articleTNC !=''">
            and ( INSTR(a.ARTICLE_TITELE,#{param.articleTNC})>0
            or INSTR(a.ARTICLE_CONTENT,#{param.articleTNC})>0 or ( a.IS_ANONYMOUS='1' and INSTR(a.CREATED_NAME,#{param.articleTNC})>0 ) )
        </if>
        <if test="param !=null and param.articleStatus !=null and param.articleStatus !=''">
            and a.ARTICLE_STATUS= #{param.articleStatus}
        </if>
        group by a.ARTICLE_ID
        ORDER BY  a.CREATED_DATE desc
    </select>
</mapper>
