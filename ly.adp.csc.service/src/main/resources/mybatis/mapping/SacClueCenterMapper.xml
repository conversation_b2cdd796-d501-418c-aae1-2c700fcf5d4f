<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacClueCenterMapper">
	<!-- 客制化回访信息查询 -->
	<select id="queryReviewInfo" resultType="java.util.Map">
		select
		REVIEW_ID,
		ORG_CODE,
		ORG_NAME,
		BILL_TYPE,
		BILL_TYPE_NAME,
		BUSINESS_TYPE,
		BUSINESS_TYPE_NAME,
		INFO_CHAN_M_CODE,
		INFO_CHAN_M_NAME,
		INFO_CHAN_D_CODE,
		INFO_CHAN_D_NAME,
		INFO_CHAN_DD_CODE,
		INFO_CHAN_DD_NAME,
		CHANNEL_CODE,
		CHANNEL_NAME,
		BILL_CODE,
		date_format(PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
		REVIEW_TIME,
		LAST_REVIEW_TIME,
		OVER_REVIEW_TIME,
		PLAN_COME_TIME,
		FACT_COME_TIME,
		IS_COME,
		ASSIGN_STATUS,
		ASSIGN_STATUS_NAME,
		ASSIGN_TIME,
		ASSIGN_PERSON_ID,
		ASSIGN_PERSON_NAME,
		REVIEW_PERSON_ID,
		REVIEW_PERSON_NAME,
		REVIEW_DESC,
		REVIEW_STATUS,
		REVIEW_STATUS_NAME,
		CUST_ID,
		CUST_NAME,
		PHONE,
		GENDER,
		GENDER_NAME,
		TOUCH_STATUS,
		TOUCH_STATUS_NAME,
		ERROR_REASON_CODE,
		ERROR_REASON_NAME,
		NODE_CODE,
		NODE_NAME,
		SEND_DLR_CODE,
		SEND_DLR_SHORT_NAME,
		SEND_TIME,
		INTEN_LEVEL_CODE,
		INTEN_LEVEL_NAME,
		INTEN_BRAND_CODE,
		INTEN_BRAND_NAME,
		INTEN_SERIES_CODE,
		INTEN_SERIES_NAME,
		INTEN_CAR_TYPE_CODE,
		INTEN_CAR_TYPE_NAME,
		CREATED_NAME,
		CREATED_DATE,
		UPDATE_CONTROL_ID,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		COLUMN6,
		COLUMN7,
		COLUMN8,
		COLUMN9,
		COLUMN10,
		COLUMN11,
		COLUMN12,
		COLUMN13,
		COLUMN14,
		COLUMN15,
		COLUMN16,
		COLUMN17,
		COLUMN18,
		COLUMN19,
		COLUMN20,
		BIG_COLUMN1,
		BIG_COLUMN2,
		BIG_COLUMN3,
		BIG_COLUMN4,
		BIG_COLUMN5,
		EXTENDS_JSON,
		PROVINCE_CODE,
		PROVINCE_NAME,
		CITY_CODE,
		CITY_NAME,
		COUNTY_CODE,
		COUNTY_NAME,
		MANAGE_LABEL_CODE,
		MANAGE_LABEL_NAME
		from t_sac_review
		where 1=1
		<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID = #{param.reviewId}</if>
		<if test="param.reviewIdIn !=null and param.reviewIdIn !=''">
			and REVIEW_ID IN
			<foreach collection="param.reviewIdIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and REVIEW_PERSON_ID=#{param.reviewPersonId}</if>
		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and REVIEW_PERSON_NAME=#{param.reviewPersonName}</if>
		<if test="param.orgCode !=null and param.orgCode !=''">and ORG_CODE=#{param.orgCode}</if>
		<if test="param.orgName !=null and param.orgName !=''">and ORG_NAME=#{param.orgName}</if>
		<if test="param.billCode !=null and param.billCode !=''">and BILL_CODE=#{param.billCode}</if>
		<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
	</select>

	<select id="queryReviewInfoNoPage" resultType="java.util.Map">
		select
		REVIEW_ID,
		ORG_CODE,
		ORG_NAME,
		BILL_TYPE,
		BILL_TYPE_NAME,
		BUSINESS_TYPE,
		BUSINESS_TYPE_NAME,
		INFO_CHAN_M_CODE,
		INFO_CHAN_M_NAME,
		INFO_CHAN_D_CODE,
		INFO_CHAN_D_NAME,
		INFO_CHAN_DD_CODE,
		INFO_CHAN_DD_NAME,
		CHANNEL_CODE,
		CHANNEL_NAME,
		BILL_CODE,
		date_format(PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
		REVIEW_TIME,
		LAST_REVIEW_TIME,
		OVER_REVIEW_TIME,
		PLAN_COME_TIME,
		FACT_COME_TIME,
		IS_COME,
		ASSIGN_STATUS,
		ASSIGN_STATUS_NAME,
		ASSIGN_TIME,
		ASSIGN_PERSON_ID,
		ASSIGN_PERSON_NAME,
		REVIEW_PERSON_ID,
		REVIEW_PERSON_NAME,
		REVIEW_DESC,
		REVIEW_STATUS,
		REVIEW_STATUS_NAME,
		CUST_ID,
		CUST_NAME,
		PHONE,
		GENDER,
		GENDER_NAME,
		TOUCH_STATUS,
		TOUCH_STATUS_NAME,
		ERROR_REASON_CODE,
		ERROR_REASON_NAME,
		NODE_CODE,
		NODE_NAME,
		SEND_DLR_CODE,
		SEND_DLR_SHORT_NAME,
		SEND_TIME,
		INTEN_LEVEL_CODE,
		INTEN_LEVEL_NAME,
		INTEN_BRAND_CODE,
		INTEN_BRAND_NAME,
		INTEN_SERIES_CODE,
		INTEN_SERIES_NAME,
		INTEN_CAR_TYPE_CODE,
		INTEN_CAR_TYPE_NAME,
		CREATED_NAME,
		CREATED_DATE,
		UPDATE_CONTROL_ID,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		COLUMN6,
		COLUMN7,
		COLUMN8,
		COLUMN9,
		COLUMN10,
		COLUMN11,
		COLUMN12,
		COLUMN13,
		COLUMN14,
		COLUMN15,
		COLUMN16,
		COLUMN17,
		COLUMN18,
		COLUMN19,
		COLUMN20,
		BIG_COLUMN1,
		BIG_COLUMN2,
		BIG_COLUMN3,
		BIG_COLUMN4,
		BIG_COLUMN5,
		EXTENDS_JSON,
		PROVINCE_CODE,
		PROVINCE_NAME,
		CITY_CODE,
		CITY_NAME,
		COUNTY_CODE,
		COUNTY_NAME,
		MANAGE_LABEL_CODE,
		MANAGE_LABEL_NAME
		from t_sac_review
		where 1=1
		<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID = #{param.reviewId}</if>
		<if test="param.reviewIdIn !=null and param.reviewIdIn !=''">
			and REVIEW_ID IN
			<foreach collection="param.reviewIdIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and REVIEW_PERSON_ID=#{param.reviewPersonId}</if>
		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and REVIEW_PERSON_NAME=#{param.reviewPersonName}</if>
		<if test="param.orgCode !=null and param.orgCode !=''">and ORG_CODE=#{param.orgCode}</if>
		<if test="param.orgName !=null and param.orgName !=''">and ORG_NAME=#{param.orgName}</if>
		<if test="param.billCode !=null and param.billCode !=''">and BILL_CODE=#{param.billCode}</if>
		<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
		<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
	</select>

	<!-- clue 更新 -->
	<!-- 客制化线索信息更新 -->
	<update id="updatesacClue" parameterType="java.util.Map">
		UPDATE
		${param.dbName}.t_sac_clue_info_dlr
		SET
		<if test="param.Cphone != null and param.Cphone !=''">PHONE = #{param.Cphone},</if>
		<if test="param.planBuyDate != null">column2 = #{param.planBuyDate},</if>
		<if test="param.planBuyDateName != null">column1 = #{param.planBuyDateName},</if>
		<if test="param.intenLevelCode != null">INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
		<if test="param.intenLevelName != null">INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
		<if test="param.intenCarTypeCode != null">INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
		<if test="param.intenCarTypeName != null">INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
		<if test="param.outColorCode != null">OUT_COLOR_CODE = #{param.outColorCode},</if>
		<if test="param.outColorName != null">OUT_COLOR_NAME = #{param.outColorName},</if>

		<if test="param.firstTime != null and param.firstTime !=''">FIRST_REVIEW_TIME=if(FIRST_REVIEW_TIME is null,#{param.firstTime},FIRST_REVIEW_TIME),</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
		<if test="param.dlrShortName != null and param.dlrShortName !=''">DLR_SHORT_NAME = #{param.dlrShortName},</if>
		<if test="param.provinceCode != null and param.provinceCode !=''">PROVINCE_CODE = #{param.provinceCode},</if>
		<if test="param.provinceName != null and param.provinceName !=''">PROVINCE_NAME = #{param.provinceName},</if>
		<if test="param.cityCode != null and param.cityCode !=''">CITY_CODE = #{param.cityCode},</if>
		<if test="param.cityName != null and param.cityName !=''">CITY_NAME = #{param.cityName},</if>
		<if test="param.countyCode != null and param.countyCode !=''">COUNTY_CODE = #{param.countyCode},</if>
		<if test="param.countyName != null and param.countyName !=''">COUNTY_NAME = #{param.countyName},</if>
		<if test="param.smartId != null and param.smartId !=''">COLUMN10 = #{param.smartId},</if>

		<if test="param.lastReviewTime != null">LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
		<if test="param.custName != null and param.custName !=''">CUST_NAME = #{param.custName},</if>
		<if test="param.genderCode != null and param.genderCode !=''">GENDER_CODE = #{param.genderCode},GENDER_NAME = #{param.genderName},</if>
		<if test="param.column11 != null and param.column11 !=''">COLUMN11 = #{param.column11},</if>
		<if test="param.businessHeatCode != null and param.businessHeatCode !=''">COLUMN5 = #{param.businessHeatCode},</if>
		<if test="param.businessHeatName != null and param.businessHeatName !=''">COLUMN6 = #{param.businessHeatName},</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
		<if test="param.dlrName != null and param.dlrName !=''">DLR_SHORT_NAME = #{param.dlrName},</if>
		<if test="param.statusCode != null and param.statusCode !=''">STATUS_CODE = #{param.statusCode},</if>
		<if test="param.statusName != null and param.statusName !=''">STATUS_NAME = #{param.statusName},</if>

		<if test="param.dccFlag != null and param.dccFlag !=''">
			COLUMN19 = #{param.dccFlag},
			REVIEW_PERSON_ID = #{param.reviewPersonId},
			REVIEW_PERSON_NAME = #{param.reviewPersonName},
		</if>

		MODIFIER=#{param.modifier},
		LAST_UPDATED_DATE=now(),
		MODIFY_NAME = #{param.modifyName}
		WHERE 1=1
		<if test="param.phone != null and param.phone !=''"> and PHONE = #{param.phone}</if>
		<if test="param.serverOrder != null and param.serverOrder !=''"> and SERVER_ORDER = #{param.serverOrder}</if>
	</update>
	<!-- clue 更新 -->
<!--	// 改了-->
	<update id="updatesacClueBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            UPDATE
            ${param.dbName}.t_sac_clue_info_dlr
            SET
            <if test="param.Cphone != null and param.Cphone !=''">PHONE = #{param.Cphone},</if>
            <if test="param.planBuyDate != null">column2 = #{param.planBuyDate},</if>
            <if test="param.planBuyDateName != null">column1 = #{param.planBuyDateName},</if>
            <if test="param.intenLevelCode != null">INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
            <if test="param.intenLevelName != null">INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
            <if test="param.intenCarTypeCode != null">INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
            <if test="param.intenCarTypeName != null">INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
            <if test="param.outColorCode != null">OUT_COLOR_CODE = #{param.outColorCode},</if>
            <if test="param.outColorName != null">OUT_COLOR_NAME = #{param.outColorName},</if>

            <if test="param.firstTime != null and param.firstTime !=''">FIRST_REVIEW_TIME=if(FIRST_REVIEW_TIME is
                null,#{param.firstTime},FIRST_REVIEW_TIME),
            </if>
            <if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
            <if test="param.dlrShortName != null and param.dlrShortName !=''">DLR_SHORT_NAME = #{param.dlrShortName},
            </if>
            <if test="param.provinceCode != null and param.provinceCode !=''">PROVINCE_CODE = #{param.provinceCode},
            </if>
            <if test="param.provinceName != null and param.provinceName !=''">PROVINCE_NAME = #{param.provinceName},
            </if>
            <if test="param.cityCode != null and param.cityCode !=''">CITY_CODE = #{param.cityCode},</if>
            <if test="param.cityName != null and param.cityName !=''">CITY_NAME = #{param.cityName},</if>
            <if test="param.countyCode != null and param.countyCode !=''">COUNTY_CODE = #{param.countyCode},</if>
            <if test="param.countyName != null and param.countyName !=''">COUNTY_NAME = #{param.countyName},</if>
            <if test="param.smartId != null and param.smartId !=''">COLUMN10 = #{param.smartId},</if>

            <if test="param.lastReviewTime != null">LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
            <if test="param.custName != null and param.custName !=''">CUST_NAME = #{param.custName},</if>
            <if test="param.genderCode != null and param.genderCode !=''">GENDER_CODE = #{param.genderCode},GENDER_NAME
                = #{param.genderName},
            </if>
            <if test="param.column11 != null and param.column11 !=''">COLUMN11 = #{param.column11},</if>
            <if test="param.businessHeatCode != null and param.businessHeatCode !=''">COLUMN5 =
                #{param.businessHeatCode},
            </if>
            <if test="param.businessHeatName != null and param.businessHeatName !=''">COLUMN6 =
                #{param.businessHeatName},
            </if>
            <if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
            <if test="param.dlrName != null and param.dlrName !=''">DLR_SHORT_NAME = #{param.dlrName},</if>
            <if test="param.statusCode != null and param.statusCode !=''">STATUS_CODE = #{param.statusCode},</if>
            <if test="param.statusName != null and param.statusName !=''">STATUS_NAME = #{param.statusName},</if>
            MODIFIER=#{param.modifier},
            LAST_UPDATED_DATE=now(),
            MODIFY_NAME = #{param.modifyName}
            WHERE 1=1
            <if test="param.phone != null and param.phone !=''">and PHONE = #{param.phone}</if>
            <if test="param.serverOrder != null and param.serverOrder !=''">and SERVER_ORDER = #{param.serverOrder}</if>
        </foreach>
    </update>

	<!-- 客制化回访信息更新 -->
	<update id="updatesacReview" parameterType="java.util.Map">
		UPDATE
		t_sac_review
		SET
		column15='定期跟进',
		<if test="param.Cphone != null and param.Cphone !=''">PHONE = #{param.Cphone},</if>
		<if test="param.planBuyDate != null">column5 = #{param.planBuyDate},</if>
		<if test="param.planBuyDateName != null">column6 = #{param.planBuyDateName},</if>
		<if test="param.intenLevelCode != null">INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
		<if test="param.intenLevelName != null">INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
		<if test="param.intenCarTypeCode != null">INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
		<if test="param.intenCarTypeName != null">INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
		<if test="param.outColorCode != null">column1 = #{param.outColorCode},</if>
		<if test="param.outColorName != null">column2 = #{param.outColorName},</if>

		<if test="param.provinceCode != null and param.provinceCode !=''">PROVINCE_CODE = #{param.provinceCode},</if>
		<if test="param.provinceName != null and param.provinceName !=''">PROVINCE_NAME = #{param.provinceName},</if>
		<if test="param.cityCode != null and param.cityCode !=''">CITY_CODE = #{param.cityCode},</if>
		<if test="param.cityName != null and param.cityName !=''">CITY_NAME = #{param.cityName},</if>
		<if test="param.countyCode != null and param.countyCode !=''">COUNTY_CODE = #{param.countyCode},</if>
		<if test="param.countyName != null and param.countyName !=''">COUNTY_NAME = #{param.countyName},</if>

		<if test="param.planReviewTime != null">PLAN_REVIEW_TIME = #{param.planReviewTime},</if>
		<if test="param.overReviewTime != null">OVER_REVIEW_TIME = #{param.overReviewTime},</if>
		<if test="param.lastReviewTime != null">LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
		<if test="param.custName != null and param.custName !=''">CUST_NAME = #{param.custName},</if>
		<if test="param.genderCode != null and param.genderCode !=''">GENDER = #{param.genderCode},GENDER_NAME = #{param.genderName},</if>
		<if test="param.businessHeatCode != null and param.businessHeatCode !=''">COLUMN7 = #{param.businessHeatCode},</if>
		<if test="param.businessHeatName != null and param.businessHeatName !=''">COLUMN8 = #{param.businessHeatName},</if>
		<!-- isSpecial是否特别关注 -->
		<if test="param.column12 != null and param.column12 !=''">COLUMN12 = #{param.column12},</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">ORG_CODE = #{param.dlrCode},</if>
		<if test="param.dlrName != null and param.dlrName !=''">ORG_NAME = #{param.dlrName},</if>
		<if test="param.dlrShortName != null and param.dlrShortName !=''">ORG_NAME = #{param.dlrShortName},</if>

		<if test="param.dccFlag != null and param.dccFlag !=''">
			REVIEW_PERSON_ID = #{param.reviewPersonId},
			REVIEW_PERSON_NAME = #{param.reviewPersonName},
			column19='1',
		</if>

		MODIFIER=#{param.modifier},
		LAST_UPDATED_DATE=now(),
		MODIFY_NAME = #{param.modifyName}
		WHERE 1=1
		<if test="param.phone != null and param.phone !=''"> and PHONE = #{param.phone}</if>
		<if test="param.serverOrder != null and param.serverOrder !=''"> and BILL_CODE = #{param.serverOrder}</if>
	</update>

	<update id="updatesacReviewBatch" parameterType="java.util.Map">
        <foreach collection="list" item="param" separator=";">
            UPDATE
            t_sac_review
            SET
            column15='定期跟进',
            <if test="param.Cphone != null and param.Cphone !=''">PHONE = #{param.Cphone},</if>
            <if test="param.planBuyDate != null">column5 = #{param.planBuyDate},</if>
            <if test="param.planBuyDateName != null">column6 = #{param.planBuyDateName},</if>
            <if test="param.intenLevelCode != null">INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
            <if test="param.intenLevelName != null">INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
            <if test="param.intenCarTypeCode != null">INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
            <if test="param.intenCarTypeName != null">INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
            <if test="param.outColorCode != null">column1 = #{param.outColorCode},</if>
            <if test="param.outColorName != null">column2 = #{param.outColorName},</if>

            <if test="param.provinceCode != null and param.provinceCode !=''">PROVINCE_CODE = #{param.provinceCode},
            </if>
            <if test="param.provinceName != null and param.provinceName !=''">PROVINCE_NAME = #{param.provinceName},
            </if>
            <if test="param.cityCode != null and param.cityCode !=''">CITY_CODE = #{param.cityCode},</if>
            <if test="param.cityName != null and param.cityName !=''">CITY_NAME = #{param.cityName},</if>
            <if test="param.countyCode != null and param.countyCode !=''">COUNTY_CODE = #{param.countyCode},</if>
            <if test="param.countyName != null and param.countyName !=''">COUNTY_NAME = #{param.countyName},</if>
			<if test="param.planReviewTime != null">PLAN_REVIEW_TIME = #{param.planReviewTime},</if>
			<if test="param.overReviewTime != null">OVER_REVIEW_TIME = #{param.overReviewTime},</if>
			<if test="param.lastReviewTime != null">LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
            <if test="param.custName != null and param.custName !=''">CUST_NAME = #{param.custName},</if>
            <if test="param.genderCode != null and param.genderCode !=''">GENDER = #{param.genderCode},GENDER_NAME =
                #{param.genderName},
            </if>
            <if test="param.businessHeatCode != null and param.businessHeatCode !=''">COLUMN7 =
                #{param.businessHeatCode},
            </if>
            <if test="param.businessHeatName != null and param.businessHeatName !=''">COLUMN8 =
                #{param.businessHeatName},
            </if>
            <!-- isSpecial是否特别关注 -->
            <if test="param.column12 != null and param.column12 !=''">COLUMN12 = #{param.column12},</if>
            <if test="param.dlrCode != null and param.dlrCode !=''">ORG_CODE = #{param.dlrCode},</if>
            <if test="param.dlrName != null and param.dlrName !=''">ORG_NAME = #{param.dlrName},</if>
            <if test="param.dlrShortName != null and param.dlrShortName !=''">ORG_NAME = #{param.dlrShortName},</if>
            MODIFIER=#{param.modifier},
            LAST_UPDATED_DATE=now(),
            MODIFY_NAME = #{param.modifyName}
            WHERE 1=1
            <if test="param.phone != null and param.phone !=''">and PHONE = #{param.phone}</if>
            <if test="param.serverOrder != null and param.serverOrder !=''">and BILL_CODE = #{param.serverOrder}</if>
        </foreach>
    </update>
	<!-- 客制化线索信息查询 -->
	<select id="sacUserCluedlrbyquery" resultType="java.util.Map">
		SELECT
			t1.ID,
			t1.SERVER_ORDER,
			t1.PV_SERVER_ORDER,
			t1.CUST_ID,
			t1.CUST_NAME,
			t1.PHONE,
			t1.PHONE_BACKUP,
			t1.INTEN_LEVEL_CODE,
			t1.INTEN_LEVEL_NAME,
			t1.INTEN_BRAND_CODE,
			t1.INTEN_BRAND_NAME,
			t1.INTEN_SERIES_CODE,
			t1.INTEN_SERIES_NAME,
			t1.INTEN_OPTION_PACKAGE_CODE,
			t1.INTEN_OPTION_PACKAGE_NAME,
			t2.INTEN_CAR_TYPE_CODE,
			t2.INTEN_CAR_TYPE_NAME,
			t2.COLUMN3 AS INNER_COLOR_CODE,
			t2.COLUMN4 as INNER_COLOR_NAME,
			t2.COLUMN1 as OUT_COLOR_CODE,
			t2.COLUMN2 as OUT_COLOR_NAME,
			ifnull(t2.COLUMN15,'定期跟进') followReason,
			t1.DLR_CODE,
			t1.DLR_SHORT_NAME,
			t1.SOURCE_SYSTEMT_CODE,
			t1.SOURCE_SYSTEMT_NAME,
			t1.RECEIVE_TIME,
			t1.SOURCE_SERVER_ORDER,
			t1.INFO_CHAN_M_CODE,
			t1.INFO_CHAN_M_NAME,
			t1.INFO_CHAN_D_CODE,
			t1.INFO_CHAN_D_NAME,
			t1.INFO_CHAN_DD_CODE,
			t1.INFO_CHAN_DD_NAME,
			t1.CHANNEL_CODE,
			t1.CHANNEL_NAME,
			t1.GENDER_CODE,
			t1.GENDER_NAME,
			t1.STATUS_CODE,
			t1.STATUS_NAME,
			t1.DEAL_NODE_CODE,
			t1.DEAL_NODE_NAME,
			t1.REVIEW_ID,
			t1.FIRST_REVIEW_TIME,
			t1.LAST_REVIEW_TIME,
			t1.EXTENDS_JSON,
			t1.OEM_ID,
			t1.GROUP_ID,
			t1.CREATOR,
			t1.CREATED_NAME,
			t1.CREATED_DATE,
			t1.MODIFIER,
			t1.MODIFY_NAME,
			t1.LAST_UPDATED_DATE,
			t1.IS_ENABLE,
			t1.UPDATE_CONTROL_ID,
			t1.ASSIGN_TIME,
			t1.REVIEW_PERSON_NAME,
			t1.REVIEW_PERSON_ID,
			t1.COLUMN6 AS businessHeatCode,
			t1.COLUMN9 AS clueScore,
			t1.COLUMN7 AS carPurchaseBudget,
			t1.BIG_COLUMN2 AS illustration,
			t1.COLUMN5 AS businessHeatName,
			t1.COLUMN2 AS planBuyDate,
			t1.COLUMN11 AS isSpecial,
			t1.COLUMN3 AS testDriveDateName,
			t1.BIG_COLUMN1 AS remark,
			t1.COLUMN4 AS testDriveDate,
			t1.COLUMN8 AS activityId,
			t1.COLUMN10 AS smartId,
			t1.COLUMN1 AS planBuyDateName,
			t1.PROVINCE_CODE,
			t1.PROVINCE_NAME,
			t1.CITY_CODE,
			t1.CITY_NAME,
			t1.COUNTY_CODE,
			t1.COUNTY_NAME,
			t1.COLUMN19 AS dccFlag,
			t2.UPDATE_CONTROL_ID as reviewUpdateControlId,
            t2.BILL_TYPE as billType,
            t2.BILL_TYPE_NAME as billTypeName,
            t2.ASSIGN_STATUS as assignStatus,
            t2.ASSIGN_STATUS_NAME as assignStatusName,
            t2.PLAN_REVIEW_TIME,
			t3.USER_GROUP_ID,
			t3.DETAIL_ID
		FROM
			t_sac_clue_info_dlr t1
			left JOIN t_sac_review t2 ON t2.BILL_CODE = t1.SERVER_ORDER 
			left JOIN t_sac_user_group_detail t3 ON t3.SERVER_ORDER = t1.SERVER_ORDER 
		WHERE
			<include refid="sacUserCluedlrbyqueryCondition"/>
		GROUP BY t1.SERVER_ORDER
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="sacUserCluedlrbyqueryServerOrder" resultType="com.ly.mp.csc.clue.entities.SacClueInfoDlr">
		SELECT
			DISTINCT t1.SERVER_ORDER
		FROM
			t_sac_clue_info_dlr t1
			left JOIN t_sac_review t2 ON t2.BILL_CODE = t1.SERVER_ORDER
			left JOIN t_sac_user_group_detail t3 ON t3.SERVER_ORDER = t1.SERVER_ORDER
		WHERE
			<include refid="sacUserCluedlrbyqueryCondition"/>
		ORDER BY
		    t1.LAST_UPDATED_DATE DESC
	</select>

	<select id="sacUserCluedlrbyqueryPerformance" resultType="java.util.Map">
		SELECT
			t1.ID,
			t1.SERVER_ORDER,
			t1.PV_SERVER_ORDER,
			t1.CUST_ID,
			t1.CUST_NAME,
			t1.PHONE,
			t1.PHONE_BACKUP,
			t1.INTEN_LEVEL_CODE,
			t1.INTEN_LEVEL_NAME,
			t1.INTEN_BRAND_CODE,
			t1.INTEN_BRAND_NAME,
			t1.INTEN_SERIES_CODE,
			t1.INTEN_SERIES_NAME,
			t1.INTEN_OPTION_PACKAGE_CODE,
			t1.INTEN_OPTION_PACKAGE_NAME,
			t2.INTEN_CAR_TYPE_CODE,
			t2.INTEN_CAR_TYPE_NAME,
			t2.COLUMN3 AS INNER_COLOR_CODE,
			t2.COLUMN4 as INNER_COLOR_NAME,
			t2.COLUMN1 as OUT_COLOR_CODE,
			t2.COLUMN2 as OUT_COLOR_NAME,
			ifnull(t2.COLUMN15,'定期跟进') followReason,
			t1.DLR_CODE,
			t1.DLR_SHORT_NAME,
			t1.SOURCE_SYSTEMT_CODE,
			t1.SOURCE_SYSTEMT_NAME,
			t1.RECEIVE_TIME,
			t1.SOURCE_SERVER_ORDER,
			t1.INFO_CHAN_M_CODE,
			t1.INFO_CHAN_M_NAME,
			t1.INFO_CHAN_D_CODE,
			t1.INFO_CHAN_D_NAME,
			t1.INFO_CHAN_DD_CODE,
			t1.INFO_CHAN_DD_NAME,
			t1.CHANNEL_CODE,
			t1.CHANNEL_NAME,
			t1.GENDER_CODE,
			t1.GENDER_NAME,
			t1.STATUS_CODE,
			t1.STATUS_NAME,
			t1.DEAL_NODE_CODE,
			t1.DEAL_NODE_NAME,
			t1.REVIEW_ID,
			t1.FIRST_REVIEW_TIME,
			t1.LAST_REVIEW_TIME,
			t1.EXTENDS_JSON,
			t1.OEM_ID,
			t1.GROUP_ID,
			t1.CREATOR,
			t1.CREATED_NAME,
			t1.CREATED_DATE,
			t1.MODIFIER,
			t1.MODIFY_NAME,
			t1.LAST_UPDATED_DATE,
			t1.IS_ENABLE,
			t1.UPDATE_CONTROL_ID,
			t1.ASSIGN_TIME,
			t1.REVIEW_PERSON_NAME,
			t1.REVIEW_PERSON_ID,
			t1.COLUMN6 AS businessHeatCode,
			t1.COLUMN9 AS clueScore,
			t1.COLUMN7 AS carPurchaseBudget,
			t1.BIG_COLUMN2 AS illustration,
			t1.COLUMN5 AS businessHeatName,
			t1.COLUMN2 AS planBuyDate,
			t1.COLUMN11 AS isSpecial,
			t1.COLUMN3 AS testDriveDateName,
			t1.BIG_COLUMN1 AS remark,
			t1.COLUMN4 AS testDriveDate,
			t1.COLUMN8 AS activityId,
			t1.COLUMN10 AS smartId,
			t1.COLUMN1 AS planBuyDateName,
			t1.PROVINCE_CODE,
			t1.PROVINCE_NAME,
			t1.CITY_CODE,
			t1.CITY_NAME,
			t1.COUNTY_CODE,
			t1.COUNTY_NAME,
			t2.UPDATE_CONTROL_ID as reviewUpdateControlId,
			t2.BILL_TYPE as billType,
			t2.BILL_TYPE_NAME as billTypeName,
			t2.ASSIGN_STATUS as assignStatus,
			t2.ASSIGN_STATUS_NAME as assignStatusName,
			t2.PLAN_REVIEW_TIME,
			t3.USER_GROUP_ID,
			t3.DETAIL_ID
		FROM
			t_sac_clue_info_dlr t1
			left JOIN t_sac_review t2 ON t2.BILL_CODE = t1.SERVER_ORDER
			left JOIN t_sac_user_group_detail t3 ON t3.SERVER_ORDER = t1.SERVER_ORDER
		WHERE
			<include refid="sacUserCluedlrbyqueryCondition"/>
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="sacUserCluedlrbyqueryCount" resultType="Long">
		SELECT
			count(DISTINCT t1.SERVER_ORDER)
		FROM
			t_sac_clue_info_dlr t1
			left JOIN t_sac_review t2 ON t2.BILL_CODE = t1.SERVER_ORDER
			left JOIN t_sac_user_group_detail t3 ON t3.SERVER_ORDER = t1.SERVER_ORDER
		WHERE
			<include refid="sacUserCluedlrbyqueryCondition"/>
	</select>

	<!-- 客制化线索信息查询条件 -->
	<sql id="sacUserCluedlrbyqueryCondition">
		1 =1
		<if test="param.billType != null and ''!= param.billType "> AND t2.bill_type = #{param.billType}</if>
		<if test="param.smartId != null and ''!= param.smartId "> AND t1.COLUMN10 = #{param.smartId}</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus "> AND t2.ASSIGN_STATUS = #{param.assignStatus}</if>
		<if test="param.planReviewTimeStart !=null and param.planReviewTimeStart !=''">and t2.PLAN_REVIEW_TIME>=#{param.planReviewTimeStart}</if>
		<if test="param.planReviewTimeEnd !=null and param.planReviewTimeEnd !=''"><![CDATA[and t2.PLAN_REVIEW_TIME<=#{param.planReviewTimeEnd}]]></if>

		<if test="param.detailId !=null and param.detailId !=''">and t3.DETAIL_ID=#{param.detailId}</if>
		<if test="param.userGroupId !=null and param.userGroupId !=''">and t3.USER_GROUP_ID=#{param.userGroupId}</if>

		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and t1.COLUMN6=#{param.businessHeatCode}</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and t1.COLUMN5=#{param.businessHeatName}</if>
		<if test="param.column5 !=null and param.column5 !=''">and t1.COLUMN5=#{param.column5}</if>
		<if test="param.id !=null and param.id !=''">and t1.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.column11 !=null and param.column11 !=''">and t1.COLUMN11=#{param.column11}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t1.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.dlrCodePaste !=null and param.dlrCodePaste !=''">and INSTR(t1.DLR_CODE,#{param.dlrCodePaste})>0</if>
		<if test="param.dlrShortNamePaste !=null and param.dlrShortNamePaste !=''">and INSTR(t1.DLR_SHORT_NAME,#{param.dlrShortNamePaste})>0</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and t1.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach></if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME=#{param.channelName}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>


		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and t1.PV_SERVER_ORDER like concat('%',#{param.pvServerOrder},'%')</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER like concat('%',#{param.serverOrder},'%')</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
		<if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and t1.REVIEW_PERSON_NAME like concat('%', #{param.reviewPersonName}, '%')</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and t1.REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and t1.ASSIGN_TIME>=#{param.assignTimeStart}</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''"><![CDATA[and t1.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and t1.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''"><![CDATA[and t1.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and t1.RECEIVE_TIME>=#{param.receiveTimeStart}</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''"><![CDATA[and t1.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''"><![CDATA[and t1.STATUS_CODE in (${param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">
			and t1.STATUS_CODE in <foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''"> and t1.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''"> and t1.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''"> and t1.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''"> and t1.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''"> and t1.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''"> and t1.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''"> and t1.PROVINCE_CODE IN <foreach collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''"> and t1.CITY_CODE IN <foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''"> and t1.COUNTY_CODE IN <foreach collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''"> and t1.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and t1.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.planBuyDateName !=null and param.planBuyDateName !=''">and t1.COLUMN1=#{param.planBuyDateName}</if>
		<if test="param.listServerOrder != null and param.listServerOrder.size()!=0">
			and t1.SERVER_ORDER in
			<foreach collection="param.listServerOrder" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>
		<if test="param.filterDccFlag">and t1.column19 is null</if>
	</sql>

	<!-- 客制化线索信息查询 -->
	<select id="sacUserCluedlrbyqueryNoPage" resultType="java.util.Map">
		SELECT
		t1.ID,
		t1.SERVER_ORDER,
		t1.PV_SERVER_ORDER,
		t1.CUST_ID,
		t1.CUST_NAME,
		t1.PHONE,
		t1.PHONE_BACKUP,
		t1.INTEN_LEVEL_CODE,
		t1.INTEN_LEVEL_NAME,
		t1.INTEN_BRAND_CODE,
		t1.INTEN_BRAND_NAME,
		t1.INTEN_SERIES_CODE,
		t1.INTEN_SERIES_NAME,
		t1.INTEN_OPTION_PACKAGE_CODE,
		t1.INTEN_OPTION_PACKAGE_NAME,
		<!--t1.INTEN_CAR_TYPE_CODE,
        t1.INTEN_CAR_TYPE_NAME,
        t1.INNER_COLOR_CODE,
        t1.INNER_COLOR_NAME,
        t1.OUT_COLOR_CODE,
        t1.OUT_COLOR_NAME,-->
		t2.INTEN_CAR_TYPE_CODE,
		t2.INTEN_CAR_TYPE_NAME,
		t2.COLUMN3 AS INNER_COLOR_CODE,
		t2.COLUMN4 as INNER_COLOR_NAME,
		t2.COLUMN1 as OUT_COLOR_CODE,
		t2.COLUMN2 as OUT_COLOR_NAME,
		ifnull(t2.COLUMN15,'定期跟进') followReason,
		t1.DLR_CODE,
		t1.DLR_SHORT_NAME,
		t1.SOURCE_SYSTEMT_CODE,
		t1.SOURCE_SYSTEMT_NAME,
		t1.RECEIVE_TIME,
		t1.SOURCE_SERVER_ORDER,
		t1.INFO_CHAN_M_CODE,
		t1.INFO_CHAN_M_NAME,
		t1.INFO_CHAN_D_CODE,
		t1.INFO_CHAN_D_NAME,
		t1.INFO_CHAN_DD_CODE,
		t1.INFO_CHAN_DD_NAME,
		t1.CHANNEL_CODE,
		t1.CHANNEL_NAME,
		t1.GENDER_CODE,
		t1.GENDER_NAME,
		t1.STATUS_CODE,
		t1.STATUS_NAME,
		t1.DEAL_NODE_CODE,
		t1.DEAL_NODE_NAME,
		t1.REVIEW_ID,
		t1.FIRST_REVIEW_TIME,
		t1.LAST_REVIEW_TIME,
		t1.EXTENDS_JSON,
		t1.OEM_ID,
		t1.GROUP_ID,
		t1.CREATOR,
		t1.CREATED_NAME,
		t1.CREATED_DATE,
		t1.MODIFIER,
		t1.MODIFY_NAME,
		t1.LAST_UPDATED_DATE,
		t1.IS_ENABLE,
		t1.UPDATE_CONTROL_ID,
		t1.ASSIGN_TIME,
		t1.REVIEW_PERSON_NAME,
		t1.REVIEW_PERSON_ID,
		t1.COLUMN6 AS businessHeatCode,
		t1.COLUMN9 AS clueScore,
		t1.COLUMN7 AS carPurchaseBudget,
		t1.BIG_COLUMN2 AS illustration,
		t1.COLUMN5 AS businessHeatName,
		t1.COLUMN2 AS planBuyDate,
		t1.COLUMN11 AS isSpecial,
		t1.COLUMN3 AS testDriveDateName,
		t1.BIG_COLUMN1 AS remark,
		t1.COLUMN4 AS testDriveDate,
		t1.COLUMN8 AS activityId,
		t1.COLUMN10 AS smartId,
		t1.COLUMN1 AS planBuyDateName,
		t1.PROVINCE_CODE,
		t1.PROVINCE_NAME,
		t1.CITY_CODE,
		t1.CITY_NAME,
		t1.COUNTY_CODE,
		t1.COUNTY_NAME,
		t2.UPDATE_CONTROL_ID as reviewUpdateControlId,
		t2.BILL_TYPE as billType,
		t2.BILL_TYPE_NAME as billTypeName,
		t2.ASSIGN_STATUS as assignStatus,
		t2.ASSIGN_STATUS_NAME as assignStatusName,
		t2.PLAN_REVIEW_TIME,
		t3.USER_GROUP_ID,
		t3.DETAIL_ID
		FROM
		adp_leads.t_sac_clue_info_dlr t1
		left JOIN t_sac_review t2 ON t2.BILL_CODE = t1.SERVER_ORDER
		left JOIN t_sac_user_group_detail t3 ON t3.SERVER_ORDER = t1.SERVER_ORDER
		WHERE
		1 =1
		<if test="param.billType != null and ''!= param.billType "> AND t2.bill_type = #{param.billType}</if>
		<if test="param.smartId != null and ''!= param.smartId "> AND t1.COLUMN10 = #{param.smartId}</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus "> AND t2.ASSIGN_STATUS = #{param.assignStatus}</if>
		<if test="param.planReviewTimeStart !=null and param.planReviewTimeStart !=''">and t2.PLAN_REVIEW_TIME>=#{param.planReviewTimeStart}</if>
		<if test="param.planReviewTimeEnd !=null and param.planReviewTimeEnd !=''"><![CDATA[and t2.PLAN_REVIEW_TIME<=#{param.planReviewTimeEnd}]]></if>

		<if test="param.detailId !=null and param.detailId !=''">and t3.DETAIL_ID=#{param.detailId}</if>
		<if test="param.userGroupId !=null and param.userGroupId !=''">and t3.USER_GROUP_ID=#{param.userGroupId}</if>

		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and t1.COLUMN6=#{param.businessHeatCode}</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and t1.COLUMN5=#{param.businessHeatName}</if>
		<if test="param.column5 !=null and param.column5 !=''">and t1.COLUMN5=#{param.column5}</if>
		<if test="param.id !=null and param.id !=''">and t1.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.column11 !=null and param.column11 !=''">and t1.COLUMN11=#{param.column11}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t1.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.dlrCodePaste !=null and param.dlrCodePaste !=''">and INSTR(t1.DLR_CODE,#{param.dlrCodePaste})>0</if>
		<if test="param.dlrShortNamePaste !=null and param.dlrShortNamePaste !=''">and INSTR(t1.DLR_SHORT_NAME,#{param.dlrShortNamePaste})>0</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and t1.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach></if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME=#{param.channelName}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>


		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and t1.PV_SERVER_ORDER like concat('%',#{param.pvServerOrder},'%')</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER like concat('%',#{param.serverOrder},'%')</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
		<if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and t1.REVIEW_PERSON_NAME like concat('%', #{param.reviewPersonName}, '%')</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and t1.REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and t1.ASSIGN_TIME>=#{param.assignTimeStart}</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''"><![CDATA[and t1.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and t1.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''"><![CDATA[and t1.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and t1.RECEIVE_TIME>=#{param.receiveTimeStart}</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''"><![CDATA[and t1.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''"><![CDATA[and t1.STATUS_CODE in (${param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">
			and t1.STATUS_CODE in <foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''"> and t1.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''"> and t1.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''"> and t1.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''"> and t1.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''"> and t1.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''"> and t1.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''"> and t1.PROVINCE_CODE IN <foreach collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''"> and t1.CITY_CODE IN <foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''"> and t1.COUNTY_CODE IN <foreach collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''"> and t1.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and t1.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.planBuyDateName !=null and param.planBuyDateName !=''">and t1.COLUMN1=#{param.planBuyDateName}</if>
		GROUP BY t1.SERVER_ORDER
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="sacUserCluedlrbyqueryNoPageSpecifyFields" resultType="java.util.Map">
		SELECT
		t1.server_order,
		t1.cust_id,
		t1.cust_name,
		t1.phone,
		t1.dlr_code,
		t1.id,
		t1.created_name,
		t1.last_Updated_Date
		FROM
		adp_leads.t_sac_clue_info_dlr t1
		WHERE
		1=1
		<if test="param.billType != null and ''!= param.billType "> AND t2.bill_type = #{param.billType}</if>
		<if test="param.smartId != null and ''!= param.smartId "> AND t1.COLUMN10 = #{param.smartId}</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus "> AND t2.ASSIGN_STATUS = #{param.assignStatus}</if>
		<if test="param.planReviewTimeStart !=null and param.planReviewTimeStart !=''">and t2.PLAN_REVIEW_TIME>=#{param.planReviewTimeStart}</if>
		<if test="param.planReviewTimeEnd !=null and param.planReviewTimeEnd !=''"><![CDATA[and t2.PLAN_REVIEW_TIME<=#{param.planReviewTimeEnd}]]></if>

		<if test="param.detailId !=null and param.detailId !=''">and t3.DETAIL_ID=#{param.detailId}</if>
		<if test="param.userGroupId !=null and param.userGroupId !=''">and t3.USER_GROUP_ID=#{param.userGroupId}</if>

		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and t1.COLUMN6=#{param.businessHeatCode}</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and t1.COLUMN5=#{param.businessHeatName}</if>
		<if test="param.column5 !=null and param.column5 !=''">and t1.COLUMN5=#{param.column5}</if>
		<if test="param.id !=null and param.id !=''">and t1.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and t1.CUST_ID=#{param.custId}</if>
		<if test="param.column11 !=null and param.column11 !=''">and t1.COLUMN11=#{param.column11}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and t1.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and t1.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and t1.INTEN_BRAND_CODE=#{param.intenBrandCode}</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and t1.INTEN_BRAND_NAME=#{param.intenBrandName}</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and t1.INTEN_SERIES_CODE=#{param.intenSeriesCode}</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and t1.INTEN_SERIES_NAME=#{param.intenSeriesName}</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and t1.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and t1.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and t1.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and t1.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and t1.INNER_COLOR_CODE=#{param.innerColorCode}</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and t1.INNER_COLOR_NAME=#{param.innerColorName}</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and t1.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and t1.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and t1.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.dlrCodePaste !=null and param.dlrCodePaste !=''">and INSTR(t1.DLR_CODE,#{param.dlrCodePaste})>0</if>
		<if test="param.dlrShortNamePaste !=null and param.dlrShortNamePaste !=''">and INSTR(t1.DLR_SHORT_NAME,#{param.dlrShortNamePaste})>0</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and t1.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and t1.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and t1.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and t1.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and t1.INFO_CHAN_M_CODE=#{param.infoChanMCode}</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and t1.INFO_CHAN_M_NAME=#{param.infoChanMName}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and t1.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach></if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and t1.INFO_CHAN_D_NAME=#{param.infoChanDName}</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and t1.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and t1.INFO_CHAN_DD_NAME=#{param.infoChanDdName}</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and t1.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and t1.CHANNEL_NAME=#{param.channelName}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and t1.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and t1.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and t1.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and t1.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and t1.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and t1.FIRST_REVIEW_TIME=#{param.firstReviewTime}</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and t1.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>


		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and t1.PV_SERVER_ORDER like concat('%',#{param.pvServerOrder},'%')</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''"> and t1.SERVER_ORDER like concat('%',#{param.serverOrder},'%')</if>
		<if test="param.serverOrderList !=null"> and t1.SERVER_ORDER IN
			<foreach collection="param.serverOrderList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>
		<if test="param.phone !=null and param.phone !=''"> and t1.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and t1.PHONE_BACKUP like concat('%',#{param.phoneBackup},'%') </if>
		<if test="param.custName!=null and param.custName !=''"> and t1.CUST_NAME like concat('%',#{param.custName},'%')</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and t1.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and t1.CREATED_DATE<=#{param.createdDateEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and t1.REVIEW_PERSON_NAME like concat('%', #{param.reviewPersonName}, '%')</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and t1.REVIEW_PERSON_ID = #{param.reviewPersonId}</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and t1.ASSIGN_TIME>=#{param.assignTimeStart}</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''"><![CDATA[and t1.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and t1.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''"><![CDATA[and t1.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and t1.RECEIVE_TIME>=#{param.receiveTimeStart}</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''"><![CDATA[and t1.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''"><![CDATA[and t1.STATUS_CODE in (${param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">
			and t1.STATUS_CODE in <foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''"> and t1.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''"> and t1.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''"> and t1.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''"> and t1.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''"> and t1.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''"> and t1.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''"> and t1.PROVINCE_CODE IN <foreach collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''"> and t1.CITY_CODE IN <foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''"> and t1.COUNTY_CODE IN <foreach collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t1.PHONE,#{param.searchCondition})>0 or INSTR(t1.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''"> and t1.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and t1.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.planBuyDateName !=null and param.planBuyDateName !=''">and t1.COLUMN1=#{param.planBuyDateName}</if>
		GROUP BY t1.SERVER_ORDER
	</select>

	<select id="queryListByDlr" resultType="java.util.Map">
		select
		r.REVIEW_ID as reviewId,
		r.ORG_CODE as orgCode,
		r.ORG_NAME as orgName,
		r.BILL_TYPE as billType,
		r.BILL_TYPE_NAME as billTypeName,
		r.BUSINESS_TYPE as businessType,
		r.BUSINESS_TYPE_NAME as businessTypeName,
		r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
		r.BILL_CODE as billCode,
		date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
		date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
		date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
		r.IS_COME,
		r.REVIEW_TIME as reviewTime,
		r.LAST_REVIEW_TIME as lastReviewTime,
		r.OVER_REVIEW_TIME as overReviewTime,
		r.ASSIGN_STATUS as assignStatus,
		r.ASSIGN_STATUS_NAME as assignStatusName,
		r.ASSIGN_TIME as assignTime,
		r.ASSIGN_PERSON_ID as assignPersonId,
		r.ASSIGN_PERSON_NAME as assignPersonName,
		r.REVIEW_PERSON_ID as reviewPersonId,
		r.REVIEW_PERSON_NAME as reviewPersonName,
		r.REVIEW_DESC as reviewDesc,
		r.REVIEW_STATUS as reviewStatus,
		r.REVIEW_STATUS_NAME as reviewStatusName,
		r.CUST_ID as custId,
		r.CUST_NAME as custName,
		r.PHONE as phone,
		r.GENDER as gender,
		r.GENDER_NAME as genderName,
		r.TOUCH_STATUS as touchStatus,
		r.TOUCH_STATUS_NAME as touchStatusName,
		r.NODE_CODE as nodeCode,
		r.NODE_NAME as nodeName,
		r.SEND_DLR_CODE as sendDlrCode,
		r.SEND_DLR_SHORT_NAME as sendDlrShortName,
		r.SEND_TIME,
		r.INTEN_LEVEL_CODE,
		r.INTEN_LEVEL_NAME,
		r.INTEN_BRAND_CODE,
		r.INTEN_BRAND_NAME,
		r.INTEN_SERIES_CODE,
		r.INTEN_SERIES_NAME,
		r.INTEN_CAR_TYPE_CODE,
		r.INTEN_CAR_TYPE_NAME,
		r.EXTENDS_JSON as extendsJson,
		r.CREATED_DATE as createdDate,
		r.UPDATE_CONTROL_ID as updateControlId,
		r.CREATED_NAME as createdName,
		r.LAST_UPDATED_DATE as lastUpdatedDate,
		case when r.send_time is not null then '是' else '否' end as isPvSend,
		case when ifnull(r.OVER_REVIEW_TIME,now()) &lt; now() then '1' else '0'
		end as isOverTime,
        <![CDATA[
        CASE WHEN r.OVER_REVIEW_TIME<=NOW() THEN 
		CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,r.OVER_REVIEW_TIME,NOW())) ,'小时'),
		CONCAT(cast(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()))-
		(floor(TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) / 1440) *1440)-
		(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) % 1440)/60)*60)) as char),'分钟' ))	 ELSE '' end beyondTimes]]>,
		<![CDATA[	
		case when TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) < 0 then 0
		else TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) end beyondDay]]>
		from t_sac_review r
		where 1=1
		<if test="param.reviewStatusIn !=null and param.reviewStatusIn !=''">
			and r.REVIEW_STATUS IN
			<foreach collection="param.reviewStatusIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.orgCode !=null and param.orgCode != ''" > AND r.org_code = #{param.orgCode} </if>
	    <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.org_code IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
			AND r.column7 = #{param.businessHeatCode}
		</if>
		<if test="param.beyondDay != null and ''!= param.beyondDay">
    		<![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
		</if>
		<if test="param.assignStartTime != null and ''!= param.assignStartTime">
    		<![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
		</if>
		<if test="param.assignEndTime != null and ''!= param.assignEndTime">
    		<![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.planStartTime != null and ''!= param.planStartTime">
    		<![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
		</if>
		<if test="param.planEndTime != null and ''!= param.planEndTime">
     		<![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
		</if> 
		<if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
      		<![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
		</if>
		<if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
      		<![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
      		<![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
		</if>
		<if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
      		<![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.lastStartTime != null and ''!= param.lastStartTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
		</if>
		<if test="param.lastEndTime != null and ''!= param.lastEndTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.sendStartTime != null and ''!= param.sendStartTime">
			<![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
		</if>
		<if test="param.sendEndTime != null and ''!= param.sendEndTime">
			<![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.createdStartTime != null and ''!= param.createdStartTime">
			<![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.billType != null and ''!= param.billType ">
			AND r.bill_type = #{param.billType}
		</if>
		<if test="param.businessType != null and ''!= param.businessType ">
			AND r.business_type = #{param.businessType}
		</if>
		<if
			test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
			AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
			AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
		</if>
		<if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
			AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
		</if>
		<if test="param.channelCode != null and ''!= param.channelCode ">
			AND r.CHANNEL_CODE = #{param.channelCode}
		</if>
		<if test="param.custName != null and ''!= param.custName ">
        <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
		</if>
		<if test="param.phone != null and ''!= param.phone ">
    		<![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
			AND r.review_person_name = #{param.reviewPersonName}
		</if>
		<if test="param.billCode != null and ''!= param.billCode ">
			AND r.bill_code = #{param.billCode}
		</if>
		<if test="param.isCome != null and ''!= param.isCome">
			AND r.is_come = #{param.isCome}
		</if>
		<if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
			AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
		</if>
		<if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
			AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
		</if>
		<if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
			AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
		</if>
		<if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
			AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
		</if>
		<if test='param.isPvSend != null and "1"== param.isPvSend'>
			AND r.SEND_TIME IS NOT NULL
		</if>
		<if test='param.isPvSend != null and "0"== param.isPvSend'>
			AND ifnull(r.SEND_TIME,'')=''
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
    		<![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
		</if>
		<if test="param.reviewStatus != null and ''!= param.reviewStatus ">
			AND r.REVIEW_STATUS = #{param.reviewStatus}
		</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus ">
			AND r.ASSIGN_STATUS = #{param.assignStatus}
		</if>
		<if test='param.isOverTime != null and "1"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
		</if>
		<if test='param.isOverTime != null and "0"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
		</if>
		<if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.ORG_CODE IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
	</select>

	<select id="queryListByDlrManual" resultType="java.util.Map">
		select
		r.REVIEW_ID as reviewId,
		r.ORG_CODE as orgCode,
		r.ORG_NAME as orgName,
		r.BILL_TYPE as billType,
		r.BILL_TYPE_NAME as billTypeName,
		r.BUSINESS_TYPE as businessType,
		r.BUSINESS_TYPE_NAME as businessTypeName,
		r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.INFO_CHAN_DD_CODE,
		r.INFO_CHAN_DD_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
		r.BILL_CODE as billCode,
		date_format(r.PLAN_REVIEW_TIME,'%Y-%m-%d %H:%i:%s') AS planReviewTime,
		date_format(r.PLAN_COME_TIME,'%Y-%m-%d %H:%i:%s') AS planComeTime,
		date_format(r.FACT_COME_TIME,'%Y-%m-%d %H:%i:%s') AS factComeTime,
		r.IS_COME,
		r.REVIEW_TIME as reviewTime,
		r.LAST_REVIEW_TIME as lastReviewTime,
		r.OVER_REVIEW_TIME as overReviewTime,
		r.ASSIGN_STATUS as assignStatus,
		r.ASSIGN_STATUS_NAME as assignStatusName,
		r.ASSIGN_TIME as assignTime,
		r.ASSIGN_PERSON_ID as assignPersonId,
		r.ASSIGN_PERSON_NAME as assignPersonName,
		r.REVIEW_PERSON_ID as reviewPersonId,
		r.REVIEW_PERSON_NAME as reviewPersonName,
		r.REVIEW_DESC as reviewDesc,
		r.REVIEW_STATUS as reviewStatus,
		r.REVIEW_STATUS_NAME as reviewStatusName,
		r.CUST_ID as custId,
		r.CUST_NAME as custName,
		r.PHONE as phone,
		r.GENDER as gender,
		r.GENDER_NAME as genderName,
		r.TOUCH_STATUS as touchStatus,
		r.TOUCH_STATUS_NAME as touchStatusName,
		r.NODE_CODE as nodeCode,
		r.NODE_NAME as nodeName,
		r.SEND_DLR_CODE as sendDlrCode,
		r.SEND_DLR_SHORT_NAME as sendDlrShortName,
		r.SEND_TIME,
		r.INTEN_LEVEL_CODE,
		r.INTEN_LEVEL_NAME,
		r.INTEN_BRAND_CODE,
		r.INTEN_BRAND_NAME,
		r.INTEN_SERIES_CODE,
		r.INTEN_SERIES_NAME,
		r.INTEN_CAR_TYPE_CODE,
		r.INTEN_CAR_TYPE_NAME,
		r.EXTENDS_JSON as extendsJson,
		r.CREATED_DATE as createdDate,
		r.UPDATE_CONTROL_ID as updateControlId,
		r.CREATED_NAME as createdName,
		r.LAST_UPDATED_DATE as lastUpdatedDate,
		case when r.send_time is not null then '是' else '否' end as isPvSend,
		case when ifnull(r.OVER_REVIEW_TIME,now()) &lt; now() then '1' else '0'
		end as isOverTime,
        <![CDATA[
        CASE WHEN r.OVER_REVIEW_TIME<=NOW() THEN
		CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,r.OVER_REVIEW_TIME,NOW())) ,'小时'),
		CONCAT(cast(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()))-
		(floor(TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) / 1440) *1440)-
		(floor((TIMESTAMPDIFF(minute,r.OVER_REVIEW_TIME,NOW()) % 1440)/60)*60)) as char),'分钟' ))	 ELSE '' end beyondTimes]]>,
		<![CDATA[
		case when TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) < 0 then 0
		else TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) end beyondDay]]>
		from t_sac_review r
		where 1=1
		<if test="param.reviewStatusIn !=null and param.reviewStatusIn !=''">
			and r.REVIEW_STATUS IN
			<foreach collection="param.reviewStatusIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.orgCode !=null and param.orgCode != ''" > AND r.org_code = #{param.orgCode} </if>
	    <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.org_code IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
			AND r.column7 = #{param.businessHeatCode}
		</if>
		<if test="param.beyondDay != null and ''!= param.beyondDay">
    		<![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
		</if>
		<if test="param.assignStartTime != null and ''!= param.assignStartTime">
    		<![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
		</if>
		<if test="param.assignEndTime != null and ''!= param.assignEndTime">
    		<![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.planStartTime != null and ''!= param.planStartTime">
    		<![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
		</if>
		<if test="param.planEndTime != null and ''!= param.planEndTime">
     		<![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
      		<![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
		</if>
		<if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
      		<![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
      		<![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
		</if>
		<if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
      		<![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.lastStartTime != null and ''!= param.lastStartTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
		</if>
		<if test="param.lastEndTime != null and ''!= param.lastEndTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.sendStartTime != null and ''!= param.sendStartTime">
			<![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
		</if>
		<if test="param.sendEndTime != null and ''!= param.sendEndTime">
			<![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.createdStartTime != null and ''!= param.createdStartTime">
			<![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.billType != null and ''!= param.billType ">
			AND r.bill_type = #{param.billType}
		</if>
		<if test="param.businessType != null and ''!= param.businessType ">
			AND r.business_type = #{param.businessType}
		</if>
		<if
			test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
			AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
			AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
		</if>
		<if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
			AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
		</if>
		<if test="param.channelCode != null and ''!= param.channelCode ">
			AND r.CHANNEL_CODE = #{param.channelCode}
		</if>
		<if test="param.custName != null and ''!= param.custName ">
        <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
		</if>
		<if test="param.phone != null and ''!= param.phone ">
    		<![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
			AND r.review_person_name = #{param.reviewPersonName}
		</if>
		<if test="param.billCode != null and ''!= param.billCode ">
			AND r.bill_code = #{param.billCode}
		</if>
		<if test="param.isCome != null and ''!= param.isCome">
			AND r.is_come = #{param.isCome}
		</if>
		<if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
			AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
		</if>
		<if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
			AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
		</if>
		<if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
			AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
		</if>
		<if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
			AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
		</if>
		<if test='param.isPvSend != null and "1"== param.isPvSend'>
			AND r.SEND_TIME IS NOT NULL
		</if>
		<if test='param.isPvSend != null and "0"== param.isPvSend'>
			AND ifnull(r.SEND_TIME,'')=''
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
    		<![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
		</if>
		<if test="param.reviewStatus != null and ''!= param.reviewStatus ">
			AND r.REVIEW_STATUS = #{param.reviewStatus}
		</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus ">
			AND r.ASSIGN_STATUS = #{param.assignStatus}
		</if>
		<if test='param.isOverTime != null and "1"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
		</if>
		<if test='param.isOverTime != null and "0"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
		</if>
		<if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.ORG_CODE IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY r.PLAN_REVIEW_TIME,r.CREATED_DATE
		limit #{param.pageNo},#{param.pageSize}
	</select>

	<select id="queryListByDlrCount" resultType="java.lang.Integer">
		select
		count(*)
		from t_sac_review r
		where 1=1
		<if test="param.reviewStatusIn !=null and param.reviewStatusIn !=''">
			and r.REVIEW_STATUS IN
			<foreach collection="param.reviewStatusIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.orgCode !=null and param.orgCode != ''" > AND r.org_code = #{param.orgCode} </if>
	    <if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.org_code IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">
			AND r.column7 = #{param.businessHeatCode}
		</if>
		<if test="param.beyondDay != null and ''!= param.beyondDay">
    		<![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(r.OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]>
		</if>
		<if test="param.assignStartTime != null and ''!= param.assignStartTime">
    		<![CDATA[ AND r.ASSIGN_TIME >= #{param.assignStartTime} ]]>
		</if>
		<if test="param.assignEndTime != null and ''!= param.assignEndTime">
    		<![CDATA[ AND r.ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.planStartTime != null and ''!= param.planStartTime">
    		<![CDATA[ AND r.PLAN_REVIEW_TIME >= #{param.planStartTime} ]]>
		</if>
		<if test="param.planEndTime != null and ''!= param.planEndTime">
     		<![CDATA[ AND r.PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.planComeStartTime != null and ''!= param.planComeStartTime">
      		<![CDATA[ AND r.PLAN_COME_TIME >= #{param.planComeStartTime} ]]>
		</if>
		<if test="param.planComeEndTime != null and ''!= param.planComeEndTime">
      		<![CDATA[ AND r.PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.factComeStartTime != null and ''!= param.factComeStartTime">
      		<![CDATA[ AND r.FACT_COME_TIME >= #{param.factComeStartTime} ]]>
		</if>
		<if test="param.factComeEndTime != null and ''!= param.factComeEndTime">
      		<![CDATA[ AND r.FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.lastStartTime != null and ''!= param.lastStartTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME >= #{param.lastStartTime} ]]>
		</if>
		<if test="param.lastEndTime != null and ''!= param.lastEndTime">
     		<![CDATA[ AND r.LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.sendStartTime != null and ''!= param.sendStartTime">
			<![CDATA[ AND r.SEND_TIME >= #{param.sendStartTime} ]]>
		</if>
		<if test="param.sendEndTime != null and ''!= param.sendEndTime">
			<![CDATA[ AND r.SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.createdStartTime != null and ''!= param.createdStartTime">
			<![CDATA[ AND r.CREATED_DATE >= #{param.createdStartTime} ]]>
		</if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime">
			<![CDATA[ AND r.CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]>
		</if>

		<if test="param.billType != null and ''!= param.billType ">
			AND r.bill_type = #{param.billType}
		</if>
		<if test="param.businessType != null and ''!= param.businessType ">
			AND r.business_type = #{param.businessType}
		</if>
		<if
			test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
			AND r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
			AND r.INFO_CHAN_D_CODE = #{param.infoChanDCode}
		</if>
		<if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">
			AND r.INFO_CHAN_DD_CODE = #{param.infoChanDdCode}
		</if>
		<if test="param.channelCode != null and ''!= param.channelCode ">
			AND r.CHANNEL_CODE = #{param.channelCode}
		</if>
		<if test="param.custName != null and ''!= param.custName ">
        <![CDATA[	AND instr(r.CUST_NAME,#{param.custName})>0 ]]>
		</if>
		<if test="param.phone != null and ''!= param.phone ">
    		<![CDATA[	AND instr(r.phone,#{param.phone})>0 ]]>
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
			AND r.review_person_name = #{param.reviewPersonName}
		</if>
		<if test="param.billCode != null and ''!= param.billCode ">
			AND r.bill_code = #{param.billCode}
		</if>
		<if test="param.isCome != null and ''!= param.isCome">
			AND r.is_come = #{param.isCome}
		</if>
		<if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
			AND r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
		</if>
		<if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">
			AND r.INTEN_BRAND_CODE = #{param.intenBrandCode}
		</if>
		<if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">
			AND r.INTEN_SERIES_CODE = #{param.intenSeriesCode}
		</if>
		<if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">
			AND r.INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}
		</if>
		<if test='param.isPvSend != null and "1"== param.isPvSend'>
			AND r.SEND_TIME IS NOT NULL
		</if>
		<if test='param.isPvSend != null and "0"== param.isPvSend'>
			AND ifnull(r.SEND_TIME,'')=''
		</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">
    		<![CDATA[	AND instr(r.review_person_name,#{param.reviewPersonName})>0 ]]>
		</if>
		<if test="param.reviewStatus != null and ''!= param.reviewStatus ">
			AND r.REVIEW_STATUS = #{param.reviewStatus}
		</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus ">
			AND r.ASSIGN_STATUS = #{param.assignStatus}
		</if>
		<if test='param.isOverTime != null and "1"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME<=now() ]]>
		</if>
		<if test='param.isOverTime != null and "0"== param.isOverTime'>
     		<![CDATA[ AND r.OVER_REVIEW_TIME>now() ]]>
		</if>
		<if test="param.orgCodeIn !=null and param.orgCodeIn !=''">
			and r.ORG_CODE IN
			<foreach collection="param.orgCodeIn.split(',')" item="item"
				separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<delete id="deleteSacReview">
		DELETE 
		FROM
			t_sac_review
		WHERE 1=1
		<!-- 描述条件 -->
		<if test="param.phone != null and param.phone !=''"> and PHONE = #{param.phone}</if>
		<if test="param.serverOrder != null and param.serverOrder !=''"> and BILL_CODE = #{param.serverOrder}</if>
	</delete>

	<select id="defeatCount" resultType="java.util.Map">
		select * from t_sac_clue_info_dlr where status_code='10' and phone = #{param.phone}
	</select>

	<select id="defeatCountSpecifyFields" resultType="java.util.Map">
		select CREATED_DATE,FIRST_REVIEW_TIME,COLUMN18,COLUMN20,INFO_CHAN_M_CODE,INFO_CHAN_M_NAME,INFO_CHAN_D_CODE,INFO_CHAN_D_NAME
		from t_sac_clue_info_dlr where status_code='10' and phone = #{param.phone}
	</select>

	<select id="queryUserClueInfo" resultType="java.util.Map">
		SELECT
			t.INTEN_LEVEL_CODE,/*线索等级（活跃度）*/
			(case t.INTEN_LEVEL_CODE
				 when 'L0' then '0'
				 when 'L1' then '1'
				 when 'L2' then '2'
				 when 'L3' then '3'
				 else '4' end) as activation,
			t.COLUMN6 businessHeatCode,/*热度*/
			ifnull(JSON_UNQUOTE(JSON_EXTRACT(t1.EXTEND_JSON,'$.buyBudget')),'0') buyBudget,/*购车预算*/
			(CASE JSON_UNQUOTE(JSON_EXTRACT(t1.EXTEND_JSON,'$.buyBudget'))
				 WHEN '1' THEN '15~20'
				 WHEN '2' THEN '20~25'
				 WHEN '3' THEN '25~30'
				 WHEN '4' THEN '30~'
				 ELSE '~15' END) purchasePower,
			t1.SMART_ID,
			t1.EXTEND_JSON,
			t.PHONE
		FROM
			t_sac_clue_info_dlr  t
				left join t_sac_onecust_info t1 on t1.CUST_ID=t.CUST_ID
		WHERE
			t.CUST_ID = #{param.custId}
	</select>
	<select id="queryTestDrive" resultType="java.lang.Integer">
		SELECT
			sum( t.num ) driveNum
		FROM
			(
				SELECT
					count( 1 ) AS num
				FROM
					t_sac_test_drive_sheet dr
						INNER JOIN t_sac_clue_info_dlr cl ON cl.PHONE = dr.CUSTOMER_PHONE
				WHERE
					cl.CUST_ID = #{param.custId}
				UNION ALL
				SELECT
					count( 1 ) AS num
				FROM
					t_sac_test_drive_sheet_his dr
						INNER JOIN t_sac_clue_info_dlr cl ON cl.PHONE = dr.CUSTOMER_PHONE
				WHERE
					cl.CUST_ID = #{param.custId}
			) t
	</select>
	<select id="queryTnviteUserNum" resultType="java.lang.Integer">
		SELECT
			count( 1 )
		FROM
			orc.t_orc_ve_bu_sale_order_to_c
		WHERE
			BUY_CUST_NAME != ORDER_USER_NAME
	AND BUY_CUST_ID = #{param.custId}
	</select>
	<select id="queryOrderNum" resultType="java.lang.Integer">
		SELECT
			count( 1 )
		FROM
			orc.t_orc_ve_bu_sale_order_to_c
		WHERE
			BUY_CUST_ID = #{param.custId}
	</select>

	<select id="findData" resultType="com.ly.adp.csc.otherservice.entities.LookupValue">
    </select>

	<!-- clue 更新 -->
	<update id="updateCscClue">
		UPDATE
		t_sac_clue_info_dlr
		SET
		<if test="param.Cphone != null and param.Cphone !=''">PHONE = #{param.Cphone},</if>
		<if test="param.planBuyDate != null">column2 = #{param.planBuyDate},</if>
		<if test="param.planBuyDateName != null">column1 = #{param.planBuyDateName},</if>
		<if test="param.intenLevelCode != null">INTEN_LEVEL_CODE = #{param.intenLevelCode},</if>
		<if test="param.intenLevelName != null">INTEN_LEVEL_NAME = #{param.intenLevelName},</if>
		<if test="param.intenCarTypeCode != null">INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode},</if>
		<if test="param.intenCarTypeName != null">INTEN_CAR_TYPE_NAME = #{param.intenCarTypeName},</if>
		<if test="param.outColorCode != null">OUT_COLOR_CODE = #{param.outColorCode},</if>
		<if test="param.outColorName != null">OUT_COLOR_NAME = #{param.outColorName},</if>

		<if test="param.firstTime != null and param.firstTime !=''">FIRST_REVIEW_TIME=if(FIRST_REVIEW_TIME is null,#{param.firstTime},FIRST_REVIEW_TIME),</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
		<if test="param.dlrShortName != null and param.dlrShortName !=''">DLR_SHORT_NAME = #{param.dlrShortName},</if>
		<if test="param.provinceCode != null and param.provinceCode !=''">PROVINCE_CODE = #{param.provinceCode},</if>
		<if test="param.provinceName != null and param.provinceName !=''">PROVINCE_NAME = #{param.provinceName},</if>
		<if test="param.cityCode != null and param.cityCode !=''">CITY_CODE = #{param.cityCode},</if>
		<if test="param.cityName != null and param.cityName !=''">CITY_NAME = #{param.cityName},</if>
		<if test="param.countyCode != null and param.countyCode !=''">COUNTY_CODE = #{param.countyCode},</if>
		<if test="param.countyName != null and param.countyName !=''">COUNTY_NAME = #{param.countyName},</if>
		<if test="param.smartId != null and param.smartId !=''">COLUMN10 = #{param.smartId},</if>

		<if test="param.lastReviewTime != null">LAST_REVIEW_TIME = #{param.lastReviewTime},</if>
		<if test="param.custName != null and param.custName !=''">CUST_NAME = #{param.custName},</if>
		<if test="param.genderCode != null and param.genderCode !=''">GENDER_CODE = #{param.genderCode},GENDER_NAME = #{param.genderName},</if>
		<if test="param.column11 != null and param.column11 !=''">COLUMN11 = #{param.column11},</if>
		<if test="param.businessHeatCode != null and param.businessHeatCode !=''">COLUMN5 = #{param.businessHeatCode},</if>
		<if test="param.businessHeatName != null and param.businessHeatName !=''">COLUMN6 = #{param.businessHeatName},</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">DLR_CODE = #{param.dlrCode},</if>
		<if test="param.dlrName != null and param.dlrName !=''">DLR_SHORT_NAME = #{param.dlrName},</if>
		<if test="param.statusCode != null and param.statusCode !=''">STATUS_CODE = #{param.statusCode},</if>
		<if test="param.statusName != null and param.statusName !=''">STATUS_NAME = #{param.statusName},</if>
		MODIFIER=#{param.modifier},
		LAST_UPDATED_DATE=now(),
		MODIFY_NAME = #{param.modifyName}
		WHERE 1=1
		<if test="param.phone != null and param.phone !=''"> and PHONE = #{param.phone}</if>
		<if test="param.serverOrder != null and param.serverOrder !=''"> and SERVER_ORDER = #{param.serverOrder}</if>
	</update>

	<delete id="deleteNewClue">
		DELETE FROM adp_leads.t_sac_clue_info_dlr
		WHERE 1=1
		<if test="param.phone != null and param.phone !=''"> and PHONE = #{param.phone}</if>
		<if test="param.serverOrder != null and param.serverOrder !=''"> and SERVER_ORDER = #{param.serverOrder}</if>
	</delete>
</mapper>