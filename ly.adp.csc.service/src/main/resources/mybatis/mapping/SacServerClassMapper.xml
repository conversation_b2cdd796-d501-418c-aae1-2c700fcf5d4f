<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacServerClassMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacServerClass">
        <id column="SERVER_CLASS_ID" property="serverClassId" />
        <result column="SERVER_CLASS_CODE" property="serverClassCode" />
        <result column="SERVER_CLASS_NAME" property="serverClassName" />
        <result column="SERVER_TYPE_CODE" property="serverTypeCode" />
        <result column="SERVER_TYPE_NAME" property="serverTypeName" />
        <result column="CLASS_LEVEL_CODE" property="classLevelCode" />
        <result column="CLASS_LEVEL_NAME" property="classLevelName" />
        <result column="PARENT_CODE" property="parentCode" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SERVER_CLASS_ID, SERVER_CLASS_CODE, SERVER_CLASS_NAME, SERVER_TYPE_CODE, SERVER_TYPE_NAME, CLASS_LEVEL_CODE, CLASS_LEVEL_NAME, PARENT_CODE, ORDER_NO, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.serverClassId !=null and param.serverClassId !=''">and SERVER_CLASS_ID=#{param.serverClassId}</if>
    	<if test="param.serverClassCode !=null and param.serverClassCode !=''">and SERVER_CLASS_CODE=#{param.serverClassCode}</if>
    	<if test="param.serverClassName !=null and param.serverClassName !=''">and SERVER_CLASS_NAME=#{param.serverClassName}</if>
    	<if test="param.serverTypeCode !=null and param.serverTypeCode !=''">and SERVER_TYPE_CODE=#{param.serverTypeCode}</if>
    	<if test="param.serverTypeName !=null and param.serverTypeName !=''">and SERVER_TYPE_NAME=#{param.serverTypeName}</if>
    	<if test="param.classLevelCode !=null and param.classLevelCode !=''">and CLASS_LEVEL_CODE=#{param.classLevelCode}</if>
    	<if test="param.classLevelName !=null and param.classLevelName !=''">and CLASS_LEVEL_NAME=#{param.classLevelName}</if>
    	<if test="param.parentCode !=null and param.parentCode !=''">and PARENT_CODE=#{param.parentCode}</if>
    	<if test="param.orderNo !=null and param.orderNo !=''">and ORDER_NO=#{param.orderNo}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.column6 !=null and param.column6 !=''">and COLUMN6=#{param.column6}</if>
    	<if test="param.column7 !=null and param.column7 !=''">and COLUMN7=#{param.column7}</if>
    	<if test="param.column8 !=null and param.column8 !=''">and COLUMN8=#{param.column8}</if>
    	<if test="param.column9 !=null and param.column9 !=''">and COLUMN9=#{param.column9}</if>
    	<if test="param.column10 !=null and param.column10 !=''">and COLUMN10=#{param.column10}</if>
    	<!-- 模糊查询 -->
    	<if test="param.serverClassName !=null and param.serverClassName !=''">and INSTR(SERVER_CLASS_NAME,#{param.serverClassName})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.createdDateStart !=null and param.createdDateStart !=''">and CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]></if>
   	</sql>
 	
 	<!-- 服务类别 信息查询 -->
	<select id="querySacServerClass" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_server_class
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<!-- 服务类别 信息删除（物理删除） -->
	<delete id="deleteSacServerClass">
		DELETE 
		FROM
			t_sac_server_class
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 服务类别 信息新增 -->
	<insert id="createSacServerClass">
		insert into t_sac_server_class(<include refid="Base_Column_List"></include>)
		value(
        	#{param.serverClassId},
			#{param.serverClassCode},
			#{param.serverClassName},
			#{param.serverTypeCode},
			#{param.serverTypeName},
			#{param.classLevelCode},
			#{param.classLevelName},
			#{param.parentCode},
			#{param.orderNo},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
		)
	</insert>
	
	<!-- 服务类别 信息更新 -->
	<update id="updateSacServerClass">
		update t_sac_server_class  set 
			<!-- 更新列表 -->
			<if test="param.serverClassCode !=null and param.serverClassCode !=''">SERVER_CLASS_CODE=#{param.serverClassCode},</if>
			<if test="param.serverClassName !=null and param.serverClassName !=''">SERVER_CLASS_NAME=#{param.serverClassName},</if>
			<if test="param.serverTypeCode !=null and param.serverTypeCode !=''">SERVER_TYPE_CODE=#{param.serverTypeCode},</if>
			<if test="param.serverTypeName !=null and param.serverTypeName !=''">SERVER_TYPE_NAME=#{param.serverTypeName},</if>
			<if test="param.classLevelCode !=null and param.classLevelCode !=''">CLASS_LEVEL_CODE=#{param.classLevelCode},</if>
			<if test="param.classLevelName !=null and param.classLevelName !=''">CLASS_LEVEL_NAME=#{param.classLevelName},</if>
			<if test="param.parentCode !=null and param.parentCode !=''">PARENT_CODE=#{param.parentCode},</if>
			<if test="param.orderNo !=null and param.orderNo !=''">ORDER_NO=#{param.orderNo},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">UPDATE_CONTROL_ID=#{param.updateControlId},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.column6 !=null and param.column6 !=''">COLUMN6=#{param.column6},</if>
			<if test="param.column7 !=null and param.column7 !=''">COLUMN7=#{param.column7},</if>
			<if test="param.column8 !=null and param.column8 !=''">COLUMN8=#{param.column8},</if>
			<if test="param.column9 !=null and param.column9 !=''">COLUMN9=#{param.column9},</if>
			<if test="param.column10 !=null and param.column10 !=''">COLUMN10=#{param.column10}</if>
			<!-- 结束无逗号 -->
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.serverClassId !=null and param.serverClassId !=''">and SERVER_CLASS_ID=#{param.serverClassId}</if>
	</update>
</mapper>
