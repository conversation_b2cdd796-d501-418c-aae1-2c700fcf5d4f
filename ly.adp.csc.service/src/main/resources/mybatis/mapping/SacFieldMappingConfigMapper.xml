<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacFieldMappingConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacFieldMappingConfig">
        <id column="ID" property="id" />
        <result column="SOURCE_TABLE_CODE" property="sourceTableCode" />
        <result column="BILL_TYPE" property="billType" />
        <result column="BILL_TYPE_NAME" property="billTypeName" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="BUSINESS_TYPE_NAME" property="businessTypeName" />
        <result column="FILED_TYPE" property="filedType" />
        <result column="FILED_CODE" property="filedCode" />
        <result column="DB_FIELD_CODE" property="dbFieldCode" />
        <result column="FILED_NAME" property="filedName" />
        <result column="MAPPING_FILED_CODE" property="mappingFiledCode" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SOURCE_TABLE_CODE, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, FILED_TYPE,DB_FIELD_CODE, FILED_CODE, FILED_NAME, MAPPING_FILED_CODE, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

 	<insert id="insertSacFieldMappingConfig">
        insert into t_sac_field_mapping_config(
         ID
        ,SOURCE_TABLE_CODE
        ,BILL_TYPE
        ,BILL_TYPE_NAME
        ,BUSINESS_TYPE
        ,BUSINESS_TYPE_NAME
        ,FILED_TYPE
        ,DB_FIELD_CODE
        ,FILED_CODE
        ,FILED_NAME
        ,MAPPING_FILED_CODE
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{id}
        ,#{sourceTableCode}
        ,#{billType}
        ,#{billTypeName}
        ,#{businessType}
        ,#{businessTypeName}
        ,#{filedType}
        ,#{dbFieldCode}
        ,#{filedCode}
        ,#{filedName}
        ,#{mappingFiledCode}
        ,#{oemId}
        ,#{groupId}
        ,#{creator}
        ,#{createdName}
        ,#{createdDate}
        ,#{modifier}
        ,#{modifyName}
        ,#{lastUpdatedDate}
        ,#{isEnable}
        ,#{updateControlId}
		)
    </insert>
    
    <update id="updateSacFieldMappingConfig">
    	update t_sac_field_mapping_config  set 
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.sourceTableCode!=null'> ,SOURCE_TABLE_CODE = #{param.sourceTableCode}</if>
	    <if test = 'param.billType!=null'> ,BILL_TYPE = #{param.billType}</if>
	    <if test = 'param.billTypeName!=null'> ,BILL_TYPE_NAME = #{param.billTypeName}</if>
	    <if test = 'param.businessType!=null'> ,BUSINESS_TYPE = #{param.businessType}</if>
	    <if test = 'param.businessTypeName!=null'> ,BUSINESS_TYPE_NAME = #{param.businessTypeName}</if>
	    <if test = 'param.filedType!=null'> ,FILED_TYPE = #{param.filedType}</if>
	    <if test = 'param.dbFieldCode!=null'> ,DB_FIELD_CODE = #{param.dbFieldCode}</if>
	    <if test = 'param.filedCode!=null'> ,FILED_CODE = #{param.filedCode}</if>
	    <if test = 'param.filedName!=null'> ,FILED_NAME = #{param.filedName}</if>
	    <if test = 'param.mappingFiledCode!=null'> ,MAPPING_FILED_CODE = #{param.mappingFiledCode}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1 
         and ID = #{param.id}
	    <if test = 'param.updateControlId!=null'>
         and UPDATE_CONTROL_ID = #{param.updateControlId}
         </if>
    	
    </update>

	 <!-- 查询字段映射关系 -->
    <select id="fileMappingList" resultType="java.util.Map">
    	SELECT c.FILED_TYPE,c.FILED_CODE, c.MAPPING_FILED_CODE,c.DB_FIELD_CODE
		FROM t_sac_field_mapping_config c
		WHERE 1=1
		AND c.SOURCE_TABLE_CODE=#{param.sourceTableCode}
		AND c.BILL_TYPE=#{param.billType}
		AND c.BUSINESS_TYPE=#{param.businessType}
		AND c.MAPPING_FILED_CODE IN
		<foreach item="item" index="index" collection="param.columnString.split(',')" open="(" separator="," close=")">  
     		 #{item}  
     	</foreach>
    </select>
</mapper>
