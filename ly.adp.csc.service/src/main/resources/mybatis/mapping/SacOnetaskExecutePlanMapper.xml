<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnetaskExecutePlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnetaskExecutePlan">
        <id column="TASK_PLAN_ID" property="taskPlanId" />
        <result column="TASK_ID" property="taskId" />
        <result column="CYCLE_UNIT_CODE" property="cycleUnitCode" />
        <result column="CYCLE_UNIT_NAME" property="cycleUnitName" />
        <result column="EXECUTE_TIME" property="executeTime" />
        <result column="NEXT_PLAN_TIME" property="nextPlanTime" />
        <result column="FINSIH_TIMES" property="finsihTimes" />
        <result column="DEADLINE" property="deadline" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TASK_PLAN_ID, TASK_ID, CYCLE_UNIT_CODE, CYCLE_UNIT_NAME, EXECUTE_TIME, NEXT_PLAN_TIME, FINSIH_TIMES, DEADLINE, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.taskPlanId !=null and param.taskPlanId !=''">and TASK_PLAN_ID=#{param.taskPlanId}</if>
    	<if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
    	<if test="param.cycleUnitCode !=null and param.cycleUnitCode !=''">and CYCLE_UNIT_CODE=#{param.cycleUnitCode}</if>
    	<if test="param.cycleUnitName !=null and param.cycleUnitName !=''">and CYCLE_UNIT_NAME=#{param.cycleUnitName}</if>
    	<if test="param.executeTime !=null and param.executeTime !=''">and EXECUTE_TIME=#{param.executeTime}</if>
    	<if test="param.nextPlanTime !=null and param.nextPlanTime !=''">and NEXT_PLAN_TIME=#{param.nextPlanTime}</if>
    	<if test="param.finsihTimes !=null and param.finsihTimes !=''">and FINSIH_TIMES=#{param.finsihTimes}</if>
    	<if test="param.deadline !=null and param.deadline !=''">and DEADLINE=#{param.deadline}</if>
    	<if test="param.extendJson !=null and param.extendJson !=''">and EXTEND_JSON=#{param.extendJson}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 任务执行周期表 信息查询 -->
	<select id="querySacOnetaskExecutePlan" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_onetask_execute_plan
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<select id="sacOnetaskExecutePlanQueryByTime" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_onetask_execute_plan
	    where 1=1
	    	<if test="param.nextPlanTimeStart != null and param.nextPlanTimeStart !=''">
	    		AND date_format(NEXT_PLAN_TIME,'%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.nextPlanTimeStart}
	    	</if>
	    	<if test="param.nextPlanTimeEnd != null and param.nextPlanTimeEnd !=''">
	    		AND date_format(NEXT_PLAN_TIME,'%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.nextPlanTimeEnd}
	    	</if>
	    	<if test="param.deadlineStart !=null and param.deadlineStart !=''">
	    		AND date_format(DEADLINE,'%Y-%m-%d %H:%i:%s') <![CDATA[ > ]]> #{param.deadlineStart}
	    	</if>
	    	<if test="param.deadlineEnd !=null and param.deadlineEnd !=''">
	    		AND date_format(DEADLINE,'%Y-%m-%d %H:%i:%s') <![CDATA[ < ]]> #{param.deadlineEnd}
	    	</if>
	    ORDER BY LAST_UPDATED_DATE
	</select>
	
	<!-- 任务执行周期表 信息删除（物理删除） -->
	<delete id="deleteSacOnetaskExecutePlan">
		DELETE 
		FROM
			t_sac_onetask_execute_plan
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 任务执行周期表 信息新增 -->
	<insert id="createSacOnetaskExecutePlan">
		insert into t_sac_onetask_execute_plan(<include refid="Base_Column_List"></include>)
		value(
        	#{param.taskPlanId},
			#{param.taskId},
			#{param.cycleUnitCode},
			#{param.cycleUnitName},
			#{param.executeTime},
			#{param.nextPlanTime},
			#{param.finsihTimes},
			#{param.deadline},
			#{param.extendJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 任务执行周期表 信息更新 -->
	<update id="updateSacOnetaskExecutePlan">
		update t_sac_onetask_execute_plan  set 
			<!-- 更新列表 -->
			<if test="param.taskId !=null and param.taskId !=''">TASK_ID=#{param.taskId},</if>
			<if test="param.cycleUnitCode !=null and param.cycleUnitCode !=''">CYCLE_UNIT_CODE=#{param.cycleUnitCode},</if>
			<if test="param.cycleUnitName !=null and param.cycleUnitName !=''">CYCLE_UNIT_NAME=#{param.cycleUnitName},</if>
			<if test="param.executeTime !=null and param.executeTime.toString() !=''">EXECUTE_TIME=#{param.executeTime},</if>
			<if test="param.nextPlanTime !=null and param.nextPlanTime.toString() !=''">NEXT_PLAN_TIME=#{param.nextPlanTime},</if>
			<if test="param.finsihTimes !=null and param.finsihTimes !=''">FINSIH_TIMES=#{param.finsihTimes},</if>
			<if test="param.deadline !=null and param.deadline.toString() !=''">DEADLINE=#{param.deadline},</if>
			<if test="param.extendJson !=null and param.extendJson !=''">EXTEND_JSON=#{param.extendJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate.toString() !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.taskPlanId !=null and param.taskPlanId !=''">and TASK_PLAN_ID=#{param.taskPlanId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
</mapper>
