<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacComplaintsDealRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacComplaintsDealRecord">
        <id column="DEAL_ID" property="dealId" />
        <result column="COMPLAINTS_ID" property="complaintsId" />
        <result column="COMPLAINTS_NO" property="complaintsNo" />
        <result column="CUST_ID" property="custId" />
        <result column="PHONE" property="phone" />
        <result column="DEAL_TYPE" property="dealType" />
        <result column="DEAL_CONTENT" property="dealContent" />
        <result column="AUDIT_CONTENT" property="auditContent" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="FILE_SRC" property="fileSrc" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DEAL_ID, COMPLAINTS_ID, CUST_ID, PHONE, COMPLAINTS_NO, DEAL_TYPE, DEAL_CONTENT, AUDIT_CONTENT, DLR_CODE, DLR_NAME, FILE_SRC, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
 	<!-- 投诉工单处理记录查询 -->
 	<select id="sacComplaintsDealRecordFindByPage" resultType="map">
 		SELECT * FROM t_sac_complaints_deal_record T
 		WHERE T.IS_ENABLE = '1'
		<if test="param.dealId !=null and param.dealId !=''">
			AND T.DEAL_ID = #{param.dealId}
		</if>
		<if test="param.custId !=null and param.custId !=''">
			AND T.CUST_ID = #{param.custId}
		</if>
		<if test="param.phone !=null and param.phone !=''">
			AND T.PHONE = #{param.phone}
		</if>
		<if test="param.complaintsId !=null and param.complaintsId !=''">
			AND T.COMPLAINTS_ID = #{param.complaintsId}
		</if>
		<if test="param.complaintsNo !=null and param.complaintsNo !=''">
			AND INSTR(T.COMPLAINTS_NO, #{param.complaintsNo}) <![CDATA[ > ]]> 0
		</if>
		<if test="param.dealType !=null and param.dealType !=''">
			AND T.DEAL_TYPE = #{param.dealType}
		</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			AND T.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.dlrName !=null and param.dlrName !=''">
			AND INSTR(T.DLR_NAME, #{param.dlrName}) <![CDATA[ > ]]> 0
		</if>
		<if test="param.dealTypeCode !=null and param.dealTypeCode !=''">
			AND T.DEAL_TYPE_CODE = #{param.dealTypeCode}
		</if>
		ORDER BY T.LAST_UPDATED_DATE DESC
 	</select>
 	
	<!-- 投诉工单处理记录新增 -->
	<insert id="insertSacComplaintsDealRecord" parameterType="map">
		INSERT INTO `t_sac_complaints_deal_record` (
			`DEAL_ID`,
			`COMPLAINTS_ID`,
			`COMPLAINTS_NO`,
			`DEAL_TYPE`,
			`DEAL_CONTENT`,
			`AUDIT_CONTENT`,
			`DLR_CODE`,
			`DLR_NAME`,
			`FILE_SRC`,
			`DEAL_TYPE_CODE`,
			`OEM_ID`,
			`GROUP_ID`,
			`OEM_CODE`,
			`GROUP_CODE`,
			`CREATOR`,
			`CREATED_NAME`,
			`CREATED_DATE`,
			`MODIFIER`,
			`MODIFY_NAME`,
			`LAST_UPDATED_DATE`,
			`IS_ENABLE`,
			`SDP_USER_ID`,
			`SDP_ORG_ID`,
			`UPDATE_CONTROL_ID`,
			`CUST_ID`,
			`PHONE`
		) VALUES (
			uuid(), <!-- #{param.dealId}, -->
			#{param.complaintsId},
			#{param.complaintsNo},
			#{param.dealType},
			#{param.dealContent},
			#{param.auditContent},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.fileSrc},
			#{param.dealTypeCode},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			'1',
			'1',
			uuid(),
			#{param.custId},
			#{param.phone}
		)
	</insert>
	<select id="selectByComplaintsNo" resultType="int">
		SELECT COUNT(*) FROM  t_sac_complaints_deal_record
		WHERE  COMPLAINTS_NO =#{param.complaintsNo}
		  <if test="param.dealTypeCode!=null and ''!=param.dealTypeCode">
			and  DEAL_TYPE_CODE =#{param.dealTypeCode}
		  </if>

	</select>
</mapper>