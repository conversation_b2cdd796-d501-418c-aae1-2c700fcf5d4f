<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacCityCounterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacCityCounter">
        <id column="COUNTER_ID" property="counterId" />
        <result column="CITY_CODE" property="cityCode" />
        <result column="CITY_NAME" property="cityName" />
        <result column="CITY_NUM" property="cityNum" />
        <result column="DLR_NUM" property="dlrNum" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COUNTER_ID, CITY_CODE, CITY_NAME, CITY_NUM, DLR_NUM, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.counterId !=null and param.counterId !=''">and COUNTER_ID=#{param.counterId}</if>
    	<if test="param.cityCode !=null and param.cityCode !=''">and CITY_CODE=#{param.cityCode}</if>
    	<if test="param.cityName !=null and param.cityName !=''">and CITY_NAME=#{param.cityName}</if>
    	<if test="param.cityNum !=null and param.cityNum !=''">and CITY_NUM=#{param.cityNum}</if>
    	<if test="param.dlrNum !=null and param.dlrNum !=''">and DLR_NUM=#{param.dlrNum}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 城市计数 信息查询 -->
	<select id="querySacCityCounter" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_city_counter
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<!-- 城市计数 信息删除（物理删除） -->
	<delete id="deleteSacCityCounter">
		DELETE 
		FROM
			t_sac_city_counter
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 城市计数 信息新增 -->
	<insert id="createSacCityCounter">
		insert into t_sac_city_counter(<include refid="Base_Column_List"></include>)
		value(
        	#{param.counterId},
			#{param.cityCode},
			#{param.cityName},
			#{param.cityNum},
			#{param.dlrNum},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			'1',
			'1',
			uuid()
		)
	</insert>
	
	<!-- 城市计数 城市计数 -->
	<update id="updateSacCityCounterCityNum">
		update t_sac_city_counter  set 
			<!-- 更新列表 -->
			CITY_NUM=(CITY_NUM+1),
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.counterId !=null and param.counterId !=''">and COUNTER_ID=#{param.counterId}</if>
    		<if test="param.cityCode !=null and param.cityCode !=''">and CITY_CODE=#{param.cityCode}</if>
	</update>
	
	<!-- 城市计数 专营店计数 -->
	<update id="updateSacCityCounterDlrNum">
		update t_sac_city_counter  set 
			<!-- 更新列表 -->
			DLR_NUM=(DLR_NUM+1),
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.counterId !=null and param.counterId !=''">and COUNTER_ID=#{param.counterId}</if>
    		<if test="param.cityCode !=null and param.cityCode !=''">and CITY_CODE=#{param.cityCode}</if>
	</update>
</mapper>
