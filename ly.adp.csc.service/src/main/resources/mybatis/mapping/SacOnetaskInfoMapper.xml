<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnetaskInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnetaskInfo">
        <id column="TASK_ID" property="taskId" />
        <result column="TASK_TITLE" property="taskTitle" />
        <result column="TASK_DESCRIBE" property="taskDescribe" />
        <result column="TASK_TYPE_CODE" property="taskTypeCode" />
        <result column="TASK_TYPE_NAME" property="taskTypeName" />
        <result column="TASK_STATE_CODE" property="taskStateCode" />
        <result column="TASK_STATE_NAME" property="taskStateName" />
        <result column="TASK_ATTESTATION_IS" property="taskAttestationIs" />
        <result column="TASK_REPEAT_IS" property="taskRepeatIs" />
        <result column="BUSS_TIME" property="bussTime" />
        <result column="BUSS_START_TIME" property="bussStartTime" />
        <result column="BUSS_END_TIME" property="bussEndTime" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TASK_ID, TASK_TITLE, TASK_DESCRIBE, TASK_TYPE_CODE, TASK_TYPE_NAME, TASK_STATE_CODE, TASK_STATE_NAME, TASK_ATTESTATION_IS, TASK_REPEAT_IS, BUSS_TIME, BUSS_START_TIME, BUSS_END_TIME, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
    	<if test="param.taskTitle !=null and param.taskTitle !=''">and TASK_TITLE=#{param.taskTitle}</if>
    	<if test="param.taskDescribe !=null and param.taskDescribe !=''">and TASK_DESCRIBE=#{param.taskDescribe}</if>
    	<if test="param.taskTypeCode !=null and param.taskTypeCode !=''">and TASK_TYPE_CODE=#{param.taskTypeCode}</if>
    	<if test="param.taskTypeName !=null and param.taskTypeName !=''">and TASK_TYPE_NAME=#{param.taskTypeName}</if>
    	<if test="param.taskStateCode !=null and param.taskStateCode !=''">and TASK_STATE_CODE=#{param.taskStateCode}</if>
    	<if test="param.taskStateName !=null and param.taskStateName !=''">and TASK_STATE_NAME=#{param.taskStateName}</if>
    	<if test="param.taskAttestationIs !=null and param.taskAttestationIs !=''">and TASK_ATTESTATION_IS=#{param.taskAttestationIs}</if>
    	<if test="param.taskRepeatIs !=null and param.taskRepeatIs !=''">and TASK_REPEAT_IS=#{param.taskRepeatIs}</if>
    	<if test="param.bussTime !=null and param.bussTime !=''">and BUSS_TIME=#{param.bussTime}</if>
    	<if test="param.bussStartTime !=null and param.bussStartTime !=''">and BUSS_START_TIME=#{param.bussStartTime}</if>
    	<if test="param.bussEndTime !=null and param.bussEndTime !=''">and BUSS_END_TIME=#{param.bussEndTime}</if>
    	<if test="param.extendJson !=null and param.extendJson !=''">and EXTEND_JSON=#{param.extendJson}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
    	<!-- 模糊查询 -->
    	<if test="param.taskTitlePaste !=null and param.taskTitlePaste !=''">and INSTR(TASK_TITLE,#{param.taskTitlePaste})>0</if>
    	<if test="param.createdNamePaste !=null and param.createdNamePaste !=''">and INSTR(CREATED_NAME,#{param.createdNamePaste})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.createdDateStart !=null and param.createdDateStart !=''">and CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]></if>
   		<if test="param.bussTimeStart !=null and param.bussTimeStart !=''">and BUSS_TIME>=#{param.bussTimeStart}</if>
	    <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''"><![CDATA[and BUSS_TIME<=#{param.bussTimeEnd}]]></if>
	</sql>
 	
 	<!-- 任务表 信息查询 -->
	<select id="querySacOnetaskInfo" resultType="map">
		select 
			<!-- 已结束状态根据时间判断 -->
			<![CDATA[CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '5' ELSE TASK_STATE_CODE END TASK_STATE_CODE,]]>
   			<![CDATA[CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '已结束' ELSE TASK_STATE_NAME END TASK_STATE_NAME,]]>
			<![CDATA[CASE WHEN TASK_REPEAT_IS = '0' THEN '否'  ELSE'是'  END taskRepeatIsName,]]>
			TASK_ID,
			TASK_TITLE,
			TASK_DESCRIBE,
			TASK_TYPE_CODE,
			TASK_TYPE_NAME,
			TASK_ATTESTATION_IS,
			TASK_REPEAT_IS,
			BUSS_TIME,
			BUSS_START_TIME,
			BUSS_END_TIME,
			EXTEND_JSON,
			COLUMN1,
			COLUMN2,
			COLUMN3,
			COLUMN4,
			COLUMN5,
			_MYCAT_OP_TIME,
			OEM_ID,
			GROUP_ID,
			OEM_CODE,
			GROUP_CODE,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			SDP_USER_ID,
			SDP_ORG_ID,
			UPDATE_CONTROL_ID 
	    from t_sac_onetask_info
	    where 1=1
	    <include refid="where_condition"></include>
	    <if test="param.isBeyond !=null and param.isBeyond !=''">
	    	AND BUSS_END_TIME IS NOT NULL AND (CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = #{param.isBeyond}
	    </if>
	    <if test="param.taskStateCodeT !=null and param.taskStateCodeT !=''">
	    	AND (CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = "0"
	    	and TASK_STATE_CODE=#{param.taskStateCodeT}
	    </if>
    	ORDER BY CREATED_DATE DESC
		<if test="param.pageNo !=null and param.pageNo >=0 and param.pageSize !=null and param.pageSize > 0">
			LIMIT #{param.pageNo},#{param.pageSize}
		</if>
	</select>

	<select id="querySacOnetaskInfoCount" resultType="java.lang.Long">
		select
			count(*)
	    from t_sac_onetask_info
	    where 1=1
	    <include refid="where_condition"></include>
	    <if test="param.isBeyond !=null and param.isBeyond !=''">
	    	AND BUSS_END_TIME IS NOT NULL AND (CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = #{param.isBeyond}
	    </if>
	    <if test="param.taskStateCodeT !=null and param.taskStateCodeT !=''">
	    	AND (CASE WHEN str_to_date(BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = "0"
	    	and TASK_STATE_CODE=#{param.taskStateCodeT}
	    </if>
	</select>

	<!-- 任务表 信息删除（物理删除） -->
	<delete id="deleteSacOnetaskInfo">
		DELETE 
		FROM
			t_sac_onetask_info
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 任务表 信息新增 -->
	<insert id="createSacOnetaskInfo">
		insert into t_sac_onetask_info(<include refid="Base_Column_List"></include>)
		value(
        	#{param.taskId},
			#{param.taskTitle},
			#{param.taskDescribe},
			#{param.taskTypeCode},
			#{param.taskTypeName},
			#{param.taskStateCode},
			#{param.taskStateName},
			#{param.taskAttestationIs},
			#{param.taskRepeatIs},
			#{param.bussTime},
			#{param.bussStartTime},
			#{param.bussEndTime},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.dccFlag},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 任务表 信息更新 -->
	<update id="updateSacOnetaskInfo">
		update t_sac_onetask_info  set 
			<!-- 更新列表 -->
			<if test="param.taskTitle !=null and param.taskTitle !=''">TASK_TITLE=#{param.taskTitle},</if>
			<if test="param.taskDescribe !=null">TASK_DESCRIBE=#{param.taskDescribe},</if>
			<if test="param.taskTypeCode !=null and param.taskTypeCode !=''">TASK_TYPE_CODE=#{param.taskTypeCode},</if>
			<if test="param.taskTypeName !=null and param.taskTypeName !=''">TASK_TYPE_NAME=#{param.taskTypeName},</if>
			<if test="param.taskStateCode !=null and param.taskStateCode !=''">TASK_STATE_CODE=#{param.taskStateCode},</if>
			<if test="param.taskStateName !=null and param.taskStateName !=''">TASK_STATE_NAME=#{param.taskStateName},</if>
			<if test="param.taskAttestationIs !=null and param.taskAttestationIs !=''">TASK_ATTESTATION_IS=#{param.taskAttestationIs},</if>
			<if test="param.taskRepeatIs !=null and param.taskRepeatIs !=''">TASK_REPEAT_IS=#{param.taskRepeatIs},</if>
			<if test="param.bussTime !=null and param.bussTime !=''">BUSS_TIME=#{param.bussTime},</if>
			<if test="param.bussStartTime !=null and param.bussStartTime !=''">BUSS_START_TIME=#{param.bussStartTime},</if>
			<if test="param.bussEndTime !=null and param.bussEndTime !=''">BUSS_END_TIME=#{param.bussEndTime},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.dccFlag !=null and param.dccFlag !=''">COLUMN5=#{param.dccFlag},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
	
	<!-- 任务表消息信息查询 -->
	<select id="sacOnetaskInfoMsg" resultType="map">
		SELECT
			msg.MESSAGE_ID,
			msg.IS_READ,
			msg.DLR_CODE,
			msg.MESSAGE_TYPE,
			msg.RECEIVE_EMP_ID,
			msg.MESSAGE_CONTENT,
			msg.RELATION_BILL_ID,
			<![CDATA[CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '5' ELSE info.TASK_STATE_CODE END TASK_STATE_CODE,]]>
			<![CDATA[CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') < str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN  '已结束' ELSE info.TASK_STATE_NAME END TASK_STATE_NAME,]]>
			<![CDATA[CASE WHEN TASK_REPEAT_IS = '0' THEN '否'  ELSE'是'  END taskRepeatIsName,]]>
			info.TASK_ID,
			info.TASK_TITLE,
			info.TASK_DESCRIBE,
			info.TASK_TYPE_CODE,
			info.TASK_TYPE_NAME,
			info.TASK_ATTESTATION_IS,
			info.TASK_REPEAT_IS,
			info.BUSS_TIME,
			info.BUSS_START_TIME,
			info.BUSS_END_TIME,
			info.EXTEND_JSON,
			info.CREATOR,
			info.CREATED_NAME,
			info.CREATED_DATE,
		    e.emp_name
		FROM t_sac_clue_msg_record msg
		INNER JOIN t_sac_onetask_info info ON msg.RELATION_BILL_ID=info.TASK_ID
		left join mp.t_usc_mdm_org_employee e on msg.RECEIVE_EMP_ID = e.emp_id
		where 1=1
		<if test="param.receiveEmpId != null and param.receiveEmpId !=''">
		and msg.RECEIVE_EMP_ID =#{param.receiveEmpId}
		</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">
			and msg.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.empId != null and param.empId !=''">
			and msg.RECEIVE_EMP_ID =#{param.empId}
		</if>
		<if test="param.messageType != null and param.messageType !=''">
			and msg.MESSAGE_TYPE in
			<foreach item="item" collection=" param.messageType.split(',')"
					 index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isRead != null and param.isRead !=''">
			and msg.IS_READ = #{param.isRead}
		</if>
		<if test="param.relationBillId != null and param.relationBillId !=''">
			and msg.RELATION_BILL_ID = #{param.relationBillId}
		</if>
		<if test="param.taskStateCode !=null and param.taskStateCode !=''">
			and info.TASK_STATE_CODE=#{param.taskStateCode}
		</if>
		<!-- 时间查询 -->
    	<if test="param.createdDateStart !=null and param.createdDateStart !=''">
			and msg.CREATED_DATE>=#{param.createdDateStart}
		</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and msg.CREATED_DATE<=#{param.createdDateEnd}]]>
		</if>
		<if test="param.taskTitlePaste !=null and param.taskTitlePaste !=''">
			and INSTR(info.TASK_TITLE,#{param.taskTitlePaste})>0
		</if>
		<if test="param.taskTypeCode !=null and param.taskTypeCode !=''">
			and info.TASK_TYPE_CODE=#{param.taskTypeCode}
		</if>
		<if test="param.isEnd !=null and param.isEnd !=''">
			AND info.BUSS_END_TIME IS NOT NULL AND (CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = #{param.isEnd}
		</if>
		<if test="param.taskStateCodeT !=null and param.taskStateCodeT !=''">
			AND (CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = "0"
		</if>
		<if test="param.filterDccFlag">and info.column5 is null</if>
		ORDER BY
		info.BUSS_TIME DESC
	</select>

	<select id="sacOnetaskInfoMsgCount" resultType="java.lang.Integer">
		SELECT
		count(1)
		FROM t_sac_clue_msg_record msg
		INNER JOIN t_sac_onetask_info info ON msg.RELATION_BILL_ID=info.TASK_ID
		left join mp.t_usc_mdm_org_employee e on msg.RECEIVE_EMP_ID = e.emp_id
		where 1=1
		<if test="param.receiveEmpId != null and param.receiveEmpId !=''">
			and msg.RECEIVE_EMP_ID =#{param.receiveEmpId}
		</if>
		<if test="param.dlrCode != null and param.dlrCode !=''">
			and msg.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.empId != null and param.empId !=''">
			and msg.RECEIVE_EMP_ID =#{param.empId}
		</if>

		<if test="param.messageType != null and param.messageType !=''">
			and msg.MESSAGE_TYPE in
			<foreach item="item" collection=" param.messageType.split(',')"
					 index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isRead != null and param.isRead !=''">
			and msg.IS_READ = #{param.isRead}
		</if>
		<if test="param.relationBillId != null and param.relationBillId !=''">
			and msg.RELATION_BILL_ID = #{param.relationBillId}
		</if>
		<if test="param.taskStateCode !=null and param.taskStateCode !=''">
			and info.TASK_STATE_CODE=#{param.taskStateCode}
		</if>
		<!-- 时间查询 -->
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">
			and msg.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and msg.CREATED_DATE<=#{param.createdDateEnd}]]>
		</if>
		<if test="param.taskTitlePaste !=null and param.taskTitlePaste !=''">
			and INSTR(info.TASK_TITLE,#{param.taskTitlePaste})>0
		</if>
		<if test="param.taskTypeCode !=null and param.taskTypeCode !=''">
			and info.TASK_TYPE_CODE=#{param.taskTypeCode}
		</if>
		<if test="param.isEnd !=null and param.isEnd !=''">
			AND info.BUSS_END_TIME IS NOT NULL AND (CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = #{param.isEnd}
		</if>
		<if test="param.taskStateCodeT !=null and param.taskStateCodeT !=''">
			AND (CASE WHEN str_to_date(info.BUSS_END_TIME,'%Y-%m-%d %H:%i:%s') > str_to_date(NOW(),'%Y-%m-%d %H:%i:%s') THEN '0'  ELSE '1' END) = "0"
		</if>
		<if test="param.filterDccFlag">and info.column5 is null</if>
	</select>

	<select id="checkIsStoreManager" resultType="java.lang.Boolean">
		SELECT EXISTS
		(
		SELECT
		1
		FROM
		mp.t_usc_mdm_org_employee employee
		INNER JOIN mp.t_usc_mdm_org_station station ON employee.station_id = station.station_id
		WHERE
		employee.emp_id = #{param.receiveEmpId}
		AND station.STATION_CODE = 'Store Manager'
		)
	</select>

    <select id="queryTaskForMq" resultType="com.ly.adp.csc.entities.dto.TaskDto">
		select
		i.task_id as taskId,
		i.created_name as taskCreator,
		case #{param.taskStatus} when '1' then date_add(i.buss_end_time, interval 1 second)
		else now()
		end as statusChangeTime
		from
		t_sac_onetask_info i
		where
		i.creator like 'by-%'
		<choose>
			<when test='param.taskStatus == "1"'>
				and date(i.buss_end_time) = #{param.bussEndTime}
			</when>
			<otherwise>
				and i.task_id = #{param.taskId} and  i.task_state_code = '4'
			</otherwise>
		</choose>
	</select>

    <select id="queryTaskClueForMq" resultType="com.ly.adp.csc.entities.dto.TaskClueMqDto">
		select
			d.task_id,
			d.cust_name as clueName,
			json_unquote(json_extract(d.extend_json, '$.phone')) as phoneNumber,
			d.state_code as status,
			d.buss_time as completionTime,
			case
				when ifnull(d.buss_time, '') = '' and str_to_date(d.buss_end_time, '%y-%m-%d %h:%i:%s') &lt; now() then '1'
				when str_to_date(d.buss_end_time, '%y-%m-%d %h:%i:%s') &lt; str_to_date(d.buss_time, '%y-%m-%d %h:%i:%s') then '1'
				else '0'
				end as isOverdue,
			d.task_person_name as completer
		from t_sac_onetask_detail d
		where
		d.task_id in
		<foreach item="item" collection="taskId.split(',')"
				 index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
</mapper>
