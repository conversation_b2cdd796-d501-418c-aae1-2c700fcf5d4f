<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacBuBoutiqueDetail">
        <id column="DETAIL_ID" property="detailId" />
        <result column="APPLY_ID" property="applyId" />
        <result column="BOUTIQUE_CODE" property="boutiqueCode" />
        <result column="BOUTIQUE_NAME" property="boutiqueName" />
        <result column="DLR_ID" property="dlrId" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="BOUTIQUE_PUTAWAY_NUM" property="boutiquePutawayNum" />
        <result column="BOUTIQUE_SOLD_OUT_NUM" property="boutiqueSoldOutNum" />
        <result column="BOUTIQUE_STATUS" property="boutiqueStatus" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DETAIL_ID, APPLY_ID, BOUTIQUE_CODE, BOUTIQUE_NAME, DLR_ID, DLR_CODE, DLR_NAME, BOUTIQUE_PUTAWAY_NUM,BOUTIQUE_CURRENT_NUM, BOUTIQUE_SOLD_OUT_NUM, BOUTIQUE_STATUS, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.detailId !=null and param.detailId !=''">and DETAIL_ID=#{param.detailId}</if>
    	<if test="param.applyId !=null and param.applyId !=''">and APPLY_ID=#{param.applyId}</if>
    	<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">and INSTR(BOUTIQUE_CODE,#{param.boutiqueCode})</if>
    	<if test="param.boutiqueName !=null and param.boutiqueName !=''">and INSTR(BOUTIQUE_NAME,#{param.boutiqueName})</if>
    	<if test="param.dlrId !=null and param.dlrId !=''">
    	and DLR_ID IN
			<foreach collection="param.dlrId.split(',')" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
    	</if>
    	<if test="param.dlrCode !=null and param.dlrCode !=''">and DLR_CODE=#{param.dlrCode}</if>
    	<if test="param.dlrName !=null and param.dlrName !=''">and DLR_NAME=#{param.dlrName}</if>
    	<if test="param.boutiquePutawayNum !=null and param.boutiquePutawayNum !=''">and BOUTIQUE_PUTAWAY_NUM=#{param.boutiquePutawayNum}</if>
    	<if test="param.boutiqueSoldOutNum !=null and param.boutiqueSoldOutNum !=''">and BOUTIQUE_SOLD_OUT_NUM=#{param.boutiqueSoldOutNum}</if>
    	<if test="param.boutiqueStatus !=null and param.boutiqueStatus !=''">AND BOUTIQUE_STATUS IN
			<foreach collection="param.boutiqueStatus.split(',')" item="item" open="(" separator="," close=")">
				#{item}
			</foreach></if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null ">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null ">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 精品明细表 信息查询 -->
	<select id="querySacBuBoutiqueDetail" resultType="map">
		select
		CASE BOUTIQUE_STATUS WHEN '1' THEN '待收货' WHEN '2' THEN '已收货' END  AS BOUTIQUE_STATUS_NAME ,
	    <include refid="Base_Column_List"></include>
	    from t_sac_bu_boutique_detail
	    where 1=1
	    <include refid="where_condition"></include>
	    <if test="param.detailIdList !=null and param.detailIdList !=''">
	    	AND DETAIL_ID IN
			<foreach collection="param.detailIdList.split(',')" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
	    </if>
		ORDER BY LAST_UPDATED_DATE DESC
	</select>
	
	<!-- 精品明细表 信息删除（物理删除） -->
	<delete id="deleteSacBuBoutiqueDetail">
	DELETE 
	FROM
		t_sac_bu_boutique_detail
	WHERE 1=1
		<include refid="where_condition"></include>
	</delete>
	
	<!-- 精品明细表 信息新增 -->
	<insert id="createSacBuBoutiqueDetail">
	insert into t_sac_bu_boutique_detail(<include refid="Base_Column_List"></include>)
	value(
       	uuid(),
		#{param.applyId},
		#{param.boutiqueCode},
		#{param.boutiqueName},
		#{param.dlrId},
		#{param.dlrCode},
		#{param.dlrName},
		#{param.boutiquePutawayNum},
		#{param.boutiqueSoldOutNum},
		#{param.boutiqueStatus},
		#{param.column1},
		#{param.column2},
		#{param.column3},
		#{param.column4},
		#{param.column5},
		#{param.remark},
		#{param.creator},
		#{param.createdName},
		now(),
		#{param.modifier},
		#{param.modifyName},
		now(),
		#{param.isEnable},
		uuid(),
	)
	</insert>
	
	<!-- 精品明细表 信息更新 -->
	<update id="updateSacBuBoutiqueDetail">
		update t_sac_bu_boutique_detail  set 
			<!-- 更新列表 -->
			<if test="param.applyId !=null and param.applyId !=''">APPLY_ID=#{param.applyId},</if>
			<if test="param.boutiqueCode !=null and param.boutiqueCode !=''">BOUTIQUE_CODE=#{param.boutiqueCode},</if>
			<if test="param.boutiqueName !=null and param.boutiqueName !=''">BOUTIQUE_NAME=#{param.boutiqueName},</if>
			<if test="param.dlrId !=null and param.dlrId !=''">DLR_ID=#{param.dlrId},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrName !=null and param.dlrName !=''">DLR_NAME=#{param.dlrName},</if>
			<if test="param.boutiquePutawayNum !=null and param.boutiquePutawayNum !=''">BOUTIQUE_PUTAWAY_NUM=#{param.boutiquePutawayNum},</if>
		    <if test="param.boutiqueCurrentNum !=null and param.boutiqueCurrentNum !=''">BOUTIQUE_CURRENT_NUM=#{param.boutiqueCurrentNum},</if>
			<if test="param.boutiqueSoldOutNum !=null and param.boutiqueSoldOutNum !=''">BOUTIQUE_SOLD_OUT_NUM=#{param.boutiqueSoldOutNum},</if>
			<if test="param.boutiqueStatus !=null and param.boutiqueStatus !=''">BOUTIQUE_STATUS=#{param.boutiqueStatus},</if>
			COLUMN1=#{param.column1},
			COLUMN2=#{param.column2},
			COLUMN3=#{param.column3},
			COLUMN4=#{param.column4},
			COLUMN5=#{param.column5},
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<!--CREATOR=#{entity.userID},
			CREATED_NAME=#{entity.empName},
			CREATED_DATE=NOW(),  -->
			MODIFIER=#{entity.userID},
			MODIFY_NAME=#{entity.empName},
			LAST_UPDATED_DATE=NOW(),
			IS_ENABLE='1',
			UPDATE_CONTROL_ID=UUID(),
			<!-- 结束无逗号 -->
			DETAIL_ID=DETAIL_ID
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.applyId!=null and ''!=param.applyId ">
				and APPLY_ID=#{param.applyId}
			</if>
			<if test="param.detailId!=null and ''!=param.detailId">
				and DETAIL_ID =#{param.detailId}
			</if>
			<if test="param.updateControlId!=null and ''!=param.updateControlId">
				and UPDATE_CONTROL_ID=#{param.updateControlId}
			</if>
	</update>
	
	<!-- 精品详情查询 -->
	<select id="sacBuBoutiqueDetailQuery" parameterType="map" resultType="map" >
		SELECT
		T.DETAIL_ID,
		t.APPLY_ID,
		t.BOUTIQUE_CODE,
		t.BOUTIQUE_NAME,
		t.DLR_ID,
		t.DLR_CODE,
		d.DLR_SHORT_NAME DLR_NAME,
		t.BOUTIQUE_STATUS,
		t.REMARK,
		IFNULL(T.BOUTIQUE_PUTAWAY_NUM,'0') AS BOUTIQUE_PUTAWAY_NUM,
		IFNULL(T.BOUTIQUE_CURRENT_NUM,'0') AS BOUTIQUE_CURRENT_NUM,
		IFNULL(T.BOUTIQUE_SOLD_OUT_NUM,'0') AS BOUTIQUE_SOLD_OUT_NUM,
		t.UPDATE_CONTROL_ID,
		t3.AREA_ID ,
		t3.AREA_NAME ,
		t3.area_code AS AREA_CODE,
		C2.AGENT_NAME,
		C1.AGENT_COMPANY_NAME,
		h.PROVINCE_NAME,
		g.CITY_NAME
		FROM t_sac_bu_boutique_detail T
		LEFT JOIN t_usc_mdm_org_dlr d ON T.DLR_ID = d.DLR_ID
		LEFT JOIN t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
		LEFT JOIN t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
		LEFT JOIN t_usc_mdm_org_city g ON d.CITY_ID = g.CITY_ID
		LEFT JOIN t_usc_mdm_org_province h ON d.PROVINCE_ID = h.PROVINCE_ID
		LEFT JOIN t_usc_mdm_agent_area t1 ON t1.AGENT_ID = d.DLR_ID AND t1.AREA_TYPE = '0' /*门店对应省份信息*/
		LEFT JOIN t_usc_area_relation t2 ON t1.AREA_ID = t2.REL_OBJ_ID AND t2.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
		LEFT JOIN t_usc_area_info t3 ON t3.AREA_ID = t2.AREA_ID AND t3.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
		WHERE T.IS_ENABLE = '1'
		<if test="param.provinceId!=null and ''!=param.provinceId">
			AND d.PROVINCE_ID = #{param.provinceId}
		</if>
		<if test="param.cityId!=null and ''!=param.cityId">
			AND d.CITY_ID = #{param.cityId}
		</if>
		<if test="param.agentId!=null and ''!=param.agentId">
			AND C1.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.companyId!=null and ''!=param.companyId">
			AND d.COMPANY_ID = #{param.companyId}
		</if>
		<if test="param.boutiqueCode != null and param.boutiqueCode != ''">
			AND INSTR(T.BOUTIQUE_CODE ,#{param.boutiqueCode})
		</if>
		<if test="param.boutiqueName != null and param.boutiqueName != ''">
			AND INSTR(T.BOUTIQUE_NAME , #{param.boutiqueName})
		</if>
		<if test="param.boutiqueStatus != null and param.boutiqueStatus != ''">
			AND T.BOUTIQUE_STATUS = #{param.boutiqueStatus}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND T.DLR_CODE IN
			<foreach collection="param.dlrCode.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrId != null and param.dlrId != ''">
			AND T.DLR_ID IN
			<foreach collection="param.dlrId.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.dlrName != null and param.dlrName != ''">
			AND INSTR(T.DLR_NAME, #{param.dlrName}) <![CDATA[>]]> 0
		</if>
		<if test="param.areaCode != null and param.areaCode != ''">
			AND t3.AREA_CODE = #{param.areaCode}
		</if>
		<if test="param.areaId != null and param.areaId != ''">
			AND t3.AREA_ID = #{param.areaId}
		</if>
		<if test="param.areaName != null and param.areaName != ''">
			AND INSTR(t3.AREA_NAME, #{param.areaName}) <![CDATA[>]]> 0
		</if>
		ORDER BY T.LAST_UPDATED_DATE DESC
	</select>
	<select id="selectByDetailId" resultType="java.util.Map">
		select
			BOUTIQUE_PUTAWAY_NUM,
			BOUTIQUE_CURRENT_NUM,
			UPDATE_CONTROL_ID
		from  t_sac_bu_boutique_detail where  DETAIL_ID =#{detailId}
	</select>
	<insert id="insertReceiving">
		insert into  t_sac_bu_boutique_receiving (
				RECEIVING_ID,
				DETAIL_ID,
				BOUTIQUE_CODE,
				BOUTIQUE_NAME,
				DLR_ID,
				DLR_CODE,
				DLR_NAME,
				BOUTIQUE_PUTAWAY_NUM,
				COLUMN1,
				COLUMN2,
				COLUMN3,
				COLUMN4,
				COLUMN5,
				REMARK,
				CREATOR,
				CREATED_NAME,
				CREATED_DATE,
				MODIFIER,
				MODIFY_NAME,
				LAST_UPDATED_DATE,
				IS_ENABLE,
				UPDATE_CONTROL_ID
		) VALUES (
				#{param.receivingId},
				#{param.detailId},
				#{param.boutiqueCode},
				#{param.boutiqueName},
				#{param.dlrId},
				#{param.dlrCode},
				#{param.dlrName},
				#{param.boutiquePutawayNum},
				#{param.column1},
				#{param.column2},
				#{param.column3},
				#{param.column4},
				#{param.column5},
				#{param.remark},
				#{param.creator},
				#{param.createdName},
				NOW(),
				#{param.modifier},
				#{param.modifyName},
				NOW(),
				'1',
				UUID()
						 )
	</insert>
</mapper>
