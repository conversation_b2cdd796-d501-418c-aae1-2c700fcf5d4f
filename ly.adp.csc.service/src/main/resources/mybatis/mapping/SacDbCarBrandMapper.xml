<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDbCarBrandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.DbCarBrand">
        <id column="UID" property="uid" />
        <result column="CAR_BRAND_CODE" property="carBrandCode" />
        <result column="CAR_BRAND_EN" property="carBrandEn" />
        <result column="CAR_BRAND_CN" property="carBrandCn" />
        <result column="ORDER_NO" property="orderNo" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, CAR_BRAND_CODE, CAR_BRAND_EN, CAR_BRAND_CN, ORDER_NO, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, REMARK
    </sql>
    <!-- 车辆品牌查询-->
	<select id="selectDbCarBrand" resultType="java.util.Map">
	    select
	        t.UID,
	        t.CAR_BRAND_CODE,
	        t.CAR_BRAND_EN,
	        t.CAR_BRAND_CN,
	        t.ORDER_NO,
	        t.OEM_ID,
	        t.GROUP_ID,
	        t.CREATOR,
	        t.CREATED_NAME,
	        t.CREATED_DATE,
	        t.MODIFIER,
	        t.MODIFY_NAME,
	        t.LAST_UPDATED_DATE,
	        t.UPDATE_CONTROL_ID, 
	        t.REMARK,
			t.IS_ENABLE
		from t_sac_db_car_brand t
		where 1=1
		<if test="param.carBrandCode !=null and param.carBrandCode !=''"> and t.CAR_BRAND_CODE like concat('%',#{param.carBrandCode},'%')</if>
		<if test="param.carBrandEn !=null and param.carBrandEn !=''"> and t.CAR_BRAND_EN like concat('%',#{param.carBrandEn},'%')</if>
		<if test="param.carBrandCn !=null and param.carBrandCn !=''"> and t.CAR_BRAND_CN like concat('%',#{param.carBrandCn},'%')</if>
		<if test="param.orderNo !=null and param.orderNo !=''"> and t.ORDER_NO=#{param.orderNo}</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> and t.IS_ENABLE=#{param.isEnable}</if>
		order by t.ORDER_NO
	</select>
</mapper>
