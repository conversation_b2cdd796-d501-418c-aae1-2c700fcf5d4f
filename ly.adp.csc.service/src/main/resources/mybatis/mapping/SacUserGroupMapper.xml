<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacUserGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacUserGroup">
        <id column="USER_GROUP_ID" property="userGroupId" />
        <result column="USER_GROUP_NAME" property="userGroupName" />
        <result column="USER_GROUP_DESC" property="userGroupDesc" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        USER_GROUP_ID, USER_GROUP_NAME, USER_GROUP_DESC, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>

    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.userGroupId !=null and param.userGroupId !=''">and USER_GROUP_ID=#{param.userGroupId}</if>
    	<if test="param.userGroupName !=null and param.userGroupName !=''">and USER_GROUP_NAME=#{param.userGroupName}</if>
    	<if test="param.userGroupDesc !=null and param.userGroupDesc !=''">and USER_GROUP_DESC=#{param.userGroupDesc}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.column6 !=null and param.column6 !=''">and COLUMN6=#{param.column6}</if>
    	<if test="param.column7 !=null and param.column7 !=''">and COLUMN7=#{param.column7}</if>
    	<if test="param.column8 !=null and param.column8 !=''">and COLUMN8=#{param.column8}</if>
    	<if test="param.column9 !=null and param.column9 !=''">and COLUMN9=#{param.column9}</if>
    	<if test="param.column10 !=null and param.column10 !=''">and COLUMN10=#{param.column10}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>

 	<!-- 用户分组主表 信息查询 -->
	<select id="querySacUserGroup" resultType="map">
		SELECT
		COUNT( T2.DETAIL_ID ) AS detailNum,
		T1.USER_GROUP_ID,
		T1.USER_GROUP_NAME,
		T1.USER_GROUP_DESC,
		T1.COLUMN1,
		T1.COLUMN2,
		T1.COLUMN3,
		T1.COLUMN4,
		T1.COLUMN5,
		T1.COLUMN6,
		T1.COLUMN7,
		T1.COLUMN8,
		T1.COLUMN9,
		T1.COLUMN10,
		T1._MYCAT_OP_TIME,
		T1.OEM_ID,
		T1.GROUP_ID,
		T1.OEM_CODE,
		T1.GROUP_CODE,
		T1.CREATOR,
		T1.CREATED_NAME,
		T1.CREATED_DATE,
		T1.MODIFIER,
		T1.MODIFY_NAME,
		T1.LAST_UPDATED_DATE,
		T1.IS_ENABLE,
		T1.SDP_USER_ID,
		T1.SDP_ORG_ID,
		T1.UPDATE_CONTROL_ID
		FROM
		t_sac_user_group T1
		LEFT JOIN t_sac_user_group_detail T2 ON T2.USER_GROUP_ID = T1.USER_GROUP_ID
		WHERE 1 = 1
		<if test="param.creator !=null and param.creator !=''">and T1.CREATOR=#{param.creator}</if>
	   	<if test="param.userGroupId !=null and param.userGroupId !=''">and T1.USER_GROUP_ID=#{param.userGroupId}</if>
    	<if test="param.userGroupName !=null and param.userGroupName !=''">and T1.USER_GROUP_NAME=#{param.userGroupName}</if>
    	<if test="param.userGroupDesc !=null and param.userGroupDesc !=''">and T1.USER_GROUP_DESC=#{param.userGroupDesc}</if>
		GROUP BY T1.USER_GROUP_ID
		ORDER BY T1.CREATED_DATE DESC
	</select>

	<!-- 用户分组主表 信息删除（物理删除） -->
	<delete id="deleteSacUserGroup">
		DELETE
		FROM
			t_sac_user_group
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>

	<!-- 用户分组主表 信息新增 -->
	<insert id="createSacUserGroup">
		insert into t_sac_user_group(<include refid="Base_Column_List"></include>)
		value(
        	#{param.userGroupId},
			#{param.userGroupName},
			#{param.userGroupDesc},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			"1",
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>

	<!-- 用户分组主表 信息更新 -->
	<update id="updateSacUserGroup">
		update t_sac_user_group  set
			<!-- 更新列表 -->
			<if test="param.userGroupName !=null and param.userGroupName !=''">USER_GROUP_NAME=#{param.userGroupName},</if>
			<if test="param.userGroupDesc !=null and param.userGroupDesc !=''">USER_GROUP_DESC=#{param.userGroupDesc},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.column6 !=null and param.column6 !=''">COLUMN6=#{param.column6},</if>
			<if test="param.column7 !=null and param.column7 !=''">COLUMN7=#{param.column7},</if>
			<if test="param.column8 !=null and param.column8 !=''">COLUMN8=#{param.column8},</if>
			<if test="param.column9 !=null and param.column9 !=''">COLUMN9=#{param.column9},</if>
			<if test="param.column10 !=null and param.column10 !=''">COLUMN10=#{param.column10},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1
			<!-- 描述条件 -->
			<if test="param.userGroupId !=null and param.userGroupId !=''">and USER_GROUP_ID=#{param.userGroupId}</if>
	</update>
</mapper>
