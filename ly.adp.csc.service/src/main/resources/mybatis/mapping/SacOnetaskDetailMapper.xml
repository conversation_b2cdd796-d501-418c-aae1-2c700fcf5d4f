<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnetaskDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnetaskDetail">
        <id column="DETAIL_ID" property="detailId" />
        <result column="TASK_ID" property="taskId" />
        <result column="TASK_PERSON_ID" property="taskPersonId" />
        <result column="TASK_PERSON_CODE" property="taskPersonCode" />
        <result column="TASK_PERSON_NAME" property="taskPersonName" />
        <result column="TASK_PERSON_DLR_CODE" property="taskPersonDlrCode" />
        <result column="TASK_PERSON_DLR_NAME" property="taskPersonDlrName" />
        <result column="AREA_CODE" property="areaCode" />
        <result column="AREA_NAME" property="areaName" />
        <result column="CUST_ID" property="custId" />
        <result column="CUST_NAME" property="custName" />
        <result column="SERVER_ORDER" property="serverOrder" />
        <result column="REVIEW_ID" property="reviewId" />
        <result column="STATE_CODE" property="stateCode" />
        <result column="STATE_NAME" property="stateName" />
        <result column="REMARK" property="remark" />
        <result column="BUSS_TIME" property="bussTime" />
        <result column="BUSS_START_TIME" property="bussStartTime" />
        <result column="BUSS_END_TIME" property="bussEndTime" />
        <result column="FILE_PATH" property="filePath" />
        <result column="SEND_TYPE" property="sendType" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DETAIL_ID, TASK_ID, TASK_PERSON_ID, TASK_PERSON_CODE, TASK_PERSON_NAME, TASK_PERSON_DLR_CODE, TASK_PERSON_DLR_NAME,AREA_CODE, AREA_NAME, CUST_ID, CUST_NAME, SERVER_ORDER, REVIEW_ID, STATE_CODE, STATE_NAME, REMARK, BUSS_TIME, BUSS_START_TIME, BUSS_END_TIME, FILE_PATH, SEND_TYPE, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.detailId !=null and param.detailId !=''">and DETAIL_ID=#{param.detailId}</if>
    	<if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
    	<if test="param.taskPersonId !=null and param.taskPersonId !=''">and TASK_PERSON_ID=#{param.taskPersonId}</if>
    	<if test="param.taskPersonCode !=null and param.taskPersonCode !=''">and TASK_PERSON_CODE=#{param.taskPersonCode}</if>
    	<if test="param.taskPersonName !=null and param.taskPersonName !=''">and TASK_PERSON_NAME=#{param.taskPersonName}</if>
    	<if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">and TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode}</if>
    	<if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">and TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName}</if>
    	<if test="param.areaCode !=null and param.areaCode !=''">and AREA_CODE=#{param.areaCode}</if>
    	<if test="param.areaName !=null and param.areaName !=''">and AREA_NAME=#{param.areaName}</if>
    	<if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
    	<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
    	<if test="param.serverOrder !=null and param.serverOrder !=''">and SERVER_ORDER=#{param.serverOrder}</if>
    	<if test="param.reviewId !=null and param.reviewId !=''">and REVIEW_ID=#{param.reviewId}</if>
    	<if test="param.stateCode !=null and param.stateCode !=''">and STATE_CODE=#{param.stateCode}</if>
    	<if test="param.stateName !=null and param.stateName !=''">and STATE_NAME=#{param.stateName}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.bussTime !=null and param.bussTime !=''">and BUSS_TIME=#{param.bussTime}</if>
    	<if test="param.bussStartTime !=null and param.bussStartTime !=''">and BUSS_START_TIME=#{param.bussStartTime}</if>
    	<if test="param.bussEndTime !=null and param.bussEndTime !=''">and BUSS_END_TIME=#{param.bussEndTime}</if>
    	<if test="param.filePath !=null and param.filePath !=''">and FILE_PATH=#{param.filePath}</if>
    	<if test="param.sendType !=null and param.sendType !=''">and SEND_TYPE=#{param.sendType}</if>
    	<if test="param.extendsJson !=null and param.extendsJson !=''">and EXTEND_JSON=#{param.extendsJson}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
    	<!-- 模糊查询 -->
    	<if test="param.taskPersonNamePaste !=null and param.taskPersonNamePaste !=''">and INSTR(TASK_PERSON_NAME,#{param.taskPersonNamePaste})>0</if>
    	<if test="param.custNamePaste !=null and param.custNamePaste !=''">and INSTR(CUST_NAME,#{param.custNamePaste})>0</if>
    	<if test="param.areaNamePaste !=null and param.areaNamePaste !=''">and INSTR(AREA_NAME,#{param.areaNamePaste})>0</if>
    	<!-- 时间查询 -->
    	<if test="param.bussTimeStart !=null and param.bussTimeStart !=''">and BUSS_TIME>=#{param.bussTimeStart}</if>
	    <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''"><![CDATA[and BUSS_TIME<=#{param.bussTimeEnd}]]></if>
	    <!-- 门店编码/门店名称混合条件模糊查询 -->
	    <if test="param.dlrShortName != null and '' != param.dlrShortName">
            AND ( INSTR(TASK_PERSON_DLR_CODE, #{param.dlrShortName}) <![CDATA[>]]> 0 OR INSTR(TASK_PERSON_DLR_NAME, #{param.dlrShortName}) <![CDATA[>]]> 0 )
        </if>
		<if test="param.listTaskPerson != null and param.listTaskPerson.size() > 0">
			AND (TASK_ID, TASK_PERSON_ID) IN(
			<foreach collection="param.listTaskPerson" item="item" open="(" separator="),(" close=")">
				#{item.taskId},#{item.taskPersonId}
			</foreach>)
		</if>
   	</sql>
 	
 	<!-- 任务详情表 信息查询 -->
	<select id="querySacOnetaskDetail" resultType="map">
		select 
		<![CDATA[CASE 
		WHEN str_to_date( BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( BUSS_TIME, '%Y-%m-%d %H:%i:%s' ) THEN '是' 
		WHEN ifnull(BUSS_TIME,'')='' AND str_to_date( BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( NOW( ), '%Y-%m-%d %H:%i:%s' ) THEN '是'
		ELSE '否' END IS_BEYOND,]]>
	    <include refid="Base_Column_List"></include>
	    from t_sac_onetask_detail
	    where 1=1
	    <include refid="where_condition"></include>
	    <if test="param.isBeyond !=null and param.isBeyond !=''">
	    	<![CDATA[AND (CASE 
				WHEN str_to_date( BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( BUSS_TIME, '%Y-%m-%d %H:%i:%s' ) THEN '1' 
				WHEN  ifnull(BUSS_TIME,'')=''  AND str_to_date( BUSS_END_TIME, '%Y-%m-%d %H:%i:%s' ) < str_to_date( NOW( ), '%Y-%m-%d %H:%i:%s' ) THEN '1' 
					ELSE '0' END) = #{param.isBeyond}]]>
	    </if>
	</select>
	
	<!-- 任务详情表 信息删除（物理删除） -->
	<delete id="deleteSacOnetaskDetail">
		DELETE 
		FROM
			t_sac_onetask_detail
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 任务详情表 信息新增 -->
	<insert id="createSacOnetaskDetail">
		insert into t_sac_onetask_detail(<include refid="Base_Column_List"></include>)
		value(
        	#{param.detailId},
			#{param.taskId},
			#{param.taskPersonId},
			#{param.taskPersonCode},
			#{param.taskPersonName},
			#{param.taskPersonDlrCode},
			#{param.taskPersonDlrName},
			#{param.areaCode},
			#{param.areaName},
			#{param.custId},
			#{param.custName},
			#{param.serverOrder},
			#{param.reviewId},
			#{param.stateCode},
			#{param.stateName},
			#{param.remark},
			#{param.bussTime},
			#{param.bussStartTime},
			#{param.bussEndTime},
			#{param.filePath},
			#{param.sendType},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 任务详情表 信息更新 -->
	<update id="updateSacOnetaskDetail">
		update t_sac_onetask_detail  set 
			<!-- 更新列表 -->
			<if test="param.taskId !=null and param.taskId !=''">TASK_ID=#{param.taskId},</if>
			<if test="param.taskPersonId !=null and param.taskPersonId !=''">TASK_PERSON_ID=#{param.taskPersonId},</if>
			<if test="param.taskPersonCode !=null and param.taskPersonCode !=''">TASK_PERSON_CODE=#{param.taskPersonCode},</if>
			<if test="param.taskPersonName !=null and param.taskPersonName !=''">TASK_PERSON_NAME=#{param.taskPersonName},</if>
			<if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode},</if>
			<if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName},</if>
			<if test="param.areaCode !=null and param.areaCode !=''">AREA_CODE=#{param.areaCode},</if>
			<if test="param.areaName !=null and param.areaName !=''">AREA_NAME=#{param.areaName},</if>
			<if test="param.custId !=null and param.custId !=''">CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''">SERVER_ORDER=#{param.serverOrder},</if>
			<if test="param.reviewId !=null and param.reviewId !=''">REVIEW_ID=#{param.reviewId},</if>
			<if test="param.stateCode !=null and param.stateCode !=''">STATE_CODE=#{param.stateCode},</if>
			<if test="param.stateName !=null and param.stateName !=''">STATE_NAME=#{param.stateName},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.bussTime !=null and param.bussTime !=''">BUSS_TIME=#{param.bussTime},</if>
			<if test="param.bussStartTime !=null">BUSS_START_TIME=#{param.bussStartTime},</if>
			<if test="param.bussEndTime !=null">BUSS_END_TIME=#{param.bussEndTime},</if>
			<if test="param.filePath !=null and param.filePath !=''">FILE_PATH=#{param.filePath},</if>
			<if test="param.sendType !=null and param.sendType !=''">SEND_TYPE=#{param.sendType},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.detailId !=null and param.detailId !=''">and DETAIL_ID=#{param.detailId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
	
	<!-- 根据任务ID更新任务详情 -->
	<update id="updateSacOnetaskDetailByTaskId">
		update t_sac_onetask_detail set 
			<if test="param.taskPersonId !=null and param.taskPersonId !=''">TASK_PERSON_ID=#{param.taskPersonId},</if>
			<if test="param.taskPersonCode !=null and param.taskPersonCode !=''">TASK_PERSON_CODE=#{param.taskPersonCode},</if>
			<if test="param.taskPersonName !=null and param.taskPersonName !=''">TASK_PERSON_NAME=#{param.taskPersonName},</if>
			<if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode},</if>
			<if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName},</if>
			<if test="param.areaCode !=null and param.areaCode !=''">AREA_CODE=#{param.areaCode},</if>
			<if test="param.areaName !=null and param.areaName !=''">AREA_NAME=#{param.areaName},</if>
			<if test="param.custId !=null and param.custId !=''">CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''">SERVER_ORDER=#{param.serverOrder},</if>
			<if test="param.reviewId !=null and param.reviewId !=''">REVIEW_ID=#{param.reviewId},</if>
			<if test="param.stateCode !=null and param.stateCode !=''">STATE_CODE=#{param.stateCode},</if>
			<if test="param.stateName !=null and param.stateName !=''">STATE_NAME=#{param.stateName},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.bussTime !=null and param.bussTime !=''">BUSS_TIME=#{param.bussTime},</if>
			<if test="param.bussStartTime !=null">BUSS_START_TIME=#{param.bussStartTime},</if>
			<if test="param.bussEndTime !=null">BUSS_END_TIME=#{param.bussEndTime},</if>
			<if test="param.filePath !=null and param.filePath !=''">FILE_PATH=#{param.filePath},</if>
			<if test="param.sendType !=null and param.sendType !=''">SEND_TYPE=#{param.sendType},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			LAST_UPDATED_DATE=now()
			where 1=1
			<!-- 描述条件 -->
			<if test="param.taskId !=null and param.taskId !=''">AND TASK_ID=#{param.taskId}</if>
			<if test="param.taskPersonId != null and param.taskPersonId != ''">
				AND TASK_PERSON_ID = #{param.taskPersonId}
			</if>
	</update>
	
	<!-- 查询任务关联的任务详情是否已被跟进 -->
	<select id="selectSacOnetaskInfoDResume" parameterType="map" resultType="map">
		SELECT * FROM t_sac_onetask_detail T
		WHERE T.IS_ENABLE = '1'	
			AND T.TASK_PERSON_ID = #{param.userId}
			AND T.TASK_ID = #{param.taskId}
			AND T.STATE_CODE = '0' <!-- 0-未完成，1-已完成，2-已取消 -->
		LIMIT 0, 1
	</select>
	
	<!-- 任务详情表 信息已取消 -->
	<update id="updateDetailCancel">
		update t_sac_onetask_detail  set 
			<!-- 更新列表 -->
			STATE_CODE=#{param.stateCode},
			STATE_NAME=#{param.stateName},
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.taskId !=null and param.taskId !=''">and TASK_ID=#{param.taskId}</if>
			<if test="param.stateCodeConditions !=null and param.stateCodeConditions !=''">and STATE_CODE=#{param.stateCodeConditions}</if>
	</update>

    <select id="queryAreaInfo" resultType="com.ly.adp.csc.entities.dto.AreaInfoDTO">
		SELECT
			c.dlr_code,
			i.area_code,
			i.area_name
		FROM
			mp.t_usc_area_info i
				LEFT JOIN mp.t_usc_area_relation r ON r.area_id = i.area_id
				LEFT JOIN mp.t_usc_mdm_org_dlr c ON c.province_id = r.rel_obj_id
		WHERE
			c.dlr_code in
			<foreach item="item" collection="list" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
    </select>

    <insert id="batchInsert">
		insert into t_sac_onetask_detail(<include refid="Base_Column_List"></include>)
		value
		<foreach collection="list" separator="," item="param">
			(
			#{param.detailId},
			#{param.taskId},
			#{param.taskPersonId},
			#{param.taskPersonCode},
			#{param.taskPersonName},
			#{param.taskPersonDlrCode},
			#{param.taskPersonDlrName},
			#{param.areaCode},
			#{param.areaName},
			#{param.custId},
			#{param.custName},
			#{param.serverOrder},
			#{param.reviewId},
			#{param.stateCode},
			#{param.stateName},
			#{param.remark},
			#{param.bussTime},
			#{param.bussStartTime},
			#{param.bussEndTime},
			#{param.filePath},
			#{param.sendType},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
			)
		</foreach>
	</insert>

    <update id="batchUpdate">
		<foreach collection="list" item="param" separator=";">
			update t_sac_onetask_detail set
			<!-- 更新列表 -->
			<if test="param.taskId !=null and param.taskId !=''">TASK_ID=#{param.taskId},</if>
			<if test="param.taskPersonId !=null and param.taskPersonId !=''">TASK_PERSON_ID=#{param.taskPersonId},</if>
			<if test="param.taskPersonCode !=null and param.taskPersonCode !=''">TASK_PERSON_CODE=#{param.taskPersonCode},</if>
			<if test="param.taskPersonName !=null and param.taskPersonName !=''">TASK_PERSON_NAME=#{param.taskPersonName},</if>
			<if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode},</if>
			<if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName},</if>
			<if test="param.areaCode !=null and param.areaCode !=''">AREA_CODE=#{param.areaCode},</if>
			<if test="param.areaName !=null and param.areaName !=''">AREA_NAME=#{param.areaName},</if>
			<if test="param.custId !=null and param.custId !=''">CUST_ID=#{param.custId},</if>
			<if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''">SERVER_ORDER=#{param.serverOrder},</if>
			<if test="param.reviewId !=null and param.reviewId !=''">REVIEW_ID=#{param.reviewId},</if>
			<if test="param.stateCode !=null and param.stateCode !=''">STATE_CODE=#{param.stateCode},</if>
			<if test="param.stateName !=null and param.stateName !=''">STATE_NAME=#{param.stateName},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.bussTime !=null and param.bussTime !=''">BUSS_TIME=#{param.bussTime},</if>
			<if test="param.bussStartTime !=null">BUSS_START_TIME=#{param.bussStartTime},</if>
			<if test="param.bussEndTime !=null">BUSS_END_TIME=#{param.bussEndTime},</if>
			<if test="param.filePath !=null and param.filePath !=''">FILE_PATH=#{param.filePath},</if>
			<if test="param.sendType !=null and param.sendType !=''">SEND_TYPE=#{param.sendType},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTEND_JSON=#{param.extendsJson},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">_MYCAT_OP_TIME=#{param.mycatOpTime},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1
			<!-- 描述条件 -->
			<if test="param.detailId !=null and param.detailId !=''">and DETAIL_ID=#{param.detailId}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		</foreach>
	</update>
</mapper>
