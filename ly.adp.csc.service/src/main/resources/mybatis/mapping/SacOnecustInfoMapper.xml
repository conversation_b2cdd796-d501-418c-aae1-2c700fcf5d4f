<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnecustInfoMapper">
	<!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnecustInfo">
        <id column="CUST_ID" property="custId" />
        <result column="SMART_ID" property="smartId" />
        <result column="CUST_NAME" property="custName" />
        <result column="GENDER_CODE" property="genderCode" />
        <result column="GENDER_NAME" property="genderName" />
        <result column="NICK_NAME" property="nickName" />
        <result column="PHONE" property="phone" />
		<result column="PHONE_STANDBY" property="phoneStandby" />
        <result column="EMAIL" property="email" />
        <result column="WECHAT" property="wechat" />
        <result column="ID_CARD" property="idCard" />
        <result column="DRIVER_CARD" property="driverCard" />
        <result column="DRIVING_LICENSE" property="drivingLicense" />
        <result column="PASSPORT" property="passport" />
		<result column="source5" property="source5" />
		<result column="source6" property="source6" />
		<result column="attr83" property="attr83" />
        <result column="COMPLAINTS_LABEL" property="complaintsLabel" />
        <result column="FREE_LABEL" property="freeLabel" />
        <result column="USER_HOBBIES_CODE" property="userHobbiesCode" />
        <result column="USER_HOBBIES_NAME" property="userHobbiesName" />
        <result column="CHARACTERISTICS_CODE" property="characteristicsCode" />
        <result column="CHARACTERISTICS_NAME" property="characteristicsName" />
        <result column="ONE_CUST_SOURCE" property="oneCustSource" />
        <result column="EXTEND_JSON" property="extendJson" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CUST_ID, SMART_ID, CUST_NAME, GENDER_CODE, GENDER_NAME, NICK_NAME, PHONE, PHONE_STANDBY, EMAIL, WECHAT, ID_CARD, DRIVER_CARD, DRIVING_LICENSE, PASSPORT,source5,source6,attr83, COMPLAINTS_LABEL, FREE_LABEL, USER_HOBBIES_CODE, USER_HOBBIES_NAME, CHARACTERISTICS_CODE, CHARACTERISTICS_NAME, ONE_CUST_SOURCE, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>

	<sql id="Base_Column_List_SpecifyFields">
        SMART_ID, PHONE
    </sql>

    <!-- where语句条件过滤 -->
	<sql id="where_condition">
	    <if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
	    <if test="param.smartId !=null and param.smartId !=''">and SMART_ID=#{param.smartId}</if>
	    <if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
	    <if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
	    <if test="param.custNamePaste !=null and param.custNamePaste !=''">and INSTR(CUST_NAME,#{param.custNamePaste})>0</if>
	    <if test="param.phonePaste !=null and param.phonePaste !=''">and INSTR(PHONE,#{param.phonePaste})>0</if>
	</sql>

    <!-- 客户信息查询 -->
	<select id="sacOnecustInfoFindInfo" resultType="map">
		select
		INSERT(PHONE,4,4,'****') as TMD_PHONE,
		(CASE JSON_UNQUOTE(JSON_EXTRACT(EXTEND_JSON,'$.buyBudget'))
		WHEN '1' THEN '15~20'
		WHEN '2' THEN '20~25'
		WHEN '3' THEN '25~30'
		WHEN '4' THEN '30~'
		ELSE '~15' END) purchasePower,
		CLUE_SOURCE,
		LOCATION as location,
		USER_MATURITY,
		ESTIMATE_TIME,
		COMPETITOR_TYPE,
		REVIEW_RECORD,
		CLUE_SOURCE_CN,
		LOCATION_CN,
		USER_MATURITY_CN,
		ESTIMATE_TIME_CN,
		COMPETITOR_TYPE_CN,
		REVIEW_RECORD_CN,
	    <include refid="Base_Column_List"></include>
	    from csc.t_sac_onecust_info
	    where 1=1
	    <include refid="where_condition"></include>
		<choose>
		<when test="param.column != null and '' != param.column and param.sorting != null and '' != param.sorting">
		order by ${#{param.column}} ${param.sorting}
		</when>
		<otherwise>ORDER BY CREATED_DATE DESC</otherwise>
		</choose>
	</select>

	<select id="sacOnecustInfoFindInfoNoPage" resultType="map">
		select
		INSERT(PHONE,4,4,'****') as TMD_PHONE,
		(CASE JSON_UNQUOTE(JSON_EXTRACT(EXTEND_JSON,'$.buyBudget'))
		WHEN '1' THEN '15~20'
		WHEN '2' THEN '20~25'
		WHEN '3' THEN '25~30'
		WHEN '4' THEN '30~'
		ELSE '~15' END) purchasePower,
		CLUE_SOURCE,
		LOCATION as location,
		USER_MATURITY,
		ESTIMATE_TIME,
		COMPETITOR_TYPE,
		REVIEW_RECORD,
		CLUE_SOURCE_CN,
		LOCATION_CN,
		USER_MATURITY_CN,
		ESTIMATE_TIME_CN,
		COMPETITOR_TYPE_CN,
		REVIEW_RECORD_CN,
	    <include refid="Base_Column_List"></include>
	    from csc.t_sac_onecust_info
	    where 1=1
	    <include refid="where_condition"></include>
		<choose>
		<when test="param.column != null and '' != param.column and param.sorting != null and '' != param.sorting">
		order by ${#{param.column}} ${param.sorting}
		</when>
		<otherwise>ORDER BY CREATED_DATE DESC</otherwise>
		</choose>
	</select>

	<select id="sacOnecustInfoFindInfoSpecifyFields" resultType="map">
		select
	    <include refid="Base_Column_List_SpecifyFields"></include>
	    from csc.t_sac_onecust_info
	    where 1=1
	    <include refid="where_condition"></include>
		<choose>
		<when test="param.column != null and '' != param.column and param.sorting != null and '' != param.sorting">
		order by ${#{param.column}} ${param.sorting}
		</when>
		<otherwise>ORDER BY CREATED_DATE DESC</otherwise>
		</choose>
	</select>

	<select id="sacOnecustInfoFindCount" resultType="int">
		select
		count(*)
		from csc.t_sac_onecust_info
		where 1=1
		<include refid="where_condition"></include>
	</select>

<!--	<choose>-->
<!--		<when test="param.column != null and '' != param.column and param.sorting != null and '' != param.sorting">-->
<!--			order by ${#{param.column}} ${param.sorting}-->
<!--		</when>-->
<!--		<otherwise>ORDER BY CREATED_DATE DESC</otherwise>-->
<!--	</choose>-->

	<!-- clue 更新 -->
	<update id="updatedlePhone" parameterType="java.util.Map">
		update ${dbName}.t_sac_clue_info_dlr set PHONE=#{phones} where PHONE=#{phone}
	</update>
	<update id="updateReviewPhone" parameterType="java.util.Map">
		update t_sac_review set PHONE=#{phones} where PHONE=#{phone}
	</update>
	<update id="updateOnecustInfoPhone" parameterType="java.util.Map">
		update t_sac_onecust_info set PHONE=#{phones} where PHONE=#{phone}
	</update>
	<!-- 客户信息查询 -->
	<select id="custCount" resultType="int">
		select
		COUNT(1)
		from t_sac_onecust_info
		where 1=1
		<if test="param.smartId !=null and param.smartId !=''">and SMART_ID=#{param.smartId}</if>
	</select>

	<!-- 客户信息更新 -->
	<update id="updateOnecustInfo">
	    update t_sac_onecust_info  set
	    	MODIFIER=#{param.modifier},
	    	MODIFY_NAME=#{param.modifyName},
	    	LAST_UPDATED_DATE=sysdate(),
		<if test = "param.clueSource != null and param.clueSource !='' "> CLUE_SOURCE = #{param.clueSource},</if>
		<if test = "param.clueSourceCn != null and param.clueSourceCn !='' "> CLUE_SOURCE_CN = #{param.clueSourceCn},</if>
		<if test = "param.location != null and param.location !='' "> LOCATION= #{param.location},</if>
		<if test = "param.locationCn != null and param.locationCn !='' "> LOCATION_CN = #{param.locationCn},</if>
		<if test = "param.userMaturity != null and param.userMaturity !='' "> USER_MATURITY = #{param.userMaturity},</if>
		<if test = "param.userMaturityCn != null and param.userMaturityCn !='' "> USER_MATURITY_CN = #{param.userMaturityCn},</if>
		<if test = "param.estimateTime != null and param.estimateTime !='' "> ESTIMATE_TIME = #{param.estimateTime},</if>
		<if test = "param.estimateTimeCn != null and param.estimateTimeCn !='' "> ESTIMATE_TIME_CN = #{param.estimateTimeCn},</if>
		<if test = "param.competitorType != null and param.competitorType !='' "> COMPETITOR_TYPE = #{param.competitorType},</if>
		<if test = "param.competitorTypeCn != null and param.competitorTypeCn !='' "> COMPETITOR_TYPE_CN = #{param.competitorTypeCn},</if>
		<if test = "param.reviewRecord != null and param.reviewRecord !='' "> REVIEW_RECORD = #{param.reviewRecord},</if>
		<if test = "param.reviewRecordCn != null and param.reviewRecordCn !='' "> REVIEW_RECORD_CN = #{param.reviewRecordCn},</if>

	       	<if test = "param.smartId != null and param.smartId !='' "> SMART_ID = #{param.smartId},</if>
			<if test = "param.custName != null and param.custName !='' " > CUST_NAME = #{param.custName},</if>
			<if test = "param.phoneStandby != null and param.phoneStandby !='' " > PHONE_STANDBY = #{param.phoneStandby},</if>
			<if test = "param.genderCode != null and param.genderCode !='' "> GENDER_CODE = #{param.genderCode},</if>
			<if test = "param.genderName != null and param.genderName !='' "> GENDER_NAME = #{param.genderName},</if>
			<if test = "param.nickName != null and param.nickName !='' "> NICK_NAME = #{param.nickName},</if>
			<if test = "param.email != null and param.email !='' "> EMAIL = #{param.email},</if>
			<if test = "param.wechat != null and param.wechat !='' "> WECHAT = #{param.wechat},</if>
			<if test = "param.idCard != null and param.idCard !='' "> ID_CARD = #{param.idCard},</if>
			<if test = "param.driverCard != null and param.driverCard !='' "> DRIVER_CARD = #{param.driverCard},</if>
			<if test = "param.drivingLicense != null and param.drivingLicense !='' "> DRIVING_LICENSE = #{param.drivingLicense},</if>
			<if test = "param.passport != null and param.passport !='' "> PASSPORT = #{param.passport},</if>
			<if test = "param.source5 != null and param.source5 !='' "> source5 = #{param.source5},</if>
			<if test = "param.source6 != null and param.source6 !='' "> source6 = #{param.source6},</if>
			<if test = "param.attr83 != null and param.attr83 !='' "> attr83 = #{param.attr83},</if>
			<if test = "param.complaintsLabel != null and param.complaintsLabel !='' "> COMPLAINTS_LABEL = #{param.complaintsLabel},</if>
			<if test = "param.freeLabel !=null and param.freeLabel !=''">FREE_LABEL=#{param.freeLabel},</if>
			<if test = "param.userHobbiesCode !=null and param.userHobbiesCode !=''">USER_HOBBIES_CODE=#{param.userHobbiesCode},</if>
			<if test = "param.userHobbiesName !=null and param.userHobbiesName !=''">USER_HOBBIES_NAME=#{param.userHobbiesName},</if>
			<if test = "param.characteristicsCode !=null and param.characteristicsCode !=''">CHARACTERISTICS_CODE=#{param.characteristicsCode},</if>
			<if test = "param.characteristicsName !=null and param.characteristicsName !=''">CHARACTERISTICS_NAME=#{param.characteristicsName},</if>
			<if test = "param.oneCustSource != null and param.oneCustSource !='' "> ONE_CUST_SOURCE = #{param.oneCustSource},</if>
			<if test = "param.extendsJson != null and param.extendsJson !='' "> EXTEND_JSON = #{param.extendsJson},</if>
			<if test = "param.column1 != null and param.column1 !='' "> COLUMN1 = #{param.column1},</if>
			<if test = "param.column2 != null and param.column2 !='' "> COLUMN2 = #{param.column2},</if>
			<if test = "param.column3 != null and param.column3 !='' "> COLUMN3 = #{param.column3},</if>
			<if test = "param.column4 != null and param.column4 !='' "> COLUMN4 = #{param.column4},</if>
			<if test = "param.column5 != null and param.column5 !='' "> COLUMN5 = #{param.column5},</if>
			<if test = "param.column6 != null and param.column6 !='' "> COLUMN6 = #{param.column6},</if>
			<if test = "param.column7 != null and param.column7 !='' "> COLUMN7 = #{param.column7},</if>
			<if test = "param.column8 != null and param.column8 !='' "> COLUMN8 = #{param.column8},</if>
			<if test = "param.column9 != null and param.column9 !='' "> COLUMN9 = #{param.column9},</if>
			<if test = "param.column10 != null and param.column10 !='' "> COLUMN10 = #{param.column10},</if>
		    UPDATE_CONTROL_ID=uuid()
	        where
		     CUST_ID = #{param.custId}
	        <if test = 'param.updateControlId!=null'>
	        AND UPDATE_CONTROL_ID = #{param.updateControlId}
	        </if>
	</update>

	<update id="updateCustNameForReview">
		update csc.t_sac_review set
	    	CUST_NAME = #{param.custName},
	    	LAST_UPDATED_DATE = now(),
	    	MODIFIER=#{param.modifier},
	    	MODIFY_NAME=#{param.modifyName}
		where
			CUST_ID = #{param.custId}
	</update>
	<update id="updateCustNameForDrive">
		update csc.t_sac_test_drive_sheet set
		    CUSTOMER_NAME = #{param.custName},
		    LAST_UPDATED_DATE=now(),
		    MODIFIER=#{param.modifier},
		    MODIFY_NAME=#{param.modifyName}
		where
			CUSTOMER_ID = #{param.custId}
	</update>

	<!-- 客户信息新增 -->
	<insert id="createOnecustInfo">
	    INSERT INTO `t_sac_onecust_info` (
		`CUST_ID`,
		`SMART_ID`,
		`CUST_NAME`,
		`GENDER_CODE`,
		`GENDER_NAME`,
		`NICK_NAME`,
		`PHONE`,
		`PHONE_STANDBY`,
		`EMAIL`,
		`WECHAT`,
		`ID_CARD`,
		`DRIVER_CARD`,
		`DRIVING_LICENSE`,
		`PASSPORT`,
		`source5`,
		`source6`,
		`attr83`,
		`COMPLAINTS_LABEL`,
		`FREE_LABEL`,
		`USER_HOBBIES_CODE`,
		`USER_HOBBIES_NAME`,
		`CHARACTERISTICS_CODE`,
		`CHARACTERISTICS_NAME`,
		`ONE_CUST_SOURCE`,
		`EXTEND_JSON`,
		`COLUMN1`,
		`COLUMN2`,
		`COLUMN3`,
		`COLUMN4`,
		`COLUMN5`,
		`COLUMN6`,
		`COLUMN7`,
		`COLUMN8`,
		`COLUMN9`,
		`COLUMN10`,
		`OEM_ID`,
		`GROUP_ID`,
		`OEM_CODE`,
		`GROUP_CODE`,
		`CREATOR`,
		`CREATED_NAME`,
		`CREATED_DATE`,
		`MODIFIER`,
		`MODIFY_NAME`,
		`LAST_UPDATED_DATE`,
		`IS_ENABLE`,
		`SDP_USER_ID`,
		`SDP_ORG_ID`,
		`UPDATE_CONTROL_ID`,
		CLUE_SOURCE,
		CLUE_SOURCE_CN,
		LOCATION,
		LOCATION_CN,
		USER_MATURITY,
		USER_MATURITY_CN,
		ESTIMATE_TIME,
		ESTIMATE_TIME_CN,
		COMPETITOR_TYPE,
		COMPETITOR_TYPE_CN,
		REVIEW_RECORD,
		REVIEW_RECORD_CN

	)
	VALUES
		(
			#{param.custId},
			#{param.smartId},
			#{param.custName},
			#{param.genderCode},
			#{param.genderName},
			#{param.nickName},
			#{param.phone},
			#{param.phoneStandby},
			#{param.email},
			#{param.wechat},
			#{param.idCard},
			#{param.driverCard},
			#{param.drivingLicense},
			#{param.passport},
			#{param.source5},
			#{param.source6},
			#{param.attr83},
			#{param.complaintsLabel},
			#{param.freeLabel},
			#{param.userHobbiesCode},
			#{param.userHobbiesName},
			#{param.characteristicsCode},
			#{param.characteristicsName},
			#{param.oneCustSource},
			#{param.extendsJson},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
		    #{param.column6},
		    #{param.column7},
			#{param.column8},
		    #{param.column9},
		    #{param.column10},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
	        #{param.creator},
	        #{param.createdName},
	        now(),
	        #{param.modifier},
	        #{param.modifyName},
	        now(),
	        '1',
	        '1',
	        '1',
	        uuid(),
		#{param.clueSource},
		#{param.clueSourcCn},
		#{param.location},
		#{param.locationCn},
		#{param.userMaturity},
		#{param.userMaturityCn},
		#{param.estimateTime},
		#{param.estimateTimeCn},
		#{param.competitorType},
		#{param.competitorTypeCn},
		#{param.reviewRecord},
		#{param.reviewRecordCn}
		)
	</insert>
	<!-- 客户信息更新 -->
	<update id="updateOnecustInfo1">
		update t_sac_onecust_info  set
		MODIFIER=#{param.modifier},
		MODIFY_NAME=#{param.modifyName},
		LAST_UPDATED_DATE=sysdate(),
		<if test = "param.smartId != null and param.smartId !='' "> SMART_ID = #{param.smartId},</if>

		<if test = "param.phoneStandby != null and param.phoneStandby !='' " > PHONE_STANDBY = #{param.phoneStandby},</if>
		<if test = "param.phone != null and param.phone !='' " > PHONE = #{param.phone},</if>
		<if test = "param.genderCode != null and param.genderCode !='' "> GENDER_CODE = #{param.genderCode},</if>
		<if test = "param.genderName != null and param.genderName !='' "> GENDER_NAME = #{param.genderName},</if>
		<if test = "param.nickName != null and param.nickName !='' "> NICK_NAME = #{param.nickName},</if>
		<if test = "param.email != null and param.email !='' "> EMAIL = #{param.email},</if>
		<if test = "param.wechat != null and param.wechat !='' "> WECHAT = #{param.wechat},</if>
		<if test = "param.idCard != null and param.idCard !='' "> ID_CARD = #{param.idCard},</if>
		<if test = "param.driverCard != null and param.driverCard !='' "> DRIVER_CARD = #{param.driverCard},</if>
		<if test = "param.drivingLicense != null and param.drivingLicense !='' "> DRIVING_LICENSE = #{param.drivingLicense},</if>
		<if test = "param.passport != null and param.passport !='' "> PASSPORT = #{param.passport},</if>
		<if test = "param.source5 != null and param.source5 !='' "> source5 = #{param.source5},</if>
		<if test = "param.source6 != null and param.source6 !='' "> source6 = #{param.source6},</if>
		<if test = "param.attr83 != null and param.attr83 !='' "> attr83 = #{param.attr83},</if>
		<if test = "param.complaintsLabel != null and param.complaintsLabel !='' "> COMPLAINTS_LABEL = #{param.complaintsLabel},</if>
		<if test = "param.freeLabel !=null and param.freeLabel !=''">FREE_LABEL=#{param.freeLabel},</if>
		<if test = "param.userHobbiesCode !=null and param.userHobbiesCode !=''">USER_HOBBIES_CODE=#{param.userHobbiesCode},</if>
		<if test = "param.userHobbiesName !=null and param.userHobbiesName !=''">USER_HOBBIES_NAME=#{param.userHobbiesName},</if>
		<if test = "param.characteristicsCode !=null and param.characteristicsCode !=''">CHARACTERISTICS_CODE=#{param.characteristicsCode},</if>
		<if test = "param.characteristicsName !=null and param.characteristicsName !=''">CHARACTERISTICS_NAME=#{param.characteristicsName},</if>
		<if test = "param.oneCustSource != null and param.oneCustSource !='' "> ONE_CUST_SOURCE = #{param.oneCustSource},</if>
		<if test = "param.extendsJson != null and param.extendsJson !='' "> EXTEND_JSON = #{param.extendsJson},</if>
		<if test = "param.column1 != null and param.column1 !='' "> COLUMN1 = #{param.column1},</if>
		<if test = "param.column2 != null and param.column2 !='' "> COLUMN2 = #{param.column2},</if>
		<if test = "param.column3 != null and param.column3 !='' "> COLUMN3 = #{param.column3},</if>
		<if test = "param.column4 != null and param.column4 !='' "> COLUMN4 = #{param.column4},</if>
		<if test = "param.column5 != null and param.column5 !='' "> COLUMN5 = #{param.column5},</if>
		<if test = "param.column6 != null and param.column6 !='' "> COLUMN6 = #{param.column6},</if>
		<if test = "param.column7 != null and param.column7 !='' "> COLUMN7 = #{param.column7},</if>
		<if test = "param.column8 != null and param.column8 !='' "> COLUMN8 = #{param.column8},</if>
		<if test = "param.column9 != null and param.column9 !='' "> COLUMN9 = #{param.column9},</if>
		<if test = "param.column10 != null and param.column10 !='' "> COLUMN10 = #{param.column10},</if>
		UPDATE_CONTROL_ID=uuid()
		where 1=1
		AND SMART_ID =#{param.smartId}
		<if test = "param.updateControlId != null">
			AND UPDATE_CONTROL_ID =#{param.updateControlId}
		</if>
	</update>
	<update id="updateOnecustInfo2">
		update t_sac_onecust_info  set
		MODIFIER=#{param.modifier},
		MODIFY_NAME=#{param.modifyName},
		LAST_UPDATED_DATE=sysdate(),
		<if test = "param.smartId != null and param.smartId !='' "> SMART_ID = #{param.smartId},</if>

		<if test = "param.phoneStandby != null and param.phoneStandby !='' " > PHONE_STANDBY = #{param.phoneStandby},</if>
		<if test = "param.phone != null and param.phone !='' " > PHONE = #{param.phone},</if>
		<if test = "param.genderCode != null and param.genderCode !='' "> GENDER_CODE = #{param.genderCode},</if>
		<if test = "param.genderName != null and param.genderName !='' "> GENDER_NAME = #{param.genderName},</if>
		<if test = "param.nickName != null and param.nickName !='' "> NICK_NAME = #{param.nickName},</if>
		<if test = "param.email != null and param.email !='' "> EMAIL = #{param.email},</if>
		<if test = "param.wechat != null and param.wechat !='' "> WECHAT = #{param.wechat},</if>
		<if test = "param.idCard != null and param.idCard !='' "> ID_CARD = #{param.idCard},</if>
		<if test = "param.driverCard != null and param.driverCard !='' "> DRIVER_CARD = #{param.driverCard},</if>
		<if test = "param.drivingLicense != null and param.drivingLicense !='' "> DRIVING_LICENSE = #{param.drivingLicense},</if>
		<if test = "param.passport != null and param.passport !='' "> PASSPORT = #{param.passport},</if>
		<if test = "param.source5 != null and param.source5 !='' "> source5 = #{param.source5},</if>
		<if test = "param.source6 != null and param.source6 !='' "> source6 = #{param.source6},</if>
		<if test = "param.attr83 != null and param.attr83 !='' "> attr83 = #{param.attr83},</if>
		<if test = "param.complaintsLabel != null and param.complaintsLabel !='' "> COMPLAINTS_LABEL = #{param.complaintsLabel},</if>
		<if test = "param.freeLabel !=null and param.freeLabel !=''">FREE_LABEL=#{param.freeLabel},</if>
		<if test = "param.userHobbiesCode !=null and param.userHobbiesCode !=''">USER_HOBBIES_CODE=#{param.userHobbiesCode},</if>
		<if test = "param.userHobbiesName !=null and param.userHobbiesName !=''">USER_HOBBIES_NAME=#{param.userHobbiesName},</if>
		<if test = "param.characteristicsCode !=null and param.characteristicsCode !=''">CHARACTERISTICS_CODE=#{param.characteristicsCode},</if>
		<if test = "param.characteristicsName !=null and param.characteristicsName !=''">CHARACTERISTICS_NAME=#{param.characteristicsName},</if>
		<if test = "param.oneCustSource != null and param.oneCustSource !='' "> ONE_CUST_SOURCE = #{param.oneCustSource},</if>
		<if test = "param.extendsJson != null and param.extendsJson !='' "> EXTEND_JSON = #{param.extendsJson},</if>
		<if test = "param.column1 != null and param.column1 !='' "> COLUMN1 = #{param.column1},</if>
		<if test = "param.column2 != null and param.column2 !='' "> COLUMN2 = #{param.column2},</if>
		<if test = "param.column3 != null and param.column3 !='' "> COLUMN3 = #{param.column3},</if>
		<if test = "param.column4 != null and param.column4 !='' "> COLUMN4 = #{param.column4},</if>
		<if test = "param.column5 != null and param.column5 !='' "> COLUMN5 = #{param.column5},</if>
		<if test = "param.column6 != null and param.column6 !='' "> COLUMN6 = #{param.column6},</if>
		<if test = "param.column7 != null and param.column7 !='' "> COLUMN7 = #{param.column7},</if>
		<if test = "param.column8 != null and param.column8 !='' "> COLUMN8 = #{param.column8},</if>
		<if test = "param.column9 != null and param.column9 !='' "> COLUMN9 = #{param.column9},</if>
		<if test = "param.column10 != null and param.column10 !='' "> COLUMN10 = #{param.column10},</if>
		UPDATE_CONTROL_ID=uuid()
		where 1=1
		AND SMART_ID = #{param.smartId}
	</update>
	<update id="updateOnecustInfo3">
		update t_sac_onecust_info  set
		MODIFIER=#{param.modifier},
		MODIFY_NAME=#{param.modifyName},
		LAST_UPDATED_DATE=sysdate(),
		<if test = "param.smartId != null and param.smartId !='' "> SMART_ID = #{param.smartId},</if>

		<if test = "param.clueSource != null and param.clueSource !='' "> CLUE_SOURCE = #{param.clueSource},</if>
		<if test = "param.location != null and param.location !='' "> LOCATION = #{param.location},</if>
		<if test = "param.userMaturity != null and param.userMaturity !='' "> USER_MATURITY = #{param.userMaturity},</if>
		<if test = "param.estimateTime != null and param.estimateTime !='' "> ESTIMATE_TIME = #{param.estimateTime},</if>
		<if test = "param.competitorType != null and param.competitorType !='' "> COMPETITOR_TYPE = #{param.competitorType},</if>
		<if test = "param.competitorModels != null and param.competitorModels !='' "> COMPETITOR_TYPE = #{param.competitorModels},</if>
		<if test = "param.reviewRecord != null and param.reviewRecord !='' "> REVIEW_RECORD = #{param.reviewRecord},</if>

		<if test = "param.phoneStandby != null and param.phoneStandby !='' " > PHONE_STANDBY = #{param.phoneStandby},</if>
		<if test = "param.phone != null and param.phone !='' " > PHONE = #{param.phone},</if>
		<if test = "param.genderCode != null and param.genderCode !='' "> GENDER_CODE = #{param.genderCode},</if>
		<if test = "param.genderName != null and param.genderName !='' "> GENDER_NAME = #{param.genderName},</if>
		<if test = "param.nickName != null and param.nickName !='' "> NICK_NAME = #{param.nickName},</if>
		<if test = "param.email != null and param.email !='' "> EMAIL = #{param.email},</if>
		<if test = "param.wechat != null and param.wechat !='' "> WECHAT = #{param.wechat},</if>
		<if test = "param.idCard != null and param.idCard !='' "> ID_CARD = #{param.idCard},</if>
		<if test = "param.driverCard != null and param.driverCard !='' "> DRIVER_CARD = #{param.driverCard},</if>
		<if test = "param.drivingLicense != null and param.drivingLicense !='' "> DRIVING_LICENSE = #{param.drivingLicense},</if>
		<if test = "param.passport != null and param.passport !='' "> PASSPORT = #{param.passport},</if>
		<if test = "param.source5 != null and param.source5 !='' "> source5 = #{param.source5},</if>
		<if test = "param.source6 != null and param.source6 !='' "> source6 = #{param.source6},</if>
		<if test = "param.attr83 != null and param.attr83 !='' "> attr83 = #{param.attr83},</if>
		<if test = "param.complaintsLabel != null and param.complaintsLabel !='' "> COMPLAINTS_LABEL = #{param.complaintsLabel},</if>
		<if test = "param.freeLabel !=null and param.freeLabel !=''">FREE_LABEL=#{param.freeLabel},</if>
		<if test = "param.userHobbiesCode !=null and param.userHobbiesCode !=''">USER_HOBBIES_CODE=#{param.userHobbiesCode},</if>
		<if test = "param.userHobbiesName !=null and param.userHobbiesName !=''">USER_HOBBIES_NAME=#{param.userHobbiesName},</if>
		<if test = "param.characteristicsCode !=null and param.characteristicsCode !=''">CHARACTERISTICS_CODE=#{param.characteristicsCode},</if>
		<if test = "param.characteristicsName !=null and param.characteristicsName !=''">CHARACTERISTICS_NAME=#{param.characteristicsName},</if>
		<if test = "param.oneCustSource != null and param.oneCustSource !='' "> ONE_CUST_SOURCE = #{param.oneCustSource},</if>
		<if test = "param.extendsJson != null and param.extendsJson !='' "> EXTEND_JSON = #{param.extendsJson},</if>
		<if test = "param.column1 != null and param.column1 !='' "> COLUMN1 = #{param.column1},</if>
		<if test = "param.column2 != null and param.column2 !='' "> COLUMN2 = #{param.column2},</if>
		<if test = "param.column3 != null and param.column3 !='' "> COLUMN3 = #{param.column3},</if>
		<if test = "param.column4 != null and param.column4 !='' "> COLUMN4 = #{param.column4},</if>
		<if test = "param.column5 != null and param.column5 !='' "> COLUMN5 = #{param.column5},</if>
		<if test = "param.column6 != null and param.column6 !='' "> COLUMN6 = #{param.column6},</if>
		<if test = "param.column7 != null and param.column7 !='' "> COLUMN7 = #{param.column7},</if>
		<if test = "param.column8 != null and param.column8 !='' "> COLUMN8 = #{param.column8},</if>
		<if test = "param.column9 != null and param.column9 !='' "> COLUMN9 = #{param.column9},</if>
		<if test = "param.column10 != null and param.column10 !='' "> COLUMN10 = #{param.column10},</if>
		UPDATE_CONTROL_ID=uuid()
		where 1=1
		AND PHONE = #{param.Aphone}
	</update>
	<update id="updateOnecustInfo4">
		update t_sac_onecust_info  set
		MODIFIER=#{param.modifier},
		MODIFY_NAME=#{param.modifyName},
		LAST_UPDATED_DATE=sysdate(),
		<if test = "param.smartId != null and param.smartId !='' "> SMART_ID = #{param.smartId},</if>

		<if test = "param.phoneStandby != null and param.phoneStandby !='' " > PHONE_STANDBY = #{param.phoneStandby},</if>
		<if test = "param.phone != null and param.phone !='' " > PHONE = #{param.phone},</if>
		<if test = "param.genderCode != null and param.genderCode !='' "> GENDER_CODE = #{param.genderCode},</if>
		<if test = "param.genderName != null and param.genderName !='' "> GENDER_NAME = #{param.genderName},</if>
		<if test = "param.nickName != null and param.nickName !='' "> NICK_NAME = #{param.nickName},</if>
		<if test = "param.email != null and param.email !='' "> EMAIL = #{param.email},</if>
		<if test = "param.wechat != null and param.wechat !='' "> WECHAT = #{param.wechat},</if>
		<if test = "param.idCard != null and param.idCard !='' "> ID_CARD = #{param.idCard},</if>
		<if test = "param.driverCard != null and param.driverCard !='' "> DRIVER_CARD = #{param.driverCard},</if>
		<if test = "param.drivingLicense != null and param.drivingLicense !='' "> DRIVING_LICENSE = #{param.drivingLicense},</if>
		<if test = "param.passport != null and param.passport !='' "> PASSPORT = #{param.passport},</if>
		<if test = "param.source5 != null and param.source5 !='' "> source5 = #{param.source5},</if>
		<if test = "param.source6 != null and param.source6 !='' "> source6 = #{param.source6},</if>
		<if test = "param.attr83 != null and param.attr83 !='' "> attr83 = #{param.attr83},</if>
		<if test = "param.complaintsLabel != null and param.complaintsLabel !='' "> COMPLAINTS_LABEL = #{param.complaintsLabel},</if>
		<if test = "param.freeLabel !=null and param.freeLabel !=''">FREE_LABEL=#{param.freeLabel},</if>
		<if test = "param.userHobbiesCode !=null and param.userHobbiesCode !=''">USER_HOBBIES_CODE=#{param.userHobbiesCode},</if>
		<if test = "param.userHobbiesName !=null and param.userHobbiesName !=''">USER_HOBBIES_NAME=#{param.userHobbiesName},</if>
		<if test = "param.characteristicsCode !=null and param.characteristicsCode !=''">CHARACTERISTICS_CODE=#{param.characteristicsCode},</if>
		<if test = "param.characteristicsName !=null and param.characteristicsName !=''">CHARACTERISTICS_NAME=#{param.characteristicsName},</if>
		<if test = "param.oneCustSource != null and param.oneCustSource !='' "> ONE_CUST_SOURCE = #{param.oneCustSource},</if>
		<if test = "param.extendsJson != null  "> EXTEND_JSON = #{param.extendsJson},</if>
		<if test = "param.column1 != null and param.column1 !='' "> COLUMN1 = #{param.column1},</if>
		<if test = "param.column2 != null and param.column2 !='' "> COLUMN2 = #{param.column2},</if>
		<if test = "param.column3 != null and param.column3 !='' "> COLUMN3 = #{param.column3},</if>
		<if test = "param.column4 != null and param.column4 !='' "> COLUMN4 = #{param.column4},</if>
		<if test = "param.column5 != null and param.column5 !='' "> COLUMN5 = #{param.column5},</if>
		<if test = "param.column6 != null and param.column6 !='' "> COLUMN6 = #{param.column6},</if>
		<if test = "param.column7 != null and param.column7 !='' "> COLUMN7 = #{param.column7},</if>
		<if test = "param.column8 != null and param.column8 !='' "> COLUMN8 = #{param.column8},</if>
		<if test = "param.column9 != null and param.column9 !='' "> COLUMN9 = #{param.column9},</if>
		<if test = "param.column10 != null and param.column10 !='' "> COLUMN10 = #{param.column10},</if>
		UPDATE_CONTROL_ID=uuid()
		where 1=1
		AND PHONE = #{param.phone}
	</update>
	<select id="selectMapping" resultType="java.util.Map">
		SELECT FILED_CODE FROM t_sac_field_mapping_config WHERE SOURCE_TABLE_CODE =#{tableName}
	</select>


	<!-- 客户信息查询 -->
	<select id="queryOneCustSupplementInfo" resultType="map">
		SELECT
			d.DLR_SHORT_NAME,
			(SELECT count(0) as num from t_sac_onecust_resume where CUST_ID=#{param.custId} and SENCE_CODE in ('2','16','19')) as arrivalNum, -- 进店次数
			(SELECT CREATED_DATE from t_sac_onecust_resume where CUST_ID=#{param.custId} and SENCE_CODE in ('2','16','19') order by CREATED_DATE desc limit 1) as arrivalTime, -- 到店时间
		(SELECT count(0) as num from t_sac_onecust_resume where CUST_ID=#{param.custId} and SENCE_CODE in ('1','14','17')
		and CREATED_DATE  <![CDATA[ <= ]]> NOW() and CREATED_DATE <![CDATA[ >= ]]>DATE_SUB(CURDATE(), INTERVAL 1 YEAR) order by CREATED_DATE desc ) as yearContactNum,  -- 近一年联系次数
			(SELECT count(0) as num from t_sac_onecust_resume where CUST_ID=#{param.custId} and SENCE_CODE in ('1','14','17') order by CREATED_DATE desc limit 1) as contactNum  -- 联系次数
		FROM
			t_sac_clue_info_dlr d
		WHERE
			d.cust_id=#{param.custId}
	</select>

    <select id="findinfoDlr" resultType="java.util.Map">
		select
			CUST_ID,
			CUST_NAME
		from
			t_sac_clue_info_dlr
		where
			PHONE=#{phone}
	</select>

    <select id="customerTagWhitelis" resultType="java.lang.Boolean">

		SELECT EXISTS(
					   SELECT 1 FROM
						   mp.t_prc_mds_lookup_value
					   WHERE
						   LOOKUP_TYPE_CODE = 'VE1118'
						 and LOOKUP_VALUE_CODE =#{param.dlrCode}

                )
	</select>

    <select id="queryClueLevel" resultType="java.util.Map">
		select
			a.CLUE_LEVEL ESTIMATE_TIME,
			b.LOOKUP_VALUE_NAME ESTIMATE_TIME_CN,
			a.LOCATION,
			c.LOOKUP_VALUE_NAME LOCATION_CN,
			a.COMPETITIVE_VEHICLE_CODE COMPETITOR_TYPE,
			d.LOOKUP_VALUE_NAME COMPETITOR_TYPE_CN,
			a.CLUE_SOURCE CLUE_SOURCE,
			e.LOOKUP_VALUE_NAME CLUE_SOURCE_CN
		from csc.t_sac_onecust_remark a
		left join mp.t_prc_mds_lookup_value b on a.CLUE_LEVEL = b.LOOKUP_VALUE_CODE and b.LOOKUP_TYPE_CODE='ADP_MKT_DealPrediction_1214'
		left join mp.t_prc_mds_lookup_value c on a.LOCATION = c.LOOKUP_VALUE_CODE and c.LOOKUP_TYPE_CODE='ADP_AREA_1214'
		left join mp.t_prc_mds_lookup_value d on a.COMPETITIVE_VEHICLE_CODE = d.LOOKUP_VALUE_CODE and d.LOOKUP_TYPE_CODE='ADP_MKT_Cvehicle_1214'
		left join mp.t_prc_mds_lookup_value e on a.CLUE_SOURCE = e.LOOKUP_VALUE_CODE and e.LOOKUP_TYPE_CODE='ADP_MKT_Source_1214'
		where a.cust_id = #{param.custId}
	</select>

    <update id="updateClueLevel">
		update
			csc.t_sac_onecust_remark
			set  CLUE_LEVEL=#{estimateTime},
				 LOCATION=#{location},
				 COMPETITIVE_VEHICLE_CODE=#{competitorType},
				 CLUE_SOURCE=#{clueSource},
				 UPDATE_DATE=now()
		where CUST_ID=#{custId}
	</update>

    <select id="findClueLevel" resultType="java.lang.Boolean">
		select exists(
					   SELECT 1 FROM
						   csc.t_sac_onecust_remark
					   WHERE
						   CUST_ID=#{custId}
                )
	</select>

    <insert id="insertClueLevel">
		insert into csc.t_sac_onecust_remark
		(
			CUST_ID,
			CLUE_LEVEL,
			LOCATION,
			COMPETITIVE_VEHICLE_CODE,
			CLUE_SOURCE,
			UPDATE_DATE
		)
		values
		(
			#{custId},
			#{estimateTime},
			#{location},
			#{competitorType},
			#{clueSource},
			now()
		)
	</insert>
	<select id="selectDlrName" resultType="java.util.Map">
		SELECT
			d.dlr_code as dlrCode,
			d.DLR_SHORT_NAME as dlrShortName,
			c1.agent_company_code as agentCompanyCode,
			c1.agent_company_name as agentCompanyName,
			c2.agent_code as agentCode,
			c2.agent_name as agentName,
			o.sale_order_code as saleOrderCode
		FROM
			orc.t_orc_ve_bu_sale_order_to_c o
				LEFT JOIN mp.t_usc_mdm_org_dlr d ON d.dlr_code = o.place_order_dlr_code
				LEFT JOIN mp.t_usc_mdm_agent_company c1 ON d.company_id = c1.agent_company_id
				LEFT JOIN mp.t_usc_mdm_agent_info c2 ON c1.agent_id = c2.agent_id
		WHERE
			o.sale_order_code in
		<foreach collection="param.listSaleOrderCode" index="i" item="item" open="(" separator=","  close=")">
			#{item}
		</foreach>
	</select>

	<!-- clue 删除-->
    <delete id="deleteClue">
		DELETE FROM ${param.dbName}.t_sac_clue_info_dlr WHERE PHONE=#{param.phone}
	</delete>

    <delete id="deleteReview">
		DELETE FROM t_sac_review WHERE PHONE=#{phone}
	</delete>
</mapper>