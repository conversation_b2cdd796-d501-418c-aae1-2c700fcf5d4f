<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.mp.acc.manage.idal.mapper.AccBuActivityCustomerMapper">

	<!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.acc.manage.entities.AccBuActivityCustomer">
        <id column="ACTIVITY_CUSTOMER_ID" property="activityCustomerId" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="CUSTOMER_NAME" property="customerName" />
        <result column="CUSTOMER_SEX_CODE" property="customerSexCode" />
        <result column="CUSTOMER_SEX_NAME" property="customerSexName" />
        <result column="CUSTOMER_PHONE" property="customerPhone" />
        <result column="CUSTOMER_BIRTH" property="customerBirth" />
        <result column="ACTIVITY_ID" property="activityId" />
        <result column="IS_CHECK_IN" property="isCheckIn" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ACTIVITY_CUSTOMER_ID, CUSTOMER_NAME, CUSTOMER_SEX_CODE, CUSTOMER_SEX_NAME, CUSTOMER_PHONE, CUSTOMER_BIRTH, ACTIVITY_ID, IS_CHECK_IN, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
	<!-- 查找sql -->
	<select id="selectAccBuActivityCustomer" resultType="java.util.Map">
		/*select * from (*/
		SELECT
		c.ACTIVITY_CUSTOMER_ID,
		c.CUSTOMER_ID,
		c.CUSTOMER_NAME,
		c.CUSTOMER_SEX_CODE,
		c.CUSTOMER_SEX_NAME,
		c.CUSTOMER_PHONE,
		c.CUSTOMER_BIRTH,
		c.ACTIVITY_ID,
		(case when a.CREATE_TYPE_CODE='DEVELOP' then '1' else c.IS_CHECK_IN end ) as IS_CHECK_IN,
		c.COLUMN1 ticketType,
		c.COLUMN2 ticketNum,
		c.COLUMN3 enorllChannel,
		c.COLUMN4 userIdentity,
		c.COLUMN5,
		(case when a.CREATE_TYPE_CODE='DEVELOP' then c.CREATED_DATE else FROM_UNIXTIME(c.apply_time/1000,'%Y-%m-%d %H:%i:%s') end ) as APPLY_TIME,
		(case when a.CREATE_TYPE_CODE='DEVELOP' then c.CREATED_DATE else FROM_UNIXTIME(c.SIGN_IN_TIME/1000,'%Y-%m-%d %H:%i:%s') end ) as SIGN_IN_TIME,
		c.OEM_ID,
		c.GROUP_ID,
		c.CREATOR,
		c.CREATED_NAME,
		c.CREATED_DATE,
		c.MODIFIER,
		c.MODIFY_NAME,
		c.LAST_UPDATED_DATE,
		c.IS_ENABLE,
		c.UPDATE_CONTROL_ID,
		case when c.IS_ADP_NEW_CLUE = 1 then '是' else '否' end as newCustFlagCn
		,a.CREATE_TYPE_CODE
		,c.sign_num
		FROM
		t_acc_bu_activity_customer  c
		left join t_acc_bu_activity a on a.ACTIVITY_ID=c.ACTIVITY_ID
		WHERE
		1 =1
		<if test="param.activityCustomerId !=null and param.activityCustomerId !=''">and c.ACTIVITY_CUSTOMER_ID=#{param.activityCustomerId}</if>
		<if test="param.customerName !=null and param.customerName !=''">and c.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerId !=null and param.customerId !=''">and c.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.customerSexCode !=null and param.customerSexCode !=''">and c.CUSTOMER_SEX_CODE=#{param.customerSexCode}</if>
		<if test="param.customerSexName !=null and param.customerSexName !=''">and c.CUSTOMER_SEX_NAME=#{param.customerSexName}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''">and c.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerBirth !=null and param.customerBirth !=''">and c.CUSTOMER_BIRTH=#{param.customerBirth}</if>
		<if test="param.activityId !=null and param.activityId !=''">and c.ACTIVITY_ID=#{param.activityId}</if>
		<if test="param.isCheckIn !=null and param.isCheckIn !=''">and c.IS_CHECK_IN=#{param.isCheckIn}</if>
		<if test="param.oemId !=null and param.oemId !=''">and c.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and c.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and c.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and c.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and c.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and c.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and c.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and c.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and c.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and c.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND c.sign_num = #{param.yhSignNum}
		  AND a.CREATE_TYPE_CODE != 'DEVELOP'
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND c.sign_num = #{param.dlSignNum}
			AND a.CREATE_TYPE_CODE = 'DEVELOP'
		</if>
		<!--) O where 1=1
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND O.yhSignNum = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND O.dlSignNum = #{param.dlSignNum}
		</if>-->
	</select>
	<!-- 插入sql -->
	<insert id="insertAccBuActivityCustomer" parameterType="java.util.List">
	insert into t_acc_bu_activity_customer(
	ACTIVITY_CUSTOMER_ID,
	CUSTOMER_ID,
	CUSTOMER_NAME,
	CUSTOMER_SEX_CODE,
	CUSTOMER_SEX_NAME,
	CUSTOMER_PHONE,
	CUSTOMER_BIRTH,
	ACTIVITY_ID,
	IS_CHECK_IN,
	COLUMN1,
	COLUMN2,
	COLUMN3,
	COLUMN4,
	COLUMN5,
	APPLY_TIME,
	OEM_ID,
	GROUP_ID,
	CREATOR,
	CREATED_NAME,
	CREATED_DATE,
	MODIFIER,
	MODIFY_NAME,
	LAST_UPDATED_DATE,
	UPDATE_CONTROL_ID,
	SIGN_NUM,
	IS_ENABLE,
	IS_ADP_NEW_CLUE
	) values
	<foreach collection="paramList" item="param" separator=",">
	(
			#{param.activityCustomerId},
			#{param.customerId},
			#{param.customerName},
			#{param.customerSexCode},
			#{param.customerSexName},
			#{param.customerPhone},
			#{param.customerBirth},
			#{param.activityId},
			#{param.isCheckIn},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.applyTime},
			#{param.oemId},
			#{param.groupId},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.updateControlId},
			#{param.signNum},
			#{param.isEnable},
			#{param.isAdpNewClue}
		)
		</foreach>
	</insert>
	<!-- 更新sql -->
	<update id="updateAccBuActivityCustomer" parameterType="java.util.List">
		<foreach collection="paramList" item="param" index="index" separator=";">
			update t_acc_bu_activity_customer set
			<if test="param.activityCustomerId !=null and param.activityCustomerId !=''"> ACTIVITY_CUSTOMER_ID=#{param.activityCustomerId},</if>
			<if test="param.customerName !=null and param.customerName !=''"> CUSTOMER_NAME=#{param.customerName},</if>
			<if test="param.customerSexCode !=null and param.customerSexCode !=''"> CUSTOMER_SEX_CODE=#{param.customerSexCode},</if>
			<if test="param.customerSexName !=null and param.customerSexName !=''"> CUSTOMER_SEX_NAME=#{param.customerSexName},</if>
			<if test="param.customerPhone !=null and param.customerPhone !=''"> CUSTOMER_PHONE=#{param.customerPhone},</if>
			<if test="param.customerBirth !=null and param.customerBirth !=''"> CUSTOMER_BIRTH=#{param.customerBirth},</if>
			<if test="param.activityId !=null and param.activityId !=''"> ACTIVITY_ID=#{param.activityId},</if>
			<if test="param.isCheckIn !=null and param.isCheckIn !=''"> IS_CHECK_IN=#{param.isCheckIn},</if>
			<if test="param.column1 !=null and param.column1 !=''"> COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''"> COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''"> COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''"> COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''"> COLUMN5=#{param.column5},</if>
			<if test="param.applyTime !=null and param.applyTime !=''"> APPLY_TIME=#{param.applyTime},</if>
			<if test="param.signInTime !=null and param.signInTime !=''"> SIGN_IN_TIME=#{param.signInTime},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
			<if test="param.signNum !=null and param.signNum !=''"> SIGN_NUM=#{param.signNum},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
			where 1=1
			<if test="param.activityId !=null and param.activityId !=''">and ACTIVITY_ID=#{param.activityId}</if>
			<if test="param.customerId !=null and param.customerId !=''"> AND CUSTOMER_ID=#{param.customerId}</if>
			<if test="param.activityCustomerId !=null and param.activityCustomerId !=''"> AND ACTIVITY_CUSTOMER_ID=#{param.activityCustomerId}</if>
			<if test="param.customerPhone !=null and param.customerPhone !=''"> AND CUSTOMER_PHONE=#{param.customerPhone}</if>
			<if test="param.oldIsEnable !=null and param.oldIsEnable !=''"> AND IS_ENABLE=#{param.oldIsEnable}</if>
			
		</foreach>
	</update>



	<select id="checkSmallType" resultType="int">
		SELECT count(1)   from t_acc_db_activity_smalltype WHERE  SMALL_TYPE_ID =#{param.SMALL_TYPE_ID} and  IS_ENABLE='1' ;
	</select>

	<update id="updateSmallType" parameterType="java.util.HashMap" >
           update t_acc_db_activity_smalltype set SMALL_TYPE_NAME=#{param.SMALL_TYPE_NAME} , LAST_UPDATED_DATE=now(),MODIFY_NAME='sys',MODIFIER='sys' where  SMALL_TYPE_ID =#{param.SMALL_TYPE_ID} and  IS_ENABLE='1' ;
	</update>

	<insert id="insertSmallType" parameterType="java.util.List">
		INSERT INTO `t_acc_db_activity_smalltype` (
			`SMALL_TYPE_ID`,
			`SMALL_TYPE_NAME`,
			`BIG_TYPE_ID`,
			`BIG_TYPE_NAME`,
			`OEM_ID`,
			`GROUP_ID`,
			`CREATOR`,
			`CREATED_NAME`,
			`CREATED_DATE`,
			`MODIFIER`,
			`MODIFY_NAME`,
			`LAST_UPDATED_DATE`,
			`IS_ENABLE`,
			`UPDATE_CONTROL_ID`
		)
		VALUES
			(
				#{param.SMALL_TYPE_ID},
				#{param.SMALL_TYPE_NAME},
				#{param.BIG_TYPE_ID},
				#{param.BIG_TYPE_NAME},
				'1',
				'1',
				'sys',
				'sys',
				NOW(),
				'sys',
				'sys',
				NOW(),
				'1',
				#{param.UPDATE_CONTROL_ID}
			);
    </insert>
	<insert id="activityApplyInfoSave">
		INSERT INTO `t_acc_bu_activity_apply` (
			ACTIVITY_APPLY_ID,
			ACTIVITY_ID,
			REVIEW_PERSON_ID,
			REVIEW_PERSON_NAME,
		    DLR_CODE,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
		)
		VALUES
		(
			#{param.activityApplyId},
			#{param.activityId},
			#{param.reviewPersonId},
			#{param.reviewPersonName},
			#{param.dlrCode},
			#{param.creator},
			#{param.createdName},
			NOW(),
			#{param.modifier},
			#{param.modifyName},
			NOW(),
			'1',
			uuid()
		);
	</insert>


	<select id="checkRepeat" resultType="int">
		select count(1) from t_acc_bu_activity_customer
		where CUSTOMER_PHONE=#{param.customerPhone} and ACTIVITY_ID=#{param.activityId} and IS_ENABLE='1'
		<if test="param.activityCustomerId !=null and param.activityCustomerId !=''"> and ACTIVITY_CUSTOMER_ID!=#{param.activityCustomerId}</if>
	</select>

	<update id="updateAppId" parameterType="java.util.HashMap" >
		update t_acc_bu_activity  set COLUMN17=#{param.appActivityId} , LAST_UPDATED_DATE=now(),MODIFY_NAME='sys',MODIFIER='sys' where  ACTIVITY_ID=#{param.adpActivityId}  and  IS_ENABLE='1' ;
	</update>

	<select id="querySignNum" resultType="int">
		select count(1)+1 from t_acc_bu_activity_customer c left join t_acc_bu_activity a on c.ACTIVITY_ID=a.ACTIVITY_ID
		where   c.is_enable='1'
		<if test="param.customerPhone !=null and param.customerPhone !=''">
			and c.CUSTOMER_PHONE=#{param.customerPhone}
		</if>
		<if test="param.customerId !=null and param.customerId !=''">
			and c.CUSTOMER_ID=#{param.customerId}
		</if>
		<if test="param.createTypeCode !=null and param.createTypeCode =='DEVELOP'">
			and a.CREATE_TYPE_CODE =  #{param.createTypeCode}
		</if>
		<if test="param.createTypeCode !=null and param.createTypeCode !='DEVELOP'">
			and a.CREATE_TYPE_CODE !=  #{param.createTypeCode}
			and c.IS_CHECK_IN='1'
		</if>
	</select>

	<select id="queryActivityInfo" resultType="java.util.Map">
		select * from t_acc_bu_activity
		where ACTIVITY_ID=#{param.activityId} and is_enable='1'
	</select>

	<select id="queryActivityInfoSpecifyFields" resultType="java.util.Map">
		select CREATE_TYPE_CODE,ACTIVITY_NAME,INFO_CHAN_D_CODE,INFO_CHAN_D_NAME,INFO_CHAN_D_DESC from t_acc_bu_activity
		where ACTIVITY_ID=#{param.activityId} and is_enable='1'
	</select>

    <select id="queryActivityApplyId" resultType="java.util.Map">
		select * from t_acc_bu_activity_apply
		where 1=1
		<if test="param.activityApplyId !=null and param.activityApplyId !=''">
			and ACTIVITY_APPLY_ID =  #{param.activityApplyId}
		</if>
		<if test="param.activityId !=null and param.activityId !=''">
			and ACTIVITY_ID =  #{param.activityId}
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">
			and REVIEW_PERSON_ID =  #{param.reviewPersonId}
		</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and DLR_CODE =  #{param.dlrCode}
		</if>
	</select>
    <select id="queryUsername" resultType="java.util.Map">
		SELECT * FROM mp.t_usc_mdm_org_employee where USER_ID=#{param.reviewPersonId}
	</select>

	<select id="checkData" resultType="java.util.Map">
		select
			t1.ACTIVITY_NAME
		from t_acc_bu_activity t1
		where
			t1.ACTIVITY_ID =#{activityId}
		  and t1.CREATE_TYPE_CODE = 'DEVELOP'
		  and t1.END_TIME &gt; #{dates}
		  AND t1.STATUS_CODE = '2'
		  AND t1.RELEASE_STATUS_CODE = '1'
		  AND t1.IS_ENABLE = '1'
		GROUP BY t1.ACTIVITY_ID
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="getMdmDlrInfoQuery" resultType="java.util.Map">
		SELECT
		a.DLR_ID,
		a.DLR_CODE,
		a.DLR_SHORT_NAME,
		b.DLR_BUILD_AGREEMENT_URL,
		a.DLR_TYPE,
		a.AGENT_AREA,
		c.AGENT_ID,
		b.AGENT_COMPANY_ID,
		a.CREATOR,
		a.CREATED_DATE,
		b.AGENT_COMPANY_NAME,
		c.AGENT_NAME
		FROM
		mp.t_usc_mdm_org_dlr a,
		mp.t_usc_mdm_agent_company b,
		mp.t_usc_mdm_agent_info c
		WHERE
		a.COMPANY_ID = b.AGENT_COMPANY_ID
		AND b.AGENT_ID = c.AGENT_ID
		<if test="param.dlrId != null and '' != param.dlrId">
			and a.DLR_ID = #{param.dlrId}
		</if>
		<if test="param.dlrCode != null and '' != param.dlrCode">
			and a.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.dlrType != null and '' != param.dlrType">
			and a.DLR_TYPE = #{param.dlrType}
		</if>
		<if test="param.dlrTypeIn != null and '' != param.dlrTypeIn">
			and a.DLR_TYPE IN
			<foreach collection="param.dlrTypeIn.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.agentId != null and '' != param.agentId">
			and c.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.dlrExchange != null and '' != param.dlrExchange">
			and a.DLR_ID != #{param.dlrExchange}
		</if>
		/*前端传参公司ID key值不确定 */
		<if test="param.companyId != null and '' != param.companyId">
			and b.AGENT_COMPANY_ID = #{param.companyId}
		</if>
		<if test="param.agentCompanyId != null and '' != param.agentCompanyId">
			and b.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
	</select>

	<update id="updateSignNum" >
		update t_acc_bu_activity_customer  set SIGN_NUM=SIGN_NUM -1 , LAST_UPDATED_DATE=now(),MODIFY_NAME='sys',MODIFIER='sys',UPDATE_CONTROL_ID=UUID()
		where  CUSTOMER_PHONE=#{param.customerPhone}  and  IS_ENABLE='1' and SIGN_NUM <![CDATA[ > ]]> #{param.signNum} ;
	</update>
</mapper>
