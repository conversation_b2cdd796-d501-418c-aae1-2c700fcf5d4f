<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBasisLogMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.adp.csc.entities.SacBasisLog">
		<id column="LOG_ID" property="logId" />
		<result column="LOG_type" property="logType" />
		<result column="LOG_FLAG" property="logFlag" />
		<result column="LOG_DESC" property="logDesc" />
		<result column="BEG_TIME" property="begTime" />
		<result column="END_TIME" property="endTime" />
		<result column="EXTEND_JSON" property="extendJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="OEM_CODE" property="oemCode" />
		<result column="GROUP_CODE" property="groupCode" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="SDP_USER_ID" property="sdpUserId" />
		<result column="SDP_ORG_ID" property="sdpOrgId" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		LOG_ID, LOG_type, LOG_FLAG, LOG_DESC, BEG_TIME, END_TIME, EXTEND_JSON,
		OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, IS_ENABLE, CREATOR,
		CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE,
		SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
	</sql>

	<!-- 基础日志查询 -->
	<select id="sacBasisLogFindInfo" resultType="map">
		select
		<include refid="Base_Column_List"></include>
		from t_sac_basis_log
		where 1=1
		<choose>
			<when
				test="param.column != null and '' != param.column and param.sorting != null and '' != param.sorting">
				order by ${param.column} ${param.sorting}
			</when>
			<otherwise>ORDER BY CREATED_DATE DESC</otherwise>
		</choose>
	</select>

	<!-- 客户信息新增 -->
	<insert id="createsacBasisLog">
		INSERT INTO `t_sac_basis_log` (
		LOG_ID,
		LOG_type,
		LOG_FLAG,
		LOG_DESC,
		BEG_TIME,
		END_TIME,
		`EXTEND_JSON`,
		`IS_ENABLE`,
		`OEM_ID`,
		`GROUP_ID`,
		`OEM_CODE`,
		`GROUP_CODE`,
		`CREATOR`,
		`CREATED_NAME`,
		`CREATED_DATE`,
		`MODIFIER`,
		`MODIFY_NAME`,
		`LAST_UPDATED_DATE`,
		`SDP_USER_ID`,
		`SDP_ORG_ID`,
		`UPDATE_CONTROL_ID`
		)
		VALUES
		(
		uuid(),
		#{param.logType},
		#{param.logFlag},
		#{param.logDesc},
		#{param.begTime},
		#{param.endTime},
		#{param.extendsJson},
		#{param.oemId},
		#{param.groupId},
		#{param.oemCode},
		#{param.groupCode},
		'1',
		#{param.creator},
		#{param.createdName},
		now(),
		#{param.modifier},
		#{param.modifyName},
		now(),
		'1',
		'1',
		uuid()
		)
	</insert>
	
	<!-- 大使切店日志记录新增 -->
	<insert id="sacSwitcchDlrLogInsertOne" parameterType="map">
		INSERT INTO t_sac_user_switch_dlr_log (
			LOG_ID, /*日志ID*/
			USER_ID, /*用户ID*/
			USER_NAME, /*用户名*/
			TO_DLR_ID, /*切入门店ID*/
			TO_DLR_CODE, /*切入门店编码*/
			TO_DLR_NAME, /*切入门店名称*/
			IS_ENABLE, /*是否可用*/
			CREATOR, /*创建人*/
			CREATED_NAME, /*创建人姓名*/
			CREATED_DATE, /*创建时间*/
			MODIFIER, /*最后更新人员*/
			MODIFY_NAME, /*修改人姓名*/
			LAST_UPDATED_DATE, /*最后更新时间*/
			UPDATE_CONTROL_ID) /*并发控制字段*/
		VALUES (
			uuid(),
			#{param.userId},
			#{param.userName},
			#{param.toDlrId},
			#{param.toDlrCode},
			#{param.toDlrName},
			#{param.isEnable},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			uuid()
		)
	</insert>
</mapper>