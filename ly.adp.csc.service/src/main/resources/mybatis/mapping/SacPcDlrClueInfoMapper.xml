<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacPcDlrClueInfoMapper">
	<!-- PC门店全部线索查询 -->
	<select id="entireReviewInfo" resultType="map">
		SELECT
			REVIEW_ID,
			ORG_CODE,
			ORG_NAME,
			BILL_TYPE,
			BILL_TYPE_NAME,
			BUSINESS_TYPE,
			BUSINESS_TYPE_NAME,
			INFO_CHAN_M_CODE,
			INFO_CHAN_M_NAME,
			INFO_CHAN_D_CODE,
			INFO_CHAN_D_NAME,
			INFO_CHAN_DD_CODE,
			INFO_CHAN_DD_NAME,
			CHANNEL_CODE,
			CHANNEL_NAME,
			B<PERSON><PERSON>_CODE,
			PLAN_REVIEW_TIME,
			REVIEW_TIME,
			LAST_REVIEW_TIME,
			OVER_REVIEW_TIME,
			PLAN_COME_TIME,
			FACT_COME_TIME,
			IS_COME,
			ASSIGN_STATUS,
			ASSIGN_STATUS_NAME,
			ASSIGN_TIME,
			ASSIGN_PERSON_ID,
			ASSIGN_PERSON_NAME,
			REVIEW_PERSON_ID,
			REVIEW_PERSON_NAME,
			REVIEW_DESC,
			REVIEW_STATUS,
			REVIEW_STATUS_NAME,
			CUST_ID,
			CUST_NAME,
			PHONE,
			GENDER,
			GENDER_NAME,
			TOUCH_STATUS,
			TOUCH_STATUS_NAME,
			ERROR_REASON_CODE,
			ERROR_REASON_NAME,
			NODE_CODE,
			NODE_NAME,
			SEND_DLR_CODE,
			SEND_DLR_SHORT_NAME,
			SEND_TIME,
			INTEN_LEVEL_CODE,
			INTEN_LEVEL_NAME,
			INTEN_BRAND_CODE,
			INTEN_BRAND_NAME,
			INTEN_SERIES_CODE,
			INTEN_SERIES_NAME,
			INTEN_CAR_TYPE_CODE,
			INTEN_CAR_TYPE_NAME,
			COLUMN1,
			COLUMN2,
			COLUMN3,
			COLUMN4,
			COLUMN5,
			COLUMN6,
			COLUMN7,
			COLUMN8,
			COLUMN9,
			COLUMN10,
			COLUMN11,
			COLUMN12,
			COLUMN13,
			COLUMN14,
			COLUMN15,
			COLUMN16,
			COLUMN17,
			COLUMN18,
			COLUMN19,
			COLUMN20,
			BIG_COLUMN1,
			BIG_COLUMN2,
			BIG_COLUMN3,
			BIG_COLUMN4,
			BIG_COLUMN5,
			EXTENDS_JSON,
			OEM_ID,
			GROUP_ID,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID,
			PROVINCE_CODE,
			PROVINCE_NAME,
			CITY_CODE,
			CITY_NAME,
			COUNTY_CODE,
			COUNTY_NAME,
			MANAGE_LABEL_CODE,
			MANAGE_LABEL_NAME 
		FROM
			t_sac_review 
		WHERE
			1 =1
		<if test="param.provinceCode !=null and param.provinceCode !=''">and PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and MANAGE_LABEL_CODE=#{param.manageLabelCode}</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and MANAGE_LABEL_NAME=#{param.manageLabelName}</if>
		<if test="param.orgCode !=null and param.orgCode != ''" > AND org_code = #{param.orgCode} </if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and COLUMN5=#{param.planBuyDate}</if>
		<if test="param.gender !=null and param.gender != ''" > AND GENDER = #{param.gender} </if>
		<if test="param.businessHeatCode != null and ''!= param.businessHeatCode ">AND column7 = #{param.businessHeatCode}</if>
		<if test="param.assignStartTime != null and ''!= param.assignStartTime"><![CDATA[ AND ASSIGN_TIME >= #{param.assignStartTime} ]]></if>
		<if test="param.assignEndTime != null and ''!= param.assignEndTime"><![CDATA[ AND ASSIGN_TIME < date_add(#{param.assignEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.planStartTime != null and ''!= param.planStartTime"><![CDATA[ AND PLAN_REVIEW_TIME >= #{param.planStartTime} ]]></if>
		<if test="param.planEndTime != null and ''!= param.planEndTime"><![CDATA[ AND PLAN_REVIEW_TIME < date_add(#{param.planEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.planComeStartTime != null and ''!= param.planComeStartTime"><![CDATA[ AND PLAN_COME_TIME >= #{param.planComeStartTime} ]]></if>
		<if test="param.planComeEndTime != null and ''!= param.planComeEndTime"><![CDATA[ AND PLAN_COME_TIME < date_add(#{param.planComeEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.factComeStartTime != null and ''!= param.factComeStartTime"><![CDATA[ AND FACT_COME_TIME >= #{param.factComeStartTime} ]]></if>
		<if test="param.factComeEndTime != null and ''!= param.factComeEndTime"><![CDATA[ AND FACT_COME_TIME < date_add(#{param.factComeEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.lastStartTime != null and ''!= param.lastStartTime"><![CDATA[ AND LAST_REVIEW_TIME >= #{param.lastStartTime} ]]></if>
		<if test="param.lastEndTime != null and ''!= param.lastEndTime"><![CDATA[ AND LAST_REVIEW_TIME < date_add(#{param.lastEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.sendStartTime != null and ''!= param.sendStartTime"><![CDATA[ AND SEND_TIME >= #{param.sendStartTime} ]]></if>
		<if test="param.sendEndTime != null and ''!= param.sendEndTime"><![CDATA[ AND SEND_TIME < date_add(#{param.sendEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.createdStartTime != null and ''!= param.createdStartTime"><![CDATA[ AND CREATED_DATE >= #{param.createdStartTime} ]]></if>
		<if test="param.createdEndTime != null and ''!= param.createdEndTime"><![CDATA[ AND CREATED_DATE < date_add(#{param.createdEndTime}, INTERVAL 1 day) ]]></if>
		<if test="param.billType != null and ''!= param.billType ">AND bill_type = #{param.billType}</if>
		<if test="param.businessType != null and ''!= param.businessType ">AND business_type = #{param.businessType}</if>
		<if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">AND INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
		<if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">AND INFO_CHAN_D_CODE = #{param.infoChanDCode}</if>
		<if test="param.infoChanDdCode != null and ''!= param.infoChanDdCode ">AND INFO_CHAN_DD_CODE = #{param.infoChanDdCode}</if>
		<if test="param.channelCode != null and ''!= param.channelCode ">AND CHANNEL_CODE = #{param.channelCode}</if>
		<if test="param.channelName != null and ''!= param.channelName ">AND CHANNEL_NAME = #{param.channelName}</if>
		<if test="param.custName != null and ''!= param.custName "><![CDATA[	AND instr(CUST_NAME,#{param.custName})>0 ]]></if>
		<if test="param.phone != null and ''!= param.phone "><![CDATA[	AND instr(phone,#{param.phone})>0 ]]></if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName ">AND review_person_name = #{param.reviewPersonName}</if>
		<if test="param.billCode != null and ''!= param.billCode ">AND bill_code = #{param.billCode}</if>
		<if test="param.isCome != null and ''!= param.isCome">AND is_come = #{param.isCome}</if>
		<if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">AND INTEN_LEVEL_CODE = #{param.intenLevelCode}</if>
		<if test="param.intenBrandCode != null and ''!= param.intenBrandCode ">AND INTEN_BRAND_CODE = #{param.intenBrandCode}</if>
		<if test="param.intenSeriesCode != null and ''!= param.intenSeriesCode ">AND INTEN_SERIES_CODE = #{param.intenSeriesCode}</if>
		<if test="param.intenCarTypeCode != null and ''!= param.intenCarTypeCode ">AND INTEN_CAR_TYPE_CODE = #{param.intenCarTypeCode}</if>
		<if test='param.isPvSend != null and "1"== param.isPvSend'>AND SEND_TIME IS NOT NULL</if>
		<if test='param.isPvSend != null and "0"== param.isPvSend'>AND ifnull(SEND_TIME,'')=''</if>
		<if test="param.reviewPersonName != null and ''!= param.reviewPersonName "><![CDATA[ AND instr(review_person_name,#{param.reviewPersonName})>0 ]]></if>
		<if test="param.reviewStatus != null and ''!= param.reviewStatus ">AND REVIEW_STATUS = #{param.reviewStatus}</if>
		<if test="param.assignStatus != null and ''!= param.assignStatus ">AND ASSIGN_STATUS = #{param.assignStatus}</if>
		<if test='param.isOverTime != null and "1"== param.isOverTime'><![CDATA[ AND OVER_REVIEW_TIME<=now() ]]></if>
		<if test='param.isOverTime != null and "0"== param.isOverTime'><![CDATA[ AND OVER_REVIEW_TIME>now() ]]></if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(PHONE,#{param.searchCondition})>0 or INSTR(CUST_NAME,#{param.searchCondition})>0)</if>
		<if test="param.orgCodeIn !=null and param.orgCodeIn !=''"> and org_code IN <foreach collection="param.orgCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach></if>
		<if test="param.beyondDay != null and ''!= param.beyondDay"><![CDATA[ AND TO_DAYS(str_to_date(NOW(),'%Y-%m-%d %H:%i:%s')) - TO_DAYS(str_to_date(OVER_REVIEW_TIME,'%Y-%m-%d %H:%i:%s')) >= #{param.beyondDay} ]]></if>
	</select>

	<sql id="entireDlrClueInfoFrom">
		<choose>
			<when test="param.newQuery != null and param.newQuery != ''">
				from ${param.dbName}.t_sac_clue_info_dlr_part A
			</when>
			<otherwise>
				from ${param.dbName}.t_sac_clue_info_dlr A
			</otherwise>
		</choose>
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
			left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
			sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
			LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
			where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
			K ON K.CUSTOMER_PHONE =A.PHONE
		</if>
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join (
		select
		subF.id,
		subF.clue_dlr_id,
		subF.dlr_code,
		subF.first_assign_dlr_time,
		subF.config_first_overdue_time,
		subF.first_overdue_time,
		subF.creator,
		subF.created_time
		from
		csc.t_sac_clue_info_dlr_firstoverdue subF
		where
		created_time = (
		select
		MAX(created_time)
		from
		csc.t_sac_clue_info_dlr_firstoverdue
		where
		subF.id = id )
		) fod on a.id = fod.clue_dlr_id
	</sql>

	<sql id="entireDlrClueInfoCountFrom">
		from ${param.dbName}.t_sac_clue_info_dlr A
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		<if test="(param.attr83 != null and ''!= param.attr83) || (param.source5 !=null and param.source5 !='') || (param.source6 !=null and param.source6 !='')">
			left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		</if>
		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
			left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
			sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
			LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
			where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
			K ON K.CUSTOMER_PHONE =A.PHONE
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		</if>
	</sql>

	<sql id="entireDlrClueInfoNonACondition">
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null
		</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND IFNULL(K.yhSignNum,0) = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND IFNULL(K.dlSignNum,0) = #{param.dlSignNum}
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>
	</sql>

	<sql id="entireDlrClueInfoACondition">
		where
		INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>
		<if test="param.cusSource !=null and param.cusSource !=''">
			and A.CUS_SOURCE=#{param.cusSource}
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
			and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
		</if>
		<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
		</if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
			and A.DLR_CODE IN
			<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
			A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
		</if>
		<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
			<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]>
		</if>
		<if test="param.filterDccFlag">
			AND A.COLUMN19 IS NULL
		</if>
	</sql>


	<select id="entireDlrClueInfoPhone" resultType="Map">
		select
		DISTINCT A.PHONE
		<include refid="entireDlrClueInfoFrom"/>
		<include refid="entireDlrClueInfoACondition"/>
		<include refid="entireDlrClueInfoNonACondition"/>
		order by A.CREATED_DATE desc
	</select>

	<select id="entireDlrClueInfoCountSingle" resultType="Long">
		select
		count(DISTINCT A.PHONE)
		from ${param.dbName}.t_sac_clue_info_dlr A
		<include refid="entireDlrClueInfoACondition"/>
	</select>

	<select id="entireDlrClueInfoCount" resultType="Long">
		select
		count(DISTINCT A.PHONE)
		<include refid="entireDlrClueInfoCountFrom"/>
		<include refid="entireDlrClueInfoACondition"/>
		<include refid="entireDlrClueInfoNonACondition"/>
	</select>

	<!-- PC门店全部线索查询 -->
	<select id="entireDlrClueInfo" resultType="Map">
		select
		A.FIRST_TESTDRIVER_TIME,
		DATE_FORMAT(A.FIRST_ARRIVAL_TIME, '%Y-%m-%d %H:%i:%S' )  FIRST_ARRIVAL_TIME,
		A.ID,
		A.SERVER_ORDER,
		A.PV_SERVER_ORDER,
		A.CUST_ID,
		A.CUST_NAME,
		A.PHONE,
		A.PHONE_BACKUP,
		<choose>
			<when test='param.notEncryption =="1"'>
				A.PHONE
			</when>
			<otherwise>
				INSERT(A.PHONE,4,4,'****')
			</otherwise>
		</choose> as TMD_PHONE,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN3 AS INNER_COLOR_CODE,
		B.COLUMN4 as INNER_COLOR_NAME,
		B.COLUMN1 as OUT_COLOR_CODE,
		B.COLUMN2 as OUT_COLOR_NAME,
		ifnull(B.COLUMN15,'定期跟进') as followReason,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		F.source5,
		F.source6,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		F.attr83,
		B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
		case
		when t.CUSTOMER_PHONE is null then '否'
		else '是' end isCompleteCarApply,/*是否完成试驾*/
		case
		when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是'
		else '否' end as isOverdue,
		<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
		<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>,

		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
		IFNULL(K.dlSignNum,0) AS dlSignNum,
		IFNULL(K.yhSignNum,0) AS yhSignNum,
		</if>

		a.ALLOCATE_TIME,
		tmlv.LOOKUP_VALUE_NAME,
		ifnull(fod.first_overdue_time, null) firstOverDueTime,
		case when fod.first_overdue_time is null then null
		when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
		else '否' end as isFirstOverdue,
		a.CUS_SOURCE
		<include refid="entireDlrClueInfoFrom"/>
		where 1 = 1
		<if test="param.phones != null and param.phones.size()!=0">
			and A.PHONE in
			<foreach collection="param.phones" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>
		order by A.CREATED_DATE desc
	</select>

	<select id="entireDlrClueInfoEntity" resultType="com.ly.adp.csc.entities.EntireDlrClueInfoExport">
		select
		A.FIRST_TESTDRIVER_TIME,
		DATE_FORMAT(A.FIRST_ARRIVAL_TIME, '%Y-%m-%d %H:%i:%S' )  FIRST_ARRIVAL_TIME,
		A.ID,
		A.SERVER_ORDER,
		A.PV_SERVER_ORDER,
		A.CUST_ID,
		A.CUST_NAME,
		A.PHONE,
		A.PHONE_BACKUP,
		<choose>
			<when test='param.notEncryption =="1"'>
				A.PHONE
			</when>
			<otherwise>
				INSERT(A.PHONE,4,4,'****')
			</otherwise>
		</choose> as TMD_PHONE,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN3 AS INNER_COLOR_CODE,
		B.COLUMN4 as INNER_COLOR_NAME,
		B.COLUMN1 as OUT_COLOR_CODE,
		B.COLUMN2 as OUT_COLOR_NAME,
		ifnull(B.COLUMN15,'定期跟进') as followReason,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		F.source5,
		F.source6,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		F.attr83,
		B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
		case
		when t.CUSTOMER_PHONE is null then '否'
		else '是' end isCompleteCarApply,/*是否完成试驾*/
		case
		when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是'
		else '否' end as isOverdue,
		<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
		<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>,

		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
		IFNULL(K.dlSignNum,0) AS dlSignNum,
		IFNULL(K.yhSignNum,0) AS yhSignNum,
		</if>

		a.ALLOCATE_TIME,
		tmlv.LOOKUP_VALUE_NAME,
		ifnull(fod.first_overdue_time, null) firstOverDueTime,
		case when fod.first_overdue_time is null then null
		when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
		else '否' end as isFirstOverdue,
		a.CUS_SOURCE
		<include refid="entireDlrClueInfoFrom"/>
		where 1 = 1
		<if test="param.phones != null and param.phones.size()!=0">
			and A.PHONE in
			<foreach collection="param.phones" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>
		order by A.CREATED_DATE desc
	</select>

	<select id="entireDlrClueInfoNoPage" resultType="Map">
		select
		A.FIRST_TESTDRIVER_TIME,
		DATE_FORMAT(A.FIRST_ARRIVAL_TIME, '%Y-%m-%d %H:%i:%S' )  FIRST_ARRIVAL_TIME,
		A.ID,
		A.SERVER_ORDER,
		A.PV_SERVER_ORDER,
		A.CUST_ID,
		A.CUST_NAME,
		A.PHONE,
		A.PHONE_BACKUP,
		<choose>
			<when test='param.notEncryption =="1"'>
				A.PHONE
			</when>
			<otherwise>
				INSERT(A.PHONE,4,4,'****')
			</otherwise>
		</choose> as TMD_PHONE,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN3 AS INNER_COLOR_CODE,
		B.COLUMN4 as INNER_COLOR_NAME,
		B.COLUMN1 as OUT_COLOR_CODE,
		B.COLUMN2 as OUT_COLOR_NAME,
		ifnull(B.COLUMN15,'定期跟进') as followReason,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		F.source5,
		F.source6,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		F.attr83,
		B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
		case
		when sum(t.CUSTOMER_PHONE) is null then '否'
		else '是' end isCompleteCarApply,/*是否完成试驾*/
		case
		when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是'
		else '否' end as isOverdue,
		<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
		<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>,

		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
		IFNULL(K.dlSignNum,0) AS dlSignNum,
		IFNULL(K.yhSignNum,0) AS yhSignNum,
		</if>

		a.ALLOCATE_TIME,
		tmlv.LOOKUP_VALUE_NAME,
		ifnull(fod.first_overdue_time, null) firstOverDueTime,
		case when fod.first_overdue_time is null then null
		when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
		else '否' end as isFirstOverdue,
		a.CUS_SOURCE
		from ${param.dbName}.t_sac_clue_info_dlr A
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
		left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		K ON K.CUSTOMER_PHONE =A.PHONE
		</if>
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join  mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		where
		INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>

		<if test="param.cusSource !=null and param.cusSource !=''">
		and A.CUS_SOURCE=#{param.cusSource}
		</if>

		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
			and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
		</if>
		<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
		</if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
			and A.DLR_CODE IN
			<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>
		<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
			A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
		</if>
		<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
			<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]>
		</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND IFNULL(K.yhSignNum,0) = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND IFNULL(K.dlSignNum,0) = #{param.dlSignNum}
		</if>
		GROUP BY A.PHONE
		order by A.CREATED_DATE desc
		LIMIT #{param.pageNo},#{param.pageSize}
	</select>

	<select id="entireDlrClueInfoNoPageCount" resultType="java.lang.Integer">
		select count(*) from (
		select
		A.ID
		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
			,IFNULL(K.dlSignNum,0) AS dlSignNum,</if>
		IFNULL(K.yhSignNum,0) AS yhSignNum
		from ${param.dbName}.t_sac_clue_info_dlr A
		left join
		csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.
		TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		<if test=
					"(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
			left
			join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN 1 ELSE 0 END )
			dlSignNum,
			sum(
			CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN 1 ELSE 0 END ) yhSignNum from
			csc.t_acc_bu_activity_customer T
			LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
			where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1' group by T.CUSTOMER_PHONE)
			K ON K.CUSTOMER_PHONE =A.PHONE
		</if>left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join mp. t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE =
		'ADP_SMALLAREA_717'
		<if test
					="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT
			JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.
			REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		where
		INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is
			not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE
			is
			null</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>

		<if test="param.cusSource !=null and param.cusSource !=''">
		and A.CUS_SOURCE=#{param.cusSource}
		</if>

		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
			and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
		</if>
		<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
		</if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
			and A.DLR_CODE IN
			<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>
		<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
			A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
		</if>
		<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
			<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]>
		</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND IFNULL(K.yhSignNum,0) = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND IFNULL(K.dlSignNum,0) = #{param.dlSignNum}
		</if>
		GROUP BY A.PHONE
		) as temp
	</select>

	<select id="entireDlrClueInfoNew" resultType="Map">
		select * from (
		select
		A.FIRST_TESTDRIVER_TIME,
		A.FIRST_ARRIVAL_TIME,
		A.ID,
		A.SERVER_ORDER,
		A.PV_SERVER_ORDER,
		A.CUST_ID,
		A.CUST_NAME,
		A.PHONE,
		A.PHONE_BACKUP,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN3 AS INNER_COLOR_CODE,
		B.COLUMN4 as INNER_COLOR_NAME,
		B.COLUMN1 as OUT_COLOR_CODE,
		B.COLUMN2 as OUT_COLOR_NAME,
		B.COLUMN15 as followReason,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		A.FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		A.CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		F.source5,
		F.source6,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		F.attr83,
		B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,/*是否完成试驾*/
		case when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是' else '否' end as isOverdue,
		a.ALLOCATE_TIME,
		G.userGroupName,
		tmlv.LOOKUP_VALUE_NAME,
		a.CUS_SOURCE,
		fod.first_overdue_time as firstOverDueTime,
		case when fod.first_overdue_time is null then null when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是' else '否' end as isFirstOverdue
		from adp_leads.t_sac_clue_info_dlr A
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		LEFT JOIN (
			SELECT
			cust_id,
			GROUP_CONCAT( USER_GROUP_NAME ) AS userGroupName
			FROM
			csc.t_sac_user_group_detail tsugd
			left join csc.t_sac_user_group tsug on tsugd.USER_GROUP_ID = tsug.USER_GROUP_ID
			where tsugd.IS_ENABLE = '1' and tsug.IS_ENABLE = '1' GROUP BY CUST_ID
		) G on G.cust_id = A.cust_id
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		where
			INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
			<if test="param.attr83 != null and ''!= param.attr83 ">
				<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
			</if>
			<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
			</if>
			<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
			<if test="param.chooseTimes !=null and param.chooseTimes !=''">
				<![CDATA[
				AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
				AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
				]]>
			</if>
			<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
				v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
			</if>
			<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
				v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
			</if>
			<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
				<foreach item="item" index="index" open="(" separator="," close=")"
						 collection="param.saleOrderState.split(',')">#{item}
				</foreach>
			</if>
			<if test="param.smartId !=null and param.smartId !=''">
				and A.COLUMN10=#{param.smartId}
			</if>

			<if test="param.cusSource !=null and param.cusSource !=''">
				and A.CUS_SOURCE=#{param.cusSource}
			</if>

			<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
				<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
			<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
				<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
			<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
				NULL
			</if>
			<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
			</if>
			<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
			</if>
			<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
				('10','7')
			</if>
			<if test="param.planStartTime !=null and param.planStartTime !=''">and
				B.PLAN_REVIEW_TIME>=#{param.planStartTime}
			</if>
			<if test="param.planEndTime !=null and param.planEndTime !=''">
				<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
			<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
			<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

			<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
			<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
			<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
				A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
			</if>
			<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
				A.INTEN_LEVEL_NAME=#{param.intenLevelName}
			</if>
			<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
				A.INTEN_BRAND_CODE=#{param.intenBrandCode}
			</if>
			<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
				A.INTEN_BRAND_NAME=#{param.intenBrandName}
			</if>
			<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
				A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
			</if>
			<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
				A.INTEN_SERIES_NAME=#{param.intenSeriesName}
			</if>
			<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
				A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
			</if>
			<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
				A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
			</if>
			<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
				A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
			</if>
			<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
				A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
			</if>
			<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
				A.INNER_COLOR_CODE=#{param.innerColorCode}
			</if>
			<if test="param.innerColorName !=null and param.innerColorName !=''">and
				A.INNER_COLOR_NAME=#{param.innerColorName}
			</if>
			<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
			<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
			<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
			<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
				A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
			</if>
			<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
				A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
			</if>
			<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
			<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
				A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
			</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
				A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
			</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
				A.INFO_CHAN_M_NAME=#{param.infoChanMName}
			</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
				A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
			</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
				A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
			</if>
			<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
				A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
			</if>
			<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
				A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
			</if>
			<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
			<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
				UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
				LOWER(concat('%',#{param.channelName},'%')))
			</if>
			<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
			<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
			<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
			<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
			<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
			<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
			<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
			<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
				A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
			</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
			<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
			<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
			<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
			<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
			<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
			<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
				A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
			</if>
			<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and
				A.UPDATE_CONTROL_ID=#{param.updateControlId}
			</if>

			<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
				concat('%',#{param.pvServerOrder},'%')
			</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>

			<choose>
				<when test="param.realPhone !=null and param.realPhone !=''">
					and A.PHONE = #{param.realPhone}
				</when>
				<otherwise>
					<if test="param.phone !=null and param.phone !=''">
					  	and A.PHONE like concat('%',#{param.phone},'%')
					</if>
				</otherwise>
			</choose>
			<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
			<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
			<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
				<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
					#{item}
				</foreach>
			</if>

			<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
				concat('%',#{param.phoneBackup},'%')
			</if>
			<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
			</if>
			<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
				A.CREATED_DATE>=#{param.createdDateStart}
			</if>
			<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
				<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
			<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
				A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
			</if>
			<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
				<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

			<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
				concat('%', #{param.reviewPersonName}, '%')
			</if>
			<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
				#{param.reviewPersonId}
			</if>
			<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
				A.ASSIGN_TIME>=#{param.assignTimeStart}
			</if>
			<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
				<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
			<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
				A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
			</if>
			<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
				<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

			<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
				and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
			</if>
			<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
				<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
			</if>

			<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
				A.RECEIVE_TIME>=#{param.receiveTimeStart}
			</if>
			<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
				<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
			<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
				<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
			<if test="param.statusCodeList !=null">and A.STATUS_CODE in
				<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
			</if>

			<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
			<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
			<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
			<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
			<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
			<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
			<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
				A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
			</if>
			<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
				A.MANAGE_LABEL_NAME=#{param.manageLabelName}
			</if>
			<if test="param.searchCondition !=null and param.searchCondition != ''">and
				(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
			</if>
			<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
					collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
					collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
					collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
				and A.DLR_CODE IN
				<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
			<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
				<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
				A.COLUMN6=#{param.businessHeatCode}
			</if>
			<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
				A.COLUMN5=#{param.businessHeatName}
			</if>
			<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
				<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
				<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
				<choose>
					<when test='param.isFirstOverdue == "1"'>
						and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
					</when>
					<otherwise>
						and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
					</otherwise>
				</choose>
			</if>
			<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
				A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
			</if>
			<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
				<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]></if>
			GROUP BY A.PHONE
			order by A.CREATED_DATE desc
				) O where 1=1
			<if test="param.yhSignNum != null and param.yhSignNum != ''">
				AND O.yhSignNum = #{param.yhSignNum}
			</if>
			<if test="param.dlSignNum != null and param.dlSignNum != ''">
				AND O.dlSignNum = #{param.dlSignNum}
			</if>
			<if test="param.userGroupName != null and param.userGroupName != ''">
				AND O.USER_GROUP_NAME like concat('%', #{param.userGroupName}, '%')
			</if>
	 	LIMIT #{param.pageNo},#{param.pageSize}
	</select>


	<select id="entireDlrClueInfoNewCount" resultType="java.lang.Integer">
		select * from (
		select
			count(*)
		from adp_leads.t_sac_clue_info_dlr A
		where
			INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
			<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
				v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
			</if>
			<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
				v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
			</if>
			<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
				<foreach item="item" index="index" open="(" separator="," close=")"
						 collection="param.saleOrderState.split(',')">#{item}
				</foreach>
			</if>
			<if test="param.smartId !=null and param.smartId !=''">
				and A.COLUMN10=#{param.smartId}
			</if>

			<if test="param.cusSource !=null and param.cusSource !=''">
				and A.CUS_SOURCE=#{param.cusSource}
			</if>

			<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
				NULL
			</if>
			<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
			</if>
			<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
			</if>
			<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
				('10','7')
			</if>

			<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
			<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
			<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
				A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
			</if>
			<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
				A.INTEN_LEVEL_NAME=#{param.intenLevelName}
			</if>
			<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
				A.INTEN_BRAND_CODE=#{param.intenBrandCode}
			</if>
			<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
				A.INTEN_BRAND_NAME=#{param.intenBrandName}
			</if>
			<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
				A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
			</if>
			<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
				A.INTEN_SERIES_NAME=#{param.intenSeriesName}
			</if>
			<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
				A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
			</if>
			<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
				A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
			</if>
			<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
				A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
			</if>
			<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
				A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
			</if>
			<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
				A.INNER_COLOR_CODE=#{param.innerColorCode}
			</if>
			<if test="param.innerColorName !=null and param.innerColorName !=''">and
				A.INNER_COLOR_NAME=#{param.innerColorName}
			</if>
			<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
			<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
			<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
			<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
				A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
			</if>
			<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
				A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
			</if>
			<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
			<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
				A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
			</if>
			<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
				A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
			</if>
			<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
				A.INFO_CHAN_M_NAME=#{param.infoChanMName}
			</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
				A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
			</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
				A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
			</if>
			<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
				A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
			</if>
			<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
				A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
			</if>
			<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
			<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
				UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
				LOWER(concat('%',#{param.channelName},'%')))
			</if>
			<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
			<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
			<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
			<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
			<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
			<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
			<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
			<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
				A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
			</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
			<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
			<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
			<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
			<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
			<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
			<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
				A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
			</if>
			<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and
				A.UPDATE_CONTROL_ID=#{param.updateControlId}
			</if>

			<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
				concat('%',#{param.pvServerOrder},'%')
			</if>
			<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>

			<choose>
				<when test="param.realPhone !=null and param.realPhone !=''">
					and A.PHONE = #{param.realPhone}
				</when>
				<otherwise>
					<if test="param.phone !=null and param.phone !=''">
					  	and A.PHONE like concat('%',#{param.phone},'%')
					</if>
				</otherwise>
			</choose>
			<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
				<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
					#{item}
				</foreach>
			</if>

			<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
				concat('%',#{param.phoneBackup},'%')
			</if>
			<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
			</if>
			<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
				A.CREATED_DATE>=#{param.createdDateStart}
			</if>
			<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
				<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
			<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
				A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
			</if>
			<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
				<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

			<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
				concat('%', #{param.reviewPersonName}, '%')
			</if>
			<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
				#{param.reviewPersonId}
			</if>
			<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
				A.ASSIGN_TIME>=#{param.assignTimeStart}
			</if>
			<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
				<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
			<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
				A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
			</if>
			<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
				<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

			<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
				and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
			</if>
			<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
				<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
			</if>

			<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
				A.RECEIVE_TIME>=#{param.receiveTimeStart}
			</if>
			<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
				<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
			<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
				<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
			<if test="param.statusCodeList !=null">and A.STATUS_CODE in
				<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
			</if>

			<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
			<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
			<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
			<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
			<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
			<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
			<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
				A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
			</if>
			<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
				A.MANAGE_LABEL_NAME=#{param.manageLabelName}
			</if>
			<if test="param.searchCondition !=null and param.searchCondition != ''">and
				(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
			</if>
			<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
					collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
					collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
					collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
			</if>
			<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
				and A.DLR_CODE IN
				<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
			<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
				<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
				A.COLUMN6=#{param.businessHeatCode}
			</if>
			<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
				A.COLUMN5=#{param.businessHeatName}
			</if>
			<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
				<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
				<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
				A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
			</if>
			<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
				<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]></if>
				) O where 1=1
			<if test="param.yhSignNum != null and param.yhSignNum != ''">
				AND O.yhSignNum = #{param.yhSignNum}
			</if>
			<if test="param.dlSignNum != null and param.dlSignNum != ''">
				AND O.dlSignNum = #{param.dlSignNum}
			</if>
			<if test="param.userGroupName != null and param.userGroupName != ''">
				AND O.USER_GROUP_NAME like concat('%', #{param.userGroupName}, '%')
			</if>
	</select>


	<select id="entireDlrClueInfoDefeat" resultType="Map">
		select * from (
		select
		A.FIRST_TESTDRIVER_TIME,
		A.FIRST_ARRIVAL_TIME,
		A.ID,
		A.SERVER_ORDER,
		A.PV_SERVER_ORDER,
		A.CUST_ID,
		A.CUST_NAME,
		A.PHONE,
		A.PHONE_BACKUP,
		<choose>
			<when test='param.notEncryption =="1"'>
				A.PHONE
			</when>
			<otherwise>
				INSERT(A.PHONE,4,4,'****')
			</otherwise>
		</choose> as TMD_PHONE,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN3 AS INNER_COLOR_CODE,
		B.COLUMN4 as INNER_COLOR_NAME,
		B.COLUMN1 as OUT_COLOR_CODE,
		B.COLUMN2 as OUT_COLOR_NAME,
		ifnull(B.COLUMN15,'定期跟进') as followReason,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		F.source5,
		F.source6,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		F.attr83,
		B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
		case
		when sum(t.CUSTOMER_PHONE) is null then '否'
		else '是' end isCompleteCarApply,/*是否完成试驾*/
		case
		when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是'
		else '否' end as isOverdue,
		<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
		<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>
		,IFNULL(K.dlSignNum,0) AS dlSignNum
		,IFNULL(K.yhSignNum,0) AS yhSignNum,
		a.ALLOCATE_TIME,
		G.userGroupName,
		tmlv.LOOKUP_VALUE_NAME,
		a.CUS_SOURCE,
		ifnull(fod.first_overdue_time, null) firstOverDueTime,
		case when fod.first_overdue_time = null then null
		when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
		else '否' end as isFirstOverdue
		from csc.t_sac_clue_info_dlr A
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on A.phone = B.phone
		left join csc.t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		K ON K.CUSTOMER_PHONE =A.PHONE
		LEFT JOIN (
		SELECT
		cust_id,
		GROUP_CONCAT( USER_GROUP_NAME ) AS userGroupName
		FROM
		csc.t_sac_user_group_detail tsugd
		left join csc.t_sac_user_group tsug on tsugd.USER_GROUP_ID = tsug.USER_GROUP_ID
		where tsugd.IS_ENABLE = '1' and tsug.IS_ENABLE = '1' GROUP BY CUST_ID
		) G on G.cust_id = A.cust_id
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join  mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		where
		INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>

		<if test="param.cusSource !=null and param.cusSource !=''">
		and A.CUS_SOURCE=#{param.cusSource}
		</if>

		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
			and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
		</if>
		<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
		</if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
			and A.DLR_CODE IN
			<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>
		GROUP BY A.PHONE
		order by A.CREATED_DATE desc
		    ) O where 1=1
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND O.yhSignNum = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND O.dlSignNum = #{param.dlSignNum}
		</if>
		<if test="param.userGroupName != null and param.userGroupName != ''">
			AND O.USER_GROUP_NAME like concat('%', #{param.userGroupName}, '%')
		</if>
	</select>

	<select id="entireDlrClueInfo3" resultType="com.ly.adp.csc.entities.DefeatEntireDlrClueInfoExport">

		select
		A.CUST_NAME,
		INSERT(A.PHONE,4,4,'****') as TMD_PHONE,

		A.GENDER_NAME,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.REVIEW_PERSON_NAME,
		A.INTEN_CAR_TYPE_NAME,
		B.COLUMN2 as OUT_COLOR_NAME,
		B.COLUMN4 as INNER_COLOR_NAME,
		A.CHANNEL_NAME,
		A.INFO_CHAN_D_NAME,
		F.source5,
		F.source6,
		A.FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		B.PLAN_REVIEW_TIME,
		case when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是' else '否' end as isOverdue,
		B.ASSIGN_STATUS_NAME,
		A.LAST_REVIEW_TIME LAST_REVIEW_TIME1,
		B.OVER_REVIEW_TIME,
		A.INTEN_LEVEL_NAME,
		A.COLUMN6,
		A.STATUS_NAME,
		A.CREATED_DATE,
		A.COLUMN10,
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,
		IFNULL(K.dlSignNum,0) AS dlSignNum,
		IFNULL(K.yhSignNum,0) AS yhSignNum

		from t_sac_clue_info_dlr A
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join t_sac_review B on A.phone = B.phone
		left join t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		K ON K.CUSTOMER_PHONE =A.PHONE
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		where  INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.smartId !=null and param.smartId !=''">and A.COLUMN10=#{param.smartId}</if>
		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">and A.DLR_CODE IN <foreach
				collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<if test="param.isFirstOverdue == '1'.toString">
				and  A.CREATED_DATE <![CDATA[<]]>  DATE_ADD(ifNull(A.FIRST_REVIEW_TIME,now()),INTERVAL -1 DAY)
			</if>
			<if test="param.isFirstOverdue == '0'.toString">
				and  A.CREATED_DATE >= DATE_ADD(ifNull(A.FIRST_REVIEW_TIME,now()),INTERVAL -1 DAY)
			</if>
		</if>
		GROUP BY A.PHONE
		order by A.CREATED_DATE desc

		<trim prefix="WHERE" prefixOverrides="AND |OR ">
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND ifnull(K.yhSignNum ,0)= #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND ifnull(K.dlSignNum,0) = #{param.dlSignNum}
		</if>
		</trim>
	</select>

	<select id="entireDlrClueInfo1" resultType="java.util.Map">
		select
			A.ID,
			A.SERVER_ORDER,
			A.PV_SERVER_ORDER,
			A.CUST_ID,
			A.CUST_NAME,
			A.PHONE,
			A.PHONE_BACKUP,
		<choose>
			<when test='param.notEncryption =="1"'>
				A.PHONE
			</when>
			<otherwise>
				INSERT(A.PHONE,4,4,'****')
			</otherwise>
		</choose> as TMD_PHONE,
		A.INTEN_LEVEL_CODE,
		A.INTEN_LEVEL_NAME,
		A.INTEN_BRAND_CODE,
		A.INTEN_BRAND_NAME,
		A.INTEN_SERIES_CODE,
		A.INTEN_SERIES_NAME,
		A.INTEN_CAR_TYPE_CODE,
		A.INTEN_CAR_TYPE_NAME,
		A.INTEN_OPTION_PACKAGE_CODE,
		A.INTEN_OPTION_PACKAGE_NAME,
		A.INNER_COLOR_CODE,
		A.INNER_COLOR_NAME,
		A.OUT_COLOR_CODE,
		A.OUT_COLOR_NAME,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.SOURCE_SYSTEMT_CODE,
		A.SOURCE_SYSTEMT_NAME,
		A.RECEIVE_TIME,
		A.SOURCE_SERVER_ORDER,
		A.INFO_CHAN_M_CODE,
		A.INFO_CHAN_M_NAME,
		A.INFO_CHAN_D_CODE,
		A.INFO_CHAN_D_NAME,
		A.INFO_CHAN_DD_CODE,
		A.INFO_CHAN_DD_NAME,
		A.CHANNEL_CODE,
		A.CHANNEL_NAME,
		A.GENDER_CODE,
		A.GENDER_NAME,
		A.STATUS_CODE,
		A.STATUS_NAME,
		A.DEAL_NODE_CODE,
		A.DEAL_NODE_NAME,
		A.REVIEW_ID,
		A.FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		A.ASSIGN_TIME,
		A.REVIEW_PERSON_NAME,
		A.REVIEW_PERSON_ID,
		A.COLUMN1,
		A.COLUMN2,
		A.COLUMN3,
		A.COLUMN4,
		A.COLUMN5,
		A.COLUMN6,
		A.COLUMN7,
		A.COLUMN8,
		A.COLUMN9,
		A.COLUMN10,
		A.COLUMN11,
		A.COLUMN12,
		A.COLUMN13,
		A.COLUMN14,
		A.COLUMN15,
		A.COLUMN16,
		A.COLUMN17,
		A.COLUMN18,
		A.COLUMN19,
		A.COLUMN20,
		A.BIG_COLUMN1,
		A.BIG_COLUMN2,
		A.BIG_COLUMN3,
		A.BIG_COLUMN4,
		A.BIG_COLUMN5,
		A.EXTENDS_JSON,
		A.OEM_ID,
		A.GROUP_ID,
		A.CREATOR,
		A.CREATED_NAME,
		A.CREATED_DATE,
		A.MODIFIER,
		A.MODIFY_NAME,
		A.LAST_UPDATED_DATE,
		A.IS_ENABLE,
		A.UPDATE_CONTROL_ID,
		A.PROVINCE_CODE,
		A.PROVINCE_NAME,
		A.CITY_CODE,
		A.CITY_NAME,
		A.COUNTY_CODE,
		A.COUNTY_NAME,
		A.MANAGE_LABEL_CODE,
		A.MANAGE_LABEL_NAME,
		B.ASSIGN_STATUS,
		B.ASSIGN_STATUS_NAME,
		B.PLAN_REVIEW_TIME,
		B.REVIEW_STATUS,
		B.REVIEW_STATUS_NAME,
		B.OVER_REVIEW_TIME,
		case when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是' else '否' end as isOverdue,
			<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
			<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>
		from t_sac_clue_info_dlr A
        left join t_sac_review B on A.phone = B.phone
		where 1=1  AND INSTR(A.DLR_CODE,"HOST" )
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
	</select>

	<select id="queryLookup" resultType="java.lang.String">
		SELECT LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE =#{param.LOOKUP_TYPE_CODE} and LOOKUP_VALUE_CODE=#{param.LOOKUP_VALUE_CODE}
	</select>
	<select id="queryLookupMap" resultType="java.util.Map">
		SELECT * from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE =#{param.LOOKUP_TYPE_CODE} and LOOKUP_VALUE_CODE=#{param.LOOKUP_VALUE_CODE}
	</select>

	<select id="queryLookupMapSpecifyFields" resultType="java.util.Map">
		SELECT LOOKUP_VALUE_NAME, ATTRIBUTE1, IS_ENABLE from mp.t_prc_mds_lookup_value where LOOKUP_TYPE_CODE =#{param.LOOKUP_TYPE_CODE} and LOOKUP_VALUE_CODE=#{param.LOOKUP_VALUE_CODE}
	</select>

	<select id="getMdmDlrInfoQuery" resultType="java.util.Map">
		SELECT
		a.DLR_ID,
		a.DLR_CODE,
		a.DLR_SHORT_NAME,
		b.DLR_BUILD_AGREEMENT_URL,
		a.DLR_TYPE,
		a.AGENT_AREA,
		c.AGENT_ID,
		b.AGENT_COMPANY_ID,
		a.CREATOR,
		a.CREATED_DATE,
		b.AGENT_COMPANY_NAME,
		c.AGENT_NAME
		FROM
		mp.t_usc_mdm_org_dlr a,
		mp.t_usc_mdm_agent_company b,
		mp.t_usc_mdm_agent_info c
		WHERE
		a.COMPANY_ID = b.AGENT_COMPANY_ID
		AND b.AGENT_ID = c.AGENT_ID
		<if test="param.dlrId != null and '' != param.dlrId">
			and a.DLR_ID = #{param.dlrId}
		</if>
		<if test="param.dlrCode != null and '' != param.dlrCode">
			and a.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.dlrType != null and '' != param.dlrType">
			and a.DLR_TYPE = #{param.dlrType}
		</if>
		<if test="param.dlrTypeIn != null and '' != param.dlrTypeIn">
			and a.DLR_TYPE IN
			<foreach collection="param.dlrTypeIn.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.agentId != null and '' != param.agentId">
			and c.AGENT_ID = #{param.agentId}
		</if>
		<if test="param.dlrExchange != null and '' != param.dlrExchange">
			and a.DLR_ID != #{param.dlrExchange}
		</if>
		/*前端传参公司ID key值不确定 */
		<if test="param.companyId != null and '' != param.companyId">
			and b.AGENT_COMPANY_ID = #{param.companyId}
		</if>
		<if test="param.agentCompanyId != null and '' != param.agentCompanyId">
			and b.AGENT_COMPANY_ID = #{param.agentCompanyId}
		</if>
	</select>

	<select id="entireDlrClueInfo4" resultType="com.ly.adp.csc.entities.EntireDlrClueInfoExport">
		select * from (
		select
		DATE_FORMAT(A.FIRST_ARRIVAL_TIME, '%Y-%m-%d %H:%i:%S' )  FIRST_ARRIVAL_TIME,
		A.FIRST_TESTDRIVER_TIME,
		A.CUST_NAME,
		INSERT(A.PHONE,4,4,'****') as TMD_PHONE,

		A.GENDER_NAME,
		A.DLR_CODE,
		A.DLR_SHORT_NAME,
		A.REVIEW_PERSON_NAME,
		B.INTEN_CAR_TYPE_NAME,
		B.COLUMN2 as OUT_COLOR_NAME,
		B.COLUMN4 as INNER_COLOR_NAME,
		A.CHANNEL_NAME,
		A.INFO_CHAN_D_NAME,
		F.source5,
		F.source6,
		F.attr83,
		DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
		A.LAST_REVIEW_TIME,
		B.ASSIGN_STATUS_NAME,
		A.STATUS_NAME,
		A.INTEN_LEVEL_NAME,
		A.COLUMN6,
		B.PLAN_REVIEW_TIME,
		DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
		case when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是' else '否' end as isOverdue,
		B.OVER_REVIEW_TIME,
		A.COLUMN10,
		case when sum(t.CUSTOMER_PHONE) is null then '否' else '是' end isCompleteCarApply,/*是否完成试驾*/
		IFNULL(K.dlSignNum,0) AS dlSignNum,
		IFNULL(K.yhSignNum,0) AS yhSignNum,
	 	a.ALLOCATE_TIME,
		G.userGroupName,
		tmlv.LOOKUP_VALUE_NAME,
		A.CUS_SOURCE,
		ifnull(fod.first_overdue_time, null) firstOverDueTime,
		case when fod.first_overdue_time = null then null
		when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
		else '否' end as isFirstOverdue
		from t_sac_clue_info_dlr A
		left join t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=A.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join t_sac_review B on A.phone = B.phone
		left join t_sac_onecust_info F on A.CUST_ID=F.CUST_ID
		left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
		sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
		LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
		K ON K.CUSTOMER_PHONE =A.PHONE
		LEFT JOIN (
		SELECT
		cust_id,
		GROUP_CONCAT( USER_GROUP_NAME ) AS userGroupName
		FROM
		csc.t_sac_user_group_detail tsugd
		left join csc.t_sac_user_group tsug on tsugd.USER_GROUP_ID = tsug.USER_GROUP_ID
		where tsugd.IS_ENABLE = '1' and tsug.IS_ENABLE = '1' GROUP BY CUST_ID
		) G on G.cust_id = A.cust_id
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = A.dlr_code
		left join  mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join csc.t_sac_clue_info_dlr_firstoverdue fod on A.id = fod.clue_dlr_id
		where INSTR(A.DLR_CODE,"HOST" ) <![CDATA[ < ]]> 1
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">and A.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">and A.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from mp.t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>

		<if test="param.cusSource !=null and param.cusSource !=''">
			and A.CUS_SOURCE=#{param.cusSource}
		</if>
		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">AND A.FIRST_REVIEW_TIME IS NOT
			NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">AND A.STATUS_CODE not in
			('10','7')
		</if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>

		<if test="param.id !=null and param.id !=''">and A.ID=#{param.id}</if>
		<if test="param.custId !=null and param.custId !=''">and A.CUST_ID=#{param.custId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and
			A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and
			A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">and
			A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">and
			A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">and
			A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">and
			A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">and
			A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">and
			A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">and
			A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">and
			A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">and
			A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">and
			A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">and A.OUT_COLOR_CODE=#{param.outColorCode}</if>
		<if test="param.outColorName !=null and param.outColorName !=''">and A.OUT_COLOR_NAME=#{param.outColorName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and A.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and A.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">and
			A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">and
			A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">and A.RECEIVE_TIME=#{param.receiveTime}</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">and
			A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">and
			A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">and
			A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">and
			A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">and
			A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">and
			A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">and
			A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">and A.CHANNEL_CODE=#{param.channelCode}</if>
		<if test="param.channelName !=null and param.channelName !=''">and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and A.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and A.GENDER_NAME=#{param.genderName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and A.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and A.STATUS_NAME=#{param.statusName}</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">and A.DEAL_NODE_CODE=#{param.dealNodeCode}</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">and A.DEAL_NODE_NAME=#{param.dealNodeName}</if>
		<if test="param.reviewId !=null and param.reviewId !=''">and A.REVIEW_ID=#{param.reviewId}</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">and
			A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">and A.EXTENDS_JSON=#{param.extendsJson}</if>
		<if test="param.oemId !=null and param.oemId !=''">and A.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and A.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and A.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and A.CREATED_NAME=#{param.createdName}</if>
		<if test="param.modifier !=null and param.modifier !=''">and A.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and A.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and
			A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and A.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and
			A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">and A.PV_SERVER_ORDER like
			concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">and A.DLR_CODE IN <foreach
				collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>

		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(A.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>

		GROUP BY A.PHONE
		order by A.CREATED_DATE desc
		) O
		<trim prefix="WHERE" prefixOverrides="AND |OR ">
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND O.yhSignNum = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND O.dlSignNum = #{param.dlSignNum}
		</if>
		<if test="param.userGroupName != null and param.userGroupName != ''">
			AND O.USER_GROUP_NAME like concat('%', #{param.userGroupName}, '%')
		</if>
		</trim>
	</select>

	<select id="selectUserIdAndEmpNameByParam" resultType="java.util.Map">
		SELECT
			`USER_ID`  AS userId,
			`EMP_NAME` AS empName
		FROM
			`mp`.`t_usc_mdm_org_employee`
		WHERE
			`STATION_ID` = 'smart_bm_0075_C'
			AND `USER_STATUS` = '1'
			<if test="param.orgType != null and param.orgType != ''">
				AND `ORG_TYPE` = #{param.orgType}
			</if>
			<if test="param.empName != null and param.empName != ''">
				AND `EMP_NAME` LIKE CONCAT('%', #{param.empName}, '%')
			</if>
	</select>

	<select id="findEmployee" resultType="java.util.Map">
		select
			USER_ID,
			EMP_NAME
			from mp.t_usc_mdm_org_employee
		where
			USER_STATUS in ('1','3')
			and station_Id in ('smart_bm_0006','smart_bm_0007','smart_bm_0018','smart_bm_0019','smart_bm_0061',
							   'smart_bm_0064','smart_bm_0005','smart_bm_0016')
			<if test="dlrCodeIn != null and dlrCodeIn != ''">
			and dlr_Code  in
				<foreach collection="dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
			</foreach>
		</if>
		<if test="empName != null and empName != ''">
			and emp_Name  like concat('%', #{empName}, '%')
		</if>
		union
		select
			t.USER_ID,
			t.EMP_NAME
		from mp.t_usc_mdm_org_employee t
		left join (SELECT EMP_ID,APPLY_NOTE,max(date_format(CREATED_DATE,'%Y-%m-%d')) LEAVE_DATE
		FROM mp.t_usc_bu_employeeedit_apply where apply_type = '3' and APPLY_STATU='2' GROUP BY EMP_ID) ea on
		ea.EMP_ID=t.EMP_ID
		where
			t.USER_STATUS='2'
		  and t.station_Id in ('smart_bm_0006','smart_bm_0007','smart_bm_0018','smart_bm_0019','smart_bm_0061',
							 'smart_bm_0064','smart_bm_0005','smart_bm_0016')
		and TIMESTAMPDIFF(DAY,ea.LEAVE_DATE,now()) &lt;=180
		<if test="dlrCodeIn != null and dlrCodeIn != ''">
			and t.dlr_Code  in
			<foreach collection="dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="empName != null and empName != ''">
			and t.emp_Name  like concat('%', #{empName}, '%')
		</if>
    </select>

	<select id="queryDlrCodeByDlrId" resultType="java.lang.String">
		select group_concat(dlr_code separator ',') as dlrcode
		from mp.t_usc_mdm_org_dlr
		where
			dlr_id in
		<foreach collection="dlrId.split(',')" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</select>
	<select id="getClueDetailAndCustomInfo" resultType="com.ly.adp.csc.entities.vo.ClueDetailCustomInfoVO">
		SELECT
			A.LAST_UPDATED_DATE,
			B.PLAN_REVIEW_TIME,
			B.OVER_REVIEW_TIME,
			A.LAST_REVIEW_TIME,
			A.column6 as businessHeatName,
			A.INFO_CHAN_M_NAME,
			A.CHANNEL_NAME,
			A.CHANNEL_NAME,
			A.INNER_COLOR_NAME,
			A.OUT_COLOR_NAME,
			A.PHONE,
			A.PHONE,
			A.GENDER_NAME,
			INSERT ( A.PHONE, 4, 4, '****' ) AS TMD_PHONE,
			A.STATUS_NAME,
			A.STATUS_CODE,
			B.REVIEW_PERSON_NAME,
			B.REVIEW_PERSON_ID,
			B.LAST_REVIEW_TIME,
			A.INTEN_CAR_TYPE_NAME,
			A.INTEN_CAR_TYPE_CODE,
			A.CUST_NAME,
			A.CUST_ID
		FROM
			t_sac_clue_info_dlr A
			LEFT JOIN csc.t_sac_review B ON A.phone = B.phone
			    and A.CUST_ID = B.CUST_ID
		WHERE A.SERVER_ORDER = #{serverOrder}
	</select>

	<!--线索列表活动相关-->
	<select id="entireDlrClueInfoActivity" resultType="java.util.Map">
		select
		T.CUSTOMER_PHONE as phone,
		sum(case when ACT.CREATE_TYPE_CODE = 'DEVELOP' then 1 else 0 end ) dlSignNum,
		sum(case when ACT.CREATE_TYPE_CODE != 'DEVELOP' then 1 else 0 end ) yhSignNum
		from
		csc.t_acc_bu_activity_customer T
		left join csc.t_acc_bu_activity ACT on
		ACT.ACTIVITY_ID = T.ACTIVITY_ID
		where
		(T.IS_CHECK_IN = '1'
		or ACT.CREATE_TYPE_CODE = 'DEVELOP')
		and T.IS_ENABLE = '1'
		and T.CUSTOMER_PHONE in
		<foreach collection="phones" close=")" open="(" separator="," item="item">
			#{item}
		</foreach>
		group by
		T.CUSTOMER_PHONE
	</select>

	<select id="entireDlrClueInfoUserGroup" resultType="java.util.Map">
		select
		tsugd.CUST_ID as custId,
		GROUP_CONCAT(USER_GROUP_NAME) as userGroupName
		from
		csc.t_sac_user_group_detail tsugd
		left join csc.t_sac_user_group tsug on
		tsugd.USER_GROUP_ID = tsug.USER_GROUP_ID
		where
		tsugd.IS_ENABLE = '1'
		and tsug.IS_ENABLE = '1'
		and tsugd.CUST_ID in
		<foreach collection="custIds" close=")" open="(" separator="," item="item">
			#{item}
		</foreach>
		group by
		tsugd.CUST_ID
	</select>

	<select id="entireDlrClueInfoAllFields" resultType="java.util.Map">
		SELECT
			A.FIRST_TESTDRIVER_TIME,
			DATE_FORMAT(A.FIRST_ARRIVAL_TIME, '%Y-%m-%d %H:%i:%S' )  FIRST_ARRIVAL_TIME,
			A.ID,
			A.SERVER_ORDER,
			A.PV_SERVER_ORDER,
			A.CUST_ID,
			A.CUST_NAME,
			A.PHONE,
			A.PHONE_BACKUP,
			<choose>
				<when test='param.notEncryption =="1"'>
					A.PHONE
				</when>
				<otherwise>
					INSERT(A.PHONE,4,4,'****')
				</otherwise>
			</choose> as TMD_PHONE,
			A.INTEN_LEVEL_CODE,
			A.INTEN_LEVEL_NAME,
			A.INTEN_BRAND_CODE,
			A.INTEN_BRAND_NAME,
			A.INTEN_SERIES_CODE,
			A.INTEN_SERIES_NAME,
			A.INTEN_OPTION_PACKAGE_CODE,
			A.INTEN_OPTION_PACKAGE_NAME,
			A.INTEN_CAR_TYPE_CODE,
			A.INTEN_CAR_TYPE_NAME,
			B.COLUMN3 AS INNER_COLOR_CODE,
			B.COLUMN4 as INNER_COLOR_NAME,
			B.COLUMN1 as OUT_COLOR_CODE,
			B.COLUMN2 as OUT_COLOR_NAME,
			ifnull(B.COLUMN15,'定期跟进') as followReason,
			A.DLR_CODE,
			A.DLR_SHORT_NAME,
			A.SOURCE_SYSTEMT_CODE,
			A.SOURCE_SYSTEMT_NAME,
			A.RECEIVE_TIME,
			A.SOURCE_SERVER_ORDER,
			A.INFO_CHAN_M_CODE,
			A.INFO_CHAN_M_NAME,
			A.INFO_CHAN_D_CODE,
			A.INFO_CHAN_D_NAME,
			A.INFO_CHAN_DD_CODE,
			A.INFO_CHAN_DD_NAME,
			A.CHANNEL_CODE,
			A.CHANNEL_NAME,
			A.GENDER_CODE,
			A.GENDER_NAME,
			A.STATUS_CODE,
			A.STATUS_NAME,
			A.DEAL_NODE_CODE,
			A.DEAL_NODE_NAME,
			A.REVIEW_ID,
			DATE_FORMAT(A.FIRST_REVIEW_TIME, '%Y-%m-%d %H:%i:%S' ) FIRST_REVIEW_TIME,
			A.LAST_REVIEW_TIME,
			A.ASSIGN_TIME,
			A.REVIEW_PERSON_NAME,
			A.REVIEW_PERSON_ID,
			A.COLUMN1,
			A.COLUMN2,
			A.COLUMN3,
			A.COLUMN4,
			A.COLUMN5,
			A.COLUMN6,
			A.COLUMN7,
			A.COLUMN8,
			A.COLUMN9,
			A.COLUMN10,
			A.COLUMN11,
			A.COLUMN12,
			A.COLUMN13,
			A.COLUMN14,
			A.COLUMN15,
			A.COLUMN16,
			A.COLUMN17,
			A.COLUMN18,
			A.COLUMN19,
			A.COLUMN20,
			A.BIG_COLUMN1,
			A.BIG_COLUMN2,
			A.BIG_COLUMN3,
			A.BIG_COLUMN4,
			A.BIG_COLUMN5,
			A.EXTENDS_JSON,
			A.OEM_ID,
			A.GROUP_ID,
			A.CREATOR,
			A.CREATED_NAME,
			DATE_FORMAT(A.CREATED_DATE, '%Y-%m-%d %H:%i:%S' ) CREATED_DATE,
			A.MODIFIER,
			A.MODIFY_NAME,
			A.LAST_UPDATED_DATE,
			A.IS_ENABLE,
			A.UPDATE_CONTROL_ID,
			A.PROVINCE_CODE,
			A.PROVINCE_NAME,
			A.CITY_CODE,
			A.CITY_NAME,
			A.COUNTY_CODE,
			A.COUNTY_NAME,
			A.MANAGE_LABEL_CODE,
			A.MANAGE_LABEL_NAME,
			F.source5,
			F.source6,
			B.ASSIGN_STATUS,
			B.ASSIGN_STATUS_NAME,
			B.PLAN_REVIEW_TIME,
			B.REVIEW_STATUS,
			B.REVIEW_STATUS_NAME,
			B.OVER_REVIEW_TIME,
			F.attr83,
			B.UPDATE_CONTROL_ID as REVIEW_UPDATE_CONTROL_ID,
			case
			when t.CUSTOMER_PHONE is null then '否'
			else '是' end isCompleteCarApply,/*是否完成试驾*/
			case
			when ifnull(B.PLAN_REVIEW_TIME,now()) &lt; now() then '是'
			else '否' end as isOverdue,
			<![CDATA[ CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时'),CONCAT(cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char),'分钟' )) hourMinuteTimes, ]]>
			<![CDATA[ CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1 ,'小时'))  ELSE  CONCAT(CONCAT((TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) ,'小时')) end as hourTimes ]]>,

			<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
				IFNULL(K.dlSignNum,0) AS dlSignNum,
				IFNULL(K.yhSignNum,0) AS yhSignNum,
			</if>

			a.ALLOCATE_TIME,
			tmlv.LOOKUP_VALUE_NAME,
			ifnull(fod.first_overdue_time, null) firstOverDueTime,
			case when fod.first_overdue_time is null then null
			when fod.first_overdue_time &lt; ifnull(A.FIRST_REVIEW_TIME, now()) then '是'
			else '否' end as isFirstOverdue,
			a.CUS_SOURCE
		<include refid="entireDlrClueInfoFrom"/>
		<include refid="entireDlrClueInfoACondition"/>
		<include refid="entireDlrClueInfoNonACondition"/>
		ORDER BY A.CREATED_DATE DESC
	</select>


	<select id="selectCallPersonByParam" resultType="java.util.Map">
		SELECT
			t0.userId,
			t0.empName,
			t0.agentCode,
			t0.agentCompanyId,
			t0.stationId,
			t0.userStatus
		FROM
			(
				SELECT
					t1.`USER_ID`          AS userId,
					t1.`EMP_NAME`         AS empName,
					t4.`AGENT_CODE`       AS agentCode,
					t3.`AGENT_COMPANY_ID` AS agentCompanyId,
					t1.`STATION_ID`       AS stationId,
					t1.`USER_STATUS`      AS userStatus
				FROM
					`mp`.`t_usc_mdm_org_employee` AS t1
					LEFT JOIN `mp`.`t_usc_mdm_org_dlr` AS t2 ON t1.`DLR_CODE` = t2.`DLR_CODE`
					LEFT JOIN `mp`.`t_usc_mdm_agent_company` AS t3 ON t2.`COMPANY_ID` = t3.`AGENT_COMPANY_ID`
					LEFT JOIN `mp`.`t_usc_mdm_agent_info` AS t4 ON t3.`AGENT_ID` = t4.`AGENT_ID`
				WHERE
					t1.`ORG_TYPE` = '1'
			UNION ALL
				SELECT
					t1.`USER_ID`          AS userId,
					t1.`EMP_NAME`         AS empName,
					t4.`AGENT_CODE`       AS agentCode,
					t3.`AGENT_COMPANY_ID` AS agentCompanyId,
					t1.`STATION_ID`       AS stationId,
					t1.`USER_STATUS`      AS userStatus
				FROM
					`mp`.`t_usc_mdm_org_employee` AS t1
					LEFT JOIN `mp`.`t_usc_mdm_org_dlr` AS t2 ON t1.`DLR_CODE` = t2.`DLR_CODE`
					LEFT JOIN `mp`.`t_usc_mdm_agent_company` AS t3 ON t2.`COMPANY_ID` = t3.`AGENT_COMPANY_ID`
					LEFT JOIN `mp`.`t_usc_mdm_agent_info` AS t4 ON t1.`ORG_ID` = t4.`AGENT_ID`
				WHERE
					t1.`ORG_TYPE` = '2'
			UNION ALL
				SELECT
					t1.`USER_ID`          AS userId,
					t1.`EMP_NAME`         AS empName,
					t3.`AGENT_CODE`       AS agentCode,
					t2.`AGENT_COMPANY_ID` AS agentCompanyId,
					t1.`STATION_ID`       AS stationId,
					t1.`USER_STATUS`      AS userStatus
				FROM
					`mp`.`t_usc_mdm_org_employee` AS t1
					LEFT JOIN `mp`.`t_usc_mdm_agent_company` t2 ON t1.`ORG_ID` = t2.`AGENT_COMPANY_ID`
					LEFT JOIN `mp`.`t_usc_mdm_agent_info` t3 ON t2.`AGENT_ID` = t3.`AGENT_ID`
				WHERE
					t1.ORG_TYPE = '3'
			) AS t0
		WHERE
			t0.stationId = 'smart_bm_0075_C'
			AND t0.userStatus = '1'
			<if test="param.agentCode != null and param.agentCode != ''">
				AND t0.agentCode = #{param.agentCode}
			</if>
			<if test="param.agentCompanyId != null and param.agentCompanyId != ''">
				AND t0.agentCompanyId = #{param.agentCompanyId}
			</if>
			<if test="param.empName != null and param.empName != ''">
				AND t0.empName LIKE CONCAT('%', #{param.empName}, '%')
			</if>
	</select>

	<sql id="entireDlrClueInfoCte">
		WITH base_data AS (
			SELECT B.PHONE,
			B.CUST_ID,
			B.dlr_code,
			B.id,
			B.CREATED_DATE
			FROM (
				SELECT A.PHONE, MAX(A.CREATED_DATE) as max_date
				FROM csc.t_sac_clue_info_dlr A
				<include refid="entireDlrClueInfoDlrCondition"/>
				GROUP BY A.PHONE
			) tmp
			JOIN csc.t_sac_clue_info_dlr B ON B.PHONE = tmp.PHONE AND B.CREATED_DATE = tmp.max_date
		),
		first_overdue AS (
			SELECT t1.clue_dlr_id, t1.first_assign_dlr_time, t1.first_overdue_time
			FROM csc.t_sac_clue_info_dlr_firstoverdue t1
			INNER JOIN (
				SELECT clue_dlr_id, MAX(created_time) as max_created_time
				FROM csc.t_sac_clue_info_dlr_firstoverdue
				GROUP BY clue_dlr_id
			) t2 ON t1.clue_dlr_id = t2.clue_dlr_id
			AND t1.created_time = t2.max_created_time
		)
	</sql>
	<sql id="entireDlrClueInfoAllFieldsFrom">
		from base_data p
		left join csc.t_sac_test_drive_sheet t on t.CUSTOMER_PHONE=p.phone and t.TEST_TYPE in ('1','2') and t.TEST_STATUS='2'
		left join csc.t_sac_review B on p.phone = B.phone
		left join csc.t_sac_onecust_info F on p.CUST_ID=F.CUST_ID
		<if test="(param.dlSignNum != null and param.dlSignNum != '') || (param.yhSignNum != null and param.yhSignNum != '')">
			left join (select T.CUSTOMER_PHONE,sum(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN	1 ELSE	0 END ) dlSignNum,
			sum(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN	1 ELSE	0 END ) yhSignNum from csc.t_acc_bu_activity_customer T
			LEFT JOIN csc.t_acc_bu_activity ACT ON ACT.ACTIVITY_ID = T.ACTIVITY_ID
			where (T.IS_CHECK_IN='1' or ACT.CREATE_TYPE_CODE='DEVELOP') and T.IS_ENABLE='1'   group by T.CUSTOMER_PHONE)
			K ON K.CUSTOMER_PHONE =p.PHONE
		</if>
		left join mp.t_usc_mdm_org_dlr dlr on dlr.DLR_CODE = p.dlr_code
		left join mp.t_prc_mds_lookup_value tmlv on dlr.sarea_id = tmlv.LOOKUP_VALUE_CODE and tmlv.LOOKUP_TYPE_CODE = 'ADP_SMALLAREA_717'
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">
			LEFT JOIN orc.T_ORC_VE_BU_SALE_ORDER_TO_C C ON B.CUST_ID = C.BUY_CUST_ID
			LEFT JOIN orc.t_orc_ve_bu_ec_return_order R ON C.SALE_ORDER_CODE = R.RETAIL_NO AND R.REFUND_TYPE = '2' AND
			R.REFUND_STATUS = '1' and r.IS_ENABLE = '1'
			LEFT join mp.t_prc_mds_lookup_value VE8080 on VE8080.LOOKUP_TYPE_CODE='VE8080' and
			concat(C.SALE_ORDER_STATE,IFNULL(R.REFUND_STATUS,''), case when C.SALE_ORDER_STATE='1' or
			C.SALE_ORDER_STATE='8' then C.CURRENT_ORDER_TYPE else '' end)=VE8080.LOOKUP_VALUE_CODE
		</if>
		left join first_overdue fod on p.id = fod.clue_dlr_id
	</sql>
	<sql id="entireDlrClueInfoDlrCondition">
		WHERE
		INSTR(A.DLR_CODE, "HOST") &lt; 1
		<if test="param.smartId !=null and param.smartId !=''">
			and A.COLUMN10=#{param.smartId}
		</if>
		<if test="param.cusSource !=null and param.cusSource !=''">
			and A.CUS_SOURCE=#{param.cusSource}
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '1'.toString()">
			AND A.FIRST_REVIEW_TIME IS NOT NULL
		</if>
		<if test="param.isClueFollow != null and param.isClueFollow == '0'.toString()">
			AND A.FIRST_REVIEW_TIME IS NULL
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '1'.toString()">
			AND A.STATUS_CODE in ('10','7')
		</if>
		<if test="param.isClueDefeat != null and param.isClueDefeat == '0'.toString()">
			AND A.STATUS_CODE not in ('10','7')
		</if>
		<if test="param.id !=null and param.id !=''">
			and A.ID=#{param.id}
		</if>
		<if test="param.custId !=null and param.custId !=''">
			and A.CUST_ID=#{param.custId}
		</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
			and A.INTEN_LEVEL_CODE=#{param.intenLevelCode}
		</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">
			and A.INTEN_LEVEL_NAME=#{param.intenLevelName}
		</if>
		<if test="param.intenBrandCode !=null and param.intenBrandCode !=''">
			and A.INTEN_BRAND_CODE=#{param.intenBrandCode}
		</if>
		<if test="param.intenBrandName !=null and param.intenBrandName !=''">
			and A.INTEN_BRAND_NAME=#{param.intenBrandName}
		</if>
		<if test="param.intenSeriesCode !=null and param.intenSeriesCode !=''">
			and A.INTEN_SERIES_CODE=#{param.intenSeriesCode}
		</if>
		<if test="param.intenSeriesName !=null and param.intenSeriesName !=''">
			and A.INTEN_SERIES_NAME=#{param.intenSeriesName}
		</if>
		<if test="param.intenCarTypeCode !=null and param.intenCarTypeCode !=''">
			and A.INTEN_CAR_TYPE_CODE=#{param.intenCarTypeCode}
		</if>
		<if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
			and A.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
		</if>
		<if test="param.intenOptionPackageCode !=null and param.intenOptionPackageCode !=''">
			and A.INTEN_OPTION_PACKAGE_CODE=#{param.intenOptionPackageCode}
		</if>
		<if test="param.intenOptionPackageName !=null and param.intenOptionPackageName !=''">
			and A.INTEN_OPTION_PACKAGE_NAME=#{param.intenOptionPackageName}
		</if>
		<if test="param.innerColorCode !=null and param.innerColorCode !=''">
			and A.INNER_COLOR_CODE=#{param.innerColorCode}
		</if>
		<if test="param.innerColorName !=null and param.innerColorName !=''">
			and A.INNER_COLOR_NAME=#{param.innerColorName}
		</if>
		<if test="param.outColorCode !=null and param.outColorCode !=''">
			and A.OUT_COLOR_CODE=#{param.outColorCode}
		</if>
		<if test="param.outColorName !=null and param.outColorName !=''">
			and A.OUT_COLOR_NAME=#{param.outColorName}
		</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">
			and A.DLR_CODE=#{param.dlrCode}
		</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">
			and A.DLR_SHORT_NAME=#{param.dlrShortName}
		</if>
		<if test="param.sourceSystemtCode !=null and param.sourceSystemtCode !=''">
			and A.SOURCE_SYSTEMT_CODE=#{param.sourceSystemtCode}
		</if>
		<if test="param.sourceSystemtName !=null and param.sourceSystemtName !=''">
			and A.SOURCE_SYSTEMT_NAME=#{param.sourceSystemtName}
		</if>
		<if test="param.receiveTime !=null and param.receiveTime !=''">
			and A.RECEIVE_TIME=#{param.receiveTime}
		</if>
		<if test="param.sourceServerOrder !=null and param.sourceServerOrder !=''">
			and A.SOURCE_SERVER_ORDER=#{param.sourceServerOrder}
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode !=''">
			and A.INFO_CHAN_M_CODE=#{param.infoChanMCode}
		</if>
		<if test="param.infoChanMName !=null and param.infoChanMName !=''">
			and A.INFO_CHAN_M_NAME=#{param.infoChanMName}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode !=''">
			and A.INFO_CHAN_D_CODE=#{param.infoChanDCode}
		</if>
		<if test="param.infoChanDName !=null and param.infoChanDName !=''">
			and A.INFO_CHAN_D_NAME like concat('%',#{param.infoChanDName},'%')
		</if>
		<if test="param.infoChanDdCode !=null and param.infoChanDdCode !=''">
			and A.INFO_CHAN_DD_CODE=#{param.infoChanDdCode}
		</if>
		<if test="param.infoChanDdName !=null and param.infoChanDdName !=''">
			and A.INFO_CHAN_DD_NAME=#{param.infoChanDdName}
		</if>
		<if test="param.channelCode !=null and param.channelCode !=''">
			and A.CHANNEL_CODE=#{param.channelCode}
		</if>
		<if test="param.channelName !=null and param.channelName !=''">
			and (UPPER(A.CHANNEL_NAME) like
			UPPER(concat('%',#{param.channelName},'%')) or LOWER(A.CHANNEL_NAME) like
			LOWER(concat('%',#{param.channelName},'%')))
		</if>
		<if test="param.genderCode !=null and param.genderCode !=''">
			and A.GENDER_CODE=#{param.genderCode}
		</if>
		<if test="param.genderName !=null and param.genderName !=''">
			and A.GENDER_NAME=#{param.genderName}
		</if>
		<if test="param.statusCode !=null and param.statusCode !=''">
			and A.STATUS_CODE=#{param.statusCode}
		</if>
		<if test="param.statusName !=null and param.statusName !=''">
			and A.STATUS_NAME=#{param.statusName}
		</if>
		<if test="param.dealNodeCode !=null and param.dealNodeCode !=''">
			and A.DEAL_NODE_CODE=#{param.dealNodeCode}
		</if>
		<if test="param.dealNodeName !=null and param.dealNodeName !=''">
			and A.DEAL_NODE_NAME=#{param.dealNodeName}
		</if>
		<if test="param.reviewId !=null and param.reviewId !=''">
			and A.REVIEW_ID=#{param.reviewId}
		</if>
		<if test="param.firstReviewTime !=null and param.firstReviewTime !=''">
			and A.FIRST_REVIEW_TIME=#{param.firstReviewTime}
		</if>
		<if test="param.extendsJson !=null and param.extendsJson !=''">
			and A.EXTENDS_JSON=#{param.extendsJson}
		</if>
		<if test="param.oemId !=null and param.oemId !=''">
			and A.OEM_ID=#{param.oemId}
		</if>
		<if test="param.groupId !=null and param.groupId !=''">
			and A.GROUP_ID=#{param.groupId}
		</if>
		<if test="param.creator !=null and param.creator !=''">
			and A.CREATOR=#{param.creator}
		</if>
		<if test="param.createdName !=null and param.createdName !=''">
			and A.CREATED_NAME=#{param.createdName}
		</if>
		<if test="param.modifier !=null and param.modifier !=''">
			and A.MODIFIER=#{param.modifier}
		</if>
		<if test="param.modifyName !=null and param.modifyName !=''">
			and A.MODIFY_NAME=#{param.modifyName}
		</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">
			and A.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
		</if>
		<if test="param.isEnable !=null and param.isEnable !=''">
			and A.IS_ENABLE=#{param.isEnable}
		</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">
			and A.UPDATE_CONTROL_ID=#{param.updateControlId}
		</if>

		<if test="param.pvServerOrder !=null and param.pvServerOrder !=''">
			and A.PV_SERVER_ORDER like concat('%',#{param.pvServerOrder},'%')
		</if>
		<if test="param.serverOrder !=null and param.serverOrder !=''">and A.SERVER_ORDER = #{param.serverOrder}</if>
		<if test="param.phone !=null and param.phone !=''">and A.PHONE like concat('%',#{param.phone},'%')</if>
		<if test="param.phone1 !=null and param.phone1 !=''">and A.PHONE in
			<foreach collection="param.phone1.split(',')" close=")" open="(" separator="," item="item">
				#{item}
			</foreach>
		</if>

		<if test="param.phoneBackup !=null and param.phoneBackup !=''">and A.PHONE_BACKUP like
			concat('%',#{param.phoneBackup},'%')
		</if>
		<if test="param.custName!=null and param.custName !=''">and A.CUST_NAME like concat('%',#{param.custName},'%')
		</if>
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and
			A.CREATED_DATE>=#{param.createdDateStart}
		</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
			<![CDATA[and A.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.firstReviewTimeStart !=null and param.firstReviewTimeStart !=''">and
			A.FIRST_REVIEW_TIME>=#{param.firstReviewTimeStart}
		</if>
		<if test="param.firstReviewTimeEnd !=null and param.firstReviewTimeEnd !=''">
			<![CDATA[and A.FIRST_REVIEW_TIME<=#{param.firstReviewTimeEnd}]]></if>

		<if test="param.reviewPersonName !=null and param.reviewPersonName !=''">and A.REVIEW_PERSON_NAME like
			concat('%', #{param.reviewPersonName}, '%')
		</if>
		<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">and A.REVIEW_PERSON_ID =
			#{param.reviewPersonId}
		</if>
		<if test="param.assignTimeStart !=null and param.assignTimeStart !=''">and
			A.ASSIGN_TIME>=#{param.assignTimeStart}
		</if>
		<if test="param.assignTimeEnd !=null and param.assignTimeEnd !=''">
			<![CDATA[and A.ASSIGN_TIME<=#{param.assignTimeEnd}]]></if>
		<if test="param.lastReviewTimeStart !=null and param.lastReviewTimeStart !=''">and
			A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart}
		</if>
		<if test="param.lastReviewTimeEnd !=null and param.lastReviewTimeEnd !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd}]]></if>

		<if test="param.lastReviewTimeStart2 !=null and param.lastReviewTimeStart2 !=''">
			and A.LAST_REVIEW_TIME>=#{param.lastReviewTimeStart2}
		</if>
		<if test="param.lastReviewTimeEnd2 !=null and param.lastReviewTimeEnd2 !=''">
			<![CDATA[and A.LAST_REVIEW_TIME<=#{param.lastReviewTimeEnd2}]]>
		</if>

		<if test="param.receiveTimeStart !=null and param.receiveTimeStart !=''">and
			A.RECEIVE_TIME>=#{param.receiveTimeStart}
		</if>
		<if test="param.receiveTimeEnd !=null and param.receiveTimeEnd !=''">
			<![CDATA[and A.RECEIVE_TIME<=#{param.receiveTimeEnd}]]></if>
		<if test="param.statusCodeMap !=null and param.statusCodeMap !=''">
			<![CDATA[and A.STATUS_CODE in (#{param.statusCodeMap})]]></if>
		<if test="param.statusCodeList !=null">and A.STATUS_CODE in
			<foreach collection="param.statusCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
		</if>

		<if test="param.provinceCode !=null and param.provinceCode !=''">and A.PROVINCE_CODE=#{param.provinceCode}</if>
		<if test="param.provinceName !=null and param.provinceName !=''">and A.PROVINCE_NAME=#{param.provinceName}</if>
		<if test="param.cityCode !=null and param.cityCode !=''">and A.CITY_CODE=#{param.cityCode}</if>
		<if test="param.cityName !=null and param.cityName !=''">and A.CITY_NAME=#{param.cityName}</if>
		<if test="param.countyCode !=null and param.countyCode !=''">and A.COUNTY_CODE=#{param.countyCode}</if>
		<if test="param.countyName !=null and param.countyName !=''">and A.COUNTY_NAME=#{param.countyName}</if>
		<if test="param.manageLabelCode !=null and param.manageLabelCode !=''">and
			A.MANAGE_LABEL_CODE=#{param.manageLabelCode}
		</if>
		<if test="param.manageLabelName !=null and param.manageLabelName !=''">and
			A.MANAGE_LABEL_NAME=#{param.manageLabelName}
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">and
			(INSTR(A.PHONE,#{param.searchCondition})>0 or INSTR(A.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.provinceCodeIn !=null and param.provinceCodeIn !=''">and A.PROVINCE_CODE IN <foreach
				collection="param.provinceCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">and A.CITY_CODE IN <foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.countyCodeIn !=null and param.countyCodeIn !=''">and A.COUNTY_CODE IN <foreach
				collection="param.countyCodeIn.split(',')" item="item" separator="," open="(" close=")">#{item}
		</foreach>
		</if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">
			and A.DLR_CODE IN
			<foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.planBuyDate !=null and param.planBuyDate !=''">and A.COLUMN2=#{param.planBuyDate}</if>
		<if test="param.intenLevelCodeIn !=null and param.intenLevelCodeIn !=''">and A.INTEN_LEVEL_CODE IN
			<foreach collection="param.intenLevelCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode !=''">and
			A.COLUMN6=#{param.businessHeatCode}
		</if>
		<if test="param.businessHeatName !=null and param.businessHeatName !=''">and
			A.COLUMN5=#{param.businessHeatName}
		</if>
		<if test="param.businessHeatCodeIn !=null and param.businessHeatCodeIn !=''">and A.COLUMN6 IN
			<foreach collection="param.businessHeatCodeIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">and A.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.arrivalDateStart !=null and param.arrivalDateStart !=''">and
			A.FIRST_ARRIVAL_TIME>=#{param.arrivalDateStart}
		</if>
		<if test="param.arrivalDateEnd !=null and param.arrivalDateEnd !=''">
			<![CDATA[and A.FIRST_ARRIVAL_TIME<=#{param.arrivalDateEnd}]]>
		</if>
	</sql>
	<sql id="entireDlrClueInfoNotDlrCondition">
		WHERE 1 = 1
		<if test="param.sysChannel!=null and param.sysChannel=='1'.toString">
			and P.CHANNEL_NAME not in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.sysChannel!=null and param.sysChannel=='0'.toString">
			and P.CHANNEL_NAME in (select
			v.LOOKUP_VALUE_NAME from t_prc_mds_lookup_value v where v.LOOKUP_TYPE_CODE='ADP_CLUE_049' and ATTRIBUTE1='agent' )
		</if>
		<if test="param.attr83 != null and ''!= param.attr83 ">
			<![CDATA[	AND instr(F.attr83,#{param.attr83})>0 ]]>
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='1'.toString">AND t.CUSTOMER_PHONE is not null
		</if>
		<if test="param.isCompleteCarApply!=null and param.isCompleteCarApply=='0'.toString">AND t.CUSTOMER_PHONE is null
		</if>
		<if test="param.chooseTimes !=null and param.chooseTimes !=''">
			<![CDATA[
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) > 0
			AND (CASE WHEN B.PLAN_REVIEW_TIME>=NOW() AND cast(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME))-(floor(TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) / 1440) *1440)-(floor((TIMESTAMPDIFF(minute,NOW(),B.PLAN_REVIEW_TIME) % 1440)/60)*60)) as char) > 0 THEN  (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME))+1  ELSE (TIMESTAMPDIFF(HOUR,NOW(),B.PLAN_REVIEW_TIME)) end) <= #{param.chooseTimes}
			]]>
		</if>
		<if test="param.saleOrderState !=null and param.saleOrderState != ''">AND VE8080.LOOKUP_VALUE_CODE IN
			<foreach item="item" index="index" open="(" separator="," close=")"
					 collection="param.saleOrderState.split(',')">#{item}
			</foreach>
		</if>
		<if test="param.isOverdue != null and param.isOverdue == '1'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME<=now() ]]></if>
		<if test="param.isOverdue != null and param.isOverdue == '0'.toString()">
			<![CDATA[ AND B.PLAN_REVIEW_TIME>now() ]]></if>
		<if test="param.planStartTime !=null and param.planStartTime !=''">and
			B.PLAN_REVIEW_TIME>=#{param.planStartTime}
		</if>
		<if test="param.planEndTime !=null and param.planEndTime !=''">
			<![CDATA[and B.PLAN_REVIEW_TIME<=#{param.planEndTime}]]></if>
		<if test="param.assignStatus !=null and param.assignStatus !=''">and B.ASSIGN_STATUS=#{param.assignStatus}</if>
		<if test="param.reviewStatus !=null and param.reviewStatus !=''">and B.REVIEW_STATUS=#{param.reviewStatus}</if>
		<if test="param.source5 !=null and param.source5 !=''">and F.source5 like concat('%',#{param.source5},'%')</if>
		<if test="param.source6 !=null and param.source6 !=''">and F.source6 like concat('%',#{param.source6},'%')</if>
		<if test="param.yhSignNum != null and param.yhSignNum != ''">
			AND IFNULL(K.yhSignNum,0) = #{param.yhSignNum}
		</if>
		<if test="param.dlSignNum != null and param.dlSignNum != ''">
			AND IFNULL(K.dlSignNum,0) = #{param.dlSignNum}
		</if>
		<if test="param.isFirstOverdue !=null and param.isFirstOverdue !=''">
			<choose>
				<when test='param.isFirstOverdue == "1"'>
					and fod.first_overdue_time &lt; ifnull(P.FIRST_REVIEW_TIME, now())
				</when>
				<otherwise>
					and fod.first_overdue_time &gt;= ifnull(P.FIRST_REVIEW_TIME, now())
				</otherwise>
			</choose>
		</if>
	</sql>
</mapper>
