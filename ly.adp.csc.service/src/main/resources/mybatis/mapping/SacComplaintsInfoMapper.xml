<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacComplaintsInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacComplaintsInfo">
		<id column="COMPLAINTS_ID" property="complaintsId" />
        <result column="COMPLAINTS_NO" property="complaintsNo" />
        <result column="SMART_ID" property="smartId" />
        <result column="CUST_NAME" property="custName" />
        <result column="PHONE" property="phone" />
        <result column="GENDER_CODE" property="genderCode" />
        <result column="GENDER_NAME" property="genderName" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="SOURCE_CODE" property="sourceCode" />
        <result column="SOURCE_NAME" property="sourceName" />
        <result column="COMPLAINT_THEME" property="complaintTheme" />
        <result column="FIRST_TYPE_CODE" property="firstTypeCode" />
        <result column="FIRST__TYPE_NAME" property="firstTypeName" />
        <result column="SECOND_TYPE_CODE" property="secondTypeCode" />
        <result column="SECOND_TYPE_NAME" property="secondTypeName" />
        <result column="THIRD_TYPE_CODE" property="thirdTypeCode" />
        <result column="THIRD_TYPE_NAME" property="thirdTypeName" />
        <result column="FOUR_TYPE_CODE" property="fourTypeCode" />
        <result column="FOUR_TYPE_NAME" property="fourTypeName" />
        <result column="RESPONDENT_ID" property="respondentId" />
        <result column="RESPONDENT_NAME" property="respondentName" />
        <result column="CUST_TYPE_CODE" property="custTypeCode" />
        <result column="CUST_TYPE_NAME" property="custTypeName" />
        <result column="PRIORITY_CODE" property="priorityCode" />
        <result column="PRIORITY_NAME" property="priorityName" />
        <result column="COMPLAINT_CONTENT" property="complaintContent" />
        <result column="IS_FORCE_CLOSE" property="isForceClose" />
        <result column="COMPLAINT_STATUS_CODE" property="complaintStatusCode" />
        <result column="COMPLAINT_STATUS_NAME" property="complaintStatusName" />
        <result column="COMPLAINTS_SOURCE" property="complaintsSource" />
        <result column="IS_NEED_REVIEW" property="isNeedReview" />
        <result column="IS_EXCEPTION_CLOSE" property="isExceptionClose" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COMPLAINTS_ID, COMPLAINTS_NO, SMART_ID, CUST_NAME, PHONE, GENDER_CODE, GENDER_NAME, DLR_CODE, DLR_NAME, COMPLAINT_TYPE, SOURCE_CODE, SOURCE_NAME, COMPLAINT_THEME, FIRST_TYPE_CODE, FIRST__TYPE_NAME, SECOND_TYPE_CODE, SECOND_TYPE_NAME, THIRD_TYPE_CODE, THIRD_TYPE_NAME, FOUR_TYPE_CODE, FOUR_TYPE_NAME, RESPONDENT_ID, RESPONDENT_NAME, CUST_TYPE_CODE, CUST_TYPE_NAME, PRIORITY_CODE, PRIORITY_NAME, COMPLAINT_CONTENT, IS_FORCE_CLOSE, COMPLAINT_STATUS_CODE, COMPLAINT_STATUS_NAME, COMPLAINTS_SOURCE, IS_NEED_REVIEW, IS_EXCEPTION_CLOSE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.complaintsId !=null and param.complaintsId !=''">and COMPLAINTS_ID=#{param.complaintsId}</if>
    	<if test="param.complaintsNo !=null and param.complaintsNo !=''">and COMPLAINTS_NO=#{param.complaintsNo}</if>
    	<if test="param.smartId !=null and param.smartId !=''">and SMART_ID=#{param.smartId}</if>
    	<if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
    	<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
    	<if test="param.genderCode !=null and param.genderCode !=''">and GENDER_CODE=#{param.genderCode}</if>
    	<if test="param.genderName !=null and param.genderName !=''">and GENDER_NAME=#{param.genderName}</if>
    	<if test="param.dlrCode !=null and param.dlrCode !=''">and DLR_CODE=#{param.dlrCode}</if>
    	<if test="param.dlrName !=null and param.dlrName !=''">and DLR_NAME=#{param.dlrName}</if>
    	<if test="param.sourceCode !=null and param.sourceCode !=''">and SOURCE_CODE=#{param.sourceCode}</if>
    	<if test="param.sourceName !=null and param.sourceName !=''">and SOURCE_NAME=#{param.sourceName}</if>
    	<if test="param.complaintTheme !=null and param.complaintTheme !=''">and COMPLAINT_THEME=#{param.complaintTheme}</if>
    	<if test="param.firstTypeCode !=null and param.firstTypeCode !=''">and FIRST_TYPE_CODE=#{param.firstTypeCode}</if>
    	<if test="param.firstTypeName !=null and param.firstTypeName !=''">and FIRST__TYPE_NAME=#{param.firstTypeName}</if>
    	<if test="param.secondTypeCode !=null and param.secondTypeCode !=''">and SECOND_TYPE_CODE=#{param.secondTypeCode}</if>
    	<if test="param.secondTypeName !=null and param.secondTypeName !=''">and SECOND_TYPE_NAME=#{param.secondTypeName}</if>
    	<if test="param.thirdTypeCode !=null and param.thirdTypeCode !=''">and THIRD_TYPE_CODE=#{param.thirdTypeCode}</if>
    	<if test="param.thirdTypeName !=null and param.thirdTypeName !=''">and THIRD_TYPE_NAME=#{param.thirdTypeName}</if>
    	<if test="param.fourTypeCode !=null and param.fourTypeCode !=''">and FOUR_TYPE_CODE=#{param.fourTypeCode}</if>
    	<if test="param.fourTypeName !=null and param.fourTypeName !=''">and FOUR_TYPE_NAME=#{param.fourTypeName}</if>
    	<if test="param.respondentId !=null and param.respondentId !=''">and RESPONDENT_ID=#{param.respondentId}</if>
    	<if test="param.respondentName !=null and param.respondentName !=''">and RESPONDENT_NAME=#{param.respondentName}</if>
    	<if test="param.custTypeCode !=null and param.custTypeCode !=''">and CUST_TYPE_CODE=#{param.custTypeCode}</if>
    	<if test="param.custTypeName !=null and param.custTypeName !=''">and CUST_TYPE_NAME=#{param.custTypeName}</if>
    	<if test="param.priorityCode !=null and param.priorityCode !=''">and PRIORITY_CODE=#{param.priorityCode}</if>
    	<if test="param.priorityName !=null and param.priorityName !=''">and PRIORITY_NAME=#{param.priorityName}</if>
    	<if test="param.complaintContent !=null and param.complaintContent !=''">and COMPLAINT_CONTENT=#{param.complaintContent}</if>
    	<if test="param.isForceClose !=null and param.isForceClose !=''">and IS_FORCE_CLOSE=#{param.isForceClose}</if>
    	<if test="param.complaintStatusCode !=null and param.complaintStatusCode !=''">and COMPLAINT_STATUS_CODE=#{param.complaintStatusCode}</if>
    	<if test="param.complaintStatusName !=null and param.complaintStatusName !=''">and COMPLAINT_STATUS_NAME=#{param.complaintStatusName}</if>
    	<if test="param.complaintsSource !=null and param.complaintsSource !=''">and COMPLAINTS_SOURCE=#{param.complaintsSource}</if>
    	<if test="param.isNeedReview !=null and param.isNeedReview !=''">and IS_NEED_REVIEW=#{param.isNeedReview}</if>
    	<if test="param.isExceptionClose !=null and param.isExceptionClose !=''">and IS_EXCEPTION_CLOSE=#{param.isExceptionClose}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.column6 !=null and param.column6 !=''">and COLUMN6=#{param.column6}</if>
    	<if test="param.column7 !=null and param.column7 !=''">and COLUMN7=#{param.column7}</if>
    	<if test="param.column8 !=null and param.column8 !=''">and COLUMN8=#{param.column8}</if>
    	<if test="param.column9 !=null and param.column9 !=''">and COLUMN9=#{param.column9}</if>
    	<if test="param.column10 !=null and param.column10 !=''">and COLUMN10=#{param.column10}</if>
    	<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and _MYCAT_OP_TIME=#{param.mycatOpTime}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
    	
    	<if test="param.complaintStatusCodeIn != null and param.complaintStatusCodeIn!=''"> AND COMPLAINT_STATUS_CODE in <foreach item="items" collection="param.complaintStatusCodeIn.split(',')" index="index"  open="(" separator="," close=")">#{items}</foreach></if>
   	
   		<!-- 模糊查询 -->
    	<if test="param.complaintThemePaste !=null and param.complaintThemePaste !=''">and INSTR(COMPLAINT_THEME,#{param.complaintThemePaste})>0</if>
    	<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(PHONE,#{param.searchCondition})>0 or INSTR(CUST_NAME,#{param.searchCondition})>0)</if>
    	<!-- 时间查询 -->
    	<if test="param.createdDateStart !=null and param.createdDateStart !=''">and CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]></if>
   	</sql>
 	
 	<!-- 投诉工单表 信息查询 -->
	<select id="querySacComplaintsInfo" resultType="map">
		select 
		<![CDATA[CASE WHEN a.CREATED_DATE <= NOW( ) THEN CONCAT( ( TIMESTAMPDIFF( HOUR, a.CREATED_DATE, NOW( ) ) ) ) ELSE '0' END beyondTimes,]]>
		case a.COMPLAINT_TYPE when '0' then '投诉单' when '2' then '协调单' when '3' then '舆情单' else '' end COMPLAINT_TYPE_CN,
		a.COMPLAINTS_ID,
		a.COMPLAINTS_NO,
		a.SMART_ID,
		a.CUST_NAME,
		a.PHONE,
		CONCAT(left(a.PHONE,3),'****',right(a.PHONE,4)) phonePlus,
		a.GENDER_CODE,
		a.GENDER_NAME,
		a.DLR_CODE,
		a.DLR_NAME,
		a.COMPLAINT_TYPE,
		a.SOURCE_CODE,
		a.SOURCE_NAME,
		a.COMPLAINT_THEME,
		a.FIRST_TYPE_CODE,
		a.FIRST__TYPE_NAME,
		a.SECOND_TYPE_CODE,
		a.SECOND_TYPE_NAME,
		a.THIRD_TYPE_CODE,
		a.THIRD_TYPE_NAME,
		a.FOUR_TYPE_CODE,
		a.FOUR_TYPE_NAME,
		a.RESPONDENT_ID,
		a.RESPONDENT_NAME,
		a.CUST_TYPE_CODE,
		a.CUST_TYPE_NAME,
		a.PRIORITY_CODE,
		a.PRIORITY_NAME,
		a.COMPLAINT_CONTENT,
		a.IS_FORCE_CLOSE,
		a.COMPLAINT_STATUS_CODE,
		a.COMPLAINT_STATUS_NAME,
		a.COMPLAINTS_SOURCE,
		a.IS_NEED_REVIEW,
		a.IS_EXCEPTION_CLOSE,
		a.COLUMN1,
		a.COLUMN2,
		a.COLUMN3,
		a.COLUMN4,
		a.COLUMN5,
		a.COLUMN6,
		a.COLUMN7,
		a.COLUMN8,
		a.COLUMN9,
		a.COLUMN10,
		a._MYCAT_OP_TIME,
		a.OEM_ID,
		a.GROUP_ID,
		a.OEM_CODE,
		a.GROUP_CODE,
		a.CREATOR,
		a.CREATED_NAME,
		a.CREATED_DATE,
		a.MODIFIER,
		a.MODIFY_NAME,
		a.LAST_UPDATED_DATE,
		a.IS_ENABLE,
		a.SDP_USER_ID,
		a.SDP_ORG_ID,
		a.UPDATE_CONTROL_ID,
		ar.AREA_NAME,
		oc.CITY_NAME
		from csc.t_sac_complaints_info a
		LEFT JOIN mp.t_usc_mdm_org_dlr d ON a.DLR_CODE = d.DLR_CODE
		left join mp.t_usc_area_info ar on d.BIG_AREA_ID = ar.AREA_ID
		left join mp.t_usc_mdm_org_city oc on d.CITY_ID = oc.CITY_ID
		LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
		LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
		<trim prefix="WHERE" prefixOverrides="AND |OR ">
		<if test="param.complaintsId !=null and param.complaintsId !=''">and a.COMPLAINTS_ID=#{param.complaintsId}</if>
		<if test="param.complaintsNo !=null and param.complaintsNo !=''">and a.COMPLAINTS_NO=#{param.complaintsNo}</if>
		<if test="param.smartId !=null and param.smartId !=''">and a.SMART_ID=#{param.smartId}</if>
		<if test="param.custName !=null and param.custName !=''">and a.CUST_NAME=#{param.custName}</if>
		<if test="param.phone !=null and param.phone !=''">and a.PHONE=#{param.phone}</if>
		<if test="param.genderCode !=null and param.genderCode !=''">and a.GENDER_CODE=#{param.genderCode}</if>
		<if test="param.genderName !=null and param.genderName !=''">and a.GENDER_NAME=#{param.genderName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and a.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and a.DLR_NAME=#{param.dlrName}</if>
		<if test="param.sourceCode !=null and param.sourceCode !=''">and a.SOURCE_CODE=#{param.sourceCode}</if>
		<if test="param.sourceName !=null and param.sourceName !=''">and a.SOURCE_NAME=#{param.sourceName}</if>
		<if test="param.complaintTheme !=null and param.complaintTheme !=''">and a.COMPLAINT_THEME=#{param.complaintTheme}</if>
		<if test="param.firstTypeCode !=null and param.firstTypeCode !=''">and a.FIRST_TYPE_CODE=#{param.firstTypeCode}</if>
		<if test="param.firstTypeName !=null and param.firstTypeName !=''">and a.FIRST__TYPE_NAME=#{param.firstTypeName}</if>
		<if test="param.secondTypeName !=null and param.secondTypeName !=''">and a.SECOND_TYPE_NAME=#{param.secondTypeName}</if>
		<if test="param.thirdTypeCode !=null and param.thirdTypeCode !=''">and a.THIRD_TYPE_CODE=#{param.thirdTypeCode}</if>
		<if test="param.thirdTypeName !=null and param.thirdTypeName !=''">and a.THIRD_TYPE_NAME=#{param.thirdTypeName}</if>
		<if test="param.fourTypeCode !=null and param.fourTypeCode !=''">and a.FOUR_TYPE_CODE=#{param.fourTypeCode}</if>
		<if test="param.fourTypeName !=null and param.fourTypeName !=''">and a.FOUR_TYPE_NAME=#{param.fourTypeName}</if>
		<if test="param.respondentId !=null and param.respondentId !=''">and a.RESPONDENT_ID=#{param.respondentId}</if>
		<if test="param.respondentName !=null and param.respondentName !=''">and a.RESPONDENT_NAME=#{param.respondentName}</if>
		<if test="param.custTypeCode !=null and param.custTypeCode !=''">and a.CUST_TYPE_CODE=#{param.custTypeCode}</if>
		<if test="param.custTypeName !=null and param.custTypeName !=''">and a.CUST_TYPE_NAME=#{param.custTypeName}</if>
		<if test="param.priorityCode !=null and param.priorityCode !=''">and a.PRIORITY_CODE=#{param.priorityCode}</if>
		<if test="param.priorityName !=null and param.priorityName !=''">and a.PRIORITY_NAME=#{param.priorityName}</if>
		<if test="param.complaintContent !=null and param.complaintContent !=''">and a.COMPLAINT_CONTENT=#{param.complaintContent}</if>
		<if test="param.isForceClose !=null and param.isForceClose !=''">and a.IS_FORCE_CLOSE=#{param.isForceClose}</if>
		<if test="param.complaintStatusCode !=null and param.complaintStatusCode !=''">and a.COMPLAINT_STATUS_CODE=#{param.complaintStatusCode}</if>
		<if test="param.complaintStatusName !=null and param.complaintStatusName !=''">and a.COMPLAINT_STATUS_NAME=#{param.complaintStatusName}</if>
		<if test="param.complaintsSource !=null and param.complaintsSource !=''">and a.COMPLAINTS_SOURCE=#{param.complaintsSource}</if>
		<if test="param.isNeedReview !=null and param.isNeedReview !=''">and a.IS_NEED_REVIEW=#{param.isNeedReview}</if>
		<if test="param.isExceptionClose !=null and param.isExceptionClose !=''">and a.IS_EXCEPTION_CLOSE=#{param.isExceptionClose}</if>
		<if test="param.column10 !=null and param.column10 !=''">and a.COLUMN10=#{param.column10}</if>
		<if test="param.mycatOpTime !=null and param.mycatOpTime !=''">and a._MYCAT_OP_TIME=#{param.mycatOpTime}</if>
		<if test="param.oemId !=null and param.oemId !=''">and a.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and a.GROUP_ID=#{param.groupId}</if>
		<if test="param.oemCode !=null and param.oemCode !=''">and a.OEM_CODE=#{param.oemCode}</if>
		<if test="param.groupCode !=null and param.groupCode !=''">and a.GROUP_CODE=#{param.groupCode}</if>
		<if test="param.creator !=null and param.creator !=''">and a.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and a.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and a.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and a.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and a.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and a.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and a.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.sdpUserId !=null and param.sdpUserId !=''">and a.SDP_USER_ID=#{param.sdpUserId}</if>
		<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and a.SDP_ORG_ID=#{param.sdpOrgId}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and a.UPDATE_CONTROL_ID=#{param.updateControlId}</if>

		<if test="param.complaintStatusCodeIn != null and param.complaintStatusCodeIn!=''"> AND a.COMPLAINT_STATUS_CODE in <foreach item="items" collection="param.complaintStatusCodeIn.split(',')" index="index"  open="(" separator="," close=")">#{items}</foreach></if>

		<!-- 模糊查询 -->
		<if test="param.complaintThemePaste !=null and param.complaintThemePaste !=''">and INSTR(a.COMPLAINT_THEME,#{param.complaintThemePaste})>0</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(a.PHONE,#{param.searchCondition})>0 or INSTR(a.CUST_NAME,#{param.searchCondition})>0)</if>
		<!-- 时间查询 -->
		<if test="param.createdDateStart !=null and param.createdDateStart !=''">and a.CREATED_DATE>=#{param.createdDateStart}</if>
		<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and a.CREATED_DATE<=#{param.createdDateEnd}]]></if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn.size()>0">
			AND a.DLR_CODE IN
			<foreach collection="param.dlrCodeIn" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
			<if test="param.dlrCodeLists !=null and param.dlrCodeLists.size()>0">
				AND a.DLR_CODE IN
				<foreach collection="param.dlrCodeLists" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.agentCode !=null and param.agentCode !=''">and  C2.AGENT_CODE = #{param.agentCode}</if>
			<if test="param.areaId !=null and param.areaId !=''">and ar.AREA_ID=#{param.areaId} </if>
			<if test="param.deliveryCityCode !=null and param.deliveryCityCode !=''">and oc.CITY_ID=#{param.deliveryCityCode}</if>
		</trim>
	    ORDER BY CREATED_DATE DESC
	</select>
	
	<!-- 投诉工单表 信息删除（物理删除） -->
	<delete id="deleteSacComplaintsInfo">
		DELETE 
		FROM
			t_sac_complaints_info
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 投诉工单表 信息新增 -->
	<insert id="createSacComplaintsInfo">
			insert into t_sac_complaints_info(<include refid="Base_Column_List"></include>)
		value(
        	#{param.complaintsId},
			#{param.complaintsNo},
			#{param.smartId},
			#{param.custName},
			#{param.phone},
			#{param.genderCode},
			#{param.genderName},
			#{param.dlrCode},
			#{param.dlrName},
			#{param.complaintType},
			#{param.sourceCode},
			#{param.sourceName},
			#{param.complaintTheme},
			#{param.firstTypeCode},
			#{param.firstTypeName},
			#{param.secondTypeCode},
			#{param.secondTypeName},
			#{param.thirdTypeCode},
			#{param.thirdTypeName},
			#{param.fourTypeCode},
			#{param.fourTypeName},
			#{param.respondentId},
			#{param.respondentName},
			#{param.custTypeCode},
			#{param.custTypeName},
			#{param.priorityCode},
			#{param.priorityName},
			#{param.complaintContent},
			#{param.isForceClose},
			#{param.complaintStatusCode},
			#{param.complaintStatusName},
			#{param.complaintsSource},
			#{param.isNeedReview},
			#{param.isExceptionClose},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
			#{param.mycatOpTime},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			#{param.creator},
			#{param.createdName},
			#{param.createdDate},
			#{param.modifier},
			#{param.modifyName},
			#{param.lastUpdatedDate},
			#{param.isEnable},
			#{param.sdpUserId},
			#{param.sdpOrgId},
			#{param.updateControlId}
		)
	</insert>
	
	<!-- 投诉工单表 信息更新 -->
	<update id="updateSacComplaintsInfo">
		update t_sac_complaints_info  set 
			<!-- 更新列表 -->
			<if test="param.smartId !=null and param.smartId !=''">SMART_ID=#{param.smartId},</if>
			<if test="param.custName !=null and param.custName !=''">CUST_NAME=#{param.custName},</if>
			<if test="param.phone !=null and param.phone !=''">PHONE=#{param.phone},</if>
			<if test="param.genderCode !=null and param.genderCode !=''">GENDER_CODE=#{param.genderCode},</if>
			<if test="param.genderName !=null and param.genderName !=''">GENDER_NAME=#{param.genderName},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrName !=null and param.dlrName !=''">DLR_NAME=#{param.dlrName},</if>
			<if test="param.sourceCode !=null and param.sourceCode !=''">SOURCE_CODE=#{param.sourceCode},</if>
			<if test="param.sourceName !=null and param.sourceName !=''">SOURCE_NAME=#{param.sourceName},</if>
			<if test="param.complaintTheme !=null and param.complaintTheme !=''">COMPLAINT_THEME=#{param.complaintTheme},</if>
			<if test="param.firstTypeCode !=null and param.firstTypeCode !=''">FIRST_TYPE_CODE=#{param.firstTypeCode},</if>
			<if test="param.firstTypeName !=null and param.firstTypeName !=''">FIRST__TYPE_NAME=#{param.firstTypeName},</if>
			<if test="param.secondTypeCode !=null and param.secondTypeCode !=''">SECOND_TYPE_CODE=#{param.secondTypeCode},</if>
			<if test="param.secondTypeName !=null and param.secondTypeName !=''">SECOND_TYPE_NAME=#{param.secondTypeName},</if>
			<if test="param.thirdTypeCode !=null and param.thirdTypeCode !=''">THIRD_TYPE_CODE=#{param.thirdTypeCode},</if>
			<if test="param.thirdTypeName !=null and param.thirdTypeName !=''">THIRD_TYPE_NAME=#{param.thirdTypeName},</if>
			<if test="param.fourTypeCode !=null and param.fourTypeCode !=''">FOUR_TYPE_CODE=#{param.fourTypeCode},</if>
			<if test="param.fourTypeName !=null and param.fourTypeName !=''">FOUR_TYPE_NAME=#{param.fourTypeName},</if>
			<if test="param.respondentId !=null and param.respondentId !=''">RESPONDENT_ID=#{param.respondentId},</if>
			<if test="param.respondentName !=null and param.respondentName !=''">RESPONDENT_NAME=#{param.respondentName},</if>
			<if test="param.custTypeCode !=null and param.custTypeCode !=''">CUST_TYPE_CODE=#{param.custTypeCode},</if>
			<if test="param.custTypeName !=null and param.custTypeName !=''">CUST_TYPE_NAME=#{param.custTypeName},</if>
			<if test="param.priorityCode !=null and param.priorityCode !=''">PRIORITY_CODE=#{param.priorityCode},</if>
			<if test="param.priorityName !=null and param.priorityName !=''">PRIORITY_NAME=#{param.priorityName},</if>
			<if test="param.complaintContent !=null and param.complaintContent !=''">COMPLAINT_CONTENT=#{param.complaintContent},</if>
			<if test="param.isForceClose !=null and param.isForceClose !=''">IS_FORCE_CLOSE=#{param.isForceClose},</if>
			<if test="param.complaintStatusCode !=null and param.complaintStatusCode !=''">COMPLAINT_STATUS_CODE=#{param.complaintStatusCode},</if>
			<if test="param.complaintStatusName !=null and param.complaintStatusName !=''">COMPLAINT_STATUS_NAME=#{param.complaintStatusName},</if>
			<if test="param.complaintsSource !=null and param.complaintsSource !=''">COMPLAINTS_SOURCE=#{param.complaintsSource},</if>
			<if test="param.isNeedReview !=null and param.isNeedReview !=''">IS_NEED_REVIEW=#{param.isNeedReview},</if>
			<if test="param.isExceptionClose !=null and param.isExceptionClose !=''">IS_EXCEPTION_CLOSE=#{param.isExceptionClose},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
    		<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
    		<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
    		<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.oemCode !=null and param.oemCode !=''">OEM_CODE=#{param.oemCode},</if>
			<if test="param.groupCode !=null and param.groupCode !=''">GROUP_CODE=#{param.groupCode},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			LAST_UPDATED_DATE=now(),
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.sdpUserId !=null and param.sdpUserId !=''">SDP_USER_ID=#{param.sdpUserId},</if>
			<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">SDP_ORG_ID=#{param.sdpOrgId},</if>
			<!-- 结束无逗号 -->
			UPDATE_CONTROL_ID=uuid()
			where 1=1 
			<!-- 描述条件 -->
			<if test="param.complaintsId !=null and param.complaintsId !=''">and COMPLAINTS_ID=#{param.complaintsId}</if>
    		<if test="param.complaintsNo !=null and param.complaintsNo !=''">and COMPLAINTS_NO=#{param.complaintsNo}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	</update>
	
 	<select id="sacComplaintsInfoFind" resultType="map">
 		SELECT 
			info.COMPLAINTS_ID,
			info.COMPLAINTS_NO,
			info.SMART_ID,
			info.CUST_NAME,
			info.PHONE,
			info.GENDER_CODE,
			info.GENDER_NAME,
			info.DLR_CODE,
			info.DLR_NAME,
			info.SOURCE_CODE,
			info.SOURCE_NAME,
			info.COMPLAINT_THEME,
			info.FIRST_TYPE_CODE,
			info.FIRST__TYPE_NAME,
			info.SECOND_TYPE_CODE,
			info.SECOND_TYPE_NAME,
			info.THIRD_TYPE_CODE,
			info.THIRD_TYPE_NAME,
			info.FOUR_TYPE_CODE,
			info.FOUR_TYPE_NAME,
			info.RESPONDENT_ID,
			info.RESPONDENT_NAME,
			info.CUST_TYPE_CODE,
			info.CUST_TYPE_NAME,
			info.PRIORITY_CODE,
			info.PRIORITY_NAME,
			info.COMPLAINT_CONTENT,
			info.IS_FORCE_CLOSE,
			info.COMPLAINT_STATUS_CODE,
			info.COMPLAINT_STATUS_NAME,
			info.COMPLAINTS_SOURCE,
			info.IS_NEED_REVIEW,
			info.IS_EXCEPTION_CLOSE,
			info.CREATOR,
			info.CREATED_NAME,
			info.CREATED_DATE,
			info.MODIFIER,
			info.MODIFY_NAME,
			info.LAST_UPDATED_DATE,
			info.IS_ENABLE,
			info.UPDATE_CONTROL_ID,
			record.DEAL_ID AS recordDealId,
			record.DEAL_TYPE_CODE AS recordDealTypeCode,
			record.DEAL_TYPE AS recordDealType,
			record.DEAL_CONTENT AS recordDealContent,
			record.AUDIT_CONTENT AS recordAuditContent,
			record.DLR_CODE AS recordDlrCode,
			record.DLR_NAME AS recordDlrName,
			record.PHONE AS recordPhone,
			record.FILE_SRC AS recordFileSrc,
			record.CREATOR AS recordCreator,
			record.CREATED_NAME recordCreatedName,
			record.CREATED_DATE recordCreatedDate
		FROM
		t_sac_complaints_info info
		LEFT JOIN t_sac_complaints_deal_record record ON info.COMPLAINTS_NO=record.COMPLAINTS_NO
		where 1=1
		<if test="param.phone !=null and param.phone !=''">and info.PHONE=#{param.phone}</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(info.PHONE,#{param.searchCondition})>0 or INSTR(info.CUST_NAME,#{param.searchCondition})>0)</if>
		ORDER BY record.CREATED_DATE DESC
 	</select>
 	
 	<!-- 投诉工单表 报表查询 -->
	<select id="sacServerClassReport" resultType="map">
		SELECT 
			REGULAR_NUM,
			FORCE_NUM,
			ACTIVITY_NUM,
			MARKET_NUM,
			DELIVERY_NUM,
			ACTIVITY_FORCE_NUM,
			MARKET_FORCE_NUM,
			DELIVERY_FORCE_NUM
		FROM
		( 
			SELECT
				IFNULL(SUM( CASE WHEN T1.IS_FORCE_CLOSE = '0' AND T1.COMPLAINT_STATUS_CODE = '4' THEN 1 ELSE 0 END ),0) AS REGULAR_NUM,
				IFNULL(SUM( CASE WHEN T1.IS_FORCE_CLOSE = '1' AND T1.COMPLAINT_STATUS_CODE = '3' THEN 1 ELSE 0 END ),0) AS FORCE_NUM,
				IFNULL(SUM( CASE WHEN T1.FIRST__TYPE_NAME = '活动类' THEN 1 ELSE 0 END ),0) AS ACTIVITY_NUM,
				IFNULL(SUM( CASE WHEN T1.FIRST__TYPE_NAME = '销售类' THEN 1 ELSE 0 END ),0) AS MARKET_NUM,
				IFNULL(SUM( CASE WHEN T1.FIRST__TYPE_NAME = '交付类' THEN 1 ELSE 0 END ),0) AS DELIVERY_NUM,
				IFNULL(SUM( CASE WHEN T1.IS_FORCE_CLOSE = '1' AND T1.COMPLAINT_STATUS_CODE = '3' AND T1.FIRST__TYPE_NAME = '活动类' THEN 1 ELSE 0 END ),0) AS ACTIVITY_FORCE_NUM,
				IFNULL(SUM( CASE WHEN T1.IS_FORCE_CLOSE = '1' AND T1.COMPLAINT_STATUS_CODE = '3' AND T1.FIRST__TYPE_NAME = '销售类' THEN 1 ELSE 0 END ),0) AS MARKET_FORCE_NUM,
				IFNULL(SUM( CASE WHEN T1.IS_FORCE_CLOSE = '1' AND T1.COMPLAINT_STATUS_CODE = '3' AND T1.FIRST__TYPE_NAME = '交付类' THEN 1 ELSE 0 END ),0) AS DELIVERY_FORCE_NUM
			FROM
				t_sac_complaints_info T1
			WHERE 1=1
			<if test="param.createdDateStart !=null and param.createdDateStart !=''">and T1.CREATED_DATE>=#{param.createdDateStart}</if>
			<if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and T1.CREATED_DATE<=#{param.createdDateEnd}]]></if>
			<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''"> and T1.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		) T2;
	</select>
	<update id="updateStatusByIssueCaseNo">
		UPDATE t_sac_complaints_info
		SET
			COMPLAINT_STATUS_CODE = #{param.complaintStatusCode},
			COMPLAINT_STATUS_NAME = #{param.complaintStatusName},
			COLUMN5 = #{param.obsoleteApprovalReason},
			MODIFIER = #{param.modifier},
			MODIFY_NAME = #{param.modifyName},
			LAST_UPDATED_DATE = NOW()
		WHERE
			COMPLAINTS_NO = #{param.issueCaseNo};
	</update>
	<select id="selectByComplaintsNo" resultType="java.util.Map">
		SELECT  <include refid="Base_Column_List"></include>
		FROM
		t_sac_complaints_info
		where 1=1
		AND COMPLAINTS_NO =#{complaintsNo}
	</select>

    <select id="findAgentDlrCodeList" resultType="java.lang.String">
		select DLR_CODE
		from mp.t_usc_mdm_org_dlr d
		LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
		LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
		where c2.AGENT_CODE = #{agentCode}
	</select>

    <select id="findAgentCompany" resultType="java.lang.String">

		SELECT
			DLR_CODE
		FROM
			mp.t_usc_mdm_org_dlr
		WHERE
			COMPANY_ID=#{orgId}
    </select>

    <select id="findAuditId" resultType="java.util.Map">
		select
			AUDIT_ID,
			UPDATE_CONTROL_ID
		from csc.t_sac_common_audit
		where
			BILL_CODE=#{activityId}
		order by CREATED_DATE desc limit 1
    </select>
</mapper>
