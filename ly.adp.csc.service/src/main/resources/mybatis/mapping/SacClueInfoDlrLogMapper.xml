<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacClueInfoDlrLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacClueInfoDlrLog">
        <id column="ID" property="id" />
        <result column="PHONE" property="phone" />
        <result column="SYSTEM_PARAM" property="systemParam" />
        <result column="SYSTEM_SOURCE" property="systemSource" />
        <result column="SYSTEM_RECORD" property="systemRecord" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PHONE, SYSTEM_PARAM, SYSTEM_SOURCE, SYSTEM_RECORD, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, IS_ENABLE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.id !=null and param.id !=''">and ID=#{param.id}</if>
    	<if test="param.phone !=null and param.phone !=''">and PHONE=#{param.phone}</if>
    	<if test="param.systemParam !=null and param.systemParam !=''">and SYSTEM_PARAM=#{param.systemParam}</if>
    	<if test="param.systemSource !=null and param.systemSource !=''">and SYSTEM_SOURCE=#{param.systemSource}</if>
    	<if test="param.systemRecord !=null and param.systemRecord !=''">and SYSTEM_RECORD=#{param.systemRecord}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.oemCode !=null and param.oemCode !=''">and OEM_CODE=#{param.oemCode}</if>
    	<if test="param.groupCode !=null and param.groupCode !=''">and GROUP_CODE=#{param.groupCode}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.sdpUserId !=null and param.sdpUserId !=''">and SDP_USER_ID=#{param.sdpUserId}</if>
    	<if test="param.sdpOrgId !=null and param.sdpOrgId !=''">and SDP_ORG_ID=#{param.sdpOrgId}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>

    	<if test="param.createdDateStart !=null and param.createdDateStart !=''">and CREATED_DATE>=#{param.createdDateStart}</if>
	    <if test="param.createdDateEnd !=null and param.createdDateEnd !=''"><![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]></if>
   	</sql>
 	
 	<!-- 店端线索来源日志表 信息查询 -->
	<select id="querySacClueInfoDlrLog" resultType="map">
		select
	    <include refid="Base_Column_List"></include>
	    from t_sac_clue_info_dlr_log
	    where 1=1
	    <include refid="where_condition"></include>
		ORDER BY CREATED_DATE DESC
	</select>
	
	<!-- 店端线索来源日志表 信息删除（物理删除） -->
	<delete id="deleteSacClueInfoDlrLog">
		DELETE 
		FROM
			t_sac_clue_info_dlr_log
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 店端线索来源日志表 信息新增 -->
	<insert id="createSacClueInfoDlrLog">
		insert into t_sac_clue_info_dlr_log(<include refid="Base_Column_List"></include>)
		value(
        	uuid(),
			#{param.phone},
			#{param.systemParam},
			#{param.systemSource},
			#{param.systemRecord},
			#{param.oemId},
			#{param.groupId},
			#{param.oemCode},
			#{param.groupCode},
			'1',
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			'1',
			uuid()
		)
	</insert>
</mapper>
