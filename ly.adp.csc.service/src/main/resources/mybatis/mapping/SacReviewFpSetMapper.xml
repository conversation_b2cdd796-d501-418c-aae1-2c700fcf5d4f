<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewFpSetMapper">

 <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewFpSet">
        <id column="SET_ID" property="setId" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="ORG_NAME" property="orgName" />
        <result column="BILL_TYPE" property="billType" />
        <result column="BILL_TYPE_NAME" property="billTypeName" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="BUSINESS_TYPE_NAME" property="businessTypeName" />
        <result column="CHANNEL_CODE" property="channelCode" />
        <result column="CHANNEL_NAME" property="channelName" />
        <result column="PERSON_QUEUE_CODE" property="personQueueCode" />
        <result column="PERSON_QUEUE_NAME" property="personQueueName" />
        <result column="PERSON_QUEUE_VALUE_CODE" property="personQueueValueCode" />
        <result column="PERSON_QUEUE_VALUE_NAME" property="personQueueValueName" />
        <result column="FP_RULE_ID" property="fpRuleId" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_FIRST_SELF" property="isFirstSelf" />
        <result column="INTEN_LEVEL_CODE" property="intenLevelCode" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="INFO_CHAN_M_CODE" property="infoChanMCode" />
        <result column="INFO_CHAN_M_NAME" property="infoChanMName" />
        <result column="INFO_CHAN_D_CODE" property="infoChanDCode" />
        <result column="INFO_CHAN_D_NAME" property="infoChanDName" />
        <result column="INFO_CHAN_DD_CODE" property="infoChanDDCode" />
        <result column="INFO_CHAN_DD_NAME" property="infoChanDDName" />
    </resultMap>
    
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SET_ID, PERSON_USER_ID, PERSON_NAME, ORDER_NO, MAX_NUM, OEM_ID, GROUP_ID, CREATOR, 
    CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, IS_FIRST_SELF,INTEN_LEVEL_CODE,
    UPDATE_CONTROL_ID
  </sql>
  <delete id="deleteBySetId" parameterType="java.lang.String">
    delete from t_sac_review_fp_set_d where set_id=#{setId}
  </delete>
  
  <insert id="insertSacReviewFpSetD">
    insert into t_sac_review_fp_set_d (ID, SET_ID, PERSON_USER_ID,
      PERSON_NAME, ORDER_NO, MAX_NUM,
      OEM_ID, GROUP_ID, CREATOR,
      CREATED_NAME, CREATED_DATE, MODIFIER,
      MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE,
      UPDATE_CONTROL_ID)
    values
    <foreach collection="list" item="item" separator=",">
    (
      #{item.id,jdbcType=VARCHAR}, #{item.setId,jdbcType=VARCHAR}, #{item.personUserId,jdbcType=VARCHAR},
      #{item.personName,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.maxNum,jdbcType=VARCHAR},
      #{item.oemId,jdbcType=VARCHAR}, #{item.groupId,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR},
      #{item.createdName,jdbcType=VARCHAR}, now(), #{item.modifier,jdbcType=VARCHAR},
      #{item.modifyName,jdbcType=VARCHAR}, now(), #{item.isEnable,jdbcType=VARCHAR},
      #{item.updateControlId,jdbcType=VARCHAR}
    )
    </foreach>
  </insert>

  <insert id="insertSacReviewFpSet">
    insert into t_sac_review_fp_set(
        SET_ID,
        ORG_CODE,
        ORG_NAME,
        BILL_TYPE,
        BILL_TYPE_NAME,
        BUSINESS_TYPE,
        BUSINESS_TYPE_NAME,
        CHANNEL_CODE,
        CHANNEL_NAME,
        PERSON_QUEUE_CODE,
        PERSON_QUEUE_NAME,
        PERSON_QUEUE_VALUE_CODE,
        PERSON_QUEUE_VALUE_NAME,
        FP_RULE_ID,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        UPDATE_CONTROL_ID,
        INFO_CHAN_M_CODE,
        INFO_CHAN_M_NAME,
        INFO_CHAN_D_CODE,
        INFO_CHAN_D_NAME,
        INFO_CHAN_DD_CODE,
        INFO_CHAN_DD_NAME,
		IS_ENABLE,
		IS_FIRST_SELF,
		INTEN_LEVEL_CODE)
		values(
               #{param.setId},
               #{param.orgCode},
               #{param.orgName},
               #{param.billType},
               #{param.billTypeName},
               #{param.businessType},
               #{param.businessTypeName},
               #{param.channelCode},
               #{param.channelName},
               #{param.personQueueCode},
               #{param.personQueueName},
               #{param.personQueueValueCode},
               #{param.personQueueValueName},
               #{param.ruleId},
               #{param.oemId},
               #{param.groupId},
               #{param.creator},
               #{param.createdName},
               #{param.createdDate},
               #{param.modifier},
               #{param.modifyName},
               #{param.lastUpdatedDate},
               #{param.updateControlId},
               #{param.infoChanMCode},
               #{param.infoChanMName},
               #{param.infoChanDCode},
               #{param.infoChanDName},
               #{param.infoChanDDCode},
               #{param.infoChanDDName},
			   #{param.isEnable},
			   #{param.isFirstSelf},
			   #{param.intenLevelCode}
			   )
  </insert>

  <update id="updateByPrimaryKeySetId">
    update t_sac_review_fp_set set
    	<if test="param.setId !=null and param.setId !=''"> SET_ID=#{param.setId},</if>
		<if test="param.orgCode !=null and param.orgCode !=''"> ORG_CODE=#{param.orgCode},</if>
		<if test="param.orgName !=null and param.orgName !=''"> ORG_NAME=#{param.orgName},</if>
		<if test="param.billType !=null and param.billType !=''"> BILL_TYPE=#{param.billType},</if>
		<if test="param.billTypeName !=null"> BILL_TYPE_NAME=#{param.billTypeName},</if>
		<if test="param.businessType !=null"> BUSINESS_TYPE=#{param.businessType},</if>
		<if test="param.businessTypeName !=null"> BUSINESS_TYPE_NAME=#{param.businessTypeName},</if>
		<if test="param.channelCode !=null"> CHANNEL_CODE=#{param.channelCode},</if>
		<if test="param.channelName !=null"> CHANNEL_NAME=#{param.channelName},</if>
		<if test="param.personQueueCode !=null and param.personQueueCode !=''"> PERSON_QUEUE_CODE=#{param.personQueueCode},</if>
		<if test="param.personQueueName !=null and param.personQueueName !=''"> PERSON_QUEUE_NAME=#{param.personQueueName},</if>
		<if test="param.personQueueValueCode !=null and param.personQueueValueCode !=''"> PERSON_QUEUE_VALUE_CODE=#{param.personQueueValueCode},</if>
		<if test="param.personQueueValueName !=null and param.personQueueValueName !=''"> PERSON_QUEUE_VALUE_NAME=#{param.personQueueValueName},</if>
		<if test="param.ruleId !=null"> FP_RULE_ID=#{param.ruleId},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
		<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
		<if test="param.infoChanMCode !=null"> INFO_CHAN_M_CODE=#{param.infoChanMCode},</if>
		<if test="param.infoChanMName !=null"> INFO_CHAN_M_NAME=#{param.infoChanMName},</if>
		<if test="param.infoChanDCode !=null"> INFO_CHAN_D_CODE=#{param.infoChanDCode},</if>
		<if test="param.infoChanDName !=null"> INFO_CHAN_D_NAME=#{param.infoChanDName},</if>
		<if test="param.infoChanDDCode !=null"> INFO_CHAN_DD_CODE=#{param.infoChanDDCode},</if>
		<if test="param.infoChanDDName !=null"> INFO_CHAN_DD_NAME=#{param.infoChanDDName},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if test="param.isFirstSelf !=null and param.isFirstSelf !=''"> IS_FIRST_SELF=#{param.isFirstSelf},</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''"> INTEN_LEVEL_CODE=#{param.intenLevelCode},</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId}</if>
	 where SET_ID=#{param.setId}
  </update>

  <select id="queryListReviewAssignInfo" resultType="java.util.Map">
    select
        s.set_id as setId,
        s.org_code,
        s.IS_FIRST_SELF as isFirstSelf,
        s.bill_type as billType,
        s.bill_type_name as billTypeName,
        s.business_type as businessType,
        s.business_type_name as businessTypeName,
        s.channel_code as channelCode,
        s.channel_name as channelName,
        s.person_queue_code as personQueueCode,
        s.person_queue_name as personQueueName,
        s.person_queue_value_code as personQueueValueCode,
        s.person_queue_value_name as personQueueValueName,
        r.rule_id as ruleId,
        r.rule_name as ruleName,
        s.INFO_CHAN_M_CODE as infoChanMCode,
        s.INFO_CHAN_M_NAME as infoChanMName,
        s.INFO_CHAN_D_CODE as infoChanDCode,
        s.INFO_CHAN_D_NAME as infoChanDName,
        s.INFO_CHAN_DD_CODE as infoChanDDCode,
        s.INFO_CHAN_DD_NAME as infoChanDDName,
        s.INTEN_LEVEL_CODE as intenLevelCode
    from t_sac_review_fp_set s
    inner join t_sac_review_fp_rule r on s.fp_rule_id=r.rule_id
     <where>
     	<if test="param.businessType != null and ''!= param.businessType ">
            AND s.business_type = #{param.businessType}
        </if>
        <if test="param.billType != null and ''!= param.billType ">
            AND s.bill_type = #{param.billType}
        </if>
        <if test="param.isFirstSelf !=null and param.isFirstSelf !=''">
        	AND s.IS_FIRST_SELF=#{param.isFirstSelf}
        </if>
        <if test="param.orgCode != null and ''!= param.orgCode ">
            AND s.org_code = #{param.orgCode}
        </if>
        <if test="param.channelCode != null and ''!= param.channelCode ">
            AND s.CHANNEL_CODE = #{param.channelCode}
        </if>
        <if test="param.infoChanMCode != null and ''!= param.infoChanMCode ">
            AND s.INFO_CHAN_M_CODE = #{param.infoChanMCode}
        </if>
        <if test="param.infoChanDCode != null and ''!= param.infoChanDCode ">
            AND s.INFO_CHAN_D_CODE = #{param.infoChanDCode}
        </if>
        <if test="param.infoChanDDCode != null and ''!= param.infoChanDDCode ">
            AND s.INFO_CHAN_DD_CODE = #{param.infoChanDDCode}
        </if>
        <if test="param.intenLevelCode != null and ''!= param.intenLevelCode ">
            AND s.INTEN_LEVEL_CODE = #{param.intenLevelCode}
        </if>
     </where>
     order by s.LAST_UPDATED_DATE desc
  </select>
  <select id="checkReviewAssign" resultType="int">
    select
        count(1) as countNo
    from t_sac_review_fp_set s
    where 1=1
    AND s.is_enable='1'
    AND s.bill_type = #{param.billType}
    AND s.org_code=#{param.dlrCode}
    <choose>
	      <when test="param.businessType !=null and param.businessType !=''"> AND s.business_type = #{param.businessType}</when>
	      <otherwise>and (s.business_type is null or s.business_type='')</otherwise>
      </choose>
      <choose>
	      <when test="param.infoChanMCode !=null and param.infoChanMCode !=''"> AND s.INFO_CHAN_M_CODE=#{param.infoChanMCode}</when>
	      <otherwise>and (s.INFO_CHAN_M_CODE is null or s.INFO_CHAN_M_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.infoChanDCode !=null and param.infoChanDCode !=''"> AND s.INFO_CHAN_D_CODE=#{param.infoChanDCode}</when>
	      <otherwise>and (s.INFO_CHAN_D_CODE is null or s.INFO_CHAN_D_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.infoChanDDCode !=null and param.infoChanDDCode !=''"> AND s.INFO_CHAN_DD_CODE=#{param.infoChanDDCode}</when>
	      <otherwise>and (s.INFO_CHAN_DD_CODE is null or s.INFO_CHAN_DD_CODE='')</otherwise>
      </choose>
      <choose>
	      <when test="param.channelCode !=null and param.channelCode !=''"> AND s.CHANNEL_CODE = #{param.channelCode}</when>
	      <otherwise>and (s.CHANNEL_CODE is null or s.CHANNEL_CODE='')</otherwise>
      </choose>
      <choose>
       	  <when test="param.intenLevelCode !=null and param.intenLevelCode !=''"> AND s.INTEN_LEVEL_CODE = #{param.intenLevelCode}</when>
          <otherwise>and (s.INTEN_LEVEL_CODE is null or s.INTEN_LEVEL_CODE='')</otherwise>
      </choose>
    <if test="param.setId !=null and param.setId !=''">
     AND s.set_id !=#{param.setId}
    </if>
  </select>
  <select id="queryReviewRuleInfo" resultType="java.util.Map">
    select
        RULE_ID   as ruleId,
        RULE_CODE as ruleCode,
        RULE_NAME as ruleName,
        RULE_DESC as ruleDesc,
        RULE_TYPE as ruleType
    from t_sac_review_fp_rule r
    <where>
        <if test="param.ruleId != null and ''!= param.ruleId ">
            AND r.rule_id = #{param.ruleId}
        </if>
    </where>
  </select>
<!--auto generated by MybatisCodeHelper on 2021-08-17-->
  <select id="countBySetId" resultType="java.lang.Integer">
    select count(1)
    from t_sac_review_fp_set
    where SET_ID=#{setId,jdbcType=VARCHAR}
  </select>

   <delete id="deleteReviewAssignInfo">
        delete from t_sac_review_fp_set where SET_ID = #{setId}
   </delete>
   
   <select id="machReviewFpRule" resultType="java.util.Map">
    WITH recursive classTb(channel_code) AS (
		SELECT CHANNEL_CODE
		FROM t_sac_channel_info 
		WHERE CHANNEL_CODE=#{param.channelCode}
		UNION ALL
		SELECT s.PARENT_CHANNEL_CODE
		FROM t_sac_channel_info s,classTb
		WHERE 1=1
			AND s.CHANNEL_CODE=classTb.CHANNEL_CODE
	),
	classTb2 as(
		SELECT 
		s.CHANNEL_CODE,s.CHANNEL_LEVEL_CODE
		FROM classTb C 
		INNER JOIN t_sac_channel_info s ON c.CHANNEL_CODE=s.CHANNEL_CODE
		UNION ALL
		SELECT '全部' AS  CHANNEL_CODE,'-1' AS CHANNEL_LEVEL_CODE FROM DUAL
	)
	SELECT 
	M.SET_ID,
	M.IS_FIRST_SELF,
	M.PERSON_QUEUE_CODE,
	M.PERSON_QUEUE_NAME,
	M.PERSON_QUEUE_VALUE_CODE,
	M.PERSON_QUEUE_VALUE_NAME,
    M.INTEN_LEVEL_CODE,
	M.FP_RULE_ID,
	R.RULE_CODE,
	R.RULE_NAME,
	R.RULE_TYPE 
	FROM T_SAC_REVIEW_FP_SET M
	inner JOIN classTb2 C ON c.CHANNEL_CODE=if(ifnull(m.CHANNEL_CODE,'')='','全部',m.CHANNEL_CODE)
	left join T_SAC_REVIEW_FP_RULE R ON M.FP_RULE_ID=R.RULE_ID
	WHERE m.is_enable='1' 
	AND M.ORG_CODE = #{param.orgCode}
	AND M.BILL_TYPE = #{param.billType}
	AND (IFNULL(M.BUSINESS_TYPE,'') ='' OR M.BUSINESS_TYPE = #{param.businessType})
	AND (IFNULL(M.INTEN_LEVEL_CODE,'') ='' OR M.INTEN_LEVEL_CODE = #{param.intenLevelCode})
	ORDER BY C.CHANNEL_LEVEL_CODE DESC
   </select>
   
   <select id="queryReviewPersonBySetD" resultType="java.util.Map">
	SELECT 
	*
	FROM(
		SELECT 
		d.PERSON_USER_ID,
		d.PERSON_NAME,
		d.MAX_NUM,
		sum(case when r.REVIEW_ID is not null then 1 ELSE 0 end) AS CURRENT_NUM,
		d.ORDER_NO
		from T_SAC_REVIEW_FP_SET_D d 
		LEFT JOIN t_sac_review r ON d.PERSON_USER_ID=r.REVIEW_PERSON_ID and r.REVIEW_STATUS in('0','1')
		AND (DATE(r.PLAN_REVIEW_TIME)=DATE(#{param.planReviewTime}) OR ifnull(r.PLAN_REVIEW_TIME,'')='') 
		WHERE d.is_enable='1'
		and D.SET_ID=#{param.setId}
		GROUP BY d.PERSON_USER_ID
	) A 
	WHERE a.MAX_NUM>a.CURRENT_NUM
	order by A.ORDER_NO
   </select>
   
   <select id="queryReviewFpSetD" resultType="java.util.Map">
	SELECT 
	d.PERSON_USER_ID,d.PERSON_NAME,d.MAX_NUM,d.ORDER_NO
	FROM t_sac_review_fp_set_d d 
	WHERE 1=1
	AND d.IS_ENABLE='1'
	and D.SET_ID=#{param.setId}
	ORDER BY d.ORDER_NO
   </select>

</mapper>