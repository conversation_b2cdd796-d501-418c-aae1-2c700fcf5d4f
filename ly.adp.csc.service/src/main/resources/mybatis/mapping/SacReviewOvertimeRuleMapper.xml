<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewOvertimeRuleMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewOvertimeRule">
    <id column="RULE_ID" jdbcType="VARCHAR" property="ruleId" />
    <result column="RULE_NAME" jdbcType="VARCHAR" property="ruleName" />
    <result column="RULE_DESC" jdbcType="VARCHAR" property="ruleDesc" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
    <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
  </resultMap>
  <sql id="Base_Column_List">
    RULE_ID, RULE_NAME, RULE_DESC, ORG_CODE, ORG_NAME, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE,
    MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
  </sql>
<!--auto generated by MybatisCodeHelper on 2021-08-17-->
  <select id="selectByAll" resultType="java.util.Map">
      select
             RULE_ID as ruleId,
             RULE_NAME as ruleName,
             RULE_DESC as ruleDesc,
             ORG_CODE AS orgCode,
          	 ORG_NAME AS orgName,
             OEM_ID as oemId,
             GROUP_ID as groupId,
             CREATOR as creator,
             CREATED_NAME as createdName,
             CREATED_DATE as createdDate,
             MODIFIER as modifier,
             MODIFY_NAME as modifyName,
             LAST_UPDATED_DATE as lastUpdatedDate,
             IS_ENABLE as isEnable,
             UPDATE_CONTROL_ID as updateControlId
      from t_sac_review_overtime_rule
      <where>
      	  <if test="paraMap.orgCode != null and paraMap.orgCode != ''">
              and ORG_CODE = #{paraMap.orgCode,jdbcType=VARCHAR}
          </if>
          <if test="paraMap.ruleId != null and paraMap.ruleId != ''">
              and RULE_ID = #{paraMap.ruleId,jdbcType=VARCHAR}
          </if>
          <if test="paraMap.ruleName != null and paraMap.ruleName != ''">
              and INSTR(RULE_NAME,#{paraMap.ruleName,jdbcType=VARCHAR})
          </if>
          <if test="paraMap.isEnable != null and paraMap.isEnable != ''">
              and IS_ENABLE = #{paraMap.isEnable,jdbcType=VARCHAR}
          </if>
      </where>
      order by LAST_UPDATED_DATE desc
  </select>

  <select id="checkReviewOverTime" resultType="int">
      select count(1) as countNo
      from t_sac_review_overtime_rule p
      <where>
          AND p.is_enable = '1'
          AND p.org_code = #{paraMap.orgCode}
          AND p.rule_name = #{paraMap.ruleName}
          <if test="paraMap.ruleId != null and paraMap.ruleId != ''">
              AND p.RULE_ID != #{paraMap.ruleId}
          </if>
      </where>
  </select>
  
  <select id="queryListRuleD" resultType="java.util.Map">
    select
    ID,
    RULE_ID,
    START_TIME,
    END_TIME,
    concat(START_TIME,'-',END_TIME) as START_END_TIME,
    METHOD_TYPE,
    case when METHOD_TYPE='1' then '固定' when METHOD_TYPE='2' then '动态' end as METHOD_TYPE_NAME,
    ADD_DAYS,
    ADD_HOURS,
    ADD_DYN_HOURS,
    RULE_DESC
    from t_sac_review_overtime_rule_d r
    where 1=1
    AND r.rule_id = #{param.ruleId}
  </select>

<!-- 判断该规则在这个时间段是否已经存在规则明细 -->
  <select id="checkReviewOverTimed" resultType="int">
      select count(1) as countNo
      from t_sac_review_overtime_rule_d r
      where 1 = 1
        AND r.is_enable = '1'
        AND r.rule_id = #{param.ruleId}
        <![CDATA[ AND (
              (#{param.startTime} >= start_time AND #{param.startTime}<=end_time) OR (#{param.endTime} >=start_time AND #{param.endTime}<=end_time)
      )]]>
      <if test="param.id != null and param.id != ''">
          AND r.id !=#{param.id}
      </if>
  </select>

  <insert id="insertReviewOverTimed">
      insert into t_sac_review_overtime_rule_d (
      ID,
      RULE_ID,
      START_TIME, 
      END_TIME, 
      METHOD_TYPE, 
      ADD_DAYS, 
      ADD_HOURS,
      ADD_DYN_HOURS, 
      RULE_DESC, 
      OEM_ID, 
      GROUP_ID, 
      CREATOR, 
      CREATED_NAME,
      CREATED_DATE, 
      MODIFIER, 
      MODIFY_NAME, 
      LAST_UPDATED_DATE, 
      IS_ENABLE,
      UPDATE_CONTROL_ID)
      VALUES (
      uuid(),
      #{map.ruleId},
      #{map.startTime},
      #{map.endTime},
      #{map.methodType},
      #{map.addDays},
      #{map.addHours},
      #{map.addDynHours},
      #{map.ruleDesc},
      #{map.oemId},
      #{map.groupId},
      #{map.creator},
      #{map.createdName},
      sysdate(),
      #{map.modifier},
      #{map.modifyName},
      sysdate(),
      '1',
      uuid()
      )
  </insert>

  <update id="updateReviewOvertimeRuled">
        update t_sac_review_overtime_rule_d
        set
        <if test = "map.ruleId !=null and map.ruleId !=''">rule_id = #{map.ruleId},</if>
        <if test = "map.startTime !=null and map.startTime !=''">start_time = #{map.startTime},</if>
        <if test = "map.endTime !=null and map.endTime !=''"> end_time = #{map.endTime},</if>
        <if test = "map.methodType !=null and map.methodType !=''">method_type = #{map.methodType},</if>
        <if test = "map.addDays !=null and map.addDays !=''">add_days = #{map.addDays},</if>
        <if test = "map.addHours !=null and map.addHours !=''">add_hours = #{map.addHours},</if>
        <if test = "map.addDynHours !=null and map.addDynHours !=''"> add_dyn_hours = #{map.addDynHours},</if>
        <if test = "map.ruleDesc !=null and map.ruleDesc !=''">rule_desc = #{map.ruleDesc},</if>
        <if test = "map.modifier !=null and map.modifier !=''">MODIFIER = #{map.modifier},</if>
        <if test = "map.modifyName !=null and map.modifyName !=''">MODIFY_NAME = #{map.modifyName},</if>
        UPDATE_CONTROL_ID = #{map.updateControlId},
    	LAST_UPDATED_DATE = #{map.lastUpdatedDate}
        where id = #{map.id}
  </update>
  
  <delete id="deleteRuledInfo">
    delete from t_sac_review_overtime_rule_d where ID = #{id}
  </delete>
  
  <delete id="deleteByRuled">
    delete from t_sac_review_overtime_rule_d r where r.RULE_ID =#{ruleId}
  </delete>
  
    <select id="checkReviewOvertimeRuleExists" resultType="int">
        select count(1)
        from t_sac_review_overtime_rule
        where rule_id=#{ruleId}
    </select>
    
    <select id="checkReviewOvertimeRuleDExits" resultType="int">
        select count(1)
        from t_sac_review_overtime_rule_d t
        where t.id=#{id}
    </select>
  
</mapper>