<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacUserTagRelMapper">

    <!-- 选增性新增 -->
    <insert id="insertSelective">
        INSERT INTO `t_sac_user_tag_rel`
        <trim prefix="(" suffixOverrides="," suffix=")">
            <if test="data.relId != null">
                `REL_ID`,
            </if>
            <if test="data.smartId != null">
                `SMART_ID`,
            </if>
            <if test="data.phone != null">
                `PHONE`,
            </if>
            <if test="data.nickName != null">
                `NICK_NAME`,
            </if>
            <if test="data.relType != null">
                `REL_TYPE`,
            </if>
            <if test="data.refId != null">
                `REF_ID`,
            </if>
            <if test="data.remarkContent != null">
                `REMARK_CONTENT`,
            </if>
            <if test="data.creator != null">
                `CREATOR`,
            </if>
            <if test="data.createdName != null">
                `CREATED_NAME`,
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE`,
            </if>
            <if test="data.modifier != null">
                `MODIFIER`,
            </if>
            <if test="data.modifyName != null">
                `MODIFY_NAME`,
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffixOverrides="," suffix=")">
            <if test="data.relId != null">
                #{data.relId},
            </if>
            <if test="data.smartId != null">
                #{data.smartId},
            </if>
            <if test="data.phone != null">
                #{data.phone},
            </if>
            <if test="data.nickName != null">
                #{data.nickName},
            </if>
            <if test="data.relType != null">
                #{data.relType},
            </if>
            <if test="data.refId != null">
                #{data.refId},
            </if>
            <if test="data.remarkContent != null">
                #{data.remarkContent},
            </if>
            <if test="data.creator != null">
                #{data.creator},
            </if>
            <if test="data.createdName != null">
                #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                #{data.createdDate},
            </if>
            <if test="data.modifier != null">
                #{data.modifier},
            </if>
            <if test="data.modifyName != null">
                #{data.modifyName},
            </if>
            <if test="data.lastUpdatedDate != null">
                #{data.lastUpdatedDate},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM `t_sac_user_tag_rel` WHERE `REL_ID` = #{relId}
    </delete>

    <!-- 选增性修改 -->
    <update id="updateSelective">
        UPDATE
            `t_sac_user_tag_rel`
        <set>
            <if test="data.smartId != null and data.smartId != ''">
                `SMART_ID` = #{data.smartId},
            </if>
            <if test="data.phone != null and data.phone != ''">
                `PHONE` = #{data.phone},
            </if>
            <if test="data.nickName != null and data.nickName != ''">
                `NICK_NAME` = #{data.nickName},
            </if>
            <if test="data.relType != null">
                `REL_TYPE` = #{data.relType},
            </if>
            <if test="data.refId != null and data.refId != ''">
                `REF_ID` = #{data.refId},
            </if>
            <if test="data.remarkContent != null and data.remarkContent != ''">
                `REMARK_CONTENT` = #{data.remarkContent},
            </if>
            <if test="data.creator != null and data.creator != ''">
                `CREATOR` = #{data.creator},
            </if>
            <if test="data.createdName != null and data.createdName != ''">
                `CREATED_NAME` = #{data.createdName},
            </if>
            <if test="data.createdDate != null">
                `CREATED_DATE` = #{data.createdDate},
            </if>
            <if test="data.modifier != null and data.modifier != ''">
                `MODIFIER` = #{data.modifier},
            </if>
            <if test="data.modifyName != null and data.modifyName != ''">
                `MODIFY_NAME` = #{data.modifyName},
            </if>
            <if test="data.lastUpdatedDate != null">
                `LAST_UPDATED_DATE` = #{data.lastUpdatedDate},
            </if>
        </set>
        WHERE
            `REL_ID` = #{data.relId}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultType="com.ly.adp.csc.entities.SacUserTagRel">
        SELECT
            `REL_ID`            AS relId,
            `SMART_ID`          AS smartId,
            `PHONE`             AS phone,
            `NICK_NAME`         AS nickName,
            `REL_TYPE`          AS relType,
            `REF_ID`            AS refId,
            `REMARK_CONTENT`    AS remarkContent,
            `CREATOR`           AS creator,
            `CREATED_NAME`      AS createdName,
            `CREATED_DATE`      AS createdDate,
            `MODIFIER`          AS modifier,
            `MODIFY_NAME`       AS modifyName,
            `LAST_UPDATED_DATE` AS lastUpdatedDate
        FROM
            `t_sac_user_tag_rel`
        WHERE
            `REL_ID` = #{relId}
    </select>

</mapper>