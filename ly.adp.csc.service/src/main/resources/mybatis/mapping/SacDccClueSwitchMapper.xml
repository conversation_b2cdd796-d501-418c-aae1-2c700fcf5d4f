<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacDccClueSwitchMapper">

    <select id="queryDccInfoWithPage" resultType="com.ly.mp.csc.clue.entities.vo.DccClueSwitchVO">
        WITH employee_count AS (
        SELECT
        DLR_ID,
        COUNT(1) as call_person_count
        FROM mp.t_usc_mdm_org_employee
        WHERE STATION_ID = 'smart_bm_0075_C' AND USER_STATUS IN ('1', '3')
        GROUP BY DLR_ID
        )
        SELECT
        c.AGENT_ID,
        c.AGENT_CODE,
        c.AGENT_NAME,
        b.AGENT_COMPANY_ID,
        b.AGENT_COMPANY_CODE,
        b.AGENT_COMPANY_NAME,
        a.DLR_ID,
        a.DLR_CODE,
        a.DLR_SHORT_NAME,
        COALESCE(s.switch_value, 0) as dccSwitchStatus,
        COALESCE(ec.call_person_count, 0) as callPersonCount
        FROM mp.t_usc_mdm_org_dlr a
        INNER JOIN mp.t_usc_mdm_agent_company b ON a.COMPANY_ID = b.AGENT_COMPANY_ID
        INNER JOIN mp.t_usc_mdm_agent_info c ON b.AGENT_ID = c.AGENT_ID
        LEFT JOIN csc.t_sac_dcc_clue_switch s ON a.DLR_CODE = s.dlr_code
        LEFT JOIN employee_count ec ON b.AGENT_COMPANY_ID = ec.DLR_ID
        WHERE a.DLR_TYPE IN ('BC', 'POS')
        <if test="param.dlrCode != null and param.dlrCode != ''">
            AND a.DLR_CODE = #{param.dlrCode}
        </if>
        <if test="param.agentId != null and param.agentId != ''">
            AND c.AGENT_ID = #{param.agentId}
        </if>
        <if test="param.agentCompanyId != null and param.agentCompanyId != ''">
            AND b.AGENT_COMPANY_ID = #{param.agentCompanyId}
        </if>
        <if test="param.hasCallPerson != null">
            <choose>
                <when test="param.hasCallPerson">
                    AND COALESCE(ec.call_person_count, 0) > 0
                </when>
                <otherwise>
                    AND COALESCE(ec.call_person_count, 0) = 0
                </otherwise>
            </choose>
        </if>
        <if test="param.dccSwitchStatus != null">
            AND IFNULL(s.switch_value, 0) = #{param.dccSwitchStatus}
        </if>
    </select>
</mapper>