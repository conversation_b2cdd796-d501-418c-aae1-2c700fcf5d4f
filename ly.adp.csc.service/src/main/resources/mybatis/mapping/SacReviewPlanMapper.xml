<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewPlanMapper">
  <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewPlan">
    <!--@mbg.generated-->
    <!--@Table t_sac_review_plan-->
    <id column="PLAN_ID" jdbcType="VARCHAR" property="planId" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
    <result column="BILL_TYPE_NAME" jdbcType="VARCHAR" property="billTypeName" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_TYPE_NAME" jdbcType="VARCHAR" property="businessTypeName" />
    <result column="FIELD_CODE" jdbcType="VARCHAR" property="fieldCode" />
    <result column="FIELD_NAME" jdbcType="VARCHAR" property="fieldName" />
    <result column="FIELD_VALUE" jdbcType="VARCHAR" property="fieldValue" />
    <result column="NEXT_REVIEW_TIME" jdbcType="VARCHAR" property="nextReviewTime" />
    <result column="OEM_ID" jdbcType="VARCHAR" property="oemId" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATED_NAME" jdbcType="VARCHAR" property="createdName" />
    <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_NAME" jdbcType="VARCHAR" property="modifyName" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="UPDATE_CONTROL_ID" jdbcType="VARCHAR" property="updateControlId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PLAN_ID, ORG_CODE, ORG_NAME, BILL_TYPE, BILL_TYPE_NAME, BUSINESS_TYPE, BUSINESS_TYPE_NAME, 
    FIELD_CODE, FIELD_NAME, FIELD_VALUE, NEXT_REVIEW_TIME, OEM_ID, GROUP_ID, CREATOR, 
    CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, 
    UPDATE_CONTROL_ID
  </sql>
<!--auto generated by MybatisCodeHelper on 2021-08-17-->
  <select id="selectByAll" resultType="java.util.Map">
    select
           PLAN_ID as planId,
           ORG_CODE as orgCode,
           ORG_NAME as orgName,
           BILL_TYPE as billType,
           BILL_TYPE_NAME as billTypeName,
           BUSINESS_TYPE as businessType,
           BUSINESS_TYPE_NAME as businessTypeName,
           FIELD_CODE as fieldCode,
           FIELD_NAME as fieldName,
           FIELD_VALUE as fieldValue,
           NEXT_REVIEW_TIME as nextReviewTime,
           OEM_ID as oemId,
           GROUP_ID as groupId,
           CREATOR as creator,
           CREATED_NAME as createdName,
           CREATED_DATE as createdDate,
           MODIFIER as modifier,
           MODIFY_NAME as modifyName,
           LAST_UPDATED_DATE as lastUpdatedDate,
           IS_ENABLE as isEnable,
           UPDATE_CONTROL_ID as updateControlId
    from t_sac_review_plan
    <where>
      <if test="param.planId != null and param.planId != ''">
          and PLAN_ID = #{param.planId,jdbcType=VARCHAR}
      </if>
      <if test="param.orgCode != null and param.orgCode != ''">
          and ORG_CODE = #{param.orgCode,jdbcType=VARCHAR}
      </if>
      <if test="param.billType != null and param.billType != ''">
          and BILL_TYPE = #{param.billType,jdbcType=VARCHAR}
      </if>
      <if test="param.businessType != null and param.businessType != ''">
          and BUSINESS_TYPE = #{param.businessType,jdbcType=VARCHAR}
      </if>
      <if test="param.fieldCode != null and param.fieldCode != ''">
          and FIELD_CODE = #{param.fieldCode,jdbcType=VARCHAR}
      </if>
      <if test="param.fieldValue != null and param.fieldValue != ''">
          and FIELD_VALUE = #{param.fieldValue,jdbcType=VARCHAR}
      </if>
      <if test="param.isEnable != null and param.isEnable != ''">
          and IS_ENABLE = #{param.isEnable,jdbcType=VARCHAR}
      </if>
    </where>
    order by LAST_UPDATED_DATE desc
  </select>

  <select id="checkReviewPlanInfo" resultType="int">
      select count(1) as countNo
      from t_sac_review_plan p
      <where>
          AND p.is_enable = '1'
          AND p.bill_type = #{param.billType}
          AND p.field_code = #{param.fieldCode}
          AND p.field_value = #{param.fieldValue}
          <choose>
	        <when test="param.businessType !=null and param.businessType !=''"> AND p.business_type = #{param.businessType}</when>
	        <otherwise>and (p.business_type is null or p.business_type='')</otherwise>
      	  </choose>
          <if test="param.planId != null and param.planId != ''">
              AND PLAN_ID != #{param.planId}
          </if>
      </where>
  </select>
  
  <select id="checkReviewPlanExists" resultType="int">
        select count(1)
        from t_sac_review_plan
        where PLAN_ID=#{planId}
    </select>
</mapper>