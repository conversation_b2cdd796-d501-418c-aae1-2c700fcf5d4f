<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacTagOperationLogMapper">

    <!-- 选增性新增 -->
    <insert id="insertSelective">
        INSERT INTO `t_sac_tag_operation_log`
        <trim prefix="(" suffixOverrides="," suffix=")">
            <if test="data.logId != null">
                `LOG_ID`,
            </if>
            <if test="data.operationType != null">
                `OPERATION_TYPE`,
            </if>
            <if test="data.targetId != null">
                `TARGET_ID`,
            </if>
            <if test="data.smartId != null">
                `SMART_ID`,
            </if>
            <if test="data.batchId != null">
                `BATCH_ID`,
            </if>
            <if test="data.operator != null">
                `OPERATOR`,
            </if>
            <if test="data.operationDate != null">
                `OPERATION_DATE`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffixOverrides="," suffix=")">
            <if test="data.logId != null">
                #{data.logId},
            </if>
            <if test="data.operationType != null">
                #{data.operationType},
            </if>
            <if test="data.targetId != null">
                #{data.targetId},
            </if>
            <if test="data.smartId != null">
                #{data.smartId},
            </if>
            <if test="data.batchId != null">
                #{data.batchId},
            </if>
            <if test="data.operator != null">
                #{data.operator},
            </if>
            <if test="data.operationDate != null">
                #{data.operationDate},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM `t_sac_tag_operation_log` FROM `LOG_ID` = #{logId}
    </delete>

    <!-- 选增性修改 -->
    <update id="updateSelective">
        UPDATE
            `t_sac_tag_operation_log`
        <set>
            <if test="data.operationType != null and data.operationType != ''">
                `OPERATION_TYPE` = #{data.operationType},
            </if>
            <if test="data.targetId != null">
                `TARGET_ID` = #{data.targetId},
            </if>
            <if test="data.smartId != null and data.smartId != ''">
                `SMART_ID` = #{data.smartId},
            </if>
            <if test="data.batchId != null and data.batchId != ''">
                `BATCH_ID` = #{data.batchId},
            </if>
            <if test="data.operator != null and data.operator != ''">
                `OPERATOR` = #{data.operator},
            </if>
            <if test="data.operationDate != null">
                `OPERATION_DATE` = #{data.operationDate},
            </if>
        </set>
        WHERE
            `LOG_ID` = #{data.logId}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultType="com.ly.adp.csc.entities.SacTagOperationLog">
        SELECT
            `LOG_ID`         AS logId,
            `OPERATION_TYPE` AS operationType,
            `TARGET_ID`      AS targetId,
            `SMART_ID`       AS smartId,
            `BATCH_ID`       AS batchId,
            `OPERATOR`       AS operator,
            `OPERATION_DATE` AS operationDate
        FROM
            `t_sac_tag_operation_log`
        WHERE
            `LOG_ID` = #{logId}
    </select>

</mapper>