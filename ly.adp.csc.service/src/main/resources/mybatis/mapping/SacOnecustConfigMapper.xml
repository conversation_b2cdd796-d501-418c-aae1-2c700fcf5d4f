<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOnecustConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOnecustConfig">
        <id column="CONFIG_ID" property="configId" />
        <result column="OBJECT_TYPE" property="objectType" />
        <result column="CHANGE_FILED" property="changeFiled" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CONFIG_ID, OBJECT_TYPE, CHANGE_FILED, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>
    
    <!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.configId !=null and param.configId !=''">and CONFIG_ID=#{param.configId}</if>
	    <if test="param.objectType !=null and param.objectType !=''">and OBJECT_TYPE=#{param.objectType}</if>
	    <if test="param.changeFiled !=null and param.changeFiled !=''">and CHANGE_FILED=#{param.changeFiled}</if>
	</sql>
    
    <!-- 客户信息变更配置查询 -->
	<select id="sacOnecustConfigFindInfo" resultType="map">
		select 
	    <include refid="Base_Column_List"></include>
	    from t_sac_onecust_config
	    where 1=1
	    <include refid="where_condition"></include>
		ORDER BY CREATED_DATE DESC
	</select>
</mapper>