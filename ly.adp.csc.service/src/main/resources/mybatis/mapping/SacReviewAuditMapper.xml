<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacReviewAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacReviewAudit">
        <id column="AUDIT_ID" property="auditId" />
        <result column="REVIEW_ID" property="reviewId" />
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    	<result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="BILL_CODE" property="billCode" />
        <result column="APPLY_TYPE_CODE" property="applyTypeCode" />
        <result column="APPLY_TYPE_NAME" property="applyTypeName" />
        <result column="APPLY_DESC" property="applyDesc" />
        <result column="SH_PERSON_ID" property="shPersonId" />
        <result column="SH_PERSON_NAME" property="shPersonName" />
        <result column="SH_DESC" property="shDesc" />
        <result column="SH_TIME" property="shTime" />
        <result column="SH_STATUS" property="shStatus" />
        <result column="SH_STATUS_NAME" property="shStatusName" />
        <result column="EXTENDS_JSON" property="extendsJson" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        AUDIT_ID, REVIEW_ID, ORG_CODE, ORG_NAME, BILL_CODE, APPLY_TYPE_CODE, APPLY_TYPE_NAME, APPLY_DESC, SH_PERSON_ID, SH_PERSON_NAME, SH_DESC, SH_TIME, SH_STATUS, SH_STATUS_NAME, EXTENDS_JSON, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>

	<select id="queryListAuditReviewInfo" resultType="java.util.Map">
	        select
	        a.audit_id,
	        R.REVIEW_ID,
	        a.apply_type_code,
	        a.apply_type_name,
	        r.cust_name,
	        r.phone,
	        r.bill_type_name,
	        r.business_type_name,
	        r.bill_code,
	        a.created_date,
	        a.created_name,
	        r.touch_status_name,
	        r.error_reason_name,
	        a.apply_desc,
	        A.ORG_CODE,
	        A.ORG_NAME,
	        R.ASSIGN_STATUS,
	        A.UPDATE_CONTROL_ID,
	        R.UPDATE_CONTROL_ID AS R_UPDATE_CONTROL_ID,
	        r.REVIEW_DESC
	    from t_sac_review_audit a
	    inner join t_sac_review r on a.review_id=r.review_id
	    where 1=1
	    and a.is_enable='1'
	    and A.ORG_CODE=#{param.orgCode}
	    <if test="param.auditId != null and ''!= param.auditId ">
	        AND A.audit_id = #{param.auditId}
	    </if>
	    <if test="param.reviewId != null and ''!= param.reviewId ">
	        AND A.REVIEW_ID = #{param.reviewId}
	    </if>
	    <if test="param.custName != null and ''!= param.custName ">
	        AND r.cust_name = #{param.custName}
	    </if>
	    <if test="param.phone != null and ''!= param.phone ">
	        AND r.phone = #{param.phone}
	    </if>
	    <if test="param.shStatus != null and ''!= param.shStatus ">
	        AND A.sh_status = #{param.shStatus}
	    </if>
	    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
	        AND A.apply_type_code = #{param.applyTypeCode}
	    </if>
	    <if test="param.billCode != null and ''!= param.billCode ">
	        AND r.bill_code = #{param.billCode}
	    </if>
	    <if test="param.applyPerson != null and ''!= param.applyPerson ">
	        AND r.created_name = #{param.applyPerson}
	    </if>
	    <if test="param.startTime != null and ''!= param.startTime">
	        <![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
	    </if>
	    <if test="param.endTime != null and ''!= param.endTime">
	        <![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
	    </if>
	  </select>

	  <select id="checkAuditReviewInfo" resultType="java.util.Map">
	  select * from(
	        select
	        a.audit_id,
	        R.REVIEW_ID,
	        a.apply_type_code,
	        a.apply_type_name,
	        r.cust_name,
	        r.phone,
	        r.bill_type_name,
	        r.business_type_name,
	        r.bill_code,
	        a.created_date,
	        a.created_name,
	        r.touch_status_name,
	        r.error_reason_name,
	        a.apply_desc,
	        A.ORG_CODE,
	        A.ORG_NAME,
	        R.ASSIGN_STATUS,
	        A.UPDATE_CONTROL_ID,
	        R.UPDATE_CONTROL_ID AS R_UPDATE_CONTROL_ID
	    from t_sac_review_audit a
	    inner join t_sac_review r on a.review_id=r.review_id
	    where 1=1
	    and a.is_enable='1'
<!--	    and A.ORG_CODE=#{param.orgCode}-->
	    AND A.audit_id = #{param.auditId}
	    <if test="param.reviewId != null and ''!= param.reviewId ">
	        AND A.REVIEW_ID = #{param.reviewId}
	    </if>
	    <if test="param.custName != null and ''!= param.custName ">
	        AND r.cust_name = #{param.custName}
	    </if>
	    <if test="param.phone != null and ''!= param.phone ">
	        AND r.phone = #{param.phone}
	    </if>
	    <if test="param.shStatus != null and ''!= param.shStatus ">
	        AND A.sh_status = #{param.shStatus}
	    </if>
	    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
	        AND A.apply_type_code = #{param.applyTypeCode}
	    </if>
	    <if test="param.billCode != null and ''!= param.billCode ">
	        AND r.bill_code = #{param.billCode}
	    </if>
	    <if test="param.applyPerson != null and ''!= param.applyPerson ">
	        AND r.created_name = #{param.applyPerson}
	    </if>
	    union all
	    select
	        a.audit_id,
	        R.REVIEW_ID,
	        a.apply_type_code,
	        a.apply_type_name,
	        r.cust_name,
	        r.phone,
	        r.bill_type_name,
	        r.business_type_name,
	        r.bill_code,
	        a.created_date,
	        a.created_name,
	        r.touch_status_name,
	        r.error_reason_name,
	        a.apply_desc,
	        A.ORG_CODE,
	        A.ORG_NAME,
	        R.ASSIGN_STATUS,
	        A.UPDATE_CONTROL_ID,
	        R.UPDATE_CONTROL_ID AS R_UPDATE_CONTROL_ID
	    from t_sac_review_audit a
	    inner join t_sac_review_his r on a.review_id=r.review_id
	    where 1=1
	    and a.is_enable='1'
<!--	    and A.ORG_CODE=#{param.orgCode}-->
	    AND A.audit_id = #{param.auditId}
	    <if test="param.reviewId != null and ''!= param.reviewId ">
	        AND A.REVIEW_ID = #{param.reviewId}
	    </if>
	    <if test="param.custName != null and ''!= param.custName ">
	        AND r.cust_name = #{param.custName}
	    </if>
	    <if test="param.phone != null and ''!= param.phone ">
	        AND r.phone = #{param.phone}
	    </if>
	    <if test="param.shStatus != null and ''!= param.shStatus ">
	        AND A.sh_status = #{param.shStatus}
	    </if>
	    <if test="param.applyTypeCode != null and ''!= param.applyTypeCode ">
	        AND A.apply_type_code = #{param.applyTypeCode}
	    </if>
	    <if test="param.billCode != null and ''!= param.billCode ">
	        AND r.bill_code = #{param.billCode}
	    </if>
	    <if test="param.applyPerson != null and ''!= param.applyPerson ">
	        AND r.created_name = #{param.applyPerson}
	    </if>
	    ) A
	  </select>

 	<insert id="insertSacReviewAudit">
        insert into t_sac_review_audit(
         AUDIT_ID
        ,REVIEW_ID
        ,ORG_CODE
        ,ORG_NAME
        ,BILL_CODE
        ,APPLY_TYPE_CODE
        ,APPLY_TYPE_NAME
        ,APPLY_DESC
        ,SH_PERSON_ID
        ,SH_PERSON_NAME
        ,SH_DESC
        ,SH_TIME
        ,SH_STATUS
        ,SH_STATUS_NAME
        ,EXTENDS_JSON
        ,OEM_ID
        ,GROUP_ID
        ,CREATOR
        ,CREATED_NAME
        ,CREATED_DATE
        ,MODIFIER
        ,MODIFY_NAME
        ,LAST_UPDATED_DATE
        ,IS_ENABLE
        ,UPDATE_CONTROL_ID
        )
        values(
         #{param.auditId}
        ,#{param.reviewId}
        ,#{param.orgCode}
        ,#{param.orgName}
        ,#{param.billCode}
        ,#{param.applyTypeCode}
        ,#{param.applyTypeName}
        ,#{param.applyDesc}
        ,#{param.shPersonId}
        ,#{param.shPersonName}
        ,#{param.shDesc}
        ,#{param.shTime}
        ,#{param.shStatus}
        ,#{param.shStatusName}
        ,#{param.extendsJson}
        ,#{param.oemId}
        ,#{param.groupId}
        ,#{param.creator}
        ,#{param.createdName}
        ,#{param.createdDate}
        ,#{param.modifier}
        ,#{param.modifyName}
        ,#{param.lastUpdatedDate}
        ,'1'
        ,#{param.updateControlId}
		)
    </insert>

    <update id="updateSacReviewAudit">
    	update t_sac_review_audit  set
    	MODIFIER=#{param.modifier},
    	MODIFY_NAME=#{param.modifyName},
    	LAST_UPDATED_DATE=sysdate(),
    	UPDATE_CONTROL_ID=uuid()
	    <if test = 'param.reviewId!=null'> ,REVIEW_ID = #{param.reviewId}</if>
	    <if test = 'param.orgCode!=null'> ,ORG_CODE = #{param.orgCode}</if>
	    <if test = 'param.orgName!=null'> ,ORG_NAME = #{param.orgName}</if>
	    <if test = 'param.billCode!=null'> ,BILL_CODE = #{param.billCode}</if>
	    <if test = 'param.applyTypeCode!=null'> ,APPLY_TYPE_CODE = #{param.applyTypeCode}</if>
	    <if test = 'param.applyTypeName!=null'> ,APPLY_TYPE_NAME = #{param.applyTypeName}</if>
	    <if test = 'param.applyDesc!=null'> ,APPLY_DESC = #{param.applyDesc}</if>
	    <if test = 'param.shPersonId!=null'> ,SH_PERSON_ID = #{param.shPersonId}</if>
	    <if test = 'param.shPersonName!=null'> ,SH_PERSON_NAME = #{param.shPersonName}</if>
	    <if test = 'param.shDesc!=null'> ,SH_DESC = #{param.shDesc}</if>
	    <if test = 'param.shTime!=null'> ,SH_TIME = #{param.shTime}</if>
	    <if test = 'param.shStatus!=null'> ,SH_STATUS = #{param.shStatus}</if>
	    <if test = 'param.shStatusName!=null'> ,SH_STATUS_NAME = #{param.shStatusName}</if>
	    <if test = 'param.extendsJson!=null'> ,EXTENDS_JSON = #{param.extendsJson}</if>
	    <if test = 'param.oemId!=null'> ,OEM_ID = #{param.oemId}</if>
	    <if test = 'param.groupId!=null'> ,GROUP_ID = #{param.groupId}</if>
	    <if test = 'param.isEnable!=null'> ,IS_ENABLE = #{param.isEnable}</if>
    	where 1=1
         and AUDIT_ID = #{param.auditId}
    </update>

    <select id="findActivationAfterDefeat" resultType="java.util.Map">
		SELECT
		a.AUDIT_ID,
		r.REVIEW_ID,
		a.apply_type_name,
		r.cust_id,
		r.cust_name,
		r.phone,
		a.created_date,
		a.created_name,
		a.apply_desc,
		a.sh_status,
		a.sh_status_name,
		a.sh_desc,
		a.sh_time,
		r.REVIEW_PERSON_ID,
		r.REVIEW_PERSON_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
		r.INTEN_LEVEL_CODE,
		r.INTEN_LEVEL_NAME,
		r.INTEN_BRAND_CODE,
		r.INTEN_BRAND_NAME,
		r.INTEN_SERIES_CODE,
		r.INTEN_SERIES_NAME,
		r.INTEN_CAR_TYPE_CODE,
		r.INTEN_CAR_TYPE_NAME,
		r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.COLUMN1,
		r.COLUMN2,
		r.COLUMN3,
		r.COLUMN4,
		r.COLUMN5,
		r.COLUMN6,
		r.COLUMN7,
		r.COLUMN8,
		r.COLUMN9,
		r.COLUMN10,
		r.COLUMN11,
		r.COLUMN12,
		r.COLUMN13,
		r.COLUMN14,
		r.COLUMN15,
		r.COLUMN16,
		r.COLUMN17,
		r.COLUMN18,
		r.COLUMN19,
		r.COLUMN20,
		r.BIG_COLUMN1,
		r.BIG_COLUMN2,
		r.BIG_COLUMN3,
		r.BIG_COLUMN4,
		r.BIG_COLUMN5,
		r.EXTENDS_JSON,
		A.UPDATE_CONTROL_ID
		FROM
		t_sac_review_audit a
		left JOIN t_sac_clue_info_dlr r ON a.review_id = r.review_id
		WHERE
		a.is_enable = '1'
		and r.COLUMN20 is NULL
		and r.STATUS_CODE  = '10'
		<choose>
			<when test="param.orgId !=null and param.orgId !=''">
				and r.DLR_CODE in (
					SELECT
					DLR_CODE
					FROM
					mp.t_usc_mdm_org_dlr
					WHERE
					COMPANY_ID=#{param.orgId}
				)
			</when>
			<otherwise>
				and r.DLR_CODE=#{param.orgCode}
			</otherwise>
		</choose>
		<if test="param.identification !=null and param.identification !=''">
		and	r.REVIEW_PERSON_ID=#{param.personId}
		</if>
		<if test="param.startTime != null and ''!= param.startTime">
			<![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
		</if>
		<if test="param.endTime != null and ''!= param.endTime">
			<![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
		AND r.REVIEW_PERSON_ID IN
		<foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
				 close=")">
			#{item}
		</foreach>
		</if>
		<if test="param.shStatus != null and ''!= param.shStatus ">
			AND A.sh_status IN
			<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">
			and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and r.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY a.created_date DESC
	</select>

	<select id="findActivationAfterDefeatV2" resultType="java.util.Map">
		SELECT
			a.AUDIT_ID,
			r.REVIEW_ID,
			a.apply_type_name,
			r.cust_id,
			r.cust_name,
			r.phone,
			a.created_date,
			a.created_name,
			a.apply_desc,
			a.sh_status,
			a.sh_status_name,
			a.sh_desc,
			a.sh_time,
			r.REVIEW_PERSON_ID,
			r.REVIEW_PERSON_NAME,
			r.CHANNEL_CODE,
			r.CHANNEL_NAME,
			r.INTEN_LEVEL_CODE,
			r.INTEN_LEVEL_NAME,
			r.INTEN_BRAND_CODE,
			r.INTEN_BRAND_NAME,
			r.INTEN_SERIES_CODE,
			r.INTEN_SERIES_NAME,
			r.INTEN_CAR_TYPE_CODE,
			r.INTEN_CAR_TYPE_NAME,
			r.INFO_CHAN_M_CODE,
			r.INFO_CHAN_M_NAME,
			r.INFO_CHAN_D_CODE,
			r.INFO_CHAN_D_NAME,
			r.COLUMN1,
			r.COLUMN2,
			r.COLUMN3,
			r.COLUMN4,
			r.COLUMN5,
			r.COLUMN6,
			r.COLUMN7,
			r.COLUMN8,
			r.COLUMN9,
			r.COLUMN10,
			r.COLUMN11,
			r.COLUMN12,
			r.COLUMN13,
			r.COLUMN14,
			r.COLUMN15,
			r.COLUMN16,
			r.COLUMN17,
			r.COLUMN18,
			r.COLUMN19,
			r.COLUMN20,
			r.BIG_COLUMN1,
			r.BIG_COLUMN2,
			r.BIG_COLUMN3,
			r.BIG_COLUMN4,
			r.BIG_COLUMN5,
			r.EXTENDS_JSON,
			A.UPDATE_CONTROL_ID
		FROM
			t_sac_review_audit a
		left JOIN
			t_sac_clue_info_dlr r
		ON
			a.review_id = r.review_id
		WHERE
			a.is_enable = '1'
			and r.COLUMN20 is NULL
			and r.STATUS_CODE  = '10'
		<choose>
			<when test="param.orgId !=null and param.orgId !=''">
				and r.DLR_CODE in (
				SELECT
				DLR_CODE
				FROM
				mp.t_usc_mdm_org_dlr
				WHERE
				COMPANY_ID=#{param.orgId}
				)
			</when>
			<otherwise>
				and r.DLR_CODE=#{param.orgCode}
			</otherwise>
		</choose>
		<if test="param.identification !=null and param.identification !=''">
			and	r.REVIEW_PERSON_ID=#{param.personId}
		</if>
		<if test="param.startTime != null and ''!= param.startTime">
			<![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
		</if>
		<if test="param.endTime != null and ''!= param.endTime">
			<![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
			AND r.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.shStatus != null and ''!= param.shStatus ">
			AND A.sh_status IN
			<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">
			and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
		</if>

		<if test="param.intenLevelCode !=null and param.intenLevelCode != ''">
			and r.INTEN_LEVEL_CODE = #{param.intenLevelCode}
		</if>
		<if test="param.businessHeatCode !=null and param.businessHeatCode != ''">
			and r.COLUMN6 = #{param.businessHeatCode}
		</if>

		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and r.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY a.created_date DESC
	</select>

	<select id="selectProductByDlrCode" resultType="java.util.Map">
		SELECT
		emp_name,
		user_id
		FROM
		mp.t_usc_mdm_org_employee
		WHERE
		ORG_TYPE = '1'
		AND ( STATION_ID IN ( 'smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064' )
		OR COLUMN2 IN ( 'smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064' ) )
		AND USER_STATUS = '1'
		AND dlr_code = #{param.dlrCode}
	</select>

	<select id="newFindActivationAfterDefeat" resultType="java.util.Map">
		SELECT
		a.AUDIT_ID,
		r.REVIEW_ID,
		a.apply_type_name,
		r.cust_id,
		r.cust_name,
		r.phone,
		a.created_date,
		a.created_name,
		a.apply_desc,
		a.sh_status,
		a.sh_status_name,
		a.sh_desc,
		a.sh_time,
		r.REVIEW_PERSON_ID,
		r.REVIEW_PERSON_NAME,
		r.CHANNEL_CODE,
		r.CHANNEL_NAME,
		r.INTEN_LEVEL_CODE,
		r.INTEN_LEVEL_NAME,
		r.INTEN_BRAND_CODE,
		r.INTEN_BRAND_NAME,
		r.INTEN_SERIES_CODE,
		r.INTEN_SERIES_NAME,
		r.INTEN_CAR_TYPE_CODE,
		r.INTEN_CAR_TYPE_NAME,
		r.INFO_CHAN_M_CODE,
		r.INFO_CHAN_M_NAME,
		r.INFO_CHAN_D_CODE,
		r.INFO_CHAN_D_NAME,
		r.COLUMN1,
		r.COLUMN2,
		r.COLUMN3,
		r.COLUMN4,
		r.COLUMN5,
		r.COLUMN6,
		r.COLUMN7,
		r.COLUMN8,
		r.COLUMN9,
		r.COLUMN10,
		r.COLUMN11,
		r.COLUMN12,
		r.COLUMN13,
		r.COLUMN14,
		r.COLUMN15,
		r.COLUMN16,
		r.COLUMN17,
		r.COLUMN18,
		r.COLUMN19,
		r.COLUMN20,
		r.BIG_COLUMN1,
		r.BIG_COLUMN2,
		r.BIG_COLUMN3,
		r.BIG_COLUMN4,
		r.BIG_COLUMN5,
		r.EXTENDS_JSON,
		A.UPDATE_CONTROL_ID
		FROM
		t_sac_review_audit a
		left JOIN t_sac_clue_info_dlr r ON a.review_id = r.review_id
		WHERE
		a.is_enable = '1'
		and r.COLUMN20 is NULL
		and r.STATUS_CODE  = '10'
		<choose>
			<when test="param.orgId !=null and param.orgId !=''">
				and r.DLR_CODE in (
				SELECT
				DLR_CODE
				FROM
				mp.t_usc_mdm_org_dlr
				WHERE
				COMPANY_ID=#{param.orgId}
				)
			</when>
			<otherwise>
				and r.DLR_CODE=#{param.orgCode}
			</otherwise>
		</choose>
		<if test="param.identification !=null and param.identification !=''">
			and	r.REVIEW_PERSON_ID=#{param.personId}
		</if>
		<if test="param.startTime != null and ''!= param.startTime">
			<![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
		</if>
		<if test="param.endTime != null and ''!= param.endTime">
			<![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
			AND r.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.shStatus != null and ''!= param.shStatus ">
			AND A.sh_status IN
			<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">
			and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and r.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		ORDER BY a.created_date DESC
		<if test="param.pageNo != null and param.pageSize != null and param.pageSize >= 0 and param.pageSize >= 0">
			LIMIT #{param.pageNo}, #{param.pageSize}
		</if>
	</select>
	<select id="queryListCount" resultType="java.lang.Long">
		SELECT
		COUNT( 1 )
		FROM
		(
		SELECT
		r.DLR_CODE,
		r.PHONE
		FROM
		t_sac_review_audit a
		left JOIN t_sac_clue_info_dlr r ON a.review_id = r.review_id
		WHERE
		a.is_enable = '1'
		and r.COLUMN20 is NULL
		and r.STATUS_CODE  = '10'
		<choose>
			<when test="param.orgId !=null and param.orgId !=''">
				and r.DLR_CODE in (
				SELECT
				DLR_CODE
				FROM
				mp.t_usc_mdm_org_dlr
				WHERE
				COMPANY_ID=#{param.orgId}
				)
			</when>
			<otherwise>
				and r.DLR_CODE=#{param.orgCode}
			</otherwise>
		</choose>
		<if test="param.identification !=null and param.identification !=''">
			and	r.REVIEW_PERSON_ID=#{param.personId}
		</if>
		<if test="param.startTime != null and ''!= param.startTime">
			<![CDATA[ AND A.CREATED_DATE >= #{param.startTime} ]]>
		</if>
		<if test="param.endTime != null and ''!= param.endTime">
			<![CDATA[ AND A.CREATED_DATE < date_add(#{param.endTime}, INTERVAL 1 day) ]]>
		</if>
		<if test="param.reviewPersonId != null and ''!= param.reviewPersonId ">
			AND r.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonId.split(',')" index="index" item="item" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.shStatus != null and ''!= param.shStatus ">
			AND A.sh_status IN
			<foreach collection="param.shStatus.split(',')" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.searchCondition !=null and param.searchCondition != ''">
			and (INSTR(r.PHONE,#{param.searchCondition})>0 or INSTR(r.CUST_NAME,#{param.searchCondition})>0)
		</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">
			and r.INFO_CHAN_M_CODE = #{param.infoChanMCode}
		</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and r.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		 ) total
	</select>

    <update id="batchUpdateReviewAudit">
		<foreach collection="list" item="item" index="index"  separator=";" >
			update t_sac_review_audit  set
			MODIFIER=#{item.modifier},
			MODIFY_NAME=#{item.modifyName},
			LAST_UPDATED_DATE=sysdate(),
			UPDATE_CONTROL_ID=uuid()
			<if test = 'item.reviewId!=null'> ,REVIEW_ID = #{item.reviewId}</if>
			<if test = 'item.orgCode!=null'> ,ORG_CODE = #{item.orgCode}</if>
			<if test = 'item.orgName!=null'> ,ORG_NAME = #{item.orgName}</if>
			<if test = 'item.billCode!=null'> ,BILL_CODE = #{item.billCode}</if>
			<if test = 'item.applyTypeCode!=null'> ,APPLY_TYPE_CODE = #{item.applyTypeCode}</if>
			<if test = 'item.applyTypeName!=null'> ,APPLY_TYPE_NAME = #{item.applyTypeName}</if>
			<if test = 'item.applyDesc!=null'> ,APPLY_DESC = #{item.applyDesc}</if>
			<if test = 'item.shPersonId!=null'> ,SH_PERSON_ID = #{item.shPersonId}</if>
			<if test = 'item.shPersonName!=null'> ,SH_PERSON_NAME = #{item.shPersonName}</if>
			<if test = 'item.shDesc!=null'> ,SH_DESC = #{item.shDesc}</if>
			<if test = 'item.shTime!=null'> ,SH_TIME = #{item.shTime}</if>
			<if test = 'item.shStatus!=null'> ,SH_STATUS = #{item.shStatus}</if>
			<if test = 'item.shStatusName!=null'> ,SH_STATUS_NAME = #{item.shStatusName}</if>
			<if test = 'item.extendsJson!=null'> ,EXTENDS_JSON = #{item.extendsJson}</if>
			<if test = 'item.oemId!=null'> ,OEM_ID = #{item.oemId}</if>
			<if test = 'item.groupId!=null'> ,GROUP_ID = #{item.groupId}</if>
			<if test = 'item.isEnable!=null'> ,IS_ENABLE = #{item.isEnable}</if>
			where AUDIT_ID = #{item.auditId}
			</foreach>
	</update>

    <insert id="batchSaveResume">
		insert into t_sac_onecust_resume(RESUME_ID,/*履历ID*/
		CUST_ID,/*CUST_ID*/
		SMART_ID,/*SMARTID*/
		CLUE_LEVEL_CODE,/*意向级别(L0-L5)*/
		DLR_CODE_OWNER,/*数据所属门店编码*/
		DLR_NAME_OWNER,/*数据所属门店名称*/
		RESUME_PERSON_CODE,/*跟进人员编码*/
		RESUME_PERSON_NAME,/*跟进人员名称*/
		SENCE_CODE,/*场景编码 值列表:ADP_CLUE_001*/
		SENCE_NAME,/*场景名称 值列表:ADP_CLUE_001*/
		RESUME_DESC,/*客户履历内容*/
		REMARK,/*客户履历备注*/
		BUSS_TIME,/*作业时间*/
		BUSS_START_TIME,/*作业开始时间*/
		BUSS_END_TIME,/*作业结束时间*/
		RELATION_BILL_ID,/*关联单据ID*/
		OEM_ID,/*厂商标识ID*/
		GROUP_ID,/*集团标识ID*/
		OEM_CODE,/*厂商标识*/
		GROUP_CODE,/*集团标识*/
		CREATOR,/*创建人ID*/
		CREATED_NAME,/*创建人*/
		CREATED_DATE,/*创建日期*/
		MODIFIER,/*修改人ID*/
		MODIFY_NAME,/*修改人*/
		LAST_UPDATED_DATE,/*最后更新日期*/
		IS_ENABLE,/*是否可用*/
		SDP_USER_ID,/*SDP用户ID*/
		SDP_ORG_ID,/*SDP组织ID*/
		UPDATE_CONTROL_ID,/*并发控制ID*/
		ARRIVAL_NUM,
		ARRIVAL_TIME,
		ARRIVAL_END_TIME,
		ARRIVAL_METHOD,
		COLUMN5,
		COLUMN6,
		SALE_ORDER_CODE
		) VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			( #{item.resumeId},
			#{item.custId},
			#{item.smartId},
			#{item.clueLevelCode},
			#{item.dlrCodeOwner},
			#{item.dlrNameOwner},
			#{item.resumePersonCode},
			#{item.resumePersonName},
			#{item.senceCode},
			#{item.senceName},
			#{item.resumeDesc},
			#{item.remark},
			#{item.bussTime},
			#{item.bussStartTime},
			#{item.bussEndTime},
			#{item.relationBillId},
			#{item.oemId},
			#{item.groupId},
			#{item.oemCode},
			#{item.groupCode},
			#{item.creator},
			#{item.createdName},
			#{item.createdDate},
			#{item.modifier},
			#{item.modifyName},
			#{item.lastUpdatedDate},
			#{item.isEnable},
			#{item.sdpUserId},
			#{item.sdpOrgId},
			#{item.updateControlId},
			#{item.arrivalNum},
			#{item.arrivalTime},
			#{item.arrivalEndTime},
			#{item.arrivalMethod},
			#{item.column5},
			#{item.column6},
			#{item.saleOrderCode}
			)
		</foreach>
    </insert>

    <insert id="batchSaveMsgRecord">
		INSERT INTO `t_sac_clue_msg_record` (`MESSAGE_ID`,
		`IS_READ`,
		`DLR_CODE`,
		`PHONE`,
		`MESSAGE_TYPE`,
		`BUSI_KEYVALUE`,
		`RECEIVE_EMP_ID`,
		`MESSAGE_CONTENT`,
		`RELATION_BILL_ID`,
		`EXTEND_JSON`,
		`COLUMN1`,
		`OEM_ID`,
		`GROUP_ID`,
		`OEM_CODE`,
		`GROUP_CODE`,
		`CREATOR`,
		`CREATED_NAME`,
		`CREATED_DATE`,
		`MODIFIER`,
		`MODIFY_NAME`,
		`LAST_UPDATED_DATE`,
		`IS_ENABLE`,
		`SDP_USER_ID`,
		`SDP_ORG_ID`,
		`UPDATE_CONTROL_ID`)
		VALUES
		<foreach collection="list" index="index" item="item" separator=",">
			(#{item.messageId},
			'0',
			#{item.dlrCode},
			#{item.phone},
			#{item.messageType},
			'1',
			#{item.receiveEmpId},
			#{item.messageContent},
			#{item.relationBillId},
			#{item.extendJson},
			#{item.column1},
			#{item.oemId},
			#{item.groupId},
			#{item.oemCode},
			#{item.groupCode},
			#{item.creator},
			#{item.createdName},
			now(),
			#{item.modifier},
			#{item.modifyName},
			now(),
			'1',
			'1',
			'1',
			uuid())
		</foreach>
	</insert>
</mapper>
