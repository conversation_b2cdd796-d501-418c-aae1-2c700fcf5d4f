<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.csc.clue.idal.mapper.SacClueAllocatingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.mp.csc.clue.entities.SacClueAllocatingRecord">
        <id column="RECORD_ID" property="recordId" />
        <result column="OLD_DLR_ID" property="oldDlrId" />
        <result column="OLD_DLR_CODE" property="oldDlrCode" />
        <result column="OLD_DLR_SHORT_NAME" property="oldDlrShortName" />
        <result column="OLD_SALESPERSON_ID" property="oldSalespersonId" />
        <result column="OLD_SALESPERSON_NAME" property="oldSalespersonName" />
        <result column="NEW_DLR_ID" property="newDlrId" />
        <result column="NEW_DLR_CODE" property="newDlrCode" />
        <result column="NEW_DLR_SHORT_NAME" property="newDlrShortName" />
        <result column="NEW_SALESPERSON_ID" property="newSalespersonId" />
        <result column="NEW_SALESPERSON_NAME" property="newSalespersonName" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="EXTENDS_JSON" property="extendsJson" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, SERVER_ORDER,CUST_ID,CUST_NAME,CUST_PHONE,OLD_DLR_ID, OLD_DLR_CODE, OLD_DLR_SHORT_NAME, OLD_SALESPERSON_ID, OLD_SALESPERSON_NAME, NEW_DLR_ID, NEW_DLR_CODE, NEW_DLR_SHORT_NAME, NEW_SALESPERSON_ID, NEW_SALESPERSON_NAME, COLUMN1 , COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, EXTENDS_JSON, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.recordId !=null and param.recordId !=''">and RECORD_ID=#{param.recordId}</if>
        <if test="param.isAutomatic !=null and param.isAutomatic !=''">and COLUMN1=#{param.isAutomatic}</if>
        <if test="param.serverOrder !=null and param.serverOrder !=''">and SERVER_ORDER=#{param.serverOrder}</if>
        <if test="param.custId !=null and param.custId !=''">and CUST_ID=#{param.custId}</if>
        <if test="param.custName !=null and param.custName !=''">and CUST_NAME=#{param.custName}</if>
        <if test="param.custPhone !=null and param.custPhone !=''">and CUST_PHONE=#{param.custPhone}</if>
    	<if test="param.oldDlrId !=null and param.oldDlrId !=''">and OLD_DLR_ID=#{param.oldDlrId}</if>
    	<if test="param.oldDlrCode !=null and param.oldDlrCode !=''">and OLD_DLR_CODE=#{param.oldDlrCode}</if>
    	<if test="param.oldDlrShortName !=null and param.oldDlrShortName !=''">and OLD_DLR_SHORT_NAME=#{param.oldDlrShortName}</if>
    	<if test="param.oldSalespersonId !=null and param.oldSalespersonId !=''">and OLD_SALESPERSON_ID=#{param.oldSalespersonId}</if>
    	<if test="param.oldSalespersonName !=null and param.oldSalespersonName !=''">and OLD_SALESPERSON_NAME=#{param.oldSalespersonName}</if>
    	<if test="param.newDlrId !=null and param.newDlrId !=''">and NEW_DLR_ID=#{param.newDlrId}</if>
    	<if test="param.newDlrCode !=null and param.newDlrCode !=''">and NEW_DLR_CODE=#{param.newDlrCode}</if>
    	<if test="param.newDlrShortName !=null and param.newDlrShortName !=''">and NEW_DLR_SHORT_NAME=#{param.newDlrShortName}</if>
    	<if test="param.newSalespersonId !=null and param.newSalespersonId !=''">and NEW_SALESPERSON_ID=#{param.newSalespersonId}</if>
    	<if test="param.newSalespersonName !=null and param.newSalespersonName !=''">and NEW_SALESPERSON_NAME=#{param.newSalespersonName}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.column6 !=null and param.column6 !=''">and COLUMN6=#{param.column6}</if>
    	<if test="param.column7 !=null and param.column7 !=''">and COLUMN7=#{param.column7}</if>
    	<if test="param.column8 !=null and param.column8 !=''">and COLUMN8=#{param.column8}</if>
    	<if test="param.column9 !=null and param.column9 !=''">and COLUMN9=#{param.column9}</if>
    	<if test="param.column10 !=null and param.column10 !=''">and COLUMN10=#{param.column10}</if>
    	<if test="param.extendsJson !=null and param.extendsJson !=''">and EXTENDS_JSON=#{param.extendsJson}</if>
    	<if test="param.oemId !=null and param.oemId !=''">and OEM_ID=#{param.oemId}</if>
    	<if test="param.groupId !=null and param.groupId !=''">and GROUP_ID=#{param.groupId}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null  ">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null ">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>


	<select id="countSacClueAllocatingRecord" resultType="java.lang.Long">
		select
		count(*)
		from t_sac_clue_allocating_record
		<if test="(param.beginDate !=null and '' !=param.beginDate) || (param.endDate !=null and '' !=param.endDate)">
			force index(idx_created_date)
		</if>
		where 1=1
		<include refid="where_condition"></include>
		<if test="param.createdNamePaste !=null and param.createdNamePaste !=''">and INSTR(CREATED_NAME,#{param.createdNamePaste})>0</if>
		<if test="param.beginDate !=null and '' !=param.beginDate">
			AND CREATED_DATE &gt;= #{param.beginDate}
		</if>
		<if test="param.endDate !=null and '' !=param.endDate">
			AND CREATED_DATE &lt;= #{param.endDate}
		</if>
		<if test="param.sysCode!=null and param.sysCode=='1'.toString">
			AND INSTR(OLD_DLR_CODE,'HOST') <![CDATA[ < ]]> 1
		</if>
		<if test="param.sysCode!=null and param.sysCode=='0'.toString">
			AND INSTR(OLD_DLR_CODE,'HOST')
		</if>
	</select>
 	<!-- 线索分配记录表 信息查询 -->
	<select id="querySacClueAllocatingRecord" resultType="map">
		select
		RECORD_ID, SERVER_ORDER,CUST_ID,CUST_NAME,CUST_PHONE,OLD_DLR_ID, OLD_DLR_CODE, OLD_DLR_SHORT_NAME, OLD_SALESPERSON_ID, OLD_SALESPERSON_NAME, NEW_DLR_ID, NEW_DLR_CODE, NEW_DLR_SHORT_NAME, NEW_SALESPERSON_ID, NEW_SALESPERSON_NAME, CASE COLUMN1 WHEN '1' THEN '自动分配' WHEN '0' THEN '手动分配' END  as isAutomatic, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, EXTENDS_JSON, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
		from t_sac_clue_allocating_record
		<if test="(param.beginDate !=null and '' !=param.beginDate) || (param.endDate !=null and '' !=param.endDate)">
			force index(idx_created_date)
		</if>
	    where 1=1
	    <include refid="where_condition"></include>
		<if test="param.createdNamePaste !=null and param.createdNamePaste !=''">and INSTR(CREATED_NAME,#{param.createdNamePaste})>0</if>
		<if test="param.beginDate !=null and '' !=param.beginDate">
			AND CREATED_DATE &gt;= #{param.beginDate}
		</if>
		<if test="param.endDate !=null and '' !=param.endDate">
			AND CREATED_DATE &lt;= #{param.endDate}
		</if>
		<if test="param.sysCode!=null and param.sysCode=='1'.toString">
			AND INSTR(OLD_DLR_CODE,'HOST') <![CDATA[ < ]]> 1
		</if>
		<if test="param.sysCode!=null and param.sysCode=='0'.toString">
			AND INSTR(OLD_DLR_CODE,'HOST')
		</if>
		ORDER BY CREATED_DATE DESC
	</select>
	
	<!-- 线索分配记录表 信息删除（物理删除） -->
	<delete id="deleteSacClueAllocatingRecord">
		DELETE 
		FROM
			t_sac_clue_allocating_record
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 线索分配记录表 信息新增 -->
	<insert id="createSacClueAllocatingRecord">
		insert into t_sac_clue_allocating_record(<include refid="Base_Column_List"></include>)
		values(
        	uuid(),
        	#{param.serverOrder},
        	#{param.custId},
        	#{param.custName},
        	#{param.custPhone},
			#{param.oldDlrId},
			#{param.oldDlrCode},
			#{param.oldDlrShortName},
			#{param.oldSalespersonId},
			#{param.oldSalespersonName},
			#{param.newDlrId},
			#{param.newDlrCode},
			#{param.newDlrShortName},
			#{param.newSalespersonId},
			#{param.newSalespersonName},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
			#{param.extendsJson},
			#{param.oemId},
			#{param.groupId},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			uuid()
		)
	</insert>
<!--	// 改了-->
	<insert id="createSacClueAllocatingRecordBatch">
		insert into t_sac_clue_allocating_record(<include refid="Base_Column_List"></include>)
		values
		<foreach collection="list" item="param" index="index" separator=",">
			(uuid(),
			#{param.serverOrder},
			#{param.custId},
			#{param.custName},
			#{param.custPhone},
			#{param.oldDlrId},
			#{param.oldDlrCode},
			#{param.oldDlrShortName},
			#{param.oldSalespersonId},
			#{param.oldSalespersonName},
			#{param.newDlrId},
			#{param.newDlrCode},
			#{param.newDlrShortName},
			#{param.newSalespersonId},
			#{param.newSalespersonName},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
			#{param.extendsJson},
			#{param.oemId},
			#{param.groupId},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			uuid())
		</foreach>
	</insert>

	<insert id="batchCreateSacClueAllocatingRecord">
		insert into t_sac_clue_allocating_record(<include refid="Base_Column_List"></include>)
		values
		<foreach collection="mapList" item="param" index="index" separator=",">
			( uuid(),
			#{param.serverOrder},
			#{param.custId},
			#{param.custName},
			#{param.custPhone},
			#{param.oldDlrId},
			#{param.oldDlrCode},
			#{param.oldDlrShortName},
			#{param.oldSalespersonId},
			#{param.oldSalespersonName},
			#{param.newDlrId},
			#{param.newDlrCode},
			#{param.newDlrShortName},
			#{param.newSalespersonId},
			#{param.newSalespersonName},
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.column6},
			#{param.column7},
			#{param.column8},
			#{param.column9},
			#{param.column10},
			#{param.extendsJson},
			#{param.oemId},
			#{param.groupId},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			uuid() )
		</foreach>
	</insert>
	<!-- 线索分配记录表 信息更新 -->
	<update id="updateSacClueAllocatingRecord">
		update t_sac_clue_allocating_record  set 
			<!-- 更新列表 -->
			<if test="param.oldDlrId !=null and param.oldDlrId !=''">OLD_DLR_ID=#{param.oldDlrId},</if>
			<if test="param.oldDlrCode !=null and param.oldDlrCode !=''">OLD_DLR_CODE=#{param.oldDlrCode},</if>
			<if test="param.oldDlrShortName !=null and param.oldDlrShortName !=''">OLD_DLR_SHORT_NAME=#{param.oldDlrShortName},</if>
			<if test="param.oldSalespersonId !=null and param.oldSalespersonId !=''">OLD_SALESPERSON_ID=#{param.oldSalespersonId},</if>
			<if test="param.oldSalespersonName !=null and param.oldSalespersonName !=''">OLD_SALESPERSON_NAME=#{param.oldSalespersonName},</if>
			<if test="param.newDlrId !=null and param.newDlrId !=''">NEW_DLR_ID=#{param.newDlrId},</if>
			<if test="param.newDlrCode !=null and param.newDlrCode !=''">NEW_DLR_CODE=#{param.newDlrCode},</if>
			<if test="param.newDlrShortName !=null and param.newDlrShortName !=''">NEW_DLR_SHORT_NAME=#{param.newDlrShortName},</if>
			<if test="param.newSalespersonId !=null and param.newSalespersonId !=''">NEW_SALESPERSON_ID=#{param.newSalespersonId},</if>
			<if test="param.newSalespersonName !=null and param.newSalespersonName !=''">NEW_SALESPERSON_NAME=#{param.newSalespersonName},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.column6 !=null and param.column6 !=''">COLUMN6=#{param.column6},</if>
			<if test="param.column7 !=null and param.column7 !=''">COLUMN7=#{param.column7},</if>
			<if test="param.column8 !=null and param.column8 !=''">COLUMN8=#{param.column8},</if>
			<if test="param.column9 !=null and param.column9 !=''">COLUMN9=#{param.column9},</if>
			<if test="param.column10 !=null and param.column10 !=''">COLUMN10=#{param.column10},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''">EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''">OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''">GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null and param.createdDate !=''">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">UPDATE_CONTROL_ID=#{param.updateControlId},</if>
			<!-- 结束无逗号 -->
			RECORD_ID=RECORD_ID
			where 1=1 
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</update>
	<select id="selectReviewByReviewId" resultType="java.util.Map">
		select  REVIEW_ID,REVIEW_PERSON_ID,REVIEW_PERSON_NAME from t_sac_review where REVIEW_ID =#{param.reviewId}
	</select>
</mapper>