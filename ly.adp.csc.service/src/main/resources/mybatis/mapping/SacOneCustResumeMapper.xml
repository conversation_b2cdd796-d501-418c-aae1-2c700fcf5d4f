<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacOneCustResumeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacOneCustResume">
        <id column="RESUME_ID" property="resumeId"/>
        <result column="SMART_ID" property="smartId"/>
        <result column="CLUE_CODE" property="clueCode"/>
        <result column="CLUE_LEVEL_CODE" property="clueLevelCode"/>
        <result column="DLR_CODE_OWNER" property="dlrCodeOwner"/>
        <result column="DLR_NAME_OWNER" property="dlrNameOwner"/>
        <result column="RESUME_PERSON_CODE" property="resumePersonCode"/>
        <result column="RESUME_PERSON_NAME" property="resumePersonName"/>
        <result column="SENCE_CODE" property="senceCode"/>
        <result column="SENCE_NAME" property="senceName"/>
        <result column="RESUME_DESC" property="resumeDesc"/>
        <result column="REMARK" property="remark"/>
        <result column="BUSS_TIME" property="bussTime"/>
        <result column="BUSS_START_TIME" property="bussStartTime"/>
        <result column="BUSS_END_TIME" property="bussEndTime"/>
        <result column="COLUMN1" property="column1"/>
        <result column="COLUMN2" property="column2"/>
        <result column="COLUMN3" property="column3"/>
        <result column="COLUMN4" property="column4"/>
        <result column="COLUMN5" property="column5"/>
        <result column="COLUMN6" property="column6"/>
        <result column="COLUMN7" property="column7"/>
        <result column="COLUMN8" property="column8"/>
        <result column="COLUMN9" property="column9"/>
        <result column="COLUMN10" property="column10"/>
        <result column="_MYCAT_OP_TIME" property="mycatOpTime"/>
        <result column="OEM_ID" property="oemId"/>
        <result column="GROUP_ID" property="groupId"/>
        <result column="OEM_CODE" property="oemCode"/>
        <result column="GROUP_CODE" property="groupCode"/>
        <result column="CREATOR" property="creator"/>
        <result column="CREATED_NAME" property="createdName"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="MODIFIER" property="modifier"/>
        <result column="MODIFY_NAME" property="modifyName"/>
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate"/>
        <result column="IS_ENABLE" property="isEnable"/>
        <result column="SDP_USER_ID" property="sdpUserId"/>
        <result column="SDP_ORG_ID" property="sdpOrgId"/>
        <result column="UPDATE_CONTROL_ID" property="updateControlId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RESUME_ID
        , SMART_ID, CLUE_CODE, CLUE_LEVEL_CODE, DLR_CODE_OWNER, DLR_NAME_OWNER, RESUME_PERSON_CODE, RESUME_PERSON_NAME, SENCE_CODE, SENCE_NAME, RESUME_DESC, REMARK, BUSS_TIME, BUSS_START_TIME, BUSS_END_TIME, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>

    <select id="queryPhoneFast" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from t_sac_onecust_resume
        where CUST_ID = #{param.custId}
          and SENCE_CODE in (1, 14, 17)
    </select>
    <select id="sacQueryResumeFindAllbyphone" resultType="map">
        SELECT
        T.CUST_ID,
        c.SMART_ID, /*SMART_ID*/
        T.RESUME_ID, /*履历ID*/
        T.CLUE_LEVEL_CODE, /*意向级别(L0-L5)*/
        T.DLR_CODE_OWNER, /*数据所属门店编码*/
        T.DLR_NAME_OWNER, /*数据所属门店名称*/
        T.RESUME_PERSON_CODE,/*跟进人员编码*/
        T.RESUME_PERSON_NAME,/*跟进人员名称*/
        T.SENCE_CODE, /*场景编码*/
        T.SENCE_NAME, /*场景名称*/
        T.RESUME_DESC, /*客户履历内容*/
        T.REMARK, /*客户履历备注*/
        T.BUSS_TIME, /*作业时间*/
        T.BUSS_START_TIME, /*作业开始时间*/
        T.BUSS_END_TIME, /*作业结束时间*/
        T.UPDATE_CONTROL_ID, /*并发控制ID*/
        T.RELATION_BILL_ID, /*关联单据ID*/
        T.sale_order_code saleOrderCode
        FROM
        t_sac_onecust_resume T
        left join t_sac_onecust_info c on T.CUST_ID=c.CUST_ID
        WHERE 1 = 1
        AND T.IS_ENABLE = '1'
        <if test="param.saleOrderCode != null and '' != param.saleOrderCode">
            and T.sale_order_code=#{param.saleOrderCode}
        </if>
        <if test="param.phone != null and '' != param.phone">
            and instr(c.PHONE,#{param.phone})&gt;0
        </if>
        <if test="param.custId != null and '' != param.custId">
            AND T.CUST_ID = #{param.custId}
        </if>
        <if test="param.smartId != null and '' != param.smartId">
            AND c.SMART_ID = #{param.smartId}
        </if>
        <if test="param.senceCode != null and '' != param.senceCode">
            AND T.SENCE_CODE = #{param.senceCode}
        </if>
        <if test="param.dlrCodeOwner != null and '' != param.dlrCodeOwner">
            AND T.DLR_CODE_OWNER = #{param.dlrCodeOwner}
        </if>
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and T.CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and T.BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and T.BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>
        <if test="param.resumeDescPaste !=null and param.resumeDescPaste !=''">
            and INSTR(T.RESUME_DESC,#{param.resumeDescPaste})>0
        </if>
        <if test="param.senceCodeIn !=null and param.senceCodeIn !=''">
            AND T.SENCE_CODE IN
            <foreach collection="param.senceCodeIn.split(',')" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY T.CREATED_DATE DESC
    </select>


    <!-- 客户履历查询 -->
    <select id="sacQueryResumeFindAll" resultType="map">
        SELECT
        T.CUST_ID,
        T.SMART_ID, /*SMART_ID*/
        T.RESUME_ID, /*履历ID*/
        T.COLUMN5 CLUE_LEVEL_CODE, /*热度*/
        T.DLR_CODE_OWNER, /*数据所属门店编码*/
        T.DLR_NAME_OWNER, /*数据所属门店名称*/
        T.RESUME_PERSON_CODE,/*跟进人员编码*/
        T.RESUME_PERSON_NAME,/*跟进人员名称*/
        T.SENCE_CODE, /*场景编码*/
        T.SENCE_NAME, /*场景名称*/
        T.RESUME_DESC, /*客户履历内容*/
        T.REMARK, /*客户履历备注*/
        T.BUSS_TIME, /*作业时间*/
        T.BUSS_START_TIME, /*作业开始时间*/
        T.BUSS_END_TIME, /*作业结束时间*/
        T.UPDATE_CONTROL_ID, /*并发控制ID*/
        T.RELATION_BILL_ID, /*关联单据ID*/
        T.ARRIVAL_NUM,
        DATE_FORMAT(T.ARRIVAL_TIME,'%Y-%m-%d %H:%i') ARRIVAL_TIME,
        DATE_FORMAT(T.ARRIVAL_END_TIME,'%Y-%m-%d %H:%i') ARRIVAL_END_TIME,
        b.LOOKUP_VALUE_NAME AS ARRIVAL_METHOD
        FROM
        t_sac_onecust_resume T
        LEFT JOIN mp.t_prc_mds_lookup_value b on T.ARRIVAL_METHOD=b.LOOKUP_VALUE_CODE and b.LOOKUP_TYPE_CODE='VE1113'
        WHERE
        T.IS_ENABLE = '1'
        <if test="param.custId != null and '' != param.custId">
            AND T.CUST_ID = #{param.custId}
        </if>
        <if test="param.smartId != null and '' != param.smartId">
            AND T.SMART_ID = #{param.smartId}
        </if>
        <if test="param.senceCode != null and '' != param.senceCode">
            AND T.SENCE_CODE = #{param.senceCode}
        </if>
        <if test="param.dlrCodeOwner != null and '' != param.dlrCodeOwner">
            AND T.DLR_CODE_OWNER = #{param.dlrCodeOwner}
        </if>
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and T.CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and T.BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and T.BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>
        <if test="param.resumeDescPaste !=null and param.resumeDescPaste !=''">
            and INSTR(T.RESUME_DESC,#{param.resumeDescPaste})>0
        </if>
        <if test="param.senceCodeIn !=null and param.senceCodeIn !=''">
            AND T.SENCE_CODE IN
            <foreach collection="param.senceCodeIn.split(',')" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY T.CREATED_DATE DESC
    </select>

    <!-- 客户履历查询 -->
    <select id="sacQueryResumeFindOne" resultType="map">
        SELECT
        T.CUST_ID,
        T.SMART_ID, /*SMART_ID*/
        T.RESUME_ID, /*履历ID*/
        T.CLUE_LEVEL_CODE, /*意向级别(L0-L5)*/
        T.DLR_CODE_OWNER, /*数据所属门店编码*/
        T.DLR_NAME_OWNER, /*数据所属门店名称*/
        T.RESUME_PERSON_CODE,/*跟进人员编码*/
        T.RESUME_PERSON_NAME,/*跟进人员名称*/
        T.SENCE_CODE, /*场景编码*/
        T.SENCE_NAME, /*场景名称*/
        T.RESUME_DESC, /*客户履历内容*/
        T.REMARK, /*客户履历备注*/
        T.BUSS_TIME, /*作业时间*/
        T.BUSS_START_TIME, /*作业开始时间*/
        T.BUSS_END_TIME, /*作业结束时间*/
        T.UPDATE_CONTROL_ID, /*并发控制ID*/
        T.RELATION_BILL_ID /*关联单据ID*/
        FROM
        t_sac_onecust_resume T
        WHERE 1 = 1
        AND T.IS_ENABLE = '1'
        <if test="param.custId != null and '' != param.custId">
            AND T.CUST_ID = #{param.custId}
        </if>
        <if test="param.smartId != null and '' != param.smartId">
            AND T.SMART_ID = #{param.smartId}
        </if>
        <if test="param.senceCode != null and '' != param.senceCode">
            AND T.SENCE_CODE = #{param.senceCode}
        </if>
        <if test="param.dlrCodeOwner != null and '' != param.dlrCodeOwner">
            AND T.DLR_CODE_OWNER = #{param.dlrCodeOwner}
        </if>
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and T.CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and T.CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and T.BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and T.BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>
        <if test="param.resumeDescPaste !=null and param.resumeDescPaste !=''">
            and INSTR(T.RESUME_DESC,#{param.resumeDescPaste})>0
        </if>
        <if test="param.senceCodeIn !=null and param.senceCodeIn !=''">
            AND T.SENCE_CODE IN
            <foreach collection="param.senceCodeIn.split(',')" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY T.CREATED_DATE DESC limit 1
    </select>

    <!-- 客户履历新增 -->
    <insert id="createOnecustResume">
        insert into t_sac_onecust_resume(RESUME_ID,/*履历ID*/
                                         CUST_ID,/*CUST_ID*/
                                         SMART_ID,/*SMARTID*/
                                         CLUE_LEVEL_CODE,/*意向级别(L0-L5)*/
                                         DLR_CODE_OWNER,/*数据所属门店编码*/
                                         DLR_NAME_OWNER,/*数据所属门店名称*/
                                         RESUME_PERSON_CODE,/*跟进人员编码*/
                                         RESUME_PERSON_NAME,/*跟进人员名称*/
                                         SENCE_CODE,/*场景编码 值列表:ADP_CLUE_001*/
                                         SENCE_NAME,/*场景名称 值列表:ADP_CLUE_001*/
                                         RESUME_DESC,/*客户履历内容*/
                                         REMARK,/*客户履历备注*/
                                         BUSS_TIME,/*作业时间*/
                                         BUSS_START_TIME,/*作业开始时间*/
                                         BUSS_END_TIME,/*作业结束时间*/
                                         RELATION_BILL_ID,/*关联单据ID*/
                                         OEM_ID,/*厂商标识ID*/
                                         GROUP_ID,/*集团标识ID*/
                                         OEM_CODE,/*厂商标识*/
                                         GROUP_CODE,/*集团标识*/
                                         CREATOR,/*创建人ID*/
                                         CREATED_NAME,/*创建人*/
                                         CREATED_DATE,/*创建日期*/
                                         MODIFIER,/*修改人ID*/
                                         MODIFY_NAME,/*修改人*/
                                         LAST_UPDATED_DATE,/*最后更新日期*/
                                         IS_ENABLE,/*是否可用*/
                                         SDP_USER_ID,/*SDP用户ID*/
                                         SDP_ORG_ID,/*SDP组织ID*/
                                         UPDATE_CONTROL_ID,/*并发控制ID*/
                                         ARRIVAL_NUM,
                                         ARRIVAL_TIME,
                                         ARRIVAL_END_TIME,
                                         ARRIVAL_METHOD,
                                         COLUMN1,
                                         COLUMN5,
                                         COLUMN6,
        SALE_ORDER_CODE
        ) value
            ( #{param.resumeId},
            #{param.custId},
            #{param.smartId},
            #{param.clueLevelCode},
            #{param.dlrCodeOwner},
            #{param.dlrNameOwner},
            #{param.resumePersonCode},
            #{param.resumePersonName},
            #{param.senceCode},
            #{param.senceName},
            #{param.resumeDesc},
            #{param.remark},
            #{param.bussTime},
            #{param.bussStartTime},
            #{param.bussEndTime},
            #{param.relationBillId},
            #{param.oemId},
            #{param.groupId},
            #{param.oemCode},
            #{param.groupCode},
            #{param.creator},
            #{param.createdName},
            #{param.createdDate},
            #{param.modifier},
            #{param.modifyName},
            #{param.lastUpdatedDate},
            #{param.isEnable},
            #{param.sdpUserId},
            #{param.sdpOrgId},
            #{param.updateControlId},
            #{param.arrivalNum},
            #{param.arrivalTime},
            #{param.arrivalEndTime},
            #{param.arrivalMethod},
            #{param.clueLevel},
            #{param.businessHeatCode},
            #{param.businessHeatName},
            #{param.saleOrderCode}
            )
    </insert>

    <select id="findtestDriveSheet" resultType="java.lang.String">
        select SMALL_CAR_TYPE_CODE
        from t_sac_test_drive_sheet
        where TEST_DRIVE_SHEET_ID = #{testDriveSheetId}
    </select>

    <select id="queryVirtualRecord" resultType="java.util.Map">
        select ifnull(a.call_Time, 0) callTime,
               a.CALL_START_TIME      CallStartTime,
               a.CALL_END_TIME        callEndTime,
               b.LOOKUP_VALUE_NAME    statusName,
               a.call_id
        from mp.t_usc_mdm_virtual_record a
                 left join mp.t_prc_mds_lookup_value b
                           on a.status = b.LOOKUP_VALUE_CODE and b.LOOKUP_TYPE_CODE = 'VE1112'
        where a.CUST_ID = #{custId}
          and a.EMP_CODE = #{resumePersonCode}
          and date_format(a.CALL_START_TIME, '%Y-%m-%d') = date_format(#{bussTime}, '%Y-%m-%d')
        order by a.CALL_START_TIME desc
    </select>

    <select id="selectProductNameDlr" resultType="java.util.Map">

        select emp_name,emp_id,user_id
        from mp.t_usc_mdm_org_employee
        where ORG_TYPE = '1'
        <choose>
            <when test="param.type != null and '' != param.type">
                and (STATION_ID in ('smart_bm_0007', 'smart_bm_0018','smart_bm_0061','smart_bm_0064') or
                COLUMN2 in ('smart_bm_0007', 'smart_bm_0018','smart_bm_0061','smart_bm_0064'))
            </when>
            <otherwise>
                and (STATION_ID in ('smart_bm_0007', 'smart_bm_0018', 'smart_bm_0005', 'smart_bm_0016') or
                COLUMN2 in ('smart_bm_0007', 'smart_bm_0018'))
            </otherwise>
        </choose>
          and USER_STATUS = '1'
          and dlr_code = #{param.dlrCode}
    </select>

    <!-- 根据`SMART_ID`&`MOBILE`查询`EVENT_NAME`&`EVENT_TIME` -->
    <select id="selectEventNameAndEventTimeBySmartIdAndMobile" resultType="com.ly.adp.csc.entities.LeadsEventVO">
        SELECT
            `EVENT_NAME` AS eventName,
            `EVENT_TIME` AS eventTime
        FROM
            `t_sac_onecust_info_event`
        <where>
            <if test="param.smartId != null and param.smartId != ''">
                AND `SMART_ID` = #{param.smartId}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND `MOBILE` = #{param.phone}
            </if>
        </where>
        ORDER BY
            `EVENT_TIME` DESC
    </select>

    <select id="queryLeadsEvent" resultType="com.ly.adp.csc.entities.LeadsEventVO">
        select INFO_EVENT_ID,
               EVENT_CODE,
               EVENT_NAME,
               MOBILE,
               EVENT_TIME,
               SMART_ID
        from t_sac_onecust_info_event
        where SMART_ID = #{param.smartId}
        order by EVENT_TIME desc
    </select>

    <select id="findInfoDlr" resultType="java.util.Map">
        select
        ID,
        FIRST_ARRIVAL_TIME,
        COLUMN5,
        COLUMN6
        from csc.t_sac_clue_info_dlr
        where CUST_ID = #{custId}
        limit 1
    </select>

<!--    暂未使用-->
    <update id="updateInfoDlr">
        update
            csc.t_sac_clue_info_dlr
        set FIRST_ARRIVAL_TIME=now(),
            LAST_ARRIVAL_TIME=now()
        where CUST_ID = #{custId}
    </update>

    <!-- clue 更新 -->
    <update id="updateArrivalTime">
        update
            adp_leads.t_sac_clue_info_dlr
        set FIRST_ARRIVAL_TIME=(case
                                    when FIRST_ARRIVAL_TIME is null
                                        then #{param.lastArrivalTime}
                                    else FIRST_ARRIVAL_TIME end),

            LAST_ARRIVAL_TIME=#{param.lastArrivalTime}
        where CUST_ID = #{custId}
    </update>

    <select id="selectManagerUseDlr" resultType="java.lang.Boolean">
        SELECT EXISTS
                   (SELECT 1
                    FROM mp.t_usc_mdm_user_dlr dd
                    WHERE dd.USER_ID = #{userId}
                   )
    </select>
    <select id="queryArrivalStoreReport" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        DLR_CODE_OWNER AS dlr_code,
        DLR_NAME_OWNER AS dlr_name,
        emp.EMP_CODE as expert_code,
        RESUME_PERSON_NAME AS expert_name,
        resume.CREATED_DATE AS register_date,
        ARRIVAL_TIME,
        ARRIVAL_END_TIME,
        TIMESTAMPDIFF( SECOND, ARRIVAL_TIME, ARRIVAL_END_TIME ) AS RECEIVER_TIME,
        dlr.CUST_NAME AS customer_name,
        INSERT(dlr.PHONE,4,4,'****') AS phone,
        ARRIVAL_NUM,
        lookup1.LOOKUP_VALUE_NAME AS ARRIVAL_METHOD,
        '到店' AS arrival_action,
        RESUME_DESC,
        dlr.COLUMN5 AS heat_name,
        INTEN_CAR_TYPE_NAME,
        json_unquote ( EXTEND_JSON -> '$.focusOfCarPurchase' ) AS focusOfCarPurchase,
        json_unquote ( EXTEND_JSON -> '$.isAddBuy' ) AS isAddBuy,
        dlr.INFO_CHAN_M_CODE,
        dlr.INFO_CHAN_M_NAME,
        dlr.INFO_CHAN_D_CODE,
        dlr.INFO_CHAN_D_NAME,
        T4.AREA_NAME,
        ct.CITY_NAME,
        c2.AGENT_NAME
        FROM
        csc.t_sac_onecust_resume resume
        LEFT JOIN csc.t_sac_clue_info_dlr dlr ON resume.cust_id = dlr.CUST_ID
        LEFT JOIN csc.t_sac_onecust_info info ON resume.CUST_ID = info.CUST_ID
        LEFT JOIN mp.t_prc_mds_lookup_value lookup1 ON resume.ARRIVAL_METHOD = lookup1.LOOKUP_VALUE_CODE
        AND lookup1.LOOKUP_TYPE_CODE = 'VE1113'
        LEFT JOIN mp.t_usc_mdm_org_dlr d ON resume.DLR_CODE_OWNER = d.DLR_CODE
        LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
        LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
        LEFT JOIN mp.t_usc_mdm_agent_area t2 ON d.DLR_ID = t2.AGENT_ID
        AND t2.AREA_TYPE = '0'
        LEFT JOIN mp.t_usc_area_relation T3 ON T2.AREA_ID = T3.REL_OBJ_ID
        AND T3.REL_OBJ_TYPE = '1'
        LEFT JOIN mp.t_usc_area_info T4 ON T3.AREA_ID = T4.AREA_ID
        AND T4.AREA_TYPE = '1'
        LEFT JOIN mp.t_usc_mdm_org_city ct ON ct.CITY_ID = d.CITY_ID
        left join mp.t_usc_mdm_org_employee emp on resume.RESUME_PERSON_CODE = emp.user_id
        where 1=1
        and resume.SENCE_CODE = '2'
        <if test="param.dlrCode != null and '' != param.dlrCode">
            AND DLR_CODE_OWNER LIKE concat( '%', #{param.dlrCode}, '%' )
        </if>
        <if test="param.dlrName != null and '' != param.dlrName">
            AND DLR_NAME_OWNER LIKE concat( '%', #{param.dlrName}, '%' )
        </if>
        <if test="param.beginDate != null and '' != param.beginDate">
            AND resume.CREATED_DATE >= STR_TO_DATE( #{param.beginDate}, '%Y-%m-%d' )
        </if>
        <if test="param.endDate != null and '' != param.endDate">
            AND resume.CREATED_DATE <![CDATA[<]]>
            DATE_ADD( STR_TO_DATE( #{param.endDate}, '%Y-%m-%d' ), INTERVAL 1 DAY )
        </if>

        <if test="param.phone != null and '' != param.phone">
            AND dlr.PHONE LIKE concat( '%', #{param.phone}, '%' )
        </if>
        <if test="param.custName != null and '' != param.custName">
            AND dlr.CUST_NAME LIKE concat( '%', #{param.custName}, '%' )
        </if>
        <if test="param.areaId != null and '' != param.areaId">
            AND t4.AREA_ID = #{param.areaId}
        </if>
        <if test="param.cityCode != null and '' != param.cityCode">
            AND ct.CITY_CODE = #{param.cityCode}
        </if>
        <if test="param.agentId != null and '' != param.agentId">
            AND c2.AGENT_ID = #{param.agentId}
        </if>
        <if test="param.dlrCode2 != null and '' != param.dlrCode2">
            AND DLR_CODE_OWNER = #{param.dlrCode2}
        </if>
        <if test="param.agentCode2 != null and '' != param.agentCode2">
            AND c2.AGENT_CODE = #{param.agentCode2}
        </if>
        <if test="param.agentCompanyCode2 != null and '' != param.agentCompanyCode2">
            AND c1.AGENT_COMPANY_CODE = #{param.agentCompanyCode2}
        </if>
        <if test="param.userId != null and '' != param.userId">
            AND EXISTS (
            SELECT 1
            FROM mp.t_usc_mdm_user_dlr dlr
            WHERE dlr.dlr_id = d.dlr_id
            and dlr.user_id = #{param.userId}
            )
        </if>
        UNION ALL
        SELECT
        sheet.DLR_CODE,
        sheet.DLR_NAME,
        emp.EMP_CODE,
        SALES_CONSULTANT_NAME,
        END_TIME AS CREATED_DATE,
        START_TIME,
        END_TIME,
        TIMESTAMPDIFF( SECOND, START_TIME, END_TIME ) AS RECEIVER_TIME,
        CUSTOMER_NAME,
        INSERT(CUSTOMER_PHONE,4,4,'****') AS phone,
        '' AS ARRIVAL_NUM,
        '邀约进店' AS ARRIVAL_METHOD,
        '试驾' AS arrival_action,
        sheet.COLUMN1 as RESUME_DESC,
        dlr.COLUMN5 AS heat_name,
        dlr.INTEN_CAR_TYPE_NAME,
        json_unquote ( EXTEND_JSON -> '$.focusOfCarPurchase' ) AS focusOfCarPurchase,
        json_unquote ( EXTEND_JSON -> '$.isAddBuy' ) AS isAddBuy,
        dlr.INFO_CHAN_M_CODE,
        dlr.INFO_CHAN_M_NAME,
        dlr.INFO_CHAN_D_CODE,
        dlr.INFO_CHAN_D_NAME,
        T4.AREA_NAME,
        ct.CITY_NAME,
        c2.AGENT_NAME
        FROM
        csc.t_sac_test_drive_sheet sheet
        LEFT JOIN csc.t_sac_onecust_info info ON sheet.CUSTOMER_PHONE = info.PHONE
        LEFT JOIN csc.t_sac_clue_info_dlr dlr ON sheet.CUSTOMER_PHONE = dlr.PHONE
        LEFT JOIN mp.t_usc_mdm_org_dlr d ON sheet.DLR_CODE = d.DLR_CODE
        LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
        LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
        LEFT JOIN mp.t_usc_mdm_agent_area t2 ON d.DLR_ID = t2.AGENT_ID
        AND t2.AREA_TYPE = '0'
        LEFT JOIN mp.t_usc_area_relation T3 ON T2.AREA_ID = T3.REL_OBJ_ID
        AND T3.REL_OBJ_TYPE = '1'
        LEFT JOIN mp.t_usc_area_info T4 ON T3.AREA_ID = T4.AREA_ID
        AND T4.AREA_TYPE = '1'
        LEFT JOIN mp.t_usc_mdm_org_city ct ON ct.CITY_ID = d.CITY_ID
        left join mp.t_usc_mdm_org_employee emp on sheet.SALES_CONSULTANT_ID = emp.user_id
        where 1=1
        <if test="param.dlrCode != null and '' != param.dlrCode">
            AND sheet.DLR_CODE LIKE concat( '%', #{param.dlrCode}, '%' )
        </if>
        <if test="param.dlrName != null and '' != param.dlrName">
            AND sheet.DLR_NAME LIKE concat( '%', #{param.dlrName}, '%' )
        </if>
        <if test="param.beginDate != null and '' != param.beginDate">
            AND END_TIME >= STR_TO_DATE( #{param.beginDate}, '%Y-%m-%d' )
        </if>
        <if test="param.endDate != null and '' != param.endDate">
            AND END_TIME <![CDATA[<]]>
            DATE_ADD( STR_TO_DATE( #{param.endDate}, '%Y-%m-%d' ), INTERVAL 1 DAY )
        </if>

        <if test="param.phone != null and '' != param.phone">
            AND CUSTOMER_PHONE LIKE concat( '%', #{param.phone}, '%' )
        </if>
        <if test="param.custName != null and '' != param.custName">
            AND CUSTOMER_NAME LIKE concat( '%', #{param.custName}, '%' )
        </if>
        <if test="param.areaId != null and '' != param.areaId">
            AND t4.AREA_ID = #{param.areaId}
        </if>
        <if test="param.cityCode != null and '' != param.cityCode">
            AND ct.CITY_CODE = #{param.cityCode}
        </if>
        <if test="param.agentId != null and '' != param.agentId">
            AND c2.AGENT_ID = #{param.agentId}
        </if>
        <if test="param.dlrCode2 != null and '' != param.dlrCode2">
            AND sheet.DLR_CODE = #{param.dlrCode2}
        </if>
        <if test="param.agentCode2 != null and '' != param.agentCode2">
            AND c2.AGENT_CODE = #{param.agentCode2}
        </if>
        <if test="param.agentCompanyCode2 != null and '' != param.agentCompanyCode2">
            AND c1.AGENT_COMPANY_CODE = #{param.agentCompanyCode2}
        </if>
        <if test="param.userId != null and '' != param.userId">
            AND EXISTS (
            SELECT 1
            FROM mp.t_usc_mdm_user_dlr dlr
            WHERE dlr.dlr_id = d.dlr_id
            and dlr.user_id = #{param.userId}
            )
        </if>
        order by register_date desc
    </select>

    <select id="findUrl" resultType="java.util.Map">
        select
            LOOKUP_VALUE_NAME url,
            IS_ENABLE,
            ATTRIBUTE2
        from mp.t_prc_mds_lookup_value
        where
            LOOKUP_TYPE_CODE='VE1040'
          and LOOKUP_VALUE_CODE='1'
    </select>

    <select id="findCustInfo" resultType="java.util.Map">
        select
            CUST_NAME as CUSTOMER_NAME,
            PHONE as CUSTOMER_PHONE,
            CUST_ID as customerId
        from csc.t_sac_onecust_info
        where
            cust_id=#{custId}
    </select>
</mapper>
