<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacTestDriveReviewRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacTestDriveReviewRecord">
        <id column="RECORD_ID" property="recordId" />
        <result column="TEST_DRIVE_SHEET_ID" property="testDriveSheetId" />
        <result column="TEST_DRIVE_ORDER_NO" property="testDriveOrderNo" />
        <result column="TEST_DRIVE_DESC" property="testDriveDesc" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="_MYCAT_OP_TIME" property="mycatOpTime" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="OEM_CODE" property="oemCode" />
        <result column="GROUP_CODE" property="groupCode" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="SDP_USER_ID" property="sdpUserId" />
        <result column="SDP_ORG_ID" property="sdpOrgId" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, TEST_DRIVE_SHEET_ID, TEST_DRIVE_ORDER_NO, TEST_DRIVE_DESC, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID
    </sql>

    <!-- 试乘试驾跟进记录查询 -->
	<select id="sacTestDriveReviewRecordFindAll" resultType="map">
		SELECT
		    T.RECORD_ID,            /*跟进记录ID*/ 
		    T.TEST_DRIVE_SHEET_ID,  /*试乘试驾单ID*/ 
		    T.TEST_DRIVE_ORDER_NO,  /*试乘试驾单号 */
		    T.TEST_DRIVE_DESC,      /*试乘试驾单跟进内容*/
		    T.UPDATE_CONTROL_ID     /*并发控制ID*/
		FROM
		    t_sac_test_drive_review_record T
		WHERE 1 = 1
		<if test="param.recordId != null and '' != param.recordId">
		    AND T.RECORD_ID = #{param.recordId}
		</if>
		<if test="param.testDriveSheetId != null and '' != param.testDriveSheetId">
		    AND T.TEST_DRIVE_SHEET_ID = #{param.testDriveSheetId}
		</if>
		<if test="param.testDriveOrderNo != null and '' != param.testDriveOrderNo">
		    AND T.TEST_DRIVE_ORDER_NO = #{param.testDriveOrderNo}
		</if>
		<if test="param.testDriveDesc != null and '' != param.testDriveDesc">
		    AND T.TEST_DRIVE_DESC LIKE CONCAT('%', #{param.testDriveDesc}, '%') 
		</if>
		ORDER BY T.CREATED_DATE DESC
	</select>

    <!-- 试乘试驾跟进记录新增 -->
	<insert id="createTestDriveReviewRecordInfo" parameterType="map">
        INSERT INTO t_sac_test_drive_review_record(
            RECORD_ID, /*跟进记录ID*/
            TEST_DRIVE_SHEET_ID, /*试乘试驾单ID*/
            TEST_DRIVE_ORDER_NO, /*试乘试驾单号*/
            TEST_DRIVE_DESC, /*试乘试驾单跟进内容*/
            OEM_ID,
            GROUP_ID,
            OEM_CODE,
            GROUP_CODE,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            SDP_USER_ID,
            SDP_ORG_ID,
            UPDATE_CONTROL_ID)
        VALUES(
	        uuid(), /*跟进记录ID*/
	        #{param.testDriveSheetId}, /*试乘试驾单ID*/
	        #{param.testDriveOrderNo}, /*试乘试驾单号*/
	        #{param.testDriveDesc}, /*试乘试驾单跟进内容*/
	        #{param.oemId},
	        #{param.groupId},
	        #{param.oemCode}, 
	        #{param.groupCode}, 
	        #{param.creator}, /*创建人*/
	        #{param.createdName}, /*创建人名称*/
	        now(), /*创建时间*/
	        #{param.modifier}, /*更新人员*/
	        #{param.modifyName}, /*更新人员名称*/
	        now(), /*最后更新时间*/
	        '1',
	        '1',
	        '1',
	        uuid())
	</insert>
</mapper>
