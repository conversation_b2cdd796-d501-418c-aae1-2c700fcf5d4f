<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.SacBuBoutiqueDetailRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.SacBuBoutiqueDetailRecord">
        <id column="RECORD_ID" property="recordId" />
        <result column="DETAIL_ID" property="detailId" />
        <result column="RECORD_TYPE" property="recordType" />
        <result column="TYPE" property="type" />
        <result column="OPERATOR" property="operator" />
        <result column="NUM" property="num" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, DETAIL_ID, RECORD_TYPE, `TYPE`, OPERATOR, NUM,USE_DATE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
 	
    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <if test="param.recordId !=null and param.recordId !=''">and RECORD_ID=#{param.recordId}</if>
    	<if test="param.detailId !=null and param.detailId !=''">and DETAIL_ID=#{param.detailId}</if>
    	<if test="param.recordType !=null and param.recordType !=''">and RECORD_TYPE=#{param.recordType}</if>
    	<if test="param.type !=null and param.type !=''">and TYPE=#{param.type}</if>
    	<if test="param.operator !=null and param.operator !=''">and OPERATOR=#{param.operator}</if>
    	<if test="param.num !=null and param.num !=''">and NUM=#{param.num}</if>
    	<if test="param.column1 !=null and param.column1 !=''">and COLUMN1=#{param.column1}</if>
    	<if test="param.column2 !=null and param.column2 !=''">and COLUMN2=#{param.column2}</if>
    	<if test="param.column3 !=null and param.column3 !=''">and COLUMN3=#{param.column3}</if>
    	<if test="param.column4 !=null and param.column4 !=''">and COLUMN4=#{param.column4}</if>
    	<if test="param.column5 !=null and param.column5 !=''">and COLUMN5=#{param.column5}</if>
    	<if test="param.remark !=null and param.remark !=''">and REMARK=#{param.remark}</if>
    	<if test="param.creator !=null and param.creator !=''">and CREATOR=#{param.creator}</if>
    	<if test="param.createdName !=null and param.createdName !=''">and CREATED_NAME=#{param.createdName}</if>
    	<if test="param.createdDate !=null and param.createdDate !=''">and CREATED_DATE=#{param.createdDate}</if>
    	<if test="param.modifier !=null and param.modifier !=''">and MODIFIER=#{param.modifier}</if>
    	<if test="param.modifyName !=null and param.modifyName !=''">and MODIFY_NAME=#{param.modifyName}</if>
    	<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
    	<if test="param.isEnable !=null and param.isEnable !=''">and IS_ENABLE=#{param.isEnable}</if>
    	<if test="param.updateControlId !=null and param.updateControlId !=''">and UPDATE_CONTROL_ID=#{param.updateControlId}</if>
   	</sql>
 	
 	<!-- 精品明细记录日志表 信息查询 -->
	<select id="querySacBuBoutiqueDetailRecord" resultType="map">
		select
	    <include refid="Base_Column_List"></include>
	    from t_sac_bu_boutique_detail_record
	    where 1=1
	    <include refid="where_condition"></include>
	</select>
	
	<!-- 精品明细记录日志表 信息删除（物理删除） -->
	<delete id="deleteSacBuBoutiqueDetailRecord">
		DELETE 
		FROM
			t_sac_bu_boutique_detail_record
		WHERE 1=1
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</delete>
	
	<!-- 精品明细记录日志表 信息新增 -->
	<insert id="createSacBuBoutiqueDetailRecord">
		insert into t_sac_bu_boutique_detail_record(<include refid="Base_Column_List"></include>)
		value(
        	uuid(),
			#{param.detailId},
			#{param.recordType},
			#{param.type},
			#{param.operator},
			#{param.num},
		    IFNULL(IF(#{param.useDate}='',NULL,#{param.useDate}),NULL),
			#{param.column1},
			#{param.column2},
			#{param.column3},
			#{param.column4},
			#{param.column5},
			#{param.remark},
			#{param.creator},
			#{param.createdName},
			now(),
			#{param.modifier},
			#{param.modifyName},
			now(),
			'1',
			uuid()
		)
	</insert>
	
	<!-- 精品明细记录日志表 信息更新 -->
	<update id="updateSacBuBoutiqueDetailRecord">
		update t_sac_bu_boutique_detail_record  set 
			<!-- 更新列表 -->
			<if test="param.detailId !=null and param.detailId !=''">DETAIL_ID=#{param.detailId},</if>
			<if test="param.recordType !=null and param.recordType !=''">RECORD_TYPE=#{param.recordType},</if>
			<if test="param.type !=null and param.type !=''">TYPE=#{param.type},</if>
			<if test="param.operator !=null and param.operator !=''">OPERATOR=#{param.operator},</if>
			<if test="param.num !=null and param.num !=''">NUM=#{param.num},</if>
			<if test="param.useDate !=null and param.useDate !=''">USE_DATE=#{param.useDate},</if>
			<if test="param.column1 !=null and param.column1 !=''">COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''">COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''">COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''">COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null and param.column5 !=''">COLUMN5=#{param.column5},</if>
			<if test="param.remark !=null and param.remark !=''">REMARK=#{param.remark},</if>
			<if test="param.creator !=null and param.creator !=''">CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''">CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null ">CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''">MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''">MODIFY_NAME=#{param.modifyName},</if>
			<if test="param.lastUpdatedDate !=null ">LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
			<if test="param.isEnable !=null and param.isEnable !=''">IS_ENABLE=#{param.isEnable},</if>
			UPDATE_CONTROL_ID=UUID(),
			<!-- 结束无逗号 -->
			RECORD_ID=RECORD_ID
			where 1=1 
			<!-- 描述条件 -->
			<include refid="where_condition"></include>
	</update>
	
	<select id="querySacBuBoutiqueDetailRecordInfo" parameterType="map" resultType="map">
		SELECT
			A.RECORD_TYPE ,
			A.OPERATOR ,
		    CASE
				A.TYPE
		    WHEN
		     '1'THEN '上架' ELSE '下架' END AS TYPE ,
			A.CREATED_DATE ,
			A.NUM ,
			A.REMARK ,
			B.DLR_ID ,
			B.DLR_CODE ,
			B.DLR_NAME ,
			B.BOUTIQUE_CODE ,
			B.BOUTIQUE_NAME ,
			B.BOUTIQUE_STATUS,
		    DATE_FORMAT(A.USE_DATE,'%Y-%m-%d %H:%i:%s') USE_DATE
		FROM t_sac_bu_boutique_detail_record A
			LEFT JOIN t_sac_bu_boutique_detail B ON A.DETAIL_ID = B.DETAIL_ID 
		WHERE 1 = 1
		<if test="param.detailId != null and param.detailId != ''">
			AND B.DETAIL_ID = #{param.detailId}
		</if>
		<if test="param.dlrCode != null and param.dlrCode != ''">
			AND B.DLR_CODE = #{param.dlrCode}
		</if>
		<if test="param.dlrName != null and param.dlrName != ''">
			AND INSTR(B.DLR_NAME, #{param.dlrName}) <![CDATA[>]]> 0
		</if>
		<if test="param.boutiqueCode != null and param.boutiqueCode != ''">
			AND B.BOUTIQUE_CODE = #{param.boutiqueCode}
		</if>
		<if test="param.boutiqueName != null and param.boutiqueName != ''">
			AND INSTR(B.BOUTIQUE_NAME, #{param.boutiqueName}) <![CDATA[>]]> 0
		</if>
		<if test="param.num	!= null and param.num != ''">
			AND A.NUM = #{param.num}
		</if>
		<if test="param.recordType!=null and ''!=param.recordType">
			AND	A.RECORD_TYPE = #{param.recordType}
		</if>
		ORDER BY A.CREATED_DATE DESC
	</select>
</mapper>
