<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.mp.acc.manage.idal.mapper.AccBuActivityMapper">

	<resultMap id="AccBuActivitySummaryMap" type="com.ly.mp.acc.manage.entities.AccBuActivitySummary">
		<result column="ACTIVITY_ID" property="activityId" />
		<result column="dlSignNum" property="dlSignNum" />
		<result column="yhSignNum" property="yhSignNum" />
		<result column="phone" property="phone" />
	</resultMap>

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.ly.mp.acc.manage.entities.AccBuActivity">
		<id column="ACTIVITY_ID" property="activityId" />
		<result column="ACTIVITY_NAME" property="activityName" />
		<result column="ACTIVITY_PURPOSE" property="activityPurpose" />
		<result column="CREATE_TYPE_CODE" property="createTypeCode" />
		<result column="CREATE_TYPE_NAME" property="createTypeName" />
		<result column="STATUS_CODE" property="statusCode" />
		<result column="STATUS_NAME" property="statusName" />
		<result column="RELEASE_STATUS_CODE" property="releaseStatusCode" />
		<result column="RELEASE_STATUS_NAME" property="releaseStatusName" />
		<result column="DLR_CODE" property="dlrCode" />
		<result column="DLR_SHORT_NAME" property="dlrShortName" />
		<result column="DLR_SUPPORTED_SHORT_NAME" property="dlrSupportedShortName" />
		<result column="DLR_SUPPORTED_CODE" property="dlrSupportedCode" />
		<result column="DLR_CITY_CODE" property="dlrCityCode" />
		<result column="DLR_CITY_NAME" property="dlrCityName" />
		<result column="DLR_REGION_CODE" property="dlrRegionCode" />
		<result column="DLR_REGION_NAME" property="dlrRegionName" />
		<result column="DLR_SPACE_CODE" property="dlrSpaceCode" />
		<result column="DLR_SPACE_NAME" property="dlrSpaceName" />
		<result column="DLR_AREA_USED_START_TIME" property="dlrAreaUsedStartTime" />
		<result column="DLR_AREA_USED_END_TIME" property="dlrAreaUsedEndTime" />
		<result column="DLR_AREA_USED_LENGTH_OF_TIME" property="dlrAreaUsedLengthOfTime" />
		<result column="DLR_ADDRESS_DETAIL" property="dlrAddressDetail" />
		<result column="ADDRESS_TYPE_CODE" property="addressTypeCode" />
		<result column="ADDRESS_TYPE_NAME" property="addressTypeName" />
		<result column="ACTIVITY_TYPE_CODE" property="activityTypeCode" />
		<result column="ACTIVITY_TYPE_NAME" property="activityTypeName" />
		<result column="ACTIVITY_SUBTYPE_CODE" property="activitySubtypeCode" />
		<result column="ACTIVITY_SUBTYPE_NAME" property="activitySubtypeName" />
		<result column="ACTIVITY_RESOURCE_CODE" property="activityResourceCode" />
		<result column="ACTIVITY_RESOURCE_NAME" property="activityResourceName" />
		<result column="TIPS_TIME_CODE" property="tipsTimeCode" />
		<result column="TIPS_TIME_NAME" property="tipsTimeName" />
		<result column="APPLY_CANCEL_TIME_CODE" property="applyCancelTimeCode" />
		<result column="APPLY_CANCEL_TIME_NAME" property="applyCancelTimeName" />
		<result column="NUMBER_OF_PERSON_SUPPORTED" property="numberOfPersonSupported" />
		<result column="TEST_DRIVE_SUPPORTED" property="testDriveSupported" />
		<result column="BUDGET" property="budget" />
		<result column="BUDGET_DETAIL" property="budgetDetail" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
		<result column="APPLY_BEGIN_TIME" property="applyBeginTime" />
		<result column="APPLY_END_TIME" property="applyEndTime" />
		<result column="PUBLISH_TIME" property="publishTime" />
		<result column="ACTIVITY_COVER_PAGE_URL" property="activityCoverPageUrl" />
		<result column="ACTIVITY_INTRODUCTION" property="activityIntroduction" />
		<result column="ACTIVITY_KINDLY_REMINDER" property="activityKindlyReminder" />
		
		<result column="RESOURCE_ADVISER" property="resourceAdviser" />
		<result column="RESOURCE_CAR" property="resourceCar" />
		<result column="RESOURCE_SUPPORTED_TIME_START" property="resourceSupportedTimeStart" />
		<result column="RESOURCE_SUPPORTED_TIME_END" property="resourceSupportedTimeEnd" />
		
		<result column="ACTUAL_COST" property="actualCost" />
		<result column="FAPIAO" property="fapiao" />
		<result column="HX_STATUS_CODE" property="hxStatusCode" />
		<result column="HX_STATUS_NAME" property="hxStatusName" />
		
		<result column="COLUMN1" property="column1" />
		<result column="COLUMN2" property="column2" />
		<result column="COLUMN3" property="column3" />
		<result column="COLUMN4" property="column4" />
		<result column="COLUMN5" property="column5" />
		<result column="COLUMN6" property="column6" />
		<result column="COLUMN7" property="column7" />
		<result column="COLUMN8" property="column8" />
		<result column="COLUMN9" property="column9" />
		<result column="COLUMN10" property="column10" />
		<result column="COLUMN11" property="column11" />
		<result column="COLUMN12" property="column12" />
		<result column="COLUMN13" property="column13" />
		<result column="COLUMN14" property="column14" />
		<result column="COLUMN15" property="column15" />
		<result column="COLUMN16" property="column16" />
		<result column="COLUMN17" property="column17" />
		<result column="COLUMN18" property="column18" />
		<result column="COLUMN19" property="column19" />
		<result column="COLUMN20" property="column20" />
		<result column="COLUMN20" property="column21" />
		<result column="COLUMN21" property="column21" />
		<result column="COLUMN22" property="column22" />
		<result column="COLUMN23" property="column23" />
		<result column="COLUMN24" property="column24" />
		<result column="COLUMN25" property="column25" />
		<result column="COLUMN26" property="column26" />
		<result column="COLUMN27" property="column27" />
		<result column="COLUMN28" property="column28" />
		<result column="COLUMN29" property="column29" />
		<result column="COLUMN30" property="column30" />
		<result column="EXTENDS_JSON" property="extendsJson" />
		<result column="OEM_ID" property="oemId" />
		<result column="GROUP_ID" property="groupId" />
		<result column="CREATOR" property="creator" />
		<result column="CREATED_NAME" property="createdName" />
		<result column="CREATED_DATE" property="createdDate" />
		<result column="MODIFIER" property="modifier" />
		<result column="MODIFY_NAME" property="modifyName" />
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
		<result column="IS_ENABLE" property="isEnable" />
		<result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.ACTIVITY_PURPOSE_CODE
		, t1.CREATE_TYPE_CODE
		, t1.CREATE_TYPE_NAME
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE
		, t1.DLR_SHORT_NAME
		, t1.DLR_SUPPORTED_SHORT_NAME
		, t1.DLR_SUPPORTED_CODE
		, t1.DLR_CITY_CODE
		, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE
		, t1.DLR_REGION_NAME
		, t1.DLR_SPACE_CODE
		, t1.DLR_SPACE_NAME
		, t1.DLR_AREA_USED_START_TIME
		, t1.DLR_AREA_USED_END_TIME
		, t1.DLR_AREA_USED_LENGTH_OF_TIME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.ACTIVITY_RESOURCE_CODE
		, t1.ACTIVITY_RESOURCE_NAME
		, t1.TIPS_TIME_CODE
		, t1.TIPS_TIME_NAME
		, t1.APPLY_CANCEL_TIME_CODE
		, t1.APPLY_CANCEL_TIME_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, t1.TEST_DRIVE_SUPPORTED
		, t1.BUDGET
		, t1.BUDGET_DETAIL
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.APPLY_BEGIN_TIME
		, t1.APPLY_END_TIME
		, t1.PUBLISH_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.ACTIVITY_INTRODUCTION
		, t1.ACTIVITY_KINDLY_REMINDER
		
		, t1.RESOURCE_ADVISER
		, t1.RESOURCE_CAR
		, t1.RESOURCE_SUPPORTED_TIME_START
		, t1.RESOURCE_SUPPORTED_TIME_END
		
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.FAPIAO
		, t1.ACTUAL_COST
		, t1.COLUMN1
		, t1.COLUMN2
		, t1.COLUMN3
		, t1.COLUMN4
		, t1.COLUMN5
		, t1.COLUMN6
		, t1.COLUMN7
		, t1.COLUMN8
		, t1.COLUMN9
		, t1.COLUMN10
		, t1.COLUMN11
		, t1.COLUMN12
		, t1.COLUMN13
		, t1.COLUMN14
		, t1.COLUMN15
		, t1.COLUMN16
		, t1.COLUMN17
		, t1.COLUMN18
		, t1.COLUMN19
		, t1.COLUMN20
		, t1.COLUMN21
		, t1.COLUMN22
		, t1.COLUMN23
		, t1.COLUMN24
		, t1.COLUMN25
		, t1.COLUMN26
		, t1.COLUMN27
		, t1.COLUMN28
		, t1.COLUMN29
		, t1.COLUMN30
		, t1.EXTENDS_JSON
		, t1.OEM_ID
		, t1.GROUP_ID
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.MODIFIER
		, t1.MODIFY_NAME
		, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE
		, t1.UPDATE_CONTROL_ID
		, t1.TEMPORARY_ACTIVITY_QR_CODE
		, t1.GROUP_ACTIVITY_QR_CODE
		, t1.INFO_CHAN_D_CODE
		, t1.INFO_CHAN_D_NAME
		, t1.ACTUAL_UGC
		, t1.ACTUAL_PEOPLE_NUMBER
		, t1.ACTUAL_CLUE
	</sql>
	
	<!-- where语句条件过滤 -->
	<sql id="where_condition">
		<if test="param.inCreateTypeCodeList !=null"> and t1.CREATE_TYPE_CODE in <foreach collection="param.inCreateTypeCodeList"  item="item" open="(" close=")" separator="," >#{item}</foreach></if>
		<if test="param.inStatusCodeList !=null">and t1.STATUS_CODE in <foreach collection="param.inStatusCodeList"  item="item" open="(" close=")" separator="," >#{item}</foreach></if>
			
		<if test="param.activityId !=null and param.activityId !=''">and t1.ACTIVITY_ID=#{param.activityId}</if>
		<if test="param.activityPurpose !=null and param.activityPurpose !=''">and t1.ACTIVITY_PURPOSE=#{param.activityPurpose}</if>
		<if test="param.activityPurposeCode !=null and param.activityPurposeCode !=''">and t1.ACTIVITY_PURPOSE_CODE=#{param.activityPurposeCode}</if>
		<if test="param.createTypeCode !=null and param.createTypeCode !=''">and t1.CREATE_TYPE_CODE=#{param.createTypeCode}</if>
		<if test="param.createTypeName !=null and param.createTypeName !=''">and t1.CREATE_TYPE_NAME=#{param.createTypeName}</if>
		<if test="param.statusCode !=null and param.statusCode !=''">and t1.STATUS_CODE=#{param.statusCode}</if>
		<if test="param.statusName !=null and param.statusName !=''">and t1.STATUS_NAME=#{param.statusName}</if>
		<if test="param.releaseStatusCode !=null and param.releaseStatusCode !=''">and t1.RELEASE_STATUS_CODE=#{param.releaseStatusCode}</if>
		<if test="param.releaseStatusName !=null and param.releaseStatusName !=''">and t1.RELEASE_STATUS_NAME=#{param.releaseStatusName}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and find_in_set(#{param.dlrCode},t1.DLR_CODE)</if>
		<if test="param.dlrShortName !=null and param.dlrShortName !=''">and t1.DLR_SHORT_NAME=#{param.dlrShortName}</if>
		<if test="param.dlrSupportedShortName !=null and param.dlrSupportedShortName !=''">and t1.DLR_SUPPORTED_SHORT_NAME=#{param.dlrSupportedShortName}</if>
		<if test="param.dlrSupportedCode !=null and param.dlrSupportedCode !=''">and t1.DLR_SUPPORTED_CODE=#{param.dlrSupportedCode}</if>
		<if test="param.dlrCityCode !=null and param.dlrCityCode !=''">and find_in_set(#{param.dlrCityCode},t1.DLR_CITY_CODE)</if>
		<if test="param.dlrCityName !=null and param.dlrCityName !=''">and t1.DLR_CITY_NAME=#{param.dlrCityName}</if>
		<if test="param.dlrRegionCode !=null and param.dlrRegionCode !=''">and find_in_set(#{param.dlrRegionCode},t1.DLR_REGION_CODE)</if>
		<if test="param.dlrRegionName !=null and param.dlrRegionName !=''">and t1.DLR_REGION_NAME=#{param.dlrRegionName}</if>
		<if test="param.dlrSpaceCode !=null and param.dlrSpaceCode !=''">and t1.DLR_SPACE_CODE=#{param.dlrSpaceCode}</if>
		<if test="param.dlrSpaceName !=null and param.dlrSpaceName !=''">and t1.DLR_SPACE_NAME=#{param.dlrSpaceName}</if>
		<if test="param.dlrAreaUsedStartTime !=null">and t1.DLR_AREA_USED_START_TIME=#{param.dlrAreaUsedStartTime}</if>
		<if test="param.dlrAreaUsedEndTime !=null">and t1.DLR_AREA_USED_END_TIME=#{param.dlrAreaUsedEndTime}</if>
		<if test="param.dlrAreaUsedLengthOfTime !=null and param.dlrAreaUsedLengthOfTime !=''">and t1.DLR_AREA_USED_LENGTH_OF_TIME=#{param.dlrAreaUsedLengthOfTime}</if>
		<if test="param.dlrAddressDetail !=null and param.dlrAddressDetail !=''">and t1.DLR_ADDRESS_DETAIL=#{param.dlrAddressDetail}</if>
		<if test="param.addressTypeCode !=null and param.addressTypeCode !=''">and t1.ADDRESS_TYPE_CODE=#{param.addressTypeCode}</if>
		<if test="param.addressTypeName !=null and param.addressTypeName !=''">and t1.ADDRESS_TYPE_NAME=#{param.addressTypeName}</if>
		<if test="param.activityTypeCode !=null and param.activityTypeCode !=''">and t1.ACTIVITY_TYPE_CODE=#{param.activityTypeCode}</if>
		<if test="param.activityTypeName !=null and param.activityTypeName !=''">and t1.ACTIVITY_TYPE_NAME=#{param.activityTypeName}</if>
		<if test="param.activitySubtypeCode !=null and param.activitySubtypeCode !=''">and FIND_IN_SET(#{param.activitySubtypeCode},t1.ACTIVITY_SUBTYPE_CODE)</if>
		<if test="param.activitySubtypeName !=null and param.activitySubtypeName !=''">and t1.ACTIVITY_SUBTYPE_NAME=#{param.activitySubtypeName}</if>
		<if test="param.activityResourceCode !=null and param.activityResourceCode !=''">and t1.ACTIVITY_RESOURCE_CODE=#{param.activityResourceCode}</if>
		<if test="param.activityResourceName !=null and param.activityResourceName !=''">and t1.ACTIVITY_RESOURCE_NAME=#{param.activityResourceName}</if>
		<if test="param.tipsTimeCode !=null and param.tipsTimeCode !=''">and t1.TIPS_TIME_CODE=#{param.tipsTimeCode}</if>
		<if test="param.tipsTimeName !=null and param.tipsTimeName !=''">and t1.TIPS_TIME_NAME=#{param.tipsTimeName}</if>
		<if test="param.applyCancelTimeCode !=null and param.applyCancelTimeCode !=''">and t1.APPLY_CANCEL_TIME_CODE=#{param.applyCancelTimeCode}</if>
		<if test="param.applyCancelTimeName !=null and param.applyCancelTimeName !=''">and t1.APPLY_CANCEL_TIME_NAME=#{param.applyCancelTimeName}</if>
		<if test="param.numberOfPersonSupported !=null and param.numberOfPersonSupported !=''">and t1.NUMBER_OF_PERSON_SUPPORTED=#{param.numberOfPersonSupported}</if>
		<if test="param.testDriveSupported !=null and param.testDriveSupported !=''">and t1.TEST_DRIVE_SUPPORTED=#{param.testDriveSupported}</if>
		<if test="param.budget !=null and param.budget !=''">and t1.BUDGET=#{param.budget}</if>
		<if test="param.budgetDetail !=null and param.budgetDetail !=''">and t1.BUDGET_DETAIL=#{param.budgetDetail}</if>
		<if test="param.beginTime !=null and param.beginTime !=''">and t1.BEGIN_TIME=#{param.beginTime}</if>
		<if test="param.endTime !=null and param.endTime !=''">and t1.END_TIME=#{param.endTime}</if>
		<if test="param.applyBeginTime !=null and param.applyBeginTime !=''">and t1.APPLY_BEGIN_TIME=#{param.applyBeginTime}</if>
		<if test="param.applyEndTime !=null and param.applyEndTime !=''">and t1.APPLY_END_TIME=#{param.applyEndTime}</if>
		<if test="param.activityCoverPageUrl !=null and param.activityCoverPageUrl !=''">and t1.ACTIVITY_COVER_PAGE_URL=#{param.activityCoverPageUrl}</if>
		<if test="param.activityIntroduction !=null and param.activityIntroduction !=''">and t1.ACTIVITY_INTRODUCTION=#{param.activityIntroduction}</if>
		<if test="param.activityKindlyReminder !=null and param.activityKindlyReminder !=''">and t1.ACTIVITY_KINDLY_REMINDER=#{param.activityKindlyReminder}</if>
		
		<if test="param.resourceAdviser !=null and param.resourceAdviser !=''">and t1.RESOURCE_ADVISER=#{param.resourceAdviser}</if>
		<if test="param.resourceCar !=null and param.resourceCar !=''">and t1.RESOURCE_CAR=#{param.resourceCar}</if>
		<if test="param.resourceSupportedTimeStart !=null and param.resourceSupportedTimeStart !=''">and t1.RESOURCE_SUPPORTED_TIME_START=#{param.resourceSupportedTimeStart}</if>
		<if test="param.resourceSupportedTimeEnd !=null and param.resourceSupportedTimeEnd !=''">and t1.RESOURCE_SUPPORTED_TIME_END=#{param.resourceSupportedTimeEnd}</if>
		<if test="param.hxStatusName !=null and param.hxStatusName !=''">and t1.HX_STATUS_NAME=#{param.hxStatusName}</if>
		<if test="param.hxStatusCode !=null and param.hxStatusCode !=''">and t1.HX_STATUS_CODE=#{param.hxStatusCode}</if>
		<if test="param.fapiao !=null and param.fapiao !=''">and t1.FAPIAO=#{param.fapiao}</if>
		<if test="param.actualCost !=null and param.actualCost !=''">and t1.ACTUAL_COST=#{param.actualCost}</if>
		
		<if test="param.column1 !=null and param.column1 !=''">and t1.COLUMN1=#{param.column1}</if>
		<if test="param.column2 !=null and param.column2 !=''">and t1.COLUMN2=#{param.column2}</if>
		<if test="param.column3 !=null and param.column3 !=''">and t1.COLUMN3=#{param.column3}</if>
		<if test="param.column4 !=null and param.column4 !=''">and t1.COLUMN4=#{param.column4}</if>
		<if test="param.column5 !=null and param.column5 !=''">and t1.COLUMN5=#{param.column5}</if>
		<if test="param.column6 !=null and param.column6 !=''">and t1.COLUMN6=#{param.column6}</if>
		<if test="param.column7 !=null and param.column7 !=''">and t1.COLUMN7=#{param.column7}</if>
		<if test="param.column8 !=null and param.column8 !=''">and t1.COLUMN8=#{param.column8}</if>
		<if test="param.column9 !=null and param.column9 !=''">and t1.COLUMN9=#{param.column9}</if>
		<if test="param.column10 !=null and param.column10 !=''">and t1.COLUMN10=#{param.column10}</if>
		<if test="param.column11 !=null and param.column11 !=''">and t1.COLUMN11=#{param.column11}</if>
		<if test="param.column12 !=null and param.column12 !=''">and t1.COLUMN12=#{param.column12}</if>
		<if test="param.column13 !=null and param.column13 !=''">and t1.COLUMN13=#{param.column13}</if>
		<if test="param.column14 !=null and param.column14 !=''">and t1.COLUMN14=#{param.column14}</if>
		<if test="param.column15 !=null and param.column15 !=''">and t1.COLUMN15=#{param.column15}</if>
		<if test="param.column16 !=null and param.column16 !=''">and t1.COLUMN16=#{param.column16}</if>
		<if test="param.column17 !=null and param.column17 !=''">and t1.COLUMN17=#{param.column17}</if>
		<if test="param.column18 !=null and param.column18 !=''">and t1.COLUMN18=#{param.column18}</if>
		<if test="param.column19 !=null and param.column19 !=''">and t1.COLUMN19=#{param.column19}</if>
		<if test="param.column20 !=null and param.column20 !=''">and t1.COLUMN20=#{param.column20}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.activitySupport !=null and param.activitySupport !=''">and ifnull(t1.ACTIVITY_SUPPORT,'0')=#{param.activitySupport}</if>
		<if test="param.exhibitionEquipmen !=null and param.exhibitionEquipmen !=''">and ifnull(t1.EXHIBITION_EQUIPMEN,'0')=#{param.exhibitionEquipmen}</if>

		<if test="param.beginTimeStart !=null and param.beginTimeStart !=''">and t1.BEGIN_TIME>=#{param.beginTimeStart}</if>
		<if test="param.beginTimeEnd !=null and param.beginTimeEnd !=''"><![CDATA[and t1.BEGIN_TIME<=#{param.beginTimeEnd}]]></if>
		<if test="param.endTimeStart !=null and param.endTimeStart !=''">and t1.END_TIME>=#{param.endTimeStart}</if>
		<if test="param.endTimeEnd !=null and param.endTimeEnd !=''"><![CDATA[and t1.END_TIME<=#{param.endTimeEnd}]]></if>
		
		<if test="param.publishTimeStart !=null and param.publishTimeStart !=''">and t1.PUBLISH_TIME>=#{param.publishTimeStart}</if>
		<if test="param.publishTimeEnd !=null and param.publishTimeEnd !=''"><![CDATA[and t1.PUBLISH_TIME<=#{param.publishTimeEnd}]]></if>
		<if test='param.queryType !=null and param.queryType =="0"'>
		AND #{param.orDlrCode} REGEXP REPLACE(t1.dlr_code,',','|')
		and case when t1.CREATE_TYPE_CODE IN('DLR','DEVELOP', 'AGENT') THEN 1=1  else T1.RELEASE_STATUS_CODE='1' end
		</if>
		<if test='param.queryType !=null and param.queryType =="1"'>
		and (t1.CREATOR=#{param.orCreator} or (
		#{param.orDlrCode} REGEXP REPLACE(t1.dlr_code,',','|')
		and t1.RELEASE_STATUS_CODE = 1 
		))
		</if>
		<if test='param.queryType !=null and param.queryType =="2"'>
		-- and t1.RELEASE_STATUS_CODE = 1
		</if>
		
		<if test="param.activityName !=null and param.activityName !=''">and t1.ACTIVITY_NAME like concat('%', #{param.activityName}, '%')</if>
		

		
		
	</sql>
	
	<!-- 新增 -->
	<insert id="insertAccBuActivity">
		insert into t_acc_bu_activity(
		ACTIVITY_ID
		, ACTIVITY_NAME
		, ACTIVITY_PURPOSE
		, ACTIVITY_PURPOSE_CODE
		, CREATE_TYPE_CODE
		, CREATE_TYPE_NAME
		, STATUS_CODE
		, STATUS_NAME
		, RELEASE_STATUS_CODE
		, RELEASE_STATUS_NAME
		, DLR_CODE
		, DLR_SHORT_NAME
		, DLR_SUPPORTED_SHORT_NAME
		, DLR_SUPPORTED_CODE
		, DLR_CITY_CODE
		, DLR_CITY_NAME
		, DLR_REGION_CODE
		, DLR_REGION_NAME
		, DLR_SPACE_CODE
		, DLR_SPACE_NAME
		, DLR_AREA_USED_START_TIME
		, DLR_AREA_USED_END_TIME
		, DLR_AREA_USED_LENGTH_OF_TIME
		, DLR_ADDRESS_DETAIL
		, ADDRESS_TYPE_CODE
		, ADDRESS_TYPE_NAME
		, ACTIVITY_TYPE_CODE
		, ACTIVITY_TYPE_NAME
		, ACTIVITY_SUBTYPE_CODE
		, ACTIVITY_SUBTYPE_NAME
		, ACTIVITY_RESOURCE_CODE
		, ACTIVITY_RESOURCE_NAME
		, TIPS_TIME_CODE
		, TIPS_TIME_NAME
		, APPLY_CANCEL_TIME_CODE
		, APPLY_CANCEL_TIME_NAME
		, NUMBER_OF_PERSON_SUPPORTED
		, TEST_DRIVE_SUPPORTED
		, BUDGET
		, BUDGET_DETAIL
		, BEGIN_TIME
		, END_TIME
		, APPLY_BEGIN_TIME
		, APPLY_END_TIME
		, PUBLISH_TIME
		, ACTIVITY_COVER_PAGE_URL
		, ACTIVITY_INTRODUCTION
		, ACTIVITY_KINDLY_REMINDER
		
		, RESOURCE_ADVISER
		, RESOURCE_CAR
		, RESOURCE_SUPPORTED_TIME_START
		, RESOURCE_SUPPORTED_TIME_END
		, ACTUAL_COST
		, FAPIAO
		, HX_STATUS_CODE
		, HX_STATUS_NAME
		
		, COLUMN1
		, COLUMN2
		, COLUMN3
		, COLUMN4
		, COLUMN5
		, COLUMN6
		, COLUMN7
		, COLUMN8
		, COLUMN9
		, COLUMN10
		, COLUMN11
		, COLUMN12
		, COLUMN13
		, COLUMN14
		, COLUMN15
		, COLUMN16
		, COLUMN17
		, COLUMN18
		, COLUMN19
		, COLUMN20
		, COLUMN21
		, COLUMN22
		, COLUMN23
		, COLUMN24
		, COLUMN25
		, COLUMN26
		, COLUMN27
		, COLUMN28
		, COLUMN29
		, COLUMN30
		, EXTENDS_JSON
		, OEM_ID
		, GROUP_ID
		, CREATOR
		, CREATED_NAME
		, CREATED_DATE
		, MODIFIER
		, MODIFY_NAME
		, LAST_UPDATED_DATE
		, IS_ENABLE
		, UPDATE_CONTROL_ID
		, INFO_CHAN_D_CODE
		, INFO_CHAN_D_NAME
		, INFO_CHAN_D_DESC
		 ,ACTIVITY_SUPPORT
	 ,EXHIBITION_EQUIPMENT
		 ,EXHIBITION_EQUIPMENT_NAME
			,EXHIBITION_EQUIPMENT_AMOUNT
		) values (
		#{param.activityId}
		, #{param.activityName}
		, #{param.activityPurpose}
		, #{param.activityPurposeCode}
		, #{param.createTypeCode}
		, #{param.createTypeName}
		, #{param.statusCode}
		, #{param.statusName}
		, #{param.releaseStatusCode}
		, #{param.releaseStatusName}
		, #{param.dlrCode}
		, #{param.dlrShortName}
		, #{param.dlrSupportedShortName}
		, #{param.dlrSupportedCode}
		, #{param.dlrCityCode}
		, #{param.dlrCityName}
		, #{param.dlrRegionCode}
		, #{param.dlrRegionName}
		, #{param.dlrSpaceCode}
		, #{param.dlrSpaceName}
		, #{param.dlrAreaUsedStartTime}
		, #{param.dlrAreaUsedEndTime}
		, #{param.dlrAreaUsedLengthOfTime}
		, #{param.dlrAddressDetail}
		, #{param.addressTypeCode}
		, #{param.addressTypeName}
		, #{param.activityTypeCode}
		, #{param.activityTypeName}
		, #{param.activitySubtypeCode}
		, #{param.activitySubtypeName}
		, #{param.activityResourceCode}
		, #{param.activityResourceName}
		, #{param.tipsTimeCode}
		, #{param.tipsTimeName}
		, #{param.applyCancelTimeCode}
		, #{param.applyCancelTimeName}
		, #{param.numberOfPersonSupported}
		, #{param.testDriveSupported}
		, #{param.budget}
		, #{param.budgetDetail}
		, #{param.beginTime}
		, #{param.endTime}
		, #{param.applyBeginTime}
		, #{param.applyEndTime}
		, #{param.publishTime}
		, #{param.activityCoverPageUrl}
		, #{param.activityIntroduction}
		, #{param.activityKindlyReminder}
		
		, #{param.resourceAdviser}
		, #{param.resourceCar}
		, #{param.resourceSupportedTimeStart}
		, #{param.resourceSupportedTimeEnd}
		, #{param.actualCost}
		, #{param.fapiao}
		, #{param.hxStatusCode}
		, #{param.hxStatusName}
		
		, #{param.column1}
		, #{param.column2}
		, #{param.column3}
		, #{param.column4}
		, #{param.column5}
		, #{param.column6}
		, #{param.column7}
		, #{param.column8}
		, #{param.column9}
		, #{param.column10}
		, #{param.column11}
		, #{param.column12}
		, #{param.column13}
		, #{param.column14}
		, #{param.column15}
		, #{param.column16}
		, #{param.column17}
		, #{param.column18}
		, #{param.column19}
		, #{param.column20}
		, #{param.column21}
		, #{param.column22}
		, #{param.column23}
		, #{param.column24}
		, #{param.column25}
		, #{param.column26}
		, #{param.column27}
		, #{param.column28}
		, #{param.column29}
		, #{param.column30}
		, #{param.extendsJson}
		, #{param.oemId}
		, #{param.groupId}
		, #{param.creator}
		, #{param.createdName}
		, #{param.createdDate}
		, #{param.modifier}
		, #{param.modifyName}
		, #{param.lastUpdatedDate}
		, #{param.isEnable}
		, #{param.updateControlId}
		, #{param.infoChanDCode}
		, #{param.infoChanDName}
		, #{param.infoChanDDesc}
				 , #{param.activitySupport}
				 , #{param.exhibitionEquipment}
				 , #{param.exhibitionEquipmentName}
				 , #{param.exhibitionEquipmentAmount}
		)
	</insert>

	<!-- 修改 -->
	<update id="updateAccBuActivity">
		update t_acc_bu_activity
		<set>

			<if test="param.activityId !=null and param.activityId !=''"> ACTIVITY_ID=#{param.activityId},</if>
			<if test="param.activityName !=null and param.activityName !=''"> ACTIVITY_NAME=#{param.activityName},</if>
			<if test="param.activityPurpose !=null and param.activityPurpose !=''"> ACTIVITY_PURPOSE=#{param.activityPurpose},</if>
			<if test="param.activityPurposeCode !=null and param.activityPurposeCode !=''"> ACTIVITY_PURPOSE_CODE=#{param.activityPurposeCode},</if>
			<if test="param.createTypeCode !=null and param.createTypeCode !=''"> CREATE_TYPE_CODE=#{param.createTypeCode},</if>
			<if test="param.createTypeName !=null and param.createTypeName !=''"> CREATE_TYPE_NAME=#{param.createTypeName},</if>
			<if test="param.statusCode !=null and param.statusCode !=''"> STATUS_CODE=#{param.statusCode},</if>
			<if test="param.statusName !=null and param.statusName !=''"> STATUS_NAME=#{param.statusName},</if>
			<if test="param.releaseStatusCode !=null and param.releaseStatusCode !=''"> RELEASE_STATUS_CODE=#{param.releaseStatusCode},</if>
			<if test="param.releaseStatusName !=null and param.releaseStatusName !=''"> RELEASE_STATUS_NAME=#{param.releaseStatusName},</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
			<if test="param.dlrShortName !=null and param.dlrShortName !=''"> DLR_SHORT_NAME=#{param.dlrShortName},</if>
			<if test="param.dlrSupportedShortName !=null and param.dlrSupportedShortName !=''"> DLR_SUPPORTED_SHORT_NAME=#{param.dlrSupportedShortName},</if>
			<if test="param.dlrSupportedCode !=null and param.dlrSupportedCode !=''"> DLR_SUPPORTED_CODE=#{param.dlrSupportedCode},</if>
			<if test="param.dlrCityCode !=null and param.dlrCityCode !=''"> DLR_CITY_CODE=#{param.dlrCityCode},</if>
			<if test="param.dlrCityName !=null and param.dlrCityName !=''"> DLR_CITY_NAME=#{param.dlrCityName},</if>
			<if test="param.dlrRegionCode !=null and param.dlrRegionCode !=''"> DLR_REGION_CODE=#{param.dlrRegionCode},</if>
			<if test="param.dlrRegionName !=null and param.dlrRegionName !=''"> DLR_REGION_NAME=#{param.dlrRegionName},</if>
			<if test="param.dlrSpaceCode !=null and param.dlrSpaceCode !=''"> DLR_SPACE_CODE=#{param.dlrSpaceCode},</if>
			<if test="param.dlrSpaceName !=null and param.dlrSpaceName !=''"> DLR_SPACE_NAME=#{param.dlrSpaceName},</if>
			<if test="param.dlrAreaUsedStartTime !=null"> DLR_AREA_USED_START_TIME=#{param.dlrAreaUsedStartTime},</if>
			<if test="param.dlrAreaUsedEndTime !=null"> DLR_AREA_USED_END_TIME=#{param.dlrAreaUsedEndTime},</if>
			<if test="param.dlrAreaUsedLengthOfTime !=null and param.dlrAreaUsedLengthOfTime !=''"> DLR_AREA_USED_LENGTH_OF_TIME=#{param.dlrAreaUsedLengthOfTime},</if>
			<if test="param.dlrAddressDetail !=null"> DLR_ADDRESS_DETAIL=#{param.dlrAddressDetail},</if>
			<if test="param.addressTypeCode !=null and param.addressTypeCode !=''"> ADDRESS_TYPE_CODE=#{param.addressTypeCode},</if>
			<if test="param.addressTypeName !=null and param.addressTypeName !=''"> ADDRESS_TYPE_NAME=#{param.addressTypeName},</if>
			<if test="param.activityTypeCode !=null and param.activityTypeCode !=''"> ACTIVITY_TYPE_CODE=#{param.activityTypeCode},</if>
			<if test="param.activityTypeName !=null and param.activityTypeName !=''"> ACTIVITY_TYPE_NAME=#{param.activityTypeName},</if>
			<if test="param.activitySubtypeCode !=null and param.activitySubtypeCode !=''"> ACTIVITY_SUBTYPE_CODE=#{param.activitySubtypeCode},</if>
			<if test="param.activitySubtypeName !=null and param.activitySubtypeName !=''"> ACTIVITY_SUBTYPE_NAME=#{param.activitySubtypeName},</if>
			<if test="param.activityResourceCode !=null and param.activityResourceCode !=''"> ACTIVITY_RESOURCE_CODE=#{param.activityResourceCode},</if>
			<if test="param.activityResourceName !=null and param.activityResourceName !=''"> ACTIVITY_RESOURCE_NAME=#{param.activityResourceName},</if>
			<if test="param.tipsTimeCode !=null and param.tipsTimeCode !=''"> TIPS_TIME_CODE=#{param.tipsTimeCode},</if>
			<if test="param.tipsTimeName !=null and param.tipsTimeName !=''"> TIPS_TIME_NAME=#{param.tipsTimeName},</if>
			<if test="param.applyCancelTimeCode !=null and param.applyCancelTimeCode !=''"> APPLY_CANCEL_TIME_CODE=#{param.applyCancelTimeCode},</if>
			<if test="param.applyCancelTimeName !=null and param.applyCancelTimeName !=''"> APPLY_CANCEL_TIME_NAME=#{param.applyCancelTimeName},</if>
			<if test="param.numberOfPersonSupported !=null"> NUMBER_OF_PERSON_SUPPORTED=#{param.numberOfPersonSupported},</if>
			<if test="param.testDriveSupported !=null"> TEST_DRIVE_SUPPORTED=#{param.testDriveSupported},</if>
			<if test="param.budget !=null and param.budget !=''"> BUDGET=#{param.budget},</if>
			<if test="param.budgetDetail !=null and param.budgetDetail !=''"> BUDGET_DETAIL=#{param.budgetDetail},</if>
			<if test="param.beginTime !=null"> BEGIN_TIME=#{param.beginTime},</if>
			<if test="param.endTime !=null"> END_TIME=#{param.endTime},</if>
			<if test="param.applyBeginTime !=null"> APPLY_BEGIN_TIME=#{param.applyBeginTime},</if>
			<if test="param.applyEndTime !=null"> APPLY_END_TIME=#{param.applyEndTime},</if>
			<if test="param.publishTime !=null"> PUBLISH_TIME=#{param.publishTime},</if>
			<if test="param.activityCoverPageUrl !=null and param.activityCoverPageUrl !=''"> ACTIVITY_COVER_PAGE_URL=#{param.activityCoverPageUrl},</if>
			<if test="param.activityIntroduction !=null and param.activityIntroduction !=''"> ACTIVITY_INTRODUCTION=#{param.activityIntroduction},</if>
			<if test="param.activityKindlyReminder !=null and param.activityKindlyReminder !=''"> ACTIVITY_KINDLY_REMINDER=#{param.activityKindlyReminder},</if>
			
			<if test="param.resourceAdviser !=null and param.resourceAdviser !=''"> RESOURCE_ADVISER=#{param.resourceAdviser},</if>
			<if test="param.resourceCar !=null and param.resourceCar !=''"> RESOURCE_CAR=#{param.resourceCar},</if>
			<if test="param.resourceSupportedTimeStart !=null"> RESOURCE_SUPPORTED_TIME_START=#{param.resourceSupportedTimeStart},</if>
			<if test="param.resourceSupportedTimeEnd !=null"> RESOURCE_SUPPORTED_TIME_END=#{param.resourceSupportedTimeEnd},</if>
			<if test="param.actualCost !=null and param.actualCost !=''"> ACTUAL_COST=#{param.actualCost},</if>
			<if test="param.fapiao !=null and param.fapiao !=''"> FAPIAO=#{param.fapiao},</if>
			<if test="param.hxStatusCode !=null and param.hxStatusCode !=''"> HX_STATUS_CODE=#{param.hxStatusCode},</if>
			<if test="param.hxStatusName !=null and param.hxStatusName !=''"> HX_STATUS_NAME=#{param.hxStatusName},</if>
			
			<if test="param.column1 !=null and param.column1 !=''"> COLUMN1=#{param.column1},</if>
			<if test="param.column2 !=null and param.column2 !=''"> COLUMN2=#{param.column2},</if>
			<if test="param.column3 !=null and param.column3 !=''"> COLUMN3=#{param.column3},</if>
			<if test="param.column4 !=null and param.column4 !=''"> COLUMN4=#{param.column4},</if>
			<if test="param.column5 !=null"> COLUMN5=#{param.column5},</if>
			<if test="param.column6 !=null and param.column6 !=''"> COLUMN6=#{param.column6},</if>
			<if test="param.column7 !=null and param.column7 !=''"> COLUMN7=#{param.column7},</if>
			<if test="param.column8 !=null"> COLUMN8=#{param.column8},</if>
			<if test="param.column9 !=null"> COLUMN9=#{param.column9},</if>
			<if test="param.column10 !=null"> COLUMN10=#{param.column10},</if>
			<if test="param.column11 !=null and param.column11 !=''"> COLUMN11=#{param.column11},</if>
			<if test="param.column12 !=null"> COLUMN12=#{param.column12},</if>
			<if test="param.column13 !=null and param.column13 !=''"> COLUMN13=#{param.column13},</if>
			<if test="param.column14 !=null and param.column14 !=''"> COLUMN14=#{param.column14},</if>
			<if test="param.column15 !=null and param.column15 !=''"> COLUMN15=#{param.column15},</if>
			<if test="param.column16 !=null and param.column16 !=''"> COLUMN16=#{param.column16},</if>
			<if test="param.column17 !=null and param.column17 !=''"> COLUMN17=#{param.column17},</if>
			<if test="param.column18 !=null and param.column18 !=''"> COLUMN18=#{param.column18},</if>
			<if test="param.column19 !=null and param.column19 !=''"> COLUMN19=#{param.column19},</if>
			<if test="param.column20 !=null and param.column20 !=''"> COLUMN20=#{param.column20},</if>
			<if test="param.column21 !=null and param.column21 !=''"> COLUMN21=#{param.column21},</if>
			<if test="param.column22 !=null and param.column22 !=''"> COLUMN22=#{param.column22},</if>
			<if test="param.column23 !=null and param.column23 !=''"> COLUMN23=#{param.column23},</if>
			<if test="param.column24 !=null and param.column24 !=''"> COLUMN24=#{param.column24},</if>
			<if test="param.column25 !=null and param.column25 !=''"> COLUMN25=#{param.column25},</if>
			<if test="param.column26 !=null and param.column26 !=''"> COLUMN26=#{param.column26},</if>
			<if test="param.column27 !=null and param.column27 !=''"> COLUMN27=#{param.column27},</if>
			<if test="param.column28 !=null and param.column28 !=''"> COLUMN28=#{param.column28},</if>
			<if test="param.column29 !=null and param.column29 !=''"> COLUMN29=#{param.column29},</if>
			<if test="param.column30 !=null and param.column30 !=''"> COLUMN30=#{param.column30},</if>
			<if test="param.temporaryActivityQrCode !=null and param.temporaryActivityQrCode !=''"> TEMPORARY_ACTIVITY_QR_CODE=#{param.temporaryActivityQrCode},</if>
			<if test="param.groupActivityQrCode !=null and param.groupActivityQrCode !=''"> GROUP_ACTIVITY_QR_CODE=#{param.groupActivityQrCode},</if>
			<if test="param.extendsJson !=null and param.extendsJson !=''"> EXTENDS_JSON=#{param.extendsJson},</if>
			<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
			<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
			<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
			<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
			<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
			<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
			<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>
<!--
			<if test="param.lastUpdatedDate !=null"> LAST_UPDATED_DATE=#{param.lastUpdatedDate},</if>
-->
			<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
			<if test="param.closedFlag !=null and param.closedFlag !=''"> CLOSED_FLAG=#{param.closedFlag},</if>
			<if test="param.infoChanDCode !=null and param.infoChanDCode !=''"> INFO_CHAN_D_CODE=#{param.infoChanDCode},</if>
			<if test="param.infoChanDName !=null and param.infoChanDName !=''"> INFO_CHAN_D_NAME=#{param.infoChanDName},</if>
			<if test="param.infoChanDDesc !=null and param.infoChanDDesc !=''"> INFO_CHAN_D_DESC=#{param.infoChanDDesc},</if>
			<if test="param.activitySupport !=null and param.activitySupport !=''"> ACTIVITY_SUPPORT=#{param.activitySupport},</if>
			<if test="param.exhibitionEquipment !=null and param.exhibitionEquipment !=''"> EXHIBITION_EQUIPMENT=#{param.exhibitionEquipment},</if>
			<if test="param.exhibitionEquipmentName !=null and param.exhibitionEquipmentName !=''"> EXHIBITION_EQUIPMENT_NAME=#{param.exhibitionEquipmentName},</if>
			<if test="param.exhibitionEquipmentAmount !=null and param.exhibitionEquipmentAmount !=''"> EXHIBITION_EQUIPMENT_AMOUNT=#{param.exhibitionEquipmentAmount},</if>
<!--
			<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId},</if>
-->
			<!--代理商用户运营活动核销字段 2024-06-->
			<if test="param.actualUGC !=null and param.actualUGC !=''"> ACTUAL_UGC=#{param.actualUGC},</if>
			<if test="param.actualPeopleNumber !=null and param.actualPeopleNumber !=''"> ACTUAL_PEOPLE_NUMBER=#{param.actualPeopleNumber},</if>
			<if test="param.actualClue !=null and param.actualClue !=''"> ACTUAL_CLUE=#{param.actualClue},</if>
			LAST_UPDATED_DATE=sysdate(),
			UPDATE_CONTROL_ID=uuid()
		</set>
		where 1=1
		and ACTIVITY_ID=#{param.activityId}
	</update>
	
	<!-- 相同门店，时间段重复 -->
	<select id="activityRepeat" resultType="Map">
		select t1.ACTIVITY_ID,t1.ACTIVITY_NAME,t1.DLR_CODE,t1.BEGIN_TIME,t1.END_TIME
		from t_acc_bu_activity t1
		where  t1.IS_ENABLE = 1 
		and t1.CREATE_TYPE_CODE in ('APP', 'DLR', 'AREA', 'BIGMAN')
		and concat(',', #{param.dlrCode}, ',') regexp concat(',', REPLACE(DLR_CODE, ',', ',|,'), ',') 
		<![CDATA[and (t1.STATUS_CODE = '1' or  (t1.STATUS_CODE = '2' 
					and ( t1.RELEASE_STATUS_CODE = '0' or (t1.RELEASE_STATUS_CODE = '1' and now() < t1.END_TIME) )
					)
		)]]>
		<![CDATA[and ( 
			(#{param.dlrAreaUsedStartTime} <= t1.DLR_AREA_USED_START_TIME and #{param.dlrAreaUsedEndTime} > t1.DLR_AREA_USED_START_TIME) 
			or (#{param.dlrAreaUsedStartTime} >= t1.DLR_AREA_USED_START_TIME and #{param.dlrAreaUsedEndTime} <= t1.DLR_AREA_USED_END_TIME) 
			or (#{param.dlrAreaUsedStartTime} < t1.DLR_AREA_USED_END_TIME and #{param.dlrAreaUsedEndTime} >= t1.DLR_AREA_USED_END_TIME)
		) 
		]]>
		and ',1,2,3,' regexp concat(',', REPLACE(t1.DLR_SPACE_CODE, ',', ',|,'), ',') 
		and concat(',', #{param.dlrSpaceCode}, ',') regexp concat(',', REPLACE(t1.DLR_SPACE_CODE, ',', ',|,' ), ',') 
	</select>
	
	<select id="selectByPage" resultType="Map">
		select <include refid="Base_Column_List"></include>
		from t_acc_bu_activity t1
		where 1=1
		<include refid="where_condition"></include>
	</select>
	
	<select id="selectActivityPage" resultType="Map">
		select 
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, (CASE t1.CREATE_TYPE_CODE WHEN 'DEVELOP' THEN '代理商活动' ELSE t1.CREATE_TYPE_NAME END) AS  CREATE_TYPE_NAME
		<!--, t1.CREATE_TYPE_NAME-->
		, (CASE t1.CREATE_TYPE_CODE WHEN 'DEVELOP' THEN '代理商活动' WHEN 'AGENT' THEN '代理商用户运营活动' ELSE '总部用户运营活动' END) AS  activityType
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, CONCAT(t1.BEGIN_TIME,'-',t1.END_TIME) ACTIVITY_TIME
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
		, t1.CLOSED_FLAG
		, t1.INFO_CHAN_D_DESC
<!--		,IFNULL(s.customerNum,0) as customerNum-->
<!--		,IFNULL(s1.customerCheckNum,0) as customerCheckNum-->
		,a.SH_TIME as activityApprovedTime
		,a.SH_PERSON_NAME as activityApprovedPersonName
		,a1.SH_TIME as endApprovedTime
		,a1.SH_PERSON_NAME as endApprovedPersonName
		,TEMPORARY_ACTIVITY_QR_CODE
		,GROUP_ACTIVITY_QR_CODE,
		t1.budget,
		t1.ACTIVITY_SUPPORT,
	  	case when t1.ACTIVITY_SUPPORT = '1' then '是'  else '否' end	 ACTIVITY_SUPPORT_CN,
	  	case when t1.EXHIBITION_EQUIPMENT = '1' then '是'  else '否' end	 EXHIBITION_EQUIPMEN_CN,
		t1.EXHIBITION_EQUIPMENT_NAME,
		t1.EXHIBITION_EQUIPMENT_AMOUNT,
		t1.EXHIBITION_EQUIPMENT,
		t1.ACTIVITY_KINDLY_REMINDER
		from t_acc_bu_activity t1
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' GROUP BY ACTIVITY_ID ) s ON s.ACTIVITY_ID = t1.ACTIVITY_ID-->
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerCheckNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' and IS_CHECK_IN='1' GROUP BY ACTIVITY_ID ) s1 ON s1.ACTIVITY_ID = t1.ACTIVITY_ID-->
		left join t_sac_common_audit a on a.BILL_CODE=t1.ACTIVITY_ID and a.SH_STATUS='1' and a.NODE_CODE='HostSh' and a.BILL_TYPE='ACTION'
		left join t_sac_common_audit a1 on a1.BILL_CODE=t1.ACTIVITY_ID and a1.SH_STATUS='1' and a1.NODE_CODE='MoneySh' and a1.BILL_TYPE='ACTIONHX'
		where 1=1
		<include refid="where_condition"></include>
		<if test="param.StationFlag !=null and param.StationFlag !='' and param.StationFlag == 1 ">
		  and t1.DLR_CODE in (
		      select d.DLR_CODE from mp.t_usc_mdm_org_dlr d
			left join mp.t_usc_mdm_agent_company c on  c.AGENT_COMPANY_ID=d.COMPANY_ID
			left join mp.t_usc_mdm_agent_info i on i.AGENT_ID=c.AGENT_ID
			where i.AGENT_CODE =#{param.stationCode}   )
		</if>
		<if test="param.agentCompanyCode !=null and param.agentCompanyCode !=''">
			AND t1.DLR_CODE IN (
			SELECT
			d.DLR_CODE
			FROM
			mp.t_usc_mdm_org_dlr d
			LEFT JOIN mp.t_usc_mdm_agent_company c ON c.AGENT_COMPANY_ID = d.COMPANY_ID
			WHERE
			c.AGENT_COMPANY_CODE = #{param.agentCompanyCode}
			)
		</if>
		GROUP BY t1.ACTIVITY_ID
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="marketingActivitie" resultType="Map">
		select
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, (CASE t1.CREATE_TYPE_CODE WHEN 'DEVELOP' THEN '代理商活动' ELSE t1.CREATE_TYPE_NAME END) AS  CREATE_TYPE_NAME
		<!--, t1.CREATE_TYPE_NAME-->
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, CONCAT(t1.BEGIN_TIME,'-',t1.END_TIME) ACTIVITY_TIME
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
		, t1.CLOSED_FLAG
		,t1.TEMPORARY_ACTIVITY_QR_CODE
		,t1.GROUP_ACTIVITY_QR_CODE
		,INFO_CHAN_D_CODE
		,INFO_CHAN_D_NAME
		from t_acc_bu_activity t1
		where
		t1.CREATE_TYPE_CODE = 'DEVELOP'
		<if test="dlrCode !=null and dlrCode!=''">
		and FIND_IN_SET(#{dlrCode},t1.DLR_SUPPORTED_CODE)
		and t1.END_TIME &gt; #{time}
		</if>
		<if test="activityId !=null and activityId!=''">
			and t1.ACTIVITY_ID =#{activityId}
		</if>
		AND t1.STATUS_CODE = '2'
		AND t1.RELEASE_STATUS_CODE = '1'
		AND t1.IS_ENABLE = '1'
		GROUP BY t1.ACTIVITY_ID
		order by t1.LAST_UPDATED_DATE desc
	</select>
	<select id="selectActivityById" resultType="Map">
		select
		ifnull(A.CHECK_IN_NUM, 0) AS CHECK_IN_NUM,/*实际签到人数*/
		t2.ACTIVITY_ID as tempId,
		CONCAT(TRUNCATE(ifnull(ifnull(A.CHECK_IN_NUM, 0)/CAST(ifnull(t1.COLUMN29, '0') AS DECIMAL),0)*100,0),'%') AS CHECK_RATE,/*签约到场率*/
		t1.ACTIVITY_SUPPORT,
		t1.INFO_CHAN_D_DESC,
		t1.EXHIBITION_EQUIPMENT,
		t1.EXHIBITION_EQUIPMENT_NAME,
		t1.EXHIBITION_EQUIPMENT_AMOUNT,
		<include refid="Base_Column_List"></include>
		from t_acc_bu_activity t1
		LEFT JOIN (
		SELECT
		ACC.ACTIVITY_ID,
		/*开发类活动*/
		ifnull( sum( CASE WHEN ACC.IS_CHECK_IN = '1' THEN 1 ELSE 0 END ), 0 ) AS CHECK_IN_NUM,
		/*活动签到数量*/
		ifnull( sum( CASE WHEN ACC.ACTIVITY_ID = CLUE.COLUMN8 THEN 1 ELSE 0 END ), 0 ) AS ACT_CLUE_NUM /*活动报名线索数量*/
		FROM
		csc.t_acc_bu_activity_customer ACC
		LEFT JOIN csc.t_sac_clue_info_dlr CLUE ON ACC.CUSTOMER_PHONE = CLUE.PHONE
		WHERE
		ACC.ACTIVITY_ID = #{param.activityId}
		GROUP BY
		ACC.ACTIVITY_ID
		) A ON t1.ACTIVITY_ID = A.ACTIVITY_ID
		LEFT JOIN t_acc_bu_activity_temp t2 ON t2.ACTIVITY_ID = t1.ACTIVITY_ID
		where
		t1.ACTIVITY_ID=#{param.activityId}
	</select>

	<select id="selectDlrInfoByCode" resultType="java.util.Map">
		SELECT
			DLR_CODE ,
			DLR_SHORT_NAME
		FROM
			mp.t_usc_mdm_org_dlr
		WHERE
			DLR_CODE IN
		<foreach collection="param" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectDlrInfoAll" resultType="java.util.Map">
		SELECT
		DLR_CODE ,
		DLR_SHORT_NAME
		FROM
		mp.t_usc_mdm_org_dlr
		WHERE ONLINE_FLAG = '1'
		and DLR_TYPE not in ('DC','POL')
	</select>
	
	<!-- 活动日历查询-根据场地(门店空间&户外) -->
	<select id="accBuActivityCalenderQuery" resultType="java.util.Map">
		with recursive CALENDER(CALENDER_DATE) as (
		select <![CDATA[@num := #{param.appointmentDateMin}]]>
		union all
		select <![CDATA[@num := DATE_ADD(@num,interval 1 day) from CALENDER where CALENDER_DATE < #{param.appointmentDateMax}]]>
		),
		ACTIVITY_LIST_DLR AS(
			SELECT
			C.CALENDER_DATE,A.ADDRESS_TYPE_CODE,A.ADDRESS_TYPE_NAME,A.ACTIVITY_ID,A.ACTIVITY_NAME,A.DLR_ADDRESS_DETAIL,
			A.DLR_SPACE_CODE,A.DLR_SPACE_NAME,A.DLR_AREA_USED_START_TIME,A.DLR_AREA_USED_END_TIME,
			A.DLR_CITY_CODE,A.DLR_CITY_NAME,A.DLR_REGION_CODE,A.DLR_REGION_NAME,
			A.DLR_CODE,A.DLR_SHORT_NAME,A.BEGIN_TIME,A.END_TIME,
			A.ACTIVITY_START_DATE,A.ACTIVITY_END_DATE,
			IF(DATE(A.ACTIVITY_START_DATE)=C.CALENDER_DATE,TIME_FORMAT(A.ACTIVITY_START_TIME,'%H:%i'),'06:00') AS ACTIVITY_START_TIME,
			IF(DATE(A.ACTIVITY_END_DATE)=C.CALENDER_DATE,TIME_FORMAT(A.ACTIVITY_END_TIME,'%H:%i'),'23:59') AS ACTIVITY_END_TIME,
			CONCAT('报名签到人数(',A.checkNum,'/',A.shouldCome,')') as SIGN_STATUS
			FROM CALENDER C
			LEFT JOIN (
				<!-- 查询活动与活动顾客信息相关联 -->
				SELECT D.ACTIVITY_ID,
				D.ACTIVITY_NAME,D.DLR_SPACE_CODE,D.DLR_SPACE_NAME,D.BEGIN_TIME,D.END_TIME,D.DLR_ADDRESS_DETAIL,
				D.DLR_CODE,D.DLR_SHORT_NAME,D.ADDRESS_TYPE_CODE,D.ADDRESS_TYPE_NAME,
				D.DLR_AREA_USED_START_TIME,D.DLR_AREA_USED_END_TIME,
				D.DLR_CITY_CODE,D.DLR_CITY_NAME,D.DLR_REGION_CODE,D.DLR_REGION_NAME,<!-- 活动所属专营店所处城市、大区  -->
				DATE_FORMAT(D.DLR_AREA_USED_START_TIME,'%Y-%m-%d') AS ACTIVITY_START_DATE,
				DATE_FORMAT(D.DLR_AREA_USED_END_TIME,'%Y-%m-%d') AS ACTIVITY_END_DATE,
				TIME_FORMAT(D.DLR_AREA_USED_START_TIME,'%H:%i') AS ACTIVITY_START_TIME,
				TIME_FORMAT(D.DLR_AREA_USED_END_TIME,'%H:%i') AS ACTIVITY_END_TIME,
				IFNULL(CN.shouldCome,0)AS shouldCome,IFNULL(CN.checkNum,0)AS checkNum
				FROM t_acc_bu_activity D
				LEFT JOIN (
					SELECT SUM(case when B.CUSTOMER_PHONE is not null then 1 else 0 end) AS shouldCome,
					sum(case when B.IS_CHECK_IN='1' then 1 else 0 end)AS checkNum, B.ACTIVITY_ID
					FROM t_acc_bu_activity_customer B GROUP BY B.ACTIVITY_ID)CN ON D.ACTIVITY_ID = CN.ACTIVITY_ID
				WHERE D.RELEASE_STATUS_CODE='1' AND D.CREATE_TYPE_CODE!='DEVELOP' AND D.IS_ENABLE='1'<!-- 只查询发布的活动 -->
			) A <![CDATA[on DATE(A.DLR_AREA_USED_START_TIME)<=C.CALENDER_DATE and DATE(A.DLR_AREA_USED_END_TIME)>=C.CALENDER_DATE]]>
		)
		select * from ACTIVITY_LIST_DLR t
		where 1=1
		<!-- 专营店,活动地点类型,空间编码,区域,城市 -->
		<if test="param.dlrCode !=null and param.dlrCode !=''">and FIND_IN_SET(#{param.dlrCode},t.DLR_CODE)</if>
		<if test="param.addressTypeCode !=null and param.addressTypeCode !=''">and t.ADDRESS_TYPE_CODE=#{param.addressTypeCode}</if>
		<if test="param.dlrSpaceCodeString !=null and param.dlrSpaceCodeString !=''">and concat(',',#{param.dlrSpaceCodeString},',') regexp concat(',',REPLACE(t.DLR_SPACE_CODE,',',',|,'),',')</if>
		<if test="param.dlrRegionCodeString !=null and param.dlrRegionCodeString !=''">and concat(',',#{param.dlrRegionCodeString},',') regexp concat(',',REPLACE(t.DLR_REGION_CODE,',',',|,'),',')</if>
		<if test="param.dlrCityCodeString !=null and param.dlrCityCodeString !=''">and concat(',',#{param.dlrCityCodeString},',') regexp concat(',',REPLACE(t.DLR_CITY_CODE,',',',|,'),',')</if>
		<if test="param.dlrCodeString !=null and param.dlrCodeString !=''">and concat(',',#{param.dlrCodeString},',') regexp concat(',',REPLACE(t.DLR_CODE,',',',|,'),',')</if>
		<!-- 需要根据不一样的角色排序 -->
		order by t.CALENDER_DATE,t.DLR_CODE,t.DLR_AREA_USED_START_TIME
	</select>
	
	<!-- 活动日历查询-按周 -->
	<select id="accBuActivityCalenderQueryByWeek" resultType="java.util.Map">
		with recursive CALENDER(CALENDER_DATE) as (
			select <![CDATA[@num := #{param.appointmentDateMin}]]>
			union all
			select <![CDATA[@num := DATE_ADD(@num,interval 1 day) from CALENDER where CALENDER_DATE < #{param.appointmentDateMax}]]>
		)
		SELECT inner_activity.CALENDER_DATE,
		<![CDATA[IF(inner_activity.INNER_ACTIVITY_SUM>0 ,inner_activity.INNER_ACTIVITY_SUM,0) AS INNER_ACTIVITY_SUM,
		IF(out_activity.OUT_ACTIVITY_SUM>0,out_activity.OUT_ACTIVITY_SUM,0) AS OUT_ACTIVITY_SUM,]]>
		<!-- 当有店内活动时取最晚结束时间 -->
		<![CDATA[IF(inner_activity.INNER_ACTIVITY_SUM>0,inner_activity.MAX_INNER_FINISH_TIME,NULL) AS MAX_INNER_FINISH_TIME,]]>
		<!-- 当有店内活动时取最早开始时间 -->
		<![CDATA[IF(inner_activity.INNER_ACTIVITY_SUM>0,inner_activity.MIN_INNER_START_TIME,NULL) AS MIN_INNER_START_TIME,]]>
		<!-- 当有店外活动时取最早开始时间 -->
		<![CDATA[IF(out_activity.OUT_ACTIVITY_SUM>0,out_activity.MAX_OUT_FINISH_TIME,NULL) AS MAX_OUT_FINISH_TIME,]]>
		<!-- 当有店外活动时取最早开始时间 -->
		<![CDATA[IF(out_activity.OUT_ACTIVITY_SUM>0,out_activity.MIN_OUT_START_TIME,NULL) AS MIN_OUT_START_TIME]]>
		FROM(
			SELECT E.CALENDER_DATE,E.INNER_ACTIVITY_SUM,
			<!-- 跨天时最晚结束时间取当天最晚时间,不跨天则直接取最晚时间 -->
			IF(DATE(E.MAX_FINISH_TIME)=E.CALENDER_DATE,TIME_FORMAT(E.MAX_FINISH_TIME,'%H:%i'),'23:59') AS MAX_INNER_FINISH_TIME,
			<!-- 跨天时最早开始时间取当天最早时间,不跨天则直接取最早时间 -->
			IF(DATE(E.MIN_START_TIME)=E.CALENDER_DATE,TIME_FORMAT(E.MIN_START_TIME,'%H:%i'),'06:00') AS MIN_INNER_START_TIME
			FROM (
				select C.CALENDER_DATE,
				SUM(case when FIND_IN_SET('4',B.DLR_SPACE_CODE)=0 then 1 else 0 end) AS INNER_ACTIVITY_SUM,<!-- 统计店内活动数量 -->
				MAX(B.DLR_AREA_USED_END_TIME) AS MAX_FINISH_TIME,MIN(B.DLR_AREA_USED_START_TIME) AS MIN_START_TIME<!-- 查询最早时间与最晚时间 -->
				from CALENDER C
				LEFT JOIN t_acc_bu_activity B 
				<!-- 查找当天有的活动(包括跨天的活动),且只查询已经发布的店内活动 -->
				<![CDATA[ON(DATE(B.DLR_AREA_USED_START_TIME)<=C.CALENDER_DATE) and DATE(B.DLR_AREA_USED_END_TIME)>=C.CALENDER_DATE]]>
				AND B.RELEASE_STATUS_CODE='1' AND B.CREATE_TYPE_CODE!='DEVELOP' AND B.IS_ENABLE='1'
				AND FIND_IN_SET('4',B.DLR_SPACE_CODE)=0<!-- 门店空间不为户外 -->
				<if test="param.dlrCode !=null and param.dlrCode !=''">and FIND_IN_SET(#{param.dlrCode},B.DLR_CODE)</if>
				<if test="param.dlrSpaceCodeString !=null and param.dlrSpaceCodeString !=''">and concat(',',#{param.dlrSpaceCodeString},',') regexp concat(',',REPLACE(B.DLR_SPACE_CODE,',',',|,'),',')</if>
				<if test="param.dlrRegionCodeString !=null and param.dlrRegionCodeString !=''">and concat(',',#{param.dlrRegionCodeString},',') regexp concat(',',REPLACE(B.DLR_REGION_CODE,',',',|,'),',')</if>
				<if test="param.dlrCityCodeString !=null and param.dlrCityCodeString !=''">and concat(',',#{param.dlrCityCodeString},',') regexp concat(',',REPLACE(B.DLR_CITY_CODE,',',',|,'),',')</if>
				<if test="param.dlrCodeString !=null and param.dlrCodeString !=''">and concat(',',#{param.dlrCodeString},',') regexp concat(',',REPLACE(B.DLR_CODE,',',',|,'),',')</if>
				GROUP BY C.CALENDER_DATE) E
		) inner_activity <!-- 这个是查找店内活动数量以及当天活动最早开始时间与最晚结束时间 -->
		left JOIN( 
			SELECT E.CALENDER_DATE,E.OUT_ACTIVITY_SUM,
			<!-- 跨天时最晚结束时间取当天最晚时间,不跨天则直接取最晚时间 -->
			IF(DATE(E.MAX_FINISH_TIME)=E.CALENDER_DATE,TIME_FORMAT(E.MAX_FINISH_TIME,'%H:%i'),'23:59') AS MAX_OUT_FINISH_TIME,
			<!-- 跨天时最早开始时间取当天最早时间,不跨天则直接取最早时间 -->
			IF(DATE(E.MIN_START_TIME)=E.CALENDER_DATE,TIME_FORMAT(E.MIN_START_TIME,'%H:%i'),'06:00') AS MIN_OUT_START_TIME
			FROM (
				select C.CALENDER_DATE,
				SUM(case when FIND_IN_SET('4',B.DLR_SPACE_CODE) then 1 else 0 end) AS OUT_ACTIVITY_SUM,<!-- 统计店外活动数量 -->
				MAX(B.DLR_AREA_USED_END_TIME) AS MAX_FINISH_TIME,MIN(B.DLR_AREA_USED_START_TIME) AS MIN_START_TIME<!-- 查询最早时间与最晚时间 -->
				from CALENDER C
				LEFT JOIN t_acc_bu_activity B 
				<!-- 查找当天有的活动(包括跨天的活动),且只查询已经发布的店外活动 -->
				<![CDATA[ON(DATE(B.DLR_AREA_USED_START_TIME)<=C.CALENDER_DATE) and DATE(B.DLR_AREA_USED_END_TIME)>=C.CALENDER_DATE]]>
				AND B.RELEASE_STATUS_CODE='1' AND B.CREATE_TYPE_CODE!='DEVELOP' AND B.IS_ENABLE='1'
				AND FIND_IN_SET('4',B.DLR_SPACE_CODE)<!--门店空间为户外-->
				<if test="param.dlrCode !=null and param.dlrCode !=''">and FIND_IN_SET(#{param.dlrCode},B.DLR_CODE)</if>
				<if test="param.dlrSpaceCodeString !=null and param.dlrSpaceCodeString !=''">and concat(',',#{param.dlrSpaceCodeString},',') regexp concat(',',REPLACE(B.DLR_SPACE_CODE,',',',|,'),',')</if>
				<if test="param.dlrRegionCodeString !=null and param.dlrRegionCodeString !=''">and concat(',',#{param.dlrRegionCodeString},',') regexp concat(',',REPLACE(B.DLR_REGION_CODE,',',',|,'),',')</if>
				<if test="param.dlrCityCodeString !=null and param.dlrCityCodeString !=''">and concat(',',#{param.dlrCityCodeString},',') regexp concat(',',REPLACE(B.DLR_CITY_CODE,',',',|,'),',')</if>
				<if test="param.dlrCodeString !=null and param.dlrCodeString !=''">and concat(',',#{param.dlrCodeString},',') regexp concat(',',REPLACE(B.DLR_CODE,',',',|,'),',')</if>
				GROUP BY C.CALENDER_DATE) E
				) out_activity <!-- 这个是查找店外活动数量以及当天活动最早开始时间与最晚结束时间 -->
		on out_activity.CALENDER_DATE=inner_activity.CALENDER_DATE
		<!-- WHERE 1=1
		专营店,活动地点类型,空间编码,区域,城市
		<if test="param.dlrCode !=null and param.dlrCode !=''">and out_activity.DLR_CODE=#{param.dlrCode} and inner_activity.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.addressTypeCode !=null and param.addressTypeCode !=''">and out_activity.ADDRESS_TYPE_CODE=#{param.addressTypeCode} and inner_activity.ADDRESS_TYPE_CODE=#{param.addressTypeCode}</if>
		<if test="param.dlrSpaceCodeString !=null and param.dlrSpaceCodeString !=''">and out_activity.DLR_SPACE_CODE in (${param.dlrSpaceCodeString}) and inner_activity.DLR_SPACE_CODE in (${param.dlrSpaceCodeString})</if>
		<if test="param.dlrRegionCodeString !=null and param.dlrRegionCodeString !=''">and t.DLR_REGION_CODE in (${param.dlrRegionCodeString})</if>
		<if test="param.dlrCityCodeString !=null and param.dlrCityCodeString !=''">and t.DLR_CITY_CODE in (${param.dlrCityCodeString})</if> -->
	</select>
	
	<select id="selectAttachmentByPage" resultType="Map">
		select
		t1.ATTACHMENT_ID, t1.BILL_ID
		, t1.BILL_TYPE, t1.FILE_NAME, t1.FILE_EXTENSION, t1.FILE_PATH
		, t1.OEM_ID, t1.GROUP_ID
		, t1.CREATOR, t1.CREATED_NAME, t1.CREATED_DATE
		, t1.MODIFIER, t1.MODIFY_NAME, t1.LAST_UPDATED_DATE
		, t1.IS_ENABLE, t1.UPDATE_CONTROL_ID
		from t_sac_attachment t1
		where 1=1
		<if test="param.attachmentId !=null and param.attachmentId !=''">and t1.ATTACHMENT_ID=#{param.attachmentId}</if>
		<if test="param.billId !=null and param.billId !=''">and t1.BILL_ID=#{param.billId}</if>
		<if test="param.billType !=null and param.billType !=''">and t1.BILL_TYPE=#{param.billType}</if>
		<if test="param.fileExtension !=null and param.fileExtension !=''">and t1.FILE_EXTENSION=#{param.fileExtension}</if>
		<if test="param.filePath !=null and param.filePath !=''">and t1.FILE_PATH=#{param.filePath}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t1.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t1.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t1.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t1.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null and param.createdDate !=''">and t1.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t1.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t1.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">and t1.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t1.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t1.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.fileName !=null and param.fileName !=''">and t1.FILE_NAME like concat('%',#{param.fileName},'%')</if>
	</select>
	
	<!-- 活动小类查询  queryacsmalltype.do -->
	<select id="queryAcsSmallType" resultType="map">
    SELECT
        s.SMALL_TYPE_ID AS LOOKUP_VALUE_CODE,
        s.SMALL_TYPE_NAME AS LOOKUP_VALUE_NAME,
        s.BIG_TYPE_ID,
        s.BIG_TYPE_NAME
    FROM t_acc_db_activity_smalltype s
    WHERE 1=1
    <if test="param.smallTypeId !=null and param.smallTypeId !=''"> AND s.SMALL_TYPE_ID=#{param.smallTypeId} </if>
    <if test="param.smallTypeName !=null and param.smallTypeName !=''"> AND s.SMALL_TYPE_NAME=#{param.smallTypeName} </if>
    <if test="param.bigTypeId !=null and param.bigTypeId !=''"> AND s.BIG_TYPE_ID=#{param.bigTypeId} </if>
    <if test="param.bigTypeName !=null and param.bigTypeName !=''"> AND s.BIG_TYPE_NAME=#{param.bigTypeName}</if>
</select>

     <select id="queryActivitySubtypeCode"  resultType="map">
		 <if  test="param.type == '1'.toString() or param.type == '0'.toString() "  >
			 SELECT
			 s.SMALL_TYPE_ID AS LOOKUP_VALUE_CODE,
			 s.SMALL_TYPE_NAME AS LOOKUP_VALUE_NAME
			 FROM csc.t_acc_db_activity_smalltype s
			 WHERE 1=1
		 </if>
		 <if test="param.type == '0'.toString()  ">
			 union all
		 </if>

		 <if test="param.type == '2'.toString() or param.type == '0'.toString() ">
			 select
			 LOOKUP_VALUE_CODE,
			 LOOKUP_VALUE_NAME
			 from mp.t_prc_mds_lookup_value
			 where LOOKUP_TYPE_CODE = 'ACTIVITY_SUBTYPE'
			 and ATTRIBUTE1 in ('3','4')
		 </if>

	 </select>


	<select id="queryNeedRealseActivity" resultType="map">
	select 
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, t1.CREATE_TYPE_NAME
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
		from t_acc_bu_activity t1
		<![CDATA[where t1.STATUS_CODE='2' and now()>=t1.PUBLISH_TIME and t1.RELEASE_STATUS_CODE='0' and t1.ACTIVITY_TYPE_CODE!='APP']]>
	</select>

	<select id="selectStatusList" resultType="Map">
		select
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, (CASE t1.CREATE_TYPE_CODE WHEN 'DEVELOP' THEN '代理商活动' ELSE t1.CREATE_TYPE_NAME END) AS  CREATE_TYPE_NAME
		<!--, t1.CREATE_TYPE_NAME-->
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, CONCAT(t1.BEGIN_TIME,'-',t1.END_TIME) ACTIVITY_TIME
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
<!--		,IFNULL(s.customerNum,0) as customerNum-->
<!--		,IFNULL(s1.customerCheckNum,0) as customerCheckNum-->
		,a.SH_TIME as activityApprovedTime
		,a.SH_PERSON_NAME as activityApprovedPersonName
		,a1.SH_TIME as endApprovedTime
		,a1.SH_PERSON_NAME as endApprovedPersonName
		,a2.SH_TIME as areaShApprovedTime
		,a2.SH_PERSON_NAME as areaShApprovedPersonName
		,ifnull(a3.APPLY_PERSON_NAME,ifnull(a4.APPLY_PERSON_NAME,a6.APPLY_PERSON_NAME)) as  areaShApplyPersonName
		,ifnull(a3.APPLY_TIME,ifnull(a4.APPLY_TIME,a6.APPLY_TIME)) as areaShApplyTime
		,a4.SH_TIME as areaHxApprovedTime
		,a4.SH_PERSON_NAME as areaHxApprovedPersonName
		,a5.SH_TIME as hostShApprovedTime
		,a5.SH_PERSON_NAME as hostShApprovedPersonName
		from t_acc_bu_activity t1
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' GROUP BY ACTIVITY_ID ) s ON s.ACTIVITY_ID = t1.ACTIVITY_ID-->
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerCheckNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' and IS_CHECK_IN='1' GROUP BY ACTIVITY_ID ) s1 ON s1.ACTIVITY_ID = t1.ACTIVITY_ID-->
		left join t_sac_common_audit a on a.BILL_CODE=t1.ACTIVITY_ID and a.SH_STATUS='1' and a.NODE_CODE='HostSh' and a.BILL_TYPE='ACTION'
		left join t_sac_common_audit a1 on a1.BILL_CODE=t1.ACTIVITY_ID and a1.SH_STATUS='1' and a1.NODE_CODE='MoneySh' and a1.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a3 on a3.BILL_CODE=t1.ACTIVITY_ID and a3.SH_STATUS='0' and a3.NODE_CODE='AreaSh' and a3.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a6 on a6.BILL_CODE=t1.ACTIVITY_ID and a6.SH_STATUS='2' and a6.NODE_CODE='AreaSh' and a6.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a4 on a4.BILL_CODE=t1.ACTIVITY_ID and a4.SH_STATUS='1' and a4.NODE_CODE='AreaSh' and a4.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a5 on a5.BILL_CODE=t1.ACTIVITY_ID and a5.SH_STATUS='1' and a5.NODE_CODE='HostSh' and a5.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a2 on a2.BILL_CODE=t1.ACTIVITY_ID and a2.SH_STATUS='1' and a2.NODE_CODE='AreaSh' and a2.BILL_TYPE='ACTION'
		where 1=1
		<include refid="where_condition"></include>
		order by a6.LAST_UPDATED_DATE desc
	</select>

	<select id="selectActivitSendMessage" resultType="Map">
		select
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, (CASE t1.CREATE_TYPE_CODE WHEN 'DEVELOP' THEN '代理商活动' ELSE t1.CREATE_TYPE_NAME END) AS  CREATE_TYPE_NAME
		<!--, t1.CREATE_TYPE_NAME-->
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, CONCAT(t1.BEGIN_TIME,'-',t1.END_TIME) ACTIVITY_TIME
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
<!--		,IFNULL(s.customerNum,0) as customerNum-->
<!--		,IFNULL(s1.customerCheckNum,0) as customerCheckNum-->
		,a.SH_TIME as activityApprovedTime
		,a.SH_PERSON_NAME as activityApprovedPersonName
		,a1.SH_TIME as endApprovedTime
		,a1.SH_PERSON_NAME as endApprovedPersonName
		from t_acc_bu_activity t1
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' GROUP BY ACTIVITY_ID ) s ON s.ACTIVITY_ID = t1.ACTIVITY_ID-->
<!--		LEFT JOIN ( SELECT count( 0 ) AS customerCheckNum, ACTIVITY_ID FROM t_acc_bu_activity_customer WHERE IS_ENABLE = '1' and IS_CHECK_IN='1' GROUP BY ACTIVITY_ID ) s1 ON s1.ACTIVITY_ID = t1.ACTIVITY_ID-->
		left join t_sac_common_audit a on a.BILL_CODE=t1.ACTIVITY_ID and a.SH_STATUS='1' and a.NODE_CODE='HostSh' and a.BILL_TYPE='ACTION'
		left join t_sac_common_audit a1 on a1.BILL_CODE=t1.ACTIVITY_ID and a1.SH_STATUS='1' and a1.NODE_CODE='MoneySh' and a1.BILL_TYPE='ACTIONHX'
		where 1=1
		AND t1.CREATE_TYPE_CODE NOT IN ( 'DEVELOP' )
		AND t1.STATUS_CODE = '2'
		and ISNULL(t1.CLOSED_FLAG)
		AND t1.RELEASE_STATUS_CODE = '1'
		AND t1.IS_ENABLE = '1'
		AND t1.END_TIME <![CDATA[ <= ]]> now()
		and t1.END_TIME <![CDATA[ >= ]]> NOW()-INTERVAL 2 HOUR
		and ISNULL(t1.COLUMN30)
		GROUP BY t1.ACTIVITY_ID
		order by t1.LAST_UPDATED_DATE desc
	</select>

	<select id="querySignNum" resultType="int">
		select count(1)+1 from t_acc_bu_activity_customer c left join t_acc_bu_activity a on c.ACTIVITY_ID=a.ACTIVITY_ID
		where   c.is_enable='1'
		<if test="param.customerPhone !=null and param.customerPhone !=''">
			and c.CUSTOMER_PHONE=#{param.customerPhone}
		</if>
		<if test="param.customerId !=null and param.customerId !=''">
			and c.CUSTOMER_ID=#{param.customerId}
		</if>
		<if test="param.createTypeCode !=null and param.createTypeCode =='DEVELOP'">
			and a.CREATE_TYPE_CODE =  #{param.createTypeCode}
		</if>
	</select>

	<insert id="insertAccBuActivityCustomer">
		insert into csc.t_acc_bu_activity_customer(
		ACTIVITY_CUSTOMER_ID,
		CUSTOMER_ID,
		CUSTOMER_NAME,
		CUSTOMER_SEX_CODE,
		CUSTOMER_SEX_NAME,
		CUSTOMER_PHONE,
		CUSTOMER_BIRTH,
		ACTIVITY_ID,
		IS_CHECK_IN,
		COLUMN1,
		COLUMN2,
		COLUMN3,
		COLUMN4,
		COLUMN5,
		APPLY_TIME,
		OEM_ID,
		GROUP_ID,
		CREATOR,
		CREATED_NAME,
		CREATED_DATE,
		MODIFIER,
		MODIFY_NAME,
		LAST_UPDATED_DATE,
		UPDATE_CONTROL_ID,
		SIGN_NUM,
		IS_ENABLE,
		SIGN_IN_TIME)
		values
		(
		uuid(),
		null,
		#{custName},
		#{genderCode},
		#{genderName},
		#{phone},
		#{customerBirth},
		#{activityId},
		'1',
		#{column1},
		#{column2},
		#{column3},
		#{column4},
		#{column5},
		now(),
		#{oemId},
		#{groupId},
		#{creator},
		#{createdName},
		now(),
		#{modifier},
		#{modifyName},
		now(),
		uuid(),
		#{signNum},
		'1',
		now())
	</insert>

	<select id="findHeadquartersActivity" resultType="java.util.Map">
		select
			a.ACTIVITY_HEADQUARTERS_ID,
			a.ACTIVITY_HEADQUARTERS_NAME activityName,
			a.ACTIVITY_HEADQUARTERS_URL activityUrl,
			a.DLR_CODE,
			a.EMP_CODE,
			a.STATUS,
			a.UPDATE_CONTROL_ID,
			c.EMP_NAME,
			b.DLR_SHORT_NAME
			from csc.t_acc_bu_activity_headquarters a
		left join mp.t_usc_mdm_org_dlr b on a.DLR_CODE=b.DLR_CODE
		left join mp.t_usc_mdm_org_employee c on c.EMP_CODE=a.EMP_CODE
		<trim prefix="WHERE" prefixOverrides="AND |OR ">
			a.STATUS='1' and a.IS_ENABLE='1'
			<if test="activityName != null and activityName != ''">
				AND a.ACTIVITY_HEADQUARTERS_NAME like concat('%',#{activityName},'%') </if>
			<if test="dlrCode != null and dlrCode != ''">
				AND a.DLR_CODE =#{dlrCode} </if>
			<if test="empCode != null and empCode != ''">
				AND a.EMP_CODE =#{empCode} </if>
		</trim>
	</select>

	<select id="findDlr" resultType="java.lang.String">
		select DLR_CODE  from mp.t_usc_mdm_org_dlr where ONLINE_FLAG='1' and IS_ENABLE='1'
	</select>

	<select id="findDrlEmp" resultType="boolean">
		SELECT EXISTS(
		SELECT  1
		FROM mp.t_usc_mdm_org_employee
		where DLR_CODE=#{dlrCode} and EMP_CODE=#{empCode}
		)
	</select>

	<insert id="insertHeadquartersActivity">
		insert into csc.t_acc_bu_activity_headquarters(
			ACTIVITY_HEADQUARTERS_ID,
			ACTIVITY_HEADQUARTERS_NAME,
			ACTIVITY_HEADQUARTERS_URL,
			DLR_CODE,
			EMP_CODE,
			STATUS,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			IS_ENABLE,
			UPDATE_CONTROL_ID
	)values
		<foreach collection="list" item="item" index="index" separator=",">
			(
			uuid(),
			#{item.activityName},
			#{item.activityUrl},
			#{item.dlrCode},
			#{item.empCode},
			'1',
			#{item.creatorId},
			#{item.creator},
			now(),
			#{item.creatorId},
			#{item.creator},
			now(),
			'1',
			uuid()
			)
		</foreach>
	</insert>

	<update id="delActivity">
		update csc.t_acc_bu_activity_headquarters
			set STATUS='0',
				MODIFIER=#{modifierId},
				MODIFY_NAME=#{modifier},
				LAST_UPDATED_DATE=now(),
				UPDATE_CONTROL_ID=uuid()
		where ACTIVITY_HEADQUARTERS_ID=#{activityHeadquartersId}
	</update>

	<select id="findRjectNode" resultType="java.util.Map">
			select NODE_CODE,SH_PERSON_NAME,SH_TIME,SH_DESC,BUSINESS_TYPE  from t_sac_common_audit  where   BILL_CODE=#{activityId} and SH_STATUS='2'  order by LAST_UPDATED_DATE desc limit 1
	</select>

	<select id="selectAgentStatusList" resultType="java.util.Map">
		select
		t1.ACTIVITY_ID
		, t1.ACTIVITY_NAME
		, t1.ACTIVITY_PURPOSE
		, t1.CREATE_TYPE_CODE
		, (CASE t1.CREATE_TYPE_CODE WHEN 'AGENT' THEN '代理商用户运营活动' ELSE t1.CREATE_TYPE_NAME END) AS  CREATE_TYPE_NAME
		, t1.STATUS_CODE
		, t1.STATUS_NAME
		, t1.HX_STATUS_NAME
		, t1.HX_STATUS_CODE
		, t1.RELEASE_STATUS_CODE
		, t1.RELEASE_STATUS_NAME
		, t1.DLR_CODE, t1.DLR_SHORT_NAME
		, t1.DLR_CITY_CODE, t1.DLR_CITY_NAME
		, t1.DLR_REGION_CODE, t1.DLR_REGION_NAME
		, t1.ADDRESS_TYPE_CODE
		, t1.ADDRESS_TYPE_NAME
		, t1.DLR_ADDRESS_DETAIL
		, t1.ACTIVITY_TYPE_CODE
		, t1.ACTIVITY_TYPE_NAME
		, t1.ACTIVITY_SUBTYPE_CODE
		, t1.ACTIVITY_SUBTYPE_NAME
		, t1.NUMBER_OF_PERSON_SUPPORTED
		, CONCAT(t1.BEGIN_TIME,'-',t1.END_TIME) ACTIVITY_TIME
		, t1.BEGIN_TIME
		, t1.END_TIME
		, t1.PUBLISH_TIME
		, t1.APPLY_END_TIME
		, t1.ACTIVITY_COVER_PAGE_URL
		, t1.CREATOR
		, t1.CREATED_NAME
		, t1.CREATED_DATE
		, t1.LAST_UPDATED_DATE
		, t1.UPDATE_CONTROL_ID
		,a.SH_TIME as bigManShApprovedTime
		,a.SH_PERSON_NAME as bigManShApprovedPersonName
		,a1.SH_TIME as endApprovedTime
		,a1.SH_PERSON_NAME as endApprovedPersonName
		,ifnull(a3.APPLY_PERSON_NAME,ifnull(a4.APPLY_PERSON_NAME,a6.APPLY_PERSON_NAME)) as  areaShApplyPersonName
		,ifnull(a3.APPLY_TIME,ifnull(a4.APPLY_TIME,a6.APPLY_TIME)) as areaShApplyTime
		,a4.SH_TIME as bigManHxApprovedTime
		,a4.SH_PERSON_NAME as bigManHxApprovedPersonName
		,a5.SH_TIME as hostOoShApprovedTime
		,a5.SH_PERSON_NAME as hostOoShApprovedPersonName
		from t_acc_bu_activity t1
		left join t_sac_common_audit a on a.BILL_CODE=t1.ACTIVITY_ID and a.SH_STATUS='1' and a.NODE_CODE='BigManSh' and a.BILL_TYPE='ACTION'
		left join t_sac_common_audit a1 on a1.BILL_CODE=t1.ACTIVITY_ID and a1.SH_STATUS='1' and a1.NODE_CODE='MoneySh' and a1.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a3 on a3.BILL_CODE=t1.ACTIVITY_ID and a3.SH_STATUS='0' and a3.NODE_CODE='BigManHxSh' and a3.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a6 on a6.BILL_CODE=t1.ACTIVITY_ID and a6.SH_STATUS='2' and a6.NODE_CODE='BigManHxSh' and a6.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a4 on a4.BILL_CODE=t1.ACTIVITY_ID and a4.SH_STATUS='1' and a4.NODE_CODE='BigManHxSh' and a4.BILL_TYPE='ACTIONHX'
		left join t_sac_common_audit a5 on a5.BILL_CODE=t1.ACTIVITY_ID and a5.SH_STATUS='1' and a5.NODE_CODE='HostOoSh' and a5.BILL_TYPE='ACTIONHX'
		where 1=1
		<include refid="where_condition"></include>
		order by a6.LAST_UPDATED_DATE desc
	</select>

	<select id="selectActivityCounts" resultMap="AccBuActivitySummaryMap">
		SELECT
			ACTIVITY_ID,
			SUM(CASE WHEN ACT.CREATE_TYPE_CODE = 'DEVELOP' THEN 1 ELSE 0 END) as dlSignNum,
			SUM(CASE WHEN ACT.CREATE_TYPE_CODE != 'DEVELOP' THEN 1 ELSE 0 END) as yhSignNum
		FROM
			csc.t_acc_bu_activity ACT
		WHERE
			ACT.ACTIVITY_ID IN
			<foreach item="activityId" collection="param.activityIds" index="index" open="(" separator="," close=")">
				#{activityId}
			</foreach>
	</select>
	<select id="accBuActivityNewCalenderQuery"
			resultType="com.ly.mp.acc.manage.entities.vo.ActivityCalenderVO">
		with recursive CALENDER(CALENDER_DATE) as (
		select <![CDATA[@num := #{param.appointmentDateMin}]]>
		union all
		select <![CDATA[@num := DATE_ADD(@num,interval 1 day) from CALENDER where CALENDER_DATE < #{param.appointmentDateMax}]]>
		),
		ACTIVITY_LIST_DLR AS(
		SELECT
		C.CALENDER_DATE,
		    A.ADDRESS_TYPE_CODE,
		    A.ADDRESS_TYPE_NAME,
		    A.ACTIVITY_ID,
		    A.ACTIVITY_NAME,
		    A.DLR_ADDRESS_DETAIL,
			A.DLR_SPACE_CODE,
		    A.DLR_SPACE_NAME,
		    A.DLR_AREA_USED_START_TIME,
		    A.DLR_AREA_USED_END_TIME,
			A.DLR_CITY_CODE,
		    A.DLR_CITY_NAME,
		    A.DLR_REGION_CODE,
		    A.DLR_REGION_NAME,
			A.DLR_CODE,
		    A.DLR_SHORT_NAME,
		    A.BEGIN_TIME,
		    A.END_TIME,
			A.ACTIVITY_START_DATE,
		    A.ACTIVITY_END_DATE,
			IF(DATE(A.ACTIVITY_START_DATE)=C.CALENDER_DATE,TIME_FORMAT(A.ACTIVITY_START_TIME,'%H:%i'),'06:00') AS ACTIVITY_START_TIME,
			IF(DATE(A.ACTIVITY_END_DATE)=C.CALENDER_DATE,TIME_FORMAT(A.ACTIVITY_END_TIME,'%H:%i'),'23:59') AS ACTIVITY_END_TIME
				FROM CALENDER C
				LEFT JOIN (
				SELECT
				D.ACTIVITY_ID,
				D.ACTIVITY_NAME,
				D.DLR_SPACE_CODE,
				D.DLR_SPACE_NAME,
				D.BEGIN_TIME,
				D.END_TIME,
				D.DLR_ADDRESS_DETAIL,
				D.DLR_CODE,
				D.DLR_SHORT_NAME,
				D.ADDRESS_TYPE_CODE,
				D.ADDRESS_TYPE_NAME,
				D.DLR_AREA_USED_START_TIME,
				D.DLR_AREA_USED_END_TIME,
				D.DLR_CITY_CODE,
				D.DLR_CITY_NAME,
				D.DLR_REGION_CODE,
				D.DLR_REGION_NAME,<!-- 活动所属专营店所处城市、大区  -->
				DATE_FORMAT(D.DLR_AREA_USED_START_TIME,'%Y-%m-%d') AS ACTIVITY_START_DATE,
				DATE_FORMAT(D.DLR_AREA_USED_END_TIME,'%Y-%m-%d') AS ACTIVITY_END_DATE,
				TIME_FORMAT(D.DLR_AREA_USED_START_TIME,'%H:%i') AS ACTIVITY_START_TIME,
				TIME_FORMAT(D.DLR_AREA_USED_END_TIME,'%H:%i') AS ACTIVITY_END_TIME
				FROM t_acc_bu_activity D
				WHERE
 				<if test="param.dlrCode !=null and param.dlrCode !=''">
					D.DLR_CODE = #{param.dlrCode} and
				</if>
				D.RELEASE_STATUS_CODE='1'
				AND D.CREATE_TYPE_CODE!='DEVELOP'
				AND D.IS_ENABLE='1'
				) A <![CDATA[on DATE(A.DLR_AREA_USED_START_TIME)<=C.CALENDER_DATE and DATE(A.DLR_AREA_USED_END_TIME)>=C.CALENDER_DATE]]>
				)
		    		select * from ACTIVITY_LIST_DLR t
					where 1=1
				<!-- 专营店,活动地点类型,空间编码,区域,城市 -->
				<if test="param.dlrCode !=null and param.dlrCode !=''">
					and FIND_IN_SET(#{param.dlrCode},t.DLR_CODE)
					</if>
				<if test="param.addressTypeCode !=null and param.addressTypeCode !=''">
					and t.ADDRESS_TYPE_CODE=#{param.addressTypeCode}
					</if>
				<if test="param.dlrSpaceCodeString !=null and param.dlrSpaceCodeString !=''">
					and concat(',',#{param.dlrSpaceCodeString},',') regexp concat(',',REPLACE(t.DLR_SPACE_CODE,',',',|,'),',')
					</if>
				<if test="param.dlrRegionCodeString !=null and param.dlrRegionCodeString !=''">
				and concat(',',#{param.dlrRegionCodeString},',') regexp concat(',',REPLACE(t.DLR_REGION_CODE,',',',|,'),',')
					</if>
				<if test="param.dlrCityCodeString !=null and param.dlrCityCodeString !=''">
					and concat(',',#{param.dlrCityCodeString},',') regexp concat(',',REPLACE(t.DLR_CITY_CODE,',',',|,'),',')
					</if>
				<if test="param.dlrCodeString !=null and param.dlrCodeString !=''">
					and concat(',',#{param.dlrCodeString},',') regexp concat(',',REPLACE(t.DLR_CODE,',',',|,'),',')
					</if>
					order by
					    t.CALENDER_DATE,
					    t.DLR_CODE,
					    t.DLR_AREA_USED_START_TIME
	</select>
	<select id="selectActivitySignStatus" resultType="com.ly.mp.acc.manage.entities.vo.ActivitySignVO">
		SELECT
		ACTIVITY_ID as activityId,
		SUM( CASE WHEN CUSTOMER_PHONE IS NOT NULL THEN 1 ELSE 0 END ) as signUpNum,
		sum( CASE WHEN IS_CHECK_IN = '1' THEN 1 ELSE 0 END ) as signInNum
		FROM
		t_acc_bu_activity_customer
		WHERE
		ACTIVITY_ID IN
		<foreach item="activityId" collection="activityIds" index="index" open="(" separator="," close=")">
			#{activityId}
		</foreach>
		GROUP BY ACTIVITY_ID
	</select>

</mapper>