<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.HandleMsgLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.HandleMsgLog">
        <id column="LOG_ID" property="logId" />
        <result column="IN_PARAMETER" property="inParameter" />
        <result column="RETURN_PARAMETER" property="returnParameter" />
        <result column="INSER_DATE" property="inserDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        LOG_ID, IN_PARAMETER, RETURN_PARAMETER, INSER_DATE
    </sql>
    <insert id="insertHandleMsgLog">
        INSERT INTO t_sac_handle_msg_log ( LOG_ID,MSG_CODE ,IN_PARAMETER, RETURN_PARAMETER, INSER_DATE )
        VALUES
            (#{param.logId},#{param.msgCode},#{param.inParameter},#{param.returnParameter},now() )
    </insert>
</mapper>
