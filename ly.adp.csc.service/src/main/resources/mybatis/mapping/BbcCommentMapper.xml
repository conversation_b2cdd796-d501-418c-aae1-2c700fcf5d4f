<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.csc.idal.mapper.BbcCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.csc.entities.BbcComment">
        <id column="COMMENT_ID" property="commentId" />
        <result column="ARTICLE_ID" property="articleId" />
        <result column="COMMENT_PARENTER_ID" property="commentParenterId" />
        <result column="COMMENT_TYPE" property="commentType" />
        <result column="COMMENT_TYPE_NAME" property="commentTypeName" />
        <result column="COMMENT_CONTENT" property="commentContent" />
        <result column="COMMENT_STATUS" property="commentStatus" />
        <result column="SH_PERSON_ID" property="shPersonId" />
        <result column="SH_PERSON_NAME" property="shPersonName" />
        <result column="SH_TIME" property="shTime" />
        <result column="IS_ANONYMOUS" property="isAnonymous" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
    </resultMap>

    <resultMap id="BaseResultMap1" type="java.util.Map">
        <id column="COMMENT_ID" property="commentId" />
        <result column="ARTICLE_ID" property="articleId" />
        <result column="COMMENT_PARENTER_ID" property="commentParenterId" />
        <result column="COMMENT_TYPE" property="commentType" />
        <result column="COMMENT_TYPE_NAME" property="commentTypeName" />
        <result column="COMMENT_CONTENT" property="commentContent" />
        <result column="COMMENT_STATUS" property="commentStatus" />
        <result column="SH_PERSON_ID" property="shPersonId" />
        <result column="SH_PERSON_NAME" property="shPersonName" />
        <result column="SH_TIME" property="shTime" />
        <result column="IS_ANONYMOUS" property="isAnonymous" />
        <result column="REMARK" property="remark" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <collection property="childran" column="{param.commentParenterId=COMMENT_ID,param.page=page,param.isEnable=IS_ENABLE,param.commentStatus=status}"  select="queryBbcComment1"  ofType="java.util.Map">
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COMMENT_ID, ARTICLE_ID, COMMENT_PARENTER_ID, COMMENT_TYPE, COMMENT_TYPE_NAME, COMMENT_CONTENT, COMMENT_STATUS, SH_PERSON_ID, SH_PERSON_NAME, SH_TIME, IS_ANONYMOUS, REMARK, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, UPDATE_CONTROL_ID
    </sql>
   <insert id="saveBbcComment" parameterType="java.util.Map" >
      	INSERT INTO t_sac_bu_bbc_comment`(`COMMENT_ID`, `ARTICLE_ID`, `COMMENT_PARENTER_ID`, `COMMENT_TYPE`, `COMMENT_TYPE_NAME`, `COMMENT_CONTENT`, `COMMENT_STATUS`, `SH_PERSON_ID`, `SH_PERSON_NAME`, `SH_TIME`, `IS_ANONYMOUS`, `REMARK`, `CREATOR`, `CREATED_NAME`, `CREATED_DATE`, `MODIFIER`, `MODIFY_NAME`, `LAST_UPDATED_DATE`, `IS_ENABLE`, `UPDATE_CONTROL_ID`) VALUES
      	(UUID(), #{articleId}, #{commentParenterId}, #{commentType}, #{commentTypeName}, #{commentContent}, #{commentStatus}, #{shPersonId}, #{shPersonName}, #{shTime}, #{isAnonymous}, #{remark}, #{creator}, #{createdName}, #{createdDate}, #{modifier}, #{modifyName}, #{lastUpdatedDate}, #{isEnable}, #{updateControlId});

   </insert>

    <select id="queryCommentStatu" parameterType="java.util.Map" resultType="java.util.Map">
        select  ct.COMMENT_ID,
        ct.ARTICLE_ID,
        ct.COMMENT_PARENTER_ID,
        ct.COMMENT_TYPE,
        ct.COMMENT_TYPE_NAME,
        ct.COMMENT_CONTENT,
        ct.COMMENT_STATUS,
        ct.SH_PERSON_ID,
        ct.SH_PERSON_NAME,
        ct.SH_TIME,
        ct.IS_ANONYMOUS,
        ct.REMARK,
        ct.CREATOR,
        ct.CREATED_NAME,
        ct.CREATED_DATE,
        ct.MODIFIER,
        ct.MODIFY_NAME,
        ct.LAST_UPDATED_DATE,
        ct.IS_ENABLE,
        ct.UPDATE_CONTROL_ID,
        ct.ORDER_ON,
         t2.CREATOR as ctId,
         t2.CREATED_NAME as commentName,
        rea.ORG_NAME
         from (select
         COMMENT_ID,
ARTICLE_ID,
COMMENT_PARENTER_ID,
COMMENT_TYPE,
COMMENT_TYPE_NAME,
COMMENT_CONTENT,
COMMENT_STATUS,
SH_PERSON_ID,
SH_PERSON_NAME,
SH_TIME,
IS_ANONYMOUS,
REMARK,
CREATOR,
CREATED_NAME,
CREATED_DATE,
MODIFIER,
MODIFY_NAME,
LAST_UPDATED_DATE,
IS_ENABLE,
UPDATE_CONTROL_ID,
ORDER_ON,COMMENT_ORG_CODE
         from t_sac_bu_bbc_comment  where   COMMENT_STATUS in
        <foreach collection="param.commentStatus.split(',')"  item="item" separator="," close=")" open="(">
        #{item}
        </foreach>
        and ARTICLE_ID=#{param.articleId} ) as ct
        left join mp.t_usc_mdm_org_relation_real rea on ct.COMMENT_ORG_CODE=rea.ORG_CODE
        left join t_sac_bu_bbc_comment t2 on ct.COMMENT_PARENTER_ID=t2.COMMENT_ID

    </select>

    <select id="queryBbcComment" parameterType="java.util.Map" resultMap="BaseResultMap1">
        select
        ct.COMMENT_ID,
        ct.ARTICLE_ID,
        ct.COMMENT_PARENTER_ID,
        ct.COMMENT_TYPE,
        ct.COMMENT_TYPE_NAME,
        ct.COMMENT_CONTENT,
        ct.COMMENT_STATUS,
        ct.SH_PERSON_ID,
        ct.SH_PERSON_NAME,
        ct.SH_TIME,
        ct.IS_ANONYMOUS,
        ct.REMARK,
        ct.CREATOR,
        ct.CREATED_NAME,
        ct.CREATED_DATE,
        ct.MODIFIER,
        ct.MODIFY_NAME,
        ct.LAST_UPDATED_DATE,
        ct.IS_ENABLE,
        ct.UPDATE_CONTROL_ID,
        ct.ORDER_ON,
        rea.ORG_NAME,
        <if test="param !=null  and param.creator !=null  and param.creator!=''">
            a.ARTICLE_TITELE,
            a.CREATED_NAME as articleName,
            a.CREATED_DATE as articleDate,
            a.ARTICLE_CONTENT,

        </if>
        #{param.commentStatus} as status,
        '1' as page
	from t_sac_bu_bbc_comment ct
        left join mp.t_usc_mdm_org_relation_real rea on ct.COMMENT_ORG_CODE=rea.ORG_CODE
        <if test="param !=null  and param.creator !=null  and param.creator!=''">
left join t_sac_bu_bbc_article a on ct.ARTICLE_ID=a.ARTICLE_ID
        </if>
        where 1=1

        <if test="param !=null  and param.orderOn !=null  and param.orderOn!=''  and (param.commentParenterId ==null  or param.commentParenterId=='')">
            and ORDER_ON &lt;= #{param.orderOn}
        </if>
        <if test="param !=null  and param.orderOn !=null  and param.orderOn!='' and param.commentParenterId !=null  and param.commentParenterId!=''">
            and ORDER_ON &lt;= (select bt.ORDER_ON from  t_sac_bu_bbc_comment bt where bt.COMMENT_ID=#{param.commentParenterId})
        </if>
        <if test="param !=null  and param.creator !=null  and param.creator!=''">
            and  ct.CREATOR=#{param.creator}

        </if>
        <if test="param !=null  and param.isEnable !=null  and param.isEnable!=''" >
            and  ct.IS_ENABLE=#{param.isEnable}
        </if>
	<if test="param !=null  and param.commentStatus !=null  and param.commentStatus!=''">
       <if test="param.commentStatus !='1'.toString()">
           and ct.COMMENT_STATUS=#{param.commentStatus}
           <!--in <foreach collection="param.commentStatus.split(',')" item="item" open="(" close=")" separator=",">
              #{item}
       </foreach>
-->
       </if>
        <if test="param.commentStatus =='2'.toString() and  (param.creator==''   or param.creator ==null) ">
            and ct.COMMENT_PARENTER_ID is null
        </if>
        <if test="param.commentStatus =='1'.toString()">
            and ( exists (
            select 1 from t_sac_bu_bbc_comment cc
            where  cc.COMMENT_PARENTER_ID=ct.COMMENT_ID
            and cc.COMMENT_STATUS='1'
            ) or (ct.COMMENT_PARENTER_ID is null and  ct.COMMENT_STATUS=#{param.commentStatus}) )
        </if>
    </if>
        <if test="param !=null  and param.articleId !=null  and param.articleId!=''">
            and ct.ARTICLE_ID=#{param.articleId}
        </if>
      <!--  <if test="param !=null  and param.commentId !=null  and param.commentId!=''">
         and ct.COMMENT_ID=#{param.commentId}
        </if>-->

     order by ct.CREATED_DATE
        <if test="param !=null  and param.desc !=null  and param.desc!='' ">
            desc
        </if>

 </select>
<select id="queryBbcArticleCount" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(1) from t_sac_bu_bbc_article where ARTICLE_ID=#{param.articleId}
         and CREATOR=#{param.creator}

</select>

    <select id="queryBbcCommentCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from t_sac_bu_bbc_comment where ARTICLE_ID=#{param.articleId}
        <if test="param.creator !=null and param.creator !=''">
            and CREATOR=#{param.creator}
        </if>
        <if test="param.commentStatus !=null and param.commentStatus !=''">
            and COMMENT_STATUS=#{param.commentStatus}
        </if>

    </select>

    <insert id="insertBbcComment" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="orderOn">
    INSERT INTO `t_sac_bu_bbc_comment`
    (`COMMENT_ID`, `ARTICLE_ID`, `COMMENT_PARENTER_ID`, `COMMENT_TYPE`, `COMMENT_TYPE_NAME`, `COMMENT_CONTENT`, `COMMENT_STATUS`, `SH_PERSON_ID`, `SH_PERSON_NAME`, `SH_TIME`, `IS_ANONYMOUS`, `REMARK`, `CREATOR`, `CREATED_NAME`, `CREATED_DATE`, `MODIFIER`, `MODIFY_NAME`, `LAST_UPDATED_DATE`, `IS_ENABLE`, `UPDATE_CONTROL_ID`,ORDER_ON,COMMENT_ORG_CODE) VALUES
    (UUID(), #{articleId}, #{commentParenterId}, #{commentType}, #{commentTypeName}, #{commentContent}, #{commentStatus}, #{shPersonId}, #{shPersonName}, #{shTime}, #{isAnonymous}, #{remark}, #{creator}, #{createdName}, #{createdDate}, #{modifier}, #{modifyName}, #{lastUpdatedDate}, #{isEnable}, #{updateControlId},#{orderOn},#{articleOrgCode});
    </insert>
    <update id="updateBbcComment" parameterType="java.util.Map">
        update t_sac_bu_bbc_comment
        set
        <if test="commentStatus !='' and commentStatus !=null">
            COMMENT_STATUS=#{commentStatus},
        </if>
        <if test="articleOrgCode !='' and articleOrgCode !=null">
            COMMENT_ORG_CODE=#{articleOrgCode},
        </if>
        <if test="shPersonId !='' and shPersonId !=null">
            SH_PERSON_ID=#{shPersonId},
        </if>
        <if test="shPersonName !='' and shPersonName !=null">
            SH_PERSON_NAME=#{shPersonName},
        </if>
        <if test="shTime !=null">
            SH_TIME=#{shTime},
        </if>
        <if test="remark !='' and remark !=null">
            REMARK=#{remark},
        </if>
        `MODIFIER`= #{modifier},
        `MODIFY_NAME`= #{modifyName},
        `LAST_UPDATED_DATE`= #{lastUpdatedDate},
        `IS_ENABLE`= #{isEnable},
        `UPDATE_CONTROL_ID`=UUID()
        where 1=1
        <if test="commentId !=null and commentId !=''">
         and COMMENT_ID=#{commentId}
        </if>
        <if test="parenterId !=null and parenterId!=''  ">
            and COMMENT_PARENTER_ID=#{parenterId}
        </if>

    </update>



    <select id="queryBbcComment1" parameterType="java.util.Map" resultType="java.util.Map">
        select
        ORDER_ON,
        COMMENT_ID,
        ARTICLE_ID,
        COMMENT_PARENTER_ID,
        COMMENT_TYPE,
        COMMENT_TYPE_NAME,
        COMMENT_CONTENT,
        COMMENT_STATUS,
        SH_PERSON_ID,
        SH_PERSON_NAME,
        SH_TIME,
        IS_ANONYMOUS,
        REMARK,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        IS_ENABLE,
        UPDATE_CONTROL_ID
        from t_sac_bu_bbc_comment where 1=1
        <if test=" param.commentParenterId !=null  and param.commentParenterId!=''">
          and COMMENT_PARENTER_ID=#{param.commentParenterId}
        </if>
        <if test=" param.commentParenterId ==null  or param.commentParenterId ==''">
            and false
        </if>
        <if test="param !=null  and param.commentStatus !=null  and param.commentStatus!=''">
         and COMMENT_STATUS=#{param.commentStatus}
        </if>
        <if test="param !=null  and param.isEnable !=null  and param.isEnable!=''">
            and IS_ENABLE=#{param.isEnable,jdbcType=VARCHAR}
        </if>

        order by CREATED_DATE desc
        <if test="param !=null  and param.page !=null  and param.page!=''">
          limit 1
        </if>
    </select>

    <select id="findStation" resultType="java.util.Map">
        select ORG_REAL_ID,ORG_NAME,ORG_CODE   from
            mp.t_usc_mdm_org_relation_real
        where  FIND_IN_SET( ORG_CODE,#{articleOrgCode} )
    </select>
</mapper>
