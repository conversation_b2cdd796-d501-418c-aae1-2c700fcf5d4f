
# 是否开启Feign token兼容模式

mp.component.enable.feign.token=true

# allinone 地址

mp.component.token.url=http://ly-mp-allinone-service.adp-uat.svc:8080

# base地址

ref.url.adp.base=http://adp-java-base-service.adp-uat.svc:8080

refer.bi.baseurl= https://0165f8404b7a41f5b4e24bcddbe72ff4-cn-hangzhou.alicloudapi.com
#CDP加密策略

AESCipherKey=SmartUatTid5

#消息队列 bucn.redis.mq   adp.rabbit

bucn.ms.adapter=adp.rabbit

bucn.rabbitmq.init=true

bucn.rabbitmq.master=master

bucn.rabbitmq.pool.master.host=**************

bucn.rabbitmq.pool.master.port=5672

bucn.rabbitmq.pool.master.username=adp

bucn.rabbitmq.pool.master.password=adp@smart2021

bucn.rabbitmq.pool.master.virtualhost=/adp/public

bucn.rabbitmq.config.minIdle=8

bucn.rabbitmq.config.maxIdle=200

bucn.rabbitmq.config.maxTotal=230

# 过期时间
csc.cache.cluedlrbypage.time=50
csc.cache.departClueTransfer.time=50

#业务策略

csc.clue.addreview.strategy=addReviewDefault

spring.application.name=ly-adp-csc-service

#seata.service.default.grouplist=seata-server.adp-uat.svc:8091
seata.enabled=true
seata.application-id=ly-adp-csc-service
seata.tx-service-group=default_tx_group
seata.scan-packages=com.ly.adp
seata.enable-auto-data-source-proxy=true
seata.data-source-proxy-mode=AT
seata.service.vgroup-mapping.default_tx_group=default
seata.servicemaxFrameLength=16777216
seata.service.default.grouplist=*************:8091
service.vgroupMapping.default_tx_group=*************:8091
#seata.registry.type=file
#seata.config.type=file
#feign.refer

refer.url.adp.clue=http://adp-java-csc-service.adp-uat.svc:8080

refer.url.xapi.api=http://adp-java-xapi-api.adp-uat.svc:8080

refer.url.mp.allinone=http://ly-mp-allinone-cloud-service.adp-uat.svc:8080

#refer.url.adp.base=http://adp-java-base-service.adp-uat.svc:8080

refer.url.adp.base=http://localhost:8088

refer.url.adp.api=http://adp-java-xapi-api.adp-uat.svc:8080

refer.url.image.url=https://adp-uat.smart.cn

refer.url.adp.orc=http://adp-java-orc-service.adp-uat.svc:8080

#分布式的端口

server.port=8081

#Redis配置

#多个servers用逗号(",")隔开,不需要配置redis从机的IP,只需要redis主机IP

#sentinel模式的格式masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,例如mymaster?**************:63793,**************:63794

#redis.session.servers=**************:6379,**************:6379,**************:6379

redis.session.servers=*************:6379,**************:6379

#redis密码,所有redis服务密码必须一样

redis.session.password=adb@smart2021

#redis.servers=**************:6379,**************:6379,**************:6379

redis.servers=*************:6379,**************:6379

redis.password=adb@smart2021

#最大连接线程数

redis.pool.maxActive=10000

#连接超时时间(单位:秒)

redis.pool.timeout=3000

#缓存时间(单位:秒)

redis.pool.expires=86400

#在获取一个jedis实例时，是否提前进行alidate操作；如果为true，则得到的jedis实例均是可用的；

redis.pool.testOnBorrow=true

#在return给pool时，是否提前进行validate操作；

redis.pool.testOnReturn=true

#session time out (单位:秒)

session.timeout=3600

#  redisson分布式锁配置

# 是否启用分布式锁 true:启用 false:不启用,默认不启用, 如果没用到分布式锁 不要启用

redisson.redis.enable=true

# 集群时，需要所有主从节点地址

redisson.redis.servers=*************:6379,**************:6379

redisson.redis.password=adb@smart2021

# 监控锁的看门狗超时（宕机或进程挂了释放锁的超时时间），单位：毫秒。默认值：30000

redisson.redis.lockWatchdogTimeout=20000

# 集群状态扫描间隔时间，单位是毫秒。默认值： 1000

redisson.redis.scanInterval=1000

# 多主节点的环境里，每个 主节点的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64

redisson.redis.masterConnectionPoolSize=64

# 多从节点的环境里，每个 从服务节点里用于普通操作（非 发布和订阅）连接的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64

redisson.redis.slaveConnectionPoolSize=64

#是否启用AMQ(true,false)

mp.component.amqOpen=false

# MQ类型 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (默认使用1:RabbitMQ, 如果没有设置amqType, 为兼容之前版本使用ActiveMQ)

mp.component.amqType=1

mp.component.amqUrl=172.26.165.86:9876;172.26.165.93:9876

# MQ端口，只对RabbitMQ有用

mp.component.amqPort=5672

mp.component.amqUser=rocketadmin

mp.component.amqPwd=Mp@2020

#队列，以“队列键:队列名:队列数量;队列键:队列名:队列数量”为格式，队列数量未配时，默认为1（注：队列键与代码绑定，确定后不能修改）

mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue

#是否启用待办消息, 默认为false, 如果没用到待办消息, 不要启用

mp.component.pendingMsg=true

#是否启用公告消息, 默认为false, 如果没用到公告消息, 不要启用

mp.component.noticeMsg=true

#是否启用CC消息, 默认为false, 如果没用到CC消息, 不要启用

mp.component.ccMsg=false

# 表单文件存放目录

mp.component.form.fileDir=/mpjava/form/files

#根据使用不同的数据库，扫描不到的DAL包,多个以","逗号分隔;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver

write.mp.jdbc.packagescan=com.ly.mp.**.mysql

#mp的数据库(主库)事务策略 普通: normal 多库(jta) : jta  多服务:tcc

write.mp.jdbc.transactionPolicy=gtsx

#没有配置这配置项不影响升级,作用是返回的默认集合查询结果转成大写.没有配置或者默认是true:key转成大写,false:key不作转换

write.mp.jdbc.upperCaseColumn=true

# url,username,password可以进行加密，使用密文

write.mp.jdbc.name=mp_write

write.mp.jdbc.url=*******************************************************************************************************************************************************************

write.mp.jdbc.username=adpuser

write.mp.jdbc.password=Adp@smart2024

other.write.mp.jdbc.name[0]=tidb

other.write.mp.jdbc.url[0]=**************************************************************************************************************************************************

other.write.mp.jdbc.username[0]=adpuser

other.write.mp.jdbc.password[0]=Adp@smart2024

#read.jdbc.name[mp_write#1]=default_mp_read

#read.jdbc.url[mp_write#1]=********************************************

#read.jdbc.username[mp_write#1]=mp24

#read.jdbc.password[mp_write#1]=mp24

mp.read.db.size=0

#druid datasource

#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE

druid.initialSize=3

druid.minIdle=3

druid.maxActive=20

druid.maxWait=60000

druid.timeBetweenEvictionRunsMillis=60000

druid.minEvictableIdleTimeMillis=300000

druid.validationQuery=select 1 from dual

druid.testWhileIdle=true

druid.testOnBorrow=false

druid.testOnReturn=false

druid.poolPreparedStatements=false

druid.maxPoolPreparedStatementPerConnectionSize=20

#druid.keepAlive=true

druid.phyTimeoutMillis=1200000

#wall,slf4j,stat

druid.filters=stat

#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3

#mp2.24增量配置

#mybatis

mybatis-plus.mapperLocations=classpath:/mybatis/mapping/*Mapper.xml,classpath*:/assembly/mapper/*Mapper.xml

#实体扫描，多个package用逗号或者分号分隔

mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities

mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums

#数据库相关配置

#主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";

mybatis-plus.global-config.db-config.id-type=UUID

#字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"

mybatis-plus.global-config.db-config.field-strategy=not_empty

#驼峰下划线转换

mybatis-plus.global-config.db-config.column-underline=true

#数据库大写下划线转换

#capital-mode: true

#逻辑删除配置

mybatis-plus.global-config.db-config.logic-delete-value=0

mybatis-plus.global-config.db-config.logic-not-delete-value=1

#mybatis-plus.global-config.db-config.db-type=sqlserver

#刷新mapper 调试神器

mybatis-plus.global-config.refresh=true

# 原生配置

mybatis-plus.configuration.map-underscore-to-camel-case=true

mybatis-plus.configuration.cache-enabled=false

mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

bucn.result.stack.enable=false
