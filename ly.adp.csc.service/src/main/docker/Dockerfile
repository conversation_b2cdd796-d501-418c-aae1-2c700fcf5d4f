#FROM swr.cn-east-3.myhuaweicloud.com/adp/alpine-oraclejdk8:v2
FROM swr.cn-east-3.myhuaweicloud.com/smart/skywalking-java-agent:8.5.0-jdk8
#FROM swr.cn-east-3.myhuaweicloud.com/smart-basic/skywalking-java-agent:8.5.0-jdk8_mss
#
#RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone
#
#RUN echo "ip_resolve=4" >> /etc/yum.conf
ENV app_name=ly.adp.csc.service
ENV configDir /home/<USER>/config

ADD $app_name/build/libs/*.jar /home/<USER>/app.jar


ENV env dev
ENV TZ=Asia/Shanghai

#ENV JAVA_OPTS="-server -Xmx1g -Xms1g -Xmn512m"

#skywalking
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES="skywalking-oap.skywalking.svc:11800"
ENV SW_OPTS="-javaagent:/skywalking/agent/skywalking-agent.jar"

USER root
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add --update ttf-dejavu fontconfig && \
		rm -rf /var/cache/apk/*
# copy arthas
#COPY --from=swr.cn-east-3.myhuaweicloud.com/mss-mid/arthas:mid /opt/arthas /mss/arthas
COPY --from=swr.cn-east-3.myhuaweicloud.com/adp/arthas:v1 /opt/arthas /mss/arthas
#USER mpjava
WORKDIR /home/<USER>



#ENTRYPOINT
ENTRYPOINT  java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd  java $JAVA_OPTS $SW_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd echo 'java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/app.jar' >start.sh && chmod 750 start.sh && ./start.sh
