##################################################################################################
# LY-MP-ADP-Deploy-YAML
# 服务名{{LYMPServiceName}}，如ly-mp-allinone-service或者adp-java-base-service
# 命名空间{{LYMPNameSpace}}，如adp-sit或者adp-uat或者adp-prod
# 镜像版本{{LYMPServiceVersion}}，如v1.0
# creater:<EMAIL>
##################################################################################################
##################################################################################################
# ConfigMap
##################################################################################################
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  managedFields:
    - manager: Go-http-client
      operation: Update
      apiVersion: v1
      time: '2021-11-17T07:01:20Z'
      fieldsType: FieldsV1
      fieldsV1:
        'f:data':
          .: {}
          'f:application-addition.properties': {}
        'f:metadata':
          'f:annotations':
            .: {}
            'f:description': {}
data:
  application-addition.properties: >-
    spring.application.name=ly-adp-csc-service

   
---
##################################################################################################
# Deployment
##################################################################################################
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  labels:
    app: {{LYMPServiceName}}
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{LYMPServiceName}}
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: {{LYMPServiceName}}
        version: v1
    spec:
      volumes:
        - name: vol-{{LYMPServiceName}}
          configMap:
            name: {{LYMPServiceName}}
            items:
              - key: application-addition.properties
                path: application-addition.properties
            defaultMode: 420
      containers:
        - name: container-0
          image: 'swr.cn-east-3.myhuaweicloud.com/adp/{{LYMPServiceName}}:{{LYMPServiceVersion}}'
          env:
            - name: PAAS_APP_NAME
              value: {{LYMPServiceName}}
            - name: PAAS_NAMESPACE
              value: {{LYMPNameSpace}}
            - name: PAAS_PROJECT_ID
              value: 0aeb4a9bbe80f3e72f6ec018437ca902
            - name: FORMAT_MESSAGES_PATTERN_DISABLE_LOOKUPS
              value: 'true'
          resources:
            limits:
              cpu: '1'
              memory: 1Gi
            requests:
              cpu: '1'
              memory: 1Gi
          volumeMounts:
            - name: vol-{{LYMPServiceName}}
              readOnly: true
              mountPath: /home/<USER>/config
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: {{LYMPServiceName}}
      serviceAccount: {{LYMPServiceName}}
      securityContext: {}
      imagePullSecrets:
        - name: default-secret
      affinity: {}
      schedulerName: default-scheduler
      tolerations:
        - key: node.kubernetes.io/not-ready
          operator: Exists
          effect: NoExecute
          tolerationSeconds: 0
        - key: node.kubernetes.io/unreachable
          operator: Exists
          effect: NoExecute
          tolerationSeconds: 0
      dnsConfig:
        options:
          - name: timeout
            value: ''
          - name: ndots
            value: '5'
          - name: single-request-reopen
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 0
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
---
##################################################################################################
# Service
##################################################################################################
kind: Service
apiVersion: v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  labels:
    app: {{LYMPServiceName}}
spec:
  ports:
    - name: http-cce-service-0
      protocol: TCP
      port: 8080
      targetPort: 8080
  selector:
    app: {{LYMPServiceName}}
  type: ClusterIP
status:
  loadBalancer: {}

