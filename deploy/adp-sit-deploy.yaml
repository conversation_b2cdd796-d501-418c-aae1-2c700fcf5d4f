##################################################################################################
# LY-MP-ADP-Deploy-YAML
# 服务名{{LYMPServiceName}}，如ly-mp-allinone-service或者adp-java-base-service
# 命名空间{{LYMPNameSpace}}，如adp-sit或者adp-uat或者adp-prod
# 镜像版本{{LYMPServiceVersion}}，如v1.0
# creater:<EMAIL>
##################################################################################################
##################################################################################################
# ConfigMap
##################################################################################################
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  managedFields:
    - manager: Go-http-client
      operation: Update
      apiVersion: v1
      time: '2021-11-17T07:01:20Z'
      fieldsType: FieldsV1
      fieldsV1:
        'f:data':
          .: {}
          'f:application-addition.properties': {}
        'f:metadata':
          'f:annotations':
            .: {}
            'f:description': {}
data:
  application-addition.properties: >-
      
    spring.application.name=ly-adp-csc-service

    seata.service.default.grouplist=seata-server.adp-sit.svc:8091

    #feign.refer

    refer.url.xapi.api=http://adp-java-xapi-api.adp-sit.svc:8080

    refer.url.mp.allinone=http://ly-mp-allinone-cloud-service.adp-sit.svc:8080

    refer.url.adp.base=http://adp-java-base-service.adp-sit.svc:8080

    #分布式的端口

    server.port=8080

    #Redis配置

    #多个servers用逗号(",")隔开,不需要配置redis从机的IP,只需要redis主机IP

    #sentinel模式的格式masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,例如mymaster?**************:63793,**************:63794

    #redis.session.servers=**************:6379,**************:6379,**************:6379

    redis.session.servers=**************:6379

    #redis密码,所有redis服务密码必须一样

    redis.session.password=adb@smart2021

    #redis.servers=**************:6379,**************:6379,**************:6379

    redis.servers=**************:6379

    redis.password=adb@smart2021

    #最大连接线程数

    redis.pool.maxActive=10000

    #连接超时时间(单位:秒)

    redis.pool.timeout=3000

    #缓存时间(单位:秒)

    redis.pool.expires=86400

    #在获取一个jedis实例时，是否提前进行alidate操作；如果为true，则得到的jedis实例均是可用的；

    redis.pool.testOnBorrow=true

    #在return给pool时，是否提前进行validate操作；

    redis.pool.testOnReturn=true

    #session time out (单位:秒)

    session.timeout=1800

    #  redisson分布式锁配置

    # 是否启用分布式锁 true:启用 false:不启用,默认不启用, 如果没用到分布式锁 不要启用

    redisson.redis.enable=true

    # 集群时，需要所有主从节点地址

    redisson.redis.servers=**************:6379

    redisson.redis.password=adb@smart2021

    # 监控锁的看门狗超时（宕机或进程挂了释放锁的超时时间），单位：毫秒。默认值：30000

    redisson.redis.lockWatchdogTimeout=20000

    # 集群状态扫描间隔时间，单位是毫秒。默认值： 1000

    redisson.redis.scanInterval=1000

    # 多主节点的环境里，每个 主节点的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64

    redisson.redis.masterConnectionPoolSize=64

    # 多从节点的环境里，每个 从服务节点里用于普通操作（非 发布和订阅）连接的连接池最大容量。连接池的连接数量自动弹性伸缩。默认值：64

    redisson.redis.slaveConnectionPoolSize=64

    #是否启用AMQ(true,false)

    mp.component.amqOpen=false

    # MQ类型 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (默认使用1:RabbitMQ, 如果没有设置amqType, 为兼容之前版本使用ActiveMQ)

    mp.component.amqType=3

    mp.component.amqUrl=172.26.165.86:9876;172.26.165.93:9876

    # MQ端口，只对RabbitMQ有用

    mp.component.amqPort=5672

    mp.component.amqUser=rocketadmin

    mp.component.amqPwd=Mp@2020

    #队列，以“队列键:队列名:队列数量;队列键:队列名:队列数量”为格式，队列数量未配时，默认为1（注：队列键与代码绑定，确定后不能修改）

    mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue

    #是否启用待办消息, 默认为false, 如果没用到待办消息, 不要启用

    mp.component.pendingMsg=true

    #是否启用公告消息, 默认为false, 如果没用到公告消息, 不要启用

    mp.component.noticeMsg=true

    #是否启用CC消息, 默认为false, 如果没用到CC消息, 不要启用

    mp.component.ccMsg=false

    # 表单文件存放目录

    mp.component.form.fileDir=/mpjava/form/files

    #根据使用不同的数据库，扫描不到的DAL包,多个以","逗号分隔;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver

    write.mp.jdbc.packagescan=com.ly.mp.**.mysql

    #mp的数据库(主库)事务策略 普通: normal 多库(jta) : jta  多服务:tcc

    write.mp.jdbc.transactionPolicy=gtsx

    #没有配置这配置项不影响升级,作用是返回的默认集合查询结果转成大写.没有配置或者默认是true:key转成大写,false:key不作转换

    write.mp.jdbc.upperCaseColumn=true

    # url,username,password可以进行加密，使用密文

    write.mp.jdbc.name=mp_write

    write.mp.jdbc.url=**************************************************************************************************************

    write.mp.jdbc.username=root

    write.mp.jdbc.password=adb@smart2021

    other.write.mp.jdbc.name[0]=tidb

    other.write.mp.jdbc.url[0]=********************************************************************************************************************

    other.write.mp.jdbc.username[0]=root

    other.write.mp.jdbc.password[0]=adb@smart2021

    #read.jdbc.name[mp_write#1]=default_mp_read

    #read.jdbc.url[mp_write#1]=********************************************

    #read.jdbc.username[mp_write#1]=mp24

    #read.jdbc.password[mp_write#1]=mp24

    mp.read.db.size=0

    #druid datasource

    #https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE

    druid.initialSize=3

    druid.minIdle=3

    druid.maxActive=20

    druid.maxWait=60000

    druid.timeBetweenEvictionRunsMillis=60000

    druid.minEvictableIdleTimeMillis=300000

    druid.validationQuery=select 1 from dual

    druid.testWhileIdle=true

    druid.testOnBorrow=false

    druid.testOnReturn=false

    druid.poolPreparedStatements=false

    druid.maxPoolPreparedStatementPerConnectionSize=20

    #druid.keepAlive=true

    druid.phyTimeoutMillis=1200000

    #wall,slf4j,stat

    druid.filters=stat

    #druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3

    #mp2.24增量配置

    #mybatis

    mybatis-plus.mapperLocations=classpath:/mybatis/mapping/*Mapper.xml

    #实体扫描，多个package用逗号或者分号分隔

    mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities

    mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums

    #数据库相关配置

    #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";

    mybatis-plus.global-config.db-config.id-type=UUID

    #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"

    mybatis-plus.global-config.db-config.field-strategy=not_empty

    #驼峰下划线转换

    mybatis-plus.global-config.db-config.column-underline=true

    #数据库大写下划线转换

    #capital-mode: true

    #逻辑删除配置

    mybatis-plus.global-config.db-config.logic-delete-value=0

    mybatis-plus.global-config.db-config.logic-not-delete-value=1

    #mybatis-plus.global-config.db-config.db-type=sqlserver

    #刷新mapper 调试神器

    mybatis-plus.global-config.refresh=true

    # 原生配置

    mybatis-plus.configuration.map-underscore-to-camel-case=true

    mybatis-plus.configuration.cache-enabled=false

    mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
---
##################################################################################################
# Deployment
##################################################################################################
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  labels:
    app: {{LYMPServiceName}}
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{LYMPServiceName}}
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: {{LYMPServiceName}}
        version: v1
      annotations:
        metrics.alpha.kubernetes.io/custom-endpoints: '[{"api":"","path":"","port":"","names":""}]'
        sidecar.istio.io/inject: 'true'
    spec:
      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime
            type: ''
        - name: vol-{{LYMPServiceName}}
          configMap:
            name: {{LYMPServiceName}}
            items:
              - key: application-addition.properties
                path: application-addition.properties
            defaultMode: 420
      containers:
        - name: container-0
          image: 'swr.cn-east-3.myhuaweicloud.com/adp/{{LYMPServiceName}}:{{LYMPServiceVersion}}'
          env:
            - name: PAAS_APP_NAME
              value: {{LYMPServiceName}}
            - name: PAAS_NAMESPACE
              value: {{LYMPNameSpace}}
            - name: PAAS_PROJECT_ID
              value: 0aeb4a9bbe80f3e72f6ec018437ca902
            - name: FORMAT_MESSAGES_PATTERN_DISABLE_LOOKUPS
              value: 'true'
          resources:
            limits:
              cpu: '4'
              memory: 4Gi
            requests:
              cpu: '4'
              memory: 4Gi
          volumeMounts:
            - name: localtime
              readOnly: true
              mountPath: /etc/localtime
            - name: vol-{{LYMPServiceName}}
              readOnly: true
              mountPath: /home/<USER>/config
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: {{LYMPServiceName}}
      serviceAccount: {{LYMPServiceName}}
      securityContext: {}
      imagePullSecrets:
        - name: default-secret
      affinity: {}
      schedulerName: default-scheduler
      tolerations:
        - key: node.kubernetes.io/not-ready
          operator: Exists
          effect: NoExecute
          tolerationSeconds: 0
        - key: node.kubernetes.io/unreachable
          operator: Exists
          effect: NoExecute
          tolerationSeconds: 0
      dnsConfig:
        options:
          - name: timeout
            value: ''
          - name: ndots
            value: '5'
          - name: single-request-reopen
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 0
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
---
##################################################################################################
# Service
##################################################################################################
kind: Service
apiVersion: v1
metadata:
  name: {{LYMPServiceName}}
  namespace: {{LYMPNameSpace}}
  labels:
    app: {{LYMPServiceName}}
spec:
  ports:
    - name: http-cce-service-0
      protocol: TCP
      port: 8080
      targetPort: 8080
  selector:
    app: {{LYMPServiceName}}
  type: ClusterIP
status:
  loadBalancer: {}